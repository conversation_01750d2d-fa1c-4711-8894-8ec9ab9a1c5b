#!/usr/bin/env python3
"""
Script de test pour vérifier que les logs API fonctionnent correctement
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:5000"
USERNAME = "admin"  # Remplacez par vos identifiants
PASSWORD = "admin123"

def get_jwt_token():
    """Obtenir un token JWT"""
    login_data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        return response.json().get('access_token')
    else:
        print(f"❌ Login failed: {response.status_code} - {response.text}")
        return None

def test_live_logs_api(token, scan_id):
    """Tester l'API des logs en temps réel"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print(f"🔍 Testing live logs API for scan: {scan_id}")
    response = requests.get(f"{BASE_URL}/scan/pentesting/scan/logs/{scan_id}/live", headers=headers)
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Live logs API working")
        print(f"   Scan status: {data.get('status')}")
        print(f"   Current tool: {data.get('current_tool')}")
        print(f"   Latest logs count: {len(data.get('latest_logs', []))}")
        
        # Afficher les derniers logs
        latest_logs = data.get('latest_logs', [])
        if latest_logs:
            print("   Recent logs:")
            for log in latest_logs[:5]:  # Afficher les 5 derniers
                timestamp = log.get('timestamp', '')[:19]  # Format court
                level = log.get('level', '').upper()
                message = log.get('message', '')[:80]  # Tronquer si trop long
                print(f"     {timestamp} [{level}] {message}")
        
        return data
    else:
        print(f"❌ Error: {response.text}")
        return None

def start_test_scan(token):
    """Démarrer un scan de test pour avoir des logs"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    scan_data = {
        "target_url": "http://testphp.vulnweb.com",
        "scan_type": "basic"
    }
    
    print("🚀 Starting test web scan...")
    response = requests.post(f"{BASE_URL}/scan/pentesting/scan/web", 
                           headers=headers, json=scan_data)
    
    if response.status_code == 200:
        scan_id = response.json().get('scan_id')
        print(f"✅ Test scan started: {scan_id}")
        return scan_id
    else:
        print(f"❌ Failed to start test scan: {response.text}")
        return None

def get_running_scans(token):
    """Récupérer les scans en cours"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(f"{BASE_URL}/scan/pentesting/scans/running", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        return data.get('running_scans', [])
    else:
        print(f"❌ Error getting running scans: {response.text}")
        return []

def main():
    print("🧪 Testing Logs API")
    print("=" * 50)
    
    # 1. Obtenir le token
    print("1. Getting JWT token...")
    token = get_jwt_token()
    if not token:
        print("❌ Cannot proceed without token")
        return
    print("✅ Token obtained")
    print()
    
    # 2. Vérifier les scans en cours
    print("2. Checking for running scans...")
    running_scans = get_running_scans(token)
    
    if running_scans:
        print(f"✅ Found {len(running_scans)} running scans")
        for scan in running_scans:
            print(f"   - {scan['scan_id'][:8]}... ({scan['category']}/{scan['scan_type']}) on {scan['target']}")
    else:
        print("⚠️ No running scans found, starting a test scan...")
        test_scan_id = start_test_scan(token)
        if test_scan_id:
            time.sleep(3)  # Attendre un peu pour que le scan démarre
            running_scans = get_running_scans(token)
    
    print()
    
    # 3. Tester l'API des logs pour chaque scan en cours
    if running_scans:
        print("3. Testing logs API for running scans...")
        for scan in running_scans:
            scan_id = scan['scan_id']
            print(f"\n   Testing logs for scan: {scan_id[:8]}...")
            
            # Tester l'API des logs en temps réel
            logs_data = test_live_logs_api(token, scan_id)
            
            if logs_data:
                print(f"   ✅ Logs API working for scan {scan_id[:8]}")
            else:
                print(f"   ❌ Logs API failed for scan {scan_id[:8]}")
    else:
        print("❌ No running scans to test logs with")
    
    print("\n" + "=" * 50)
    print("🏁 Test completed!")

if __name__ == "__main__":
    main()
