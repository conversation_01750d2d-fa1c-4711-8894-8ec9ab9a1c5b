# Guide d'utilisation des tests Postman

## Fichiers créés

1. **Vulnerability_API_Tests.postman_collection.json** - Collection complète des tests
2. **Vulnerability_API_Environment.postman_environment.json** - Environnement avec variables
3. **GUIDE_POSTMAN.md** - Ce guide d'utilisation

## Import dans Postman

### 1. Importer la collection
1. Ouvrez Postman
2. Cliquez sur **Import** (bouton en haut à gauche)
3. Sélectionnez le fichier `Vulnerability_API_Tests.postman_collection.json`
4. Cliquez sur **Import**

### 2. Importer l'environnement
1. Cliquez sur **Import** 
2. Sélectionnez le fichier `Vulnerability_API_Environment.postman_environment.json`
3. Cliquez sur **Import**
4. Sélectionnez l'environnement "Vulnerability API Environment" dans le menu déroulant en haut à droite

## Tests disponibles

### 1. API Info
- **Méthode :** GET
- **URL :** `/`
- **Description :** Teste les informations de base de l'API
- **Tests automatiques :** Statut 200, structure de réponse, version API

### 2. Start Analysis - Full Report
- **Méthode :** POST
- **URL :** `/analyze`
- **Description :** Lance une analyse complète avec IA
- **Configuration requise :** 
  - Sélectionner un fichier CSV dans `Body > form-data > file`
  - `generate_full_report` est défini à `true`
- **Tests automatiques :** Sauvegarde automatique de l'analysis_id

### 3. Start Analysis - Basic Report with Scan IDs
- **Méthode :** POST
- **URL :** `/analyze`
- **Description :** Lance une analyse basique avec filtrage par scan IDs
- **Configuration requise :**
  - Sélectionner un fichier CSV dans `Body > form-data > file`
  - `scan_ids` défini à "scan1,scan2,scan3" (modifiable)
  - `generate_full_report` défini à `false`

### 4. Check Analysis Status
- **Méthode :** GET
- **URL :** `/status/{{analysis_id}}`
- **Description :** Vérifie le statut d'une analyse en cours
- **Tests automatiques :** Validation du statut et progression

### 5. Get Analysis Report
- **Méthode :** GET
- **URL :** `/report/{{analysis_id}}`
- **Description :** Récupère le rapport final
- **Tests automatiques :** Gestion des différents statuts (200, 202, 500, 404)

### 6. List All Analyses
- **Méthode :** GET
- **URL :** `/analyses`
- **Description :** Liste toutes les analyses effectuées
- **Tests automatiques :** Structure de réponse et cohérence des données

### 7. Test Error - Invalid File Type
- **Méthode :** POST
- **URL :** `/analyze`
- **Description :** Teste la gestion d'erreur pour fichier non-CSV
- **Configuration requise :** Sélectionner un fichier non-CSV (ex: .txt)

### 8. Test Error - Invalid Analysis ID
- **Méthode :** GET
- **URL :** `/status/invalid-analysis-id-12345`
- **Description :** Teste la gestion d'erreur pour ID invalide
- **Tests automatiques :** Validation erreur 404

## Workflow recommandé

### Test complet d'une analyse

1. **Démarrer l'API** : `python vulnerability_api.py`
2. **Test 1** : API Info - Vérifier que l'API fonctionne
3. **Test 2** : Start Analysis - Lancer une analyse (sélectionner votre fichier CSV)
4. **Test 4** : Check Status - Vérifier le statut (répéter jusqu'à completion)
5. **Test 5** : Get Report - Récupérer le rapport final
6. **Test 6** : List Analyses - Voir toutes les analyses

### Test des erreurs

7. **Test 7** : Invalid File Type - Tester avec un fichier .txt
8. **Test 8** : Invalid Analysis ID - Tester avec un ID inexistant

## Configuration des fichiers

### Pour les tests d'upload (Tests 2, 3, 7)

1. Allez dans l'onglet **Body**
2. Sélectionnez **form-data**
3. Pour la clé `file` :
   - Changez le type de "Text" à "File"
   - Cliquez sur "Select Files"
   - Choisissez votre fichier CSV (ou .txt pour le test d'erreur)

### Variables automatiques

Les variables suivantes sont automatiquement gérées :
- `analysis_id` : Sauvegardé automatiquement après le test 2
- `analysis_id_basic` : Sauvegardé automatiquement après le test 3
- `base_url` : Défini à `http://*************:4001`

## Logs et debugging

Chaque test affiche des informations dans la console Postman :
- Statut des requêtes
- IDs d'analyse sauvegardés
- Progression des analyses
- Messages d'erreur détaillés

Pour voir les logs :
1. Ouvrez la console Postman (View > Show Postman Console)
2. Exécutez vos tests
3. Consultez les messages dans la console

## Personnalisation

### Modifier l'URL de base
1. Allez dans l'environnement "Vulnerability API Environment"
2. Modifiez la valeur de `base_url`
3. Sauvegardez

### Modifier les scan_ids
1. Dans le test "Start Analysis - Basic Report with Scan IDs"
2. Modifiez la valeur de `scan_ids` dans Body > form-data
3. Utilisez le format : "scan1,scan2,scan3"

### Ajouter de nouveaux tests
1. Dupliquez un test existant
2. Modifiez l'URL et les paramètres
3. Adaptez les scripts de test dans l'onglet "Tests"

## Résolution de problèmes

### L'API ne répond pas
- Vérifiez que `python vulnerability_api.py` est en cours d'exécution
- Vérifiez que l'URL de base est correcte (`http://*************:4001`)

### Erreur "analysis_id not found"
- Exécutez d'abord le test "Start Analysis" pour générer un analysis_id
- Vérifiez que la variable `analysis_id` est bien définie dans l'environnement

### Fichier CSV non accepté
- Vérifiez que le fichier a bien l'extension .csv
- Vérifiez que le fichier contient les colonnes attendues

### Ollama non disponible
- Démarrez Ollama : `ollama serve`
- Vérifiez que le modèle Mistral est installé : `ollama pull mistral`

## Scripts de test personnalisés

Vous pouvez modifier les scripts de test dans l'onglet "Tests" de chaque requête. Exemples de tests utiles :

```javascript
// Vérifier un champ spécifique
pm.test("Field exists", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData).to.have.property('field_name');
});

// Sauvegarder une variable
pm.environment.set("variable_name", "value");

// Récupérer une variable
var value = pm.environment.get("variable_name");

// Test de temps de réponse
pm.test("Response time is acceptable", function () {
    pm.expect(pm.response.responseTime).to.be.below(5000);
});
```

Cette collection Postman vous permet de tester complètement votre API d'analyse de vulnérabilités de manière automatisée et reproductible.