# PICA - Automated Cybersecurity Platform
## Version 1.0.01

🛡️ **PICA** (Automated Cybersecurity Platform) is a comprehensive automated pentesting solution that integrates the best security tools in a modern web interface.

---

## 🚀 Main Features

### 🔍 4 Automated Scan Categories

| Category | Tools | Description |
|-----------|--------|-------------|
| 🟦 **Network** | Nmap + OpenVAS + Metasploit | Network and infrastructure analysis |
| 🟩 **Web** | Nikto + SQLMap + Dirb + GoBuster + ZAP | Web application security testing |
| 🟪 **Vulnerability** | OpenVAS + Metasploit | Targeted vulnerability detection |
| 🟥 **Deep** | All tools | Complete audit with progression |

### 🎯 Intensity Types
- **Basic** : Quick and non-intrusive scan
- **Aggressive** : In-depth testing and advanced detection
- **Stealth** : Stealthy scan to avoid detection
- **Comprehensive** : Maximum complete analysis

---

## 📋 Documentation

- 📖 **[Scan Guide](README_SCANS.md)** - Complete user documentation
- 🔧 **[Technical Documentation](README_TECHNICAL.md)** - Developer guide and API

---

## 🛠️ Installation

### Prerequisites
```bash
# System
Ubuntu 20.04+ / Debian 11+
Python 3.8+
Node.js 16+
MongoDB 5.0+
Docker & Docker Compose

# Pentesting tools
sudo apt update
sudo apt install nmap nikto sqlmap dirb gobuster zaproxy
```

### Installation Backend
```bash
cd backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### Installation Frontend  
```bash
cd frontend
npm install
```

### OpenVAS/GVM (Docker)
```bash
# Télécharger et démarrer Greenbone Community Edition
docker-compose -f docker-compose-openvas.yml up -d
```

---

## 🚀 Démarrage

### Backend (Port 5000)
```bash
cd backend
python3 run.py
```

### Frontend (Port 3000)
```bash
cd frontend  
npm start
```

### Accès
- **Interface Web :** http://localhost:3000
- **API Backend :** http://localhost:5000
- **OpenVAS :** http://localhost:9392

---

## 🎮 Utilisation Rapide

### 1. Connexion
- Créer un compte ou se connecter
- Accéder au module Pentesting

### 2. Lancer un Scan
```bash
# Via interface web
1. Sélectionner catégorie (Network/Web/Vulnerability/Deep)
2. Choisir type (Basic/Aggressive/Stealth/Comprehensive)  
3. Entrer cible (IP ou URL)
4. Configurer ports (optionnel)
5. Démarrer le scan

# Via API
curl -X POST http://localhost:5000/scan/pentesting/scan/web \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"target_url": "http://example.com", "scan_type": "basic"}'
```

### 3. Suivre la Progression
- Progression en temps réel
- Logs détaillés
- Résultats intermédiaires

### 4. Analyser les Résultats
- Vulnérabilités classées par sévérité
- Ports et services découverts
- Recommandations de sécurité
- Export des rapports

---

## 🔧 Architecture

```
PICA/
├── backend/                 # API Flask + Services
│   ├── app/
│   │   ├── controllers/     # Contrôleurs API
│   │   ├── services/        # Intégrations outils
│   │   ├── models/          # Modèles MongoDB
│   │   └── utils/           # Utilitaires
│   └── run.py              # Point d'entrée
├── frontend/               # Interface React
│   ├── src/
│   │   ├── pages/          # Pages principales
│   │   ├── components/     # Composants réutilisables
│   │   └── services/       # Services API
└── docs/                   # Documentation
```

---

## 🛡️ Sécurité

### Authentification
- JWT avec expiration configurable
- Rôles utilisateur (Admin/User)
- 2FA optionnel

### Autorisation
- Scans isolés par utilisateur
- Accès admin pour gestion globale
- Validation des cibles

### Audit
- Logs complets des actions
- Traçabilité des scans
- Historique des résultats

---

## 📊 Outils Intégrés

| Outil | Version | Statut | Fonction |
|-------|---------|--------|----------|
| **Nmap** | 7.94+ | ✅ | Scan ports/services |
| **OpenVAS** | 23.x | ✅ | Scanner vulnérabilités |
| **Nikto** | 2.5+ | ✅ | Scanner web |
| **SQLMap** | 1.7+ | ✅ | Injection SQL |
| **Dirb** | 2.22+ | ✅ | Énumération répertoires |
| **GoBuster** | 3.6+ | ✅ | Énumération rapide |
| **OWASP ZAP** | 2.14+ | ✅ | Proxy sécurité |
| **Metasploit** | 6.x | 🔄 | Framework exploitation |

---

## ⚠️ Avertissements Légaux

**IMPORTANT :** PICA est un outil de sécurité professionnel.

- ✅ **Autorisé :** Tests sur vos propres systèmes
- ✅ **Autorisé :** Tests avec autorisation écrite explicite
- ❌ **Interdit :** Tests sur systèmes tiers sans autorisation
- ❌ **Interdit :** Utilisation malveillante

L'utilisateur est seul responsable de l'utilisation conforme aux lois locales et internationales.

---

## 📞 Support

- 📧 **Email :** <EMAIL>
- 📚 **Documentation :** Voir fichiers README_*.md
- 🐛 **Issues :** Rapporter les problèmes via les logs
- 💬 **Contact :** Équipe de développement PICA

---

## 📄 Licence

MIT License - Voir LICENSE pour plus de détails.

---

## 🙏 Remerciements

Merci aux équipes de développement des outils intégrés :
- Nmap Project
- Greenbone Networks (OpenVAS)
- OWASP (ZAP)
- SQLMap Team
- Et tous les contributeurs open source

---

**PICA - Sécurisez votre infrastructure avec confiance** 🛡️
