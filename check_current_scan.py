#!/usr/bin/env python3
"""
Script to check the current running scan status and logs
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.extensions import mongo
from app import create_app
from datetime import datetime

def check_running_scans():
    """Check all running scans"""
    app = create_app()
    
    with app.app_context():
        print("🔍 Checking running scans...")
        
        # Find running scans
        running_scans = list(mongo.db.scans.find({'status': 'running'}))
        
        print(f"Found {len(running_scans)} running scans:")
        
        for scan in running_scans:
            print(f"\n📋 Scan ID: {scan['scan_id']}")
            print(f"   Category: {scan.get('category', 'N/A')}")
            print(f"   Type: {scan.get('scan_type', 'N/A')}")
            print(f"   Target: {scan.get('target', 'N/A')}")
            print(f"   Status: {scan.get('status', 'N/A')}")
            print(f"   Current Tool: {scan.get('current_tool', 'N/A')}")
            print(f"   Progress: {scan.get('progress', 0)}%")
            print(f"   Start Time: {scan.get('start_time', 'N/A')}")
            
            # Check logs for this scan
            logs = list(mongo.db.scan_logs.find(
                {'scan_id': scan['scan_id']},
                {'_id': 0}
            ).sort('timestamp', -1).limit(10))
            
            print(f"   Recent Logs ({len(logs)} entries):")
            for log in logs:
                timestamp = log.get('timestamp', 'N/A')
                if timestamp != 'N/A':
                    try:
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        timestamp = dt.strftime('%H:%M:%S')
                    except:
                        pass
                
                print(f"     [{timestamp}] {log.get('level', 'INFO').upper()}: {log.get('message', 'No message')}")
            
            # Check tools status
            if 'tools_status' in scan:
                print(f"   Tools Status:")
                for tool, status in scan['tools_status'].items():
                    print(f"     {tool}: {status.get('status', 'unknown')} ({status.get('progress', 0)}%)")

if __name__ == '__main__':
    check_running_scans()
