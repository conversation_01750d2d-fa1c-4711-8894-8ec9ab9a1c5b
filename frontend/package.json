{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,html}\""}, "dependencies": {"@heroicons/react": "^2.2.0", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/postcss": "^4.1.10", "@types/js-cookie": "^3.0.6", "@types/react-router-dom": "^5.3.3", "axios": "^1.9.0", "clsx": "^2.1.1", "framer-motion": "^12.18.1", "js-cookie": "^3.0.5", "lucide-react": "^0.515.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.5", "prettier": "^3.6.2", "tailwindcss": "^4.1.10", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}