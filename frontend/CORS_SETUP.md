# Configuration CORS pour le serveur Analytics

## 🚫 Problème détecté

Le serveur Analytics à `http://*************:4001` bloque les requêtes depuis `http://localhost:5173` à cause de la politique CORS (Cross-Origin Resource Sharing).

## ✅ Solution

Le serveur Analytics doit être configuré pour autoriser les requêtes depuis le frontend de développement.

### Headers CORS requis

Ajoutez ces headers HTTP à votre serveur Analytics :

```
Access-Control-Allow-Origin: http://localhost:5173
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization
Access-Control-Allow-Credentials: true
```

### Configuration selon le framework

#### Flask (Python)
```python
from flask_cors import CORS

app = Flask(__name__)
CORS(app, origins=["http://localhost:5173"])
```

#### Express.js (Node.js)
```javascript
const cors = require('cors');

app.use(cors({
  origin: 'http://localhost:5173',
  credentials: true
}));
```

#### FastAPI (Python)
```python
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

#### Nginx (Reverse Proxy)
```nginx
location / {
    add_header Access-Control-Allow-Origin "http://localhost:5173";
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
    add_header Access-Control-Allow-Headers "Content-Type, Authorization";
    add_header Access-Control-Allow-Credentials "true";
    
    if ($request_method = 'OPTIONS') {
        return 204;
    }
    
    proxy_pass http://your-analytics-server;
}
```

## 🔧 Test de la configuration

Une fois CORS configuré :

1. Redémarrez votre serveur Analytics
2. Dans PICA, allez sur la page Analytics
3. Cliquez sur "Retester la connexion"
4. L'indicateur devrait passer au vert ✅

## 🌐 Pour la production

En production, remplacez `http://localhost:5173` par l'URL de votre frontend :

```
Access-Control-Allow-Origin: https://votre-domaine.com
```

## 📋 Endpoints requis

Assurez-vous que ces endpoints existent sur votre serveur :

- `POST /analyze` - Lancement d'analyses
- `GET /status/{analysis_id}` - Statut des analyses  
- `GET /report/{analysis_id}` - Récupération des rapports
- `GET /analyses` - Liste des analyses

## 🐛 Debugging

Si les problèmes persistent :

1. Vérifiez les logs du serveur Analytics
2. Utilisez les outils de développement du navigateur (F12 → Network)
3. Testez avec un outil comme Postman ou curl
4. Vérifiez que le serveur écoute bien sur le port 4001

## 📞 Support

Si vous avez besoin d'aide, vérifiez :
- Les logs du serveur Analytics
- La configuration réseau/firewall
- Les headers de réponse dans les outils de développement
