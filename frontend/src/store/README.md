# Redux Store - PICA Frontend

Ce dossier contient la configuration Redux avec Redux Toolkit pour la gestion d'état globale de l'application PICA.

## Structure

```
store/
├── index.ts          # Configuration du store principal
├── hooks.ts          # Hooks Redux typés
├── slices/
│   ├── alertSlice.ts # Gestion des alertes/notifications
│   └── userSlice.ts  # Gestion des utilisateurs et authentification
└── README.md         # Cette documentation
```

## 🚨 Alert Slice

### Fonctionnalités
- Gestion centralisée des alertes (succès, erreur, avertissement, info)
- Support de multiples alertes simultanées
- Auto-suppression après durée configurable
- Positions personnalisables
- Limite du nombre d'alertes affichées

### Utilisation

```typescript
import { useReduxAlerts } from '../hooks/useAlerts';

function MyComponent() {
  const { showSuccess, showError, showWarning, showInfo, clearAll } = useReduxAlerts();

  const handleSuccess = () => {
    showSuccess('Opération réussie !');
  };

  const handleError = () => {
    showError('Une erreur est survenue');
  };

  return (
    <div>
      <button onClick={handleSuccess}>Succès</button>
      <button onClick={handleError}>Erreur</button>
      <button onClick={clearAll}>Effacer toutes</button>
    </div>
  );
}
```

### Actions disponibles
- `addAlert(alert)` - Ajouter une nouvelle alerte
- `removeAlert(id)` - Supprimer une alerte par ID
- `hideAlert(id)` - Masquer une alerte (animation)
- `clearAllAlerts()` - Supprimer toutes les alertes
- `clearAlertsByType(type)` - Supprimer par type
- `updateAlertSettings(settings)` - Modifier les paramètres

## 👤 User Slice

### Fonctionnalités
- Gestion de l'authentification (token, refresh token)
- Profil utilisateur complet
- Préférences utilisateur
- Cache des utilisateurs (pour admins)
- Actions asynchrones (login, register, update)
- Gestion des erreurs

### Utilisation

```typescript
import { useReduxAuth } from '../store/hooks';
import { loginUser, setCurrentUser } from '../store/slices/userSlice';

function LoginComponent() {
  const { currentUser, isAuthenticated, isLoading, dispatch } = useReduxAuth();

  const handleLogin = async () => {
    const result = await dispatch(loginUser({
      email: '<EMAIL>',
      password: 'password123',
      rememberMe: true
    }));
  };

  return (
    <div>
      {isAuthenticated ? (
        <p>Bonjour {currentUser?.firstName}!</p>
      ) : (
        <button onClick={handleLogin}>Se connecter</button>
      )}
    </div>
  );
}
```

### Actions disponibles
- `loginUser(credentials)` - Connexion (async)
- `registerUser(userData)` - Inscription (async)
- `fetchCurrentUser()` - Récupérer profil (async)
- `updateUserProfile(updates)` - Mettre à jour profil (async)
- `setAuthToken(tokenData)` - Définir token
- `clearAuth()` - Déconnexion
- `setCurrentUser(user)` - Définir utilisateur
- `updateCurrentUser(updates)` - Mise à jour locale

## 🔧 Hooks personnalisés

### useReduxAlerts()
Hook simplifié pour les alertes avec le système AlertCenter existant.

### useReduxAuth()
Hook pour l'authentification et les données utilisateur Redux (distinct du useAuth existant).

### useUserRole()
Hook pour vérifier les rôles utilisateur.

```typescript
import { useUserRole } from '../store/hooks';

function AdminPanel() {
  const { isAdmin, isModerator, role } = useUserRole();

  if (!isAdmin) {
    return <div>Accès refusé</div>;
  }

  return <div>Panel Admin</div>;
}
```

## 🎯 Intégration avec AlertCenter

Le système utilise le composant `AlertCenter` existant via `ReduxAlertDisplay` :

```typescript
// Dans votre layout principal
import ReduxAlertDisplay from '../components/alerts/ReduxAlertDisplay';

function Layout() {
  return (
    <div>
      <ReduxAlertDisplay />
      {/* Votre contenu */}
    </div>
  );
}
```

## 🧪 Page de démonstration

Visitez `/redux-demo` pour voir une démonstration interactive des slices :
- Test des différents types d'alertes
- Manipulation de l'état utilisateur
- Visualisation de l'état Redux en temps réel

## 📝 Types TypeScript

Tous les slices sont entièrement typés avec TypeScript :

```typescript
// Types d'alerte
interface Alert {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  title?: string;
  duration?: number;
  timestamp: number;
  isVisible: boolean;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

// Types utilisateur
interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  username: string;
  role: 'admin' | 'user' | 'moderator';
  // ... autres propriétés
}
```

## 🚀 Prochaines étapes

1. Intégrer les slices dans les pages existantes (Login, Register)
2. Ajouter la persistance avec Redux Persist
3. Implémenter les actions asynchrones avec les vraies APIs
4. Ajouter plus de slices selon les besoins (notifications, settings, etc.)
