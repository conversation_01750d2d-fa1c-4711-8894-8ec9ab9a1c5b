import { configureStore } from '@reduxjs/toolkit';
import alertSlice from './slices/alertSlice';
import userSlice from './slices/userSlice';

export const store = configureStore({
  reducer: {
    alerts: alertSlice,
    user: userSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
