import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Types pour l'utilisateur
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  username: string;
  dateOfBirth: string;
  gender: string;
  role: 'admin' | 'user' | 'moderator';
  isEmailVerified: boolean;
  avatar?: string;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
  preferences?: UserPreferences;
  profile?: UserProfile;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: 'fr' | 'en';
  notifications: {
    email: boolean;
    push: boolean;
    security: boolean;
  };
  privacy: {
    profileVisibility: 'public' | 'private' | 'friends';
    showEmail: boolean;
    showLastLogin: boolean;
  };
}

export interface UserProfile {
  bio?: string;
  location?: string;
  website?: string;
  company?: string;
  skills?: string[];
  socialLinks?: {
    linkedin?: string;
    twitter?: string;
    github?: string;
  };
}

export interface AuthState {
  isAuthenticated: boolean;
  token: string | null;
  refreshToken: string | null;
  tokenExpiry: number | null;
  rememberMe: boolean;
}

interface UserState {
  // Données utilisateur
  currentUser: User | null;

  // État d'authentification
  auth: AuthState;

  // États de chargement
  isLoading: boolean;
  isUpdating: boolean;
  isDeleting: boolean;

  // Gestion des erreurs
  error: string | null;

  // Cache des utilisateurs (pour les admins)
  usersList: User[];
  usersListLoading: boolean;
  usersListError: string | null;

  // Pagination pour la liste des utilisateurs
  pagination: {
    currentPage: number;
    totalPages: number;
    totalUsers: number;
    usersPerPage: number;
  };

  // Filtres et recherche
  filters: {
    search: string;
    role: string;
    status: 'all' | 'verified' | 'unverified';
    sortBy: 'createdAt' | 'lastLogin' | 'email' | 'firstName';
    sortOrder: 'asc' | 'desc';
  };
}

const initialState: UserState = {
  currentUser: null,
  auth: {
    isAuthenticated: false,
    token: null,
    refreshToken: null,
    tokenExpiry: null,
    rememberMe: false,
  },
  isLoading: false,
  isUpdating: false,
  isDeleting: false,
  error: null,
  usersList: [],
  usersListLoading: false,
  usersListError: null,
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalUsers: 0,
    usersPerPage: 10,
  },
  filters: {
    search: '',
    role: '',
    status: 'all',
    sortBy: 'createdAt',
    sortOrder: 'desc',
  },
};

// Actions asynchrones (thunks)
export const loginUser = createAsyncThunk(
  'user/login',
  async (
    credentials: { email: string; password: string; rememberMe?: boolean },
    { rejectWithValue }
  ) => {
    try {
      // Ici vous appelleriez votre API de connexion
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(credentials),
      });

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.message || 'Erreur de connexion');
      }

      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Erreur réseau');
    }
  }
);

export const registerUser = createAsyncThunk(
  'user/register',
  async (
    userData: Omit<User, 'id' | 'createdAt' | 'updatedAt' | 'isEmailVerified'> & {
      password: string;
    },
    { rejectWithValue }
  ) => {
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.message || "Erreur d'inscription");
      }

      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Erreur réseau');
    }
  }
);

export const fetchCurrentUser = createAsyncThunk(
  'user/fetchCurrent',
  async (_, { rejectWithValue, getState }) => {
    try {
      const state = getState() as { user: UserState };
      const token = state.user.auth.token;

      const response = await fetch('/api/user/me', {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.message || 'Erreur lors de la récupération du profil');
      }

      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Erreur réseau');
    }
  }
);

export const updateUserProfile = createAsyncThunk(
  'user/updateProfile',
  async (updates: Partial<User>, { rejectWithValue, getState }) => {
    try {
      const state = getState() as { user: UserState };
      const token = state.user.auth.token;

      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.message || 'Erreur lors de la mise à jour');
      }

      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Erreur réseau');
    }
  }
);

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    // Actions synchrones
    setAuthToken: (
      state,
      action: PayloadAction<{
        token: string;
        refreshToken?: string;
        expiresIn?: number;
        rememberMe?: boolean;
      }>
    ) => {
      state.auth.token = action.payload.token;
      state.auth.refreshToken = action.payload.refreshToken || null;
      state.auth.rememberMe = action.payload.rememberMe || false;
      state.auth.isAuthenticated = true;

      if (action.payload.expiresIn) {
        state.auth.tokenExpiry = Date.now() + action.payload.expiresIn * 1000;
      }
    },

    clearAuth: (state) => {
      state.auth = {
        isAuthenticated: false,
        token: null,
        refreshToken: null,
        tokenExpiry: null,
        rememberMe: false,
      };
      state.currentUser = null;
      state.error = null;
    },

    setCurrentUser: (state, action: PayloadAction<User>) => {
      state.currentUser = action.payload;
    },

    updateCurrentUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.currentUser) {
        state.currentUser = { ...state.currentUser, ...action.payload };
      }
    },

    clearError: (state) => {
      state.error = null;
      state.usersListError = null;
    },

    setFilters: (state, action: PayloadAction<Partial<UserState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },

    setPagination: (state, action: PayloadAction<Partial<UserState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },

    resetUsersList: (state) => {
      state.usersList = [];
      state.pagination = initialState.pagination;
      state.filters = initialState.filters;
    },
  },
  extraReducers: (builder) => {
    // Login
    builder
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.auth.isAuthenticated = true;
        state.auth.token = action.payload.token;
        state.auth.refreshToken = action.payload.refreshToken;
        state.auth.rememberMe = action.payload.rememberMe || false;
        if (action.payload.user) {
          state.currentUser = action.payload.user;
        }
        if (action.payload.expiresIn) {
          state.auth.tokenExpiry = Date.now() + action.payload.expiresIn * 1000;
        }
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.auth.isAuthenticated = false;
      });

    // Register
    builder
      .addCase(registerUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(registerUser.fulfilled, (state, action) => {
        state.isLoading = false;
        // Après inscription, l'utilisateur n'est pas automatiquement connecté
        // Il doit confirmer son email d'abord
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch current user
    builder
      .addCase(fetchCurrentUser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchCurrentUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentUser = action.payload;
      })
      .addCase(fetchCurrentUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update profile
    builder
      .addCase(updateUserProfile.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        state.isUpdating = false;
        if (state.currentUser) {
          state.currentUser = { ...state.currentUser, ...action.payload };
        }
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setAuthToken,
  clearAuth,
  setCurrentUser,
  updateCurrentUser,
  clearError,
  setFilters,
  setPagination,
  resetUsersList,
} = userSlice.actions;

export default userSlice.reducer;

// Sélecteurs
export const selectCurrentUser = (state: { user: UserState }) => state.user.currentUser;
export const selectIsAuthenticated = (state: { user: UserState }) =>
  state.user.auth.isAuthenticated;
export const selectAuthToken = (state: { user: UserState }) => state.user.auth.token;
export const selectIsLoading = (state: { user: UserState }) => state.user.isLoading;
export const selectError = (state: { user: UserState }) => state.user.error;
export const selectUserRole = (state: { user: UserState }) => state.user.currentUser?.role;
export const selectUserPreferences = (state: { user: UserState }) =>
  state.user.currentUser?.preferences;
