import { useEffect } from 'react';
import { useNotifications as useNotificationContext } from '../contexts/NotificationContext';
import { notificationService } from '../services/notificationService';

/**
 * Hook pour connecter le service de notifications aux systèmes existants
 * (NotificationContext pour les toasts + Redux pour le header)
 */
export const useNotificationSystem = () => {
  const { showSuccess, showError, showWarning, showInfo } = useNotificationContext();

  useEffect(() => {
    // Enregistrer les callbacks pour les toasts
    notificationService.setToastCallbacks({
      showSuccess,
      showError,
      showWarning,
      showInfo,
    });

    // Enregistrer les callbacks pour les notifications header
    notificationService.setHeaderCallbacks({
      addNotification: (notification) => {
        // Les notifications header sont maintenant gérées par la base de données
        // et chargées directement dans le Header component
        console.log('📢 Header notification will be saved to database:', notification.title);
      },
    });

    console.log('✅ Notification system initialized');
  }, [showSuccess, showError, showWarning, showInfo]);

  return {
    // Méthodes de convenance
    sendSecurityAlert: notificationService.sendSecurityAlert.bind(notificationService),
    sendSystemUpdate: notificationService.sendSystemUpdate.bind(notificationService),
    sendAnalysisCompleted: notificationService.sendAnalysisCompleted.bind(notificationService),
    sendLoginAlert: notificationService.sendLoginAlert.bind(notificationService),
    sendProfileChange: notificationService.sendProfileChange.bind(notificationService),
  };
};

export default useNotificationSystem;
