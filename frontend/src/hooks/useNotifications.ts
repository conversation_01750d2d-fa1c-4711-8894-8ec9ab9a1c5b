import { useCallback, useState } from 'react';
import { notificationService, NotificationPreferences } from '../services/notificationService';
import { useNotificationSystem } from './useNotificationSystem';

export const useNotifications = () => {
  const [preferences, setPreferences] = useState<NotificationPreferences | null>(null);

  // Initialiser le système de notifications
  const {
    sendSecurityAlert,
    sendSystemUpdate,
    sendAnalysisCompleted,
    sendLoginAlert,
    sendProfileChange,
  } = useNotificationSystem();

  /**
   * Met à jour les préférences dans le service
   */
  const updatePreferences = useCallback((newPreferences: NotificationPreferences) => {
    notificationService.updatePreferences(newPreferences);
    setPreferences(newPreferences);
  }, []);



  return {
    preferences,
    updatePreferences,
    sendSecurityAlert,
    sendSystemUpdate,
    sendAnalysisCompleted,
    sendLogin<PERSON>lert,
    sendProfileChange,
  };
};

export default useNotifications;
