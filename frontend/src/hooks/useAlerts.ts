import { useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { addAlert, removeAlert, clearAllAlerts } from '../store/slices/alertSlice';

export const useReduxAlerts = () => {
  const dispatch = useAppDispatch();
  const alerts = useAppSelector((state) => state.alerts.alerts);

  const showSuccess = useCallback(
    (message: string, title?: string) => {
      dispatch(
        addAlert({
          type: 'success',
          message,
          title,
        })
      );
    },
    [dispatch]
  );

  const showError = useCallback(
    (message: string, title?: string) => {
      dispatch(
        addAlert({
          type: 'error',
          message,
          title,
          duration: 8000, // Erreurs affichées plus longtemps
        })
      );
    },
    [dispatch]
  );

  const showWarning = useCallback(
    (message: string, title?: string) => {
      dispatch(
        addAlert({
          type: 'warning',
          message,
          title,
          duration: 6000,
        })
      );
    },
    [dispatch]
  );

  const showInfo = useCallback(
    (message: string, title?: string) => {
      dispatch(
        addAlert({
          type: 'info',
          message,
          title,
        })
      );
    },
    [dispatch]
  );

  const clearAll = useCallback(() => {
    dispatch(clearAllAlerts());
  }, [dispatch]);

  const removeAlert = useCallback(
    (id: string) => {
      dispatch(removeAlert(id));
    },
    [dispatch]
  );

  return {
    alerts,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    clearAll,
    removeAlert,
  };
};
