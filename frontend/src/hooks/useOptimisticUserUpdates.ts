import { useCallback } from 'react';

interface User {
  _id: string;
  first_name: string;
  last_name: string;
  username: string;
  email: string;
  role: 'admin' | 'user';
  active: boolean;
  banned: boolean;
  email_verified: boolean;
  created_at: string;
  date_of_birth?: string;
  gender?: string;
  _optimisticOperation?: number;
}

interface UseOptimisticUserUpdatesProps {
  setUsers: React.Dispatch<React.SetStateAction<User[]>>;
}

/**
 * Custom hook for managing optimistic user updates
 * Provides consistent optimistic update behavior across different operations
 */
export const useOptimisticUserUpdates = ({ setUsers }: UseOptimisticUserUpdatesProps) => {
  
  const updateUserOptimistically = useCallback(async (
    userId: string,
    optimisticUpdate: Partial<User>,
    revertUpdate: Partial<User>,
    operation: () => Promise<any>
  ) => {
    const operationId = Date.now();
    
    try {
      // Apply optimistic update
      setUsers(prev => prev.map(user =>
        user._id === userId
          ? { ...user, ...optimisticUpdate, _optimisticOperation: operationId }
          : user
      ));

      // Execute the actual operation
      await operation();

      // Clear optimistic operation flag on success
      setUsers(prev => prev.map(user =>
        user._id === userId
          ? { ...user, _optimisticOperation: undefined }
          : user
      ));

    } catch (error) {
      // Revert optimistic update on failure (only if it matches our operation)
      setUsers(prev => prev.map(user =>
        user._id === userId && user._optimisticOperation === operationId
          ? { ...user, ...revertUpdate, _optimisticOperation: undefined }
          : user
      ));
      
      throw error; // Re-throw to allow caller to handle
    }
  }, [setUsers]);

  const deleteUserOptimistically = useCallback(async (
    userId: string,
    operation: () => Promise<any>
  ) => {
    const operationId = Date.now();
    let userToRestore: User | null = null;
    let userIndex = -1;

    try {
      // Store user reference and position before optimistic update
      setUsers(prev => {
        userIndex = prev.findIndex(u => u._id === userId);
        userToRestore = prev[userIndex];
        
        // Mark user as being deleted and remove from list
        return prev.map(user =>
          user._id === userId
            ? { ...user, _optimisticOperation: operationId }
            : user
        ).filter(user => user._id !== userId);
      });

      // Execute the actual operation
      await operation();

    } catch (error) {
      // Revert optimistic update on failure - restore user at original position
      if (userToRestore) {
        setUsers(prev => {
          const newUsers = [...prev];
          const insertIndex = Math.min(userIndex, newUsers.length);
          newUsers.splice(insertIndex, 0, { ...userToRestore!, _optimisticOperation: undefined });
          return newUsers;
        });
      }
      
      throw error; // Re-throw to allow caller to handle
    }
  }, [setUsers]);

  return { 
    updateUserOptimistically,
    deleteUserOptimistically
  };
};
