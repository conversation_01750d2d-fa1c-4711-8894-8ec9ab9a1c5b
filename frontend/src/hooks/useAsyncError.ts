import { useCallback, useState } from 'react';

interface AsyncErrorState {
  error: Error | null;
  isLoading: boolean;
}

interface UseAsyncErrorReturn {
  error: Error | null;
  isLoading: boolean;
  executeAsync: <T>(asyncFn: () => Promise<T>) => Promise<T | null>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

/**
 * Custom hook for handling async operations with error management
 * Provides consistent error handling and loading states across components
 */
export const useAsyncError = (): UseAsyncErrorReturn => {
  const [state, setState] = useState<AsyncErrorState>({
    error: null,
    isLoading: false,
  });

  const executeAsync = useCallback(async <T>(asyncFn: () => Promise<T>): Promise<T | null> => {
    setState({ error: null, isLoading: true });
    
    try {
      const result = await asyncFn();
      setState({ error: null, isLoading: false });
      return result;
    } catch (error) {
      const errorObj = error instanceof Error ? error : new Error('An unknown error occurred');
      setState({ error: errorObj, isLoading: false });
      
      // Log error for debugging
      console.error('Async operation failed:', errorObj);
      
      return null;
    }
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({ ...prev, isLoading: loading }));
  }, []);

  return {
    error: state.error,
    isLoading: state.isLoading,
    executeAsync,
    clearError,
    setLoading,
  };
};

export default useAsyncError;
