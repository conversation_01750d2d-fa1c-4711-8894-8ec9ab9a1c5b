import { useState, useCallback } from 'react';

interface CircuitBreakerState {
  failures: number;
  isOpen: boolean;
  lastFailure: number | null;
}

interface CircuitBreakerOptions {
  threshold?: number;
  timeout?: number;
}

/**
 * Circuit breaker hook to prevent cascading failures
 * @param threshold - Number of failures before opening the circuit
 * @param timeout - Time in milliseconds before attempting to close the circuit
 */
export const useCircuitBreaker = ({ 
  threshold = 3, 
  timeout = 30000 
}: CircuitBreakerOptions = {}) => {
  const [state, setState] = useState<CircuitBreakerState>({
    failures: 0,
    isOpen: false,
    lastFailure: null
  });

  const execute = useCallback(async <T>(fn: () => Promise<T>): Promise<T> => {
    // Check if circuit is open and timeout has passed
    if (state.isOpen && state.lastFailure) {
      if (Date.now() - state.lastFailure < timeout) {
        throw new Error('Circuit breaker is open. Service temporarily unavailable.');
      } else {
        // Half-open state: try to close the circuit
        setState(prev => ({ ...prev, isOpen: false, failures: 0 }));
      }
    }

    try {
      const result = await fn();
      
      // Success: reset failures
      if (state.failures > 0) {
        setState(prev => ({ ...prev, failures: 0, isOpen: false }));
      }
      
      return result;
    } catch (error) {
      const newFailures = state.failures + 1;
      const shouldOpen = newFailures >= threshold;
      
      setState({
        failures: newFailures,
        isOpen: shouldOpen,
        lastFailure: shouldOpen ? Date.now() : state.lastFailure
      });
      
      throw error;
    }
  }, [state, threshold, timeout]);

  const reset = useCallback(() => {
    setState({
      failures: 0,
      isOpen: false,
      lastFailure: null
    });
  }, []);

  return { 
    execute, 
    isOpen: state.isOpen, 
    failures: state.failures,
    reset 
  };
};
