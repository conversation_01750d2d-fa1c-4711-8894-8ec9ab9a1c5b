import { useState, useCallback } from 'react';

interface AlertOptions {
  title?: string;
  message: string;
  type?: 'error' | 'success' | 'info' | 'warning';
  buttonText?: string;
}

interface ConfirmOptions {
  title?: string;
  message: string;
  type?: 'warning' | 'danger' | 'info' | 'success';
  confirmText?: string;
  cancelText?: string;
  confirmButtonClass?: string;
}

interface AlertState {
  isOpen: boolean;
  title: string;
  message: string;
  type: 'error' | 'success' | 'info' | 'warning';
  buttonText: string;
}

interface ConfirmState {
  isOpen: boolean;
  title: string;
  message: string;
  type: 'warning' | 'danger' | 'info' | 'success';
  confirmText: string;
  cancelText: string;
  confirmButtonClass?: string;
  onConfirm: () => void;
}

export function useModal() {
  const [alertState, setAlertState] = useState<AlertState>({
    isOpen: false,
    title: '',
    message: '',
    type: 'info',
    buttonText: 'OK',
  });

  const [confirmState, setConfirmState] = useState<ConfirmState>({
    isOpen: false,
    title: '',
    message: '',
    type: 'warning',
    confirmText: 'Confirm',
    cancelText: 'Cancel',
    onConfirm: () => {},
  });

  // Show alert modal (replacement for alert())
  const showAlert = useCallback((options: AlertOptions) => {
    setAlertState({
      isOpen: true,
      title: options.title || getDefaultTitle(options.type || 'info'),
      message: options.message,
      type: options.type || 'info',
      buttonText: options.buttonText || 'OK',
    });
  }, []);

  // Show confirm modal (replacement for confirm())
  const showConfirm = useCallback((options: ConfirmOptions): Promise<boolean> => {
    return new Promise((resolve) => {
      setConfirmState({
        isOpen: true,
        title: options.title || getDefaultConfirmTitle(options.type || 'warning'),
        message: options.message,
        type: options.type || 'warning',
        confirmText: options.confirmText || 'Confirm',
        cancelText: options.cancelText || 'Cancel',
        confirmButtonClass: options.confirmButtonClass,
        onConfirm: () => {
          resolve(true);
          closeConfirm();
        },
      });

      // Handle cancel/close
      const originalCloseConfirm = () => {
        resolve(false);
        closeConfirm();
      };

      // Store the cancel handler
      setConfirmState(
        (prev) =>
          ({
            ...prev,
            onCancel: originalCloseConfirm,
          }) as any
      );
    });
  }, []);

  const closeAlert = useCallback(() => {
    setAlertState((prev) => ({ ...prev, isOpen: false }));
  }, []);

  const closeConfirm = useCallback(() => {
    setConfirmState((prev) => ({ ...prev, isOpen: false }));
  }, []);

  return {
    // Alert modal state and controls
    alertModal: {
      ...alertState,
      onClose: closeAlert,
    },

    // Confirm modal state and controls
    confirmModal: {
      ...confirmState,
      onClose: closeConfirm,
    },

    // Helper functions
    showAlert,
    showConfirm,
    closeAlert,
    closeConfirm,
  };
}

// Helper functions for default titles
function getDefaultTitle(type: 'error' | 'success' | 'info' | 'warning'): string {
  switch (type) {
    case 'error':
      return 'Error';
    case 'success':
      return 'Success';
    case 'warning':
      return 'Warning';
    default:
      return 'Information';
  }
}

function getDefaultConfirmTitle(type: 'warning' | 'danger' | 'info' | 'success'): string {
  switch (type) {
    case 'danger':
      return 'Delete Confirmation';
    case 'success':
      return 'Confirm Action';
    case 'info':
      return 'Information';
    default:
      return 'Warning';
  }
}
