import { useAuthContext } from '../contexts/AuthContext';

export const useRole = () => {
  const { role, user } = useAuthContext();

  const isAdmin = role === 'admin';
  const isUser = role === 'user';
  const isModerator = role === 'moderator';

  return {
    role,
    user,
    isAdmin,
    isUser,
    isModerator,
    hasRole: (requiredRole: string) => role === requiredRole,
    hasAnyRole: (roles: string[]) => roles.includes(role || ''),
  };
};
