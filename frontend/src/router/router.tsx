import { createBrowserRouter } from 'react-router-dom';
import Login from '../pages/Login';
import Register from '../pages/Register';
import ForgotPassword from '../pages/ForgotPassword';
import ResetPassword from '../pages/ResetPassword';
import EmailConfirmed from '../pages/EmailConfirmed';
import Dashboard from '../pages/Dashboard';
import SecurityOverview from '../pages/SecurityOverview';
import Pentesting from '../pages/Pentesting';
import PhishingDetection from '../pages/PhishingDetection';
import MalwareDetection from '../pages/MalwareDetection';
import Incidents from '../pages/Incidents';
import Settings from '../pages/Settings';
import Analytics from '../pages/Analytics';
import UserManagement from '../pages/UserManagement';
import AuthLayout from '../layouts/AuthLayout';
import MainLayout from '../layouts/MainLayout';
import AdminLayout from '../layouts/AdminLayout';

export const router = createBrowserRouter([
  {
    path: '/',
    element: <AuthLayout />,
    children: [
      { index: true, element: <Login /> }, // Page par défaut
      { path: 'login', element: <Login /> },
      { path: 'register', element: <Register /> },
      { path: 'forgot-password', element: <ForgotPassword /> },
      { path: 'reset-password', element: <ResetPassword /> },
      { path: 'email-confirmed', element: <EmailConfirmed /> },
    ],
  },
  {
    path: '/dashboard',
    element: <MainLayout children={undefined} />,
    children: [
      { index: true, element: <Dashboard /> },
      { path: 'security', element: <SecurityOverview /> },
      // autres routes utilisateur ici...
    ],
  },
  {
    path: '/app',
    element: <MainLayout children={undefined} />,
    children: [{ index: true, element: <Pentesting /> }],
  },
  {
    path: '/pentesting',
    element: <MainLayout children={undefined} />,
    children: [{ index: true, element: <Pentesting /> }],
  },
  {
    path: '/phishing',
    element: <MainLayout children={undefined} />,
    children: [{ index: true, element: <PhishingDetection /> }],
  },
  {
    path: '/malware',
    element: <MainLayout children={undefined} />,
    children: [{ index: true, element: <MalwareDetection /> }],
  },
  {
    path: '/incidents',
    element: <MainLayout children={undefined} />,
    children: [{ index: true, element: <Incidents /> }],
  },
  {
    path: '/settings',
    element: <MainLayout children={undefined} />,
    children: [{ index: true, element: <Settings /> }],
  },
  {
    path: '/analytics',
    element: <MainLayout children={undefined} />,
    children: [{ index: true, element: <Analytics /> }],
  },

  {
    path: '/admin',
    element: <AdminLayout />,
    children: [
      { index: true, element: <UserManagement /> },
      // autres routes admin ici...
    ],
  },
]);
