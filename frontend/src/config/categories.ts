export interface CategoryConfig {
  [key: string]: string[];
}

export const TICKET_CATEGORIES: CategoryConfig = {
  Database: [
    'Backup & Restore',
    'Database Performance',
    'Data Corruption',
    'Query Optimization',
    'User Permissions',
    'Replication Issues',
    'Migration',
    'Security/Encryption',
  ],
  Software: [
    'Installation',
    'Licensing',
    'Updates/Patches',
    'Crashes/Errors',
    'Compatibility',
    'Performance',
    'Configuration',
    'Feature Request',
    'Security Vulnerability',
  ],
  Network: [
    'Connectivity Issues',
    'Latency/Performance',
    'VPN',
    'Routing',
    'Firewall Rules',
    'Port Blocking',
    'Wireless Network',
    'Network Design',
    'IP Addressing',
    'DNS Issues',
  ],
  Security: [
    'Phishing',
    'Malware',
    'Unauthorized Access',
    'Vulnerability Scan',
    'Incident Response',
    'Policy Violation',
    'Security Patch',
    'Data Leakage',
    'Compliance',
    'User Account Compromise',
  ],
  Hardware: [
    'Server Hardware',
    'Desktop/Laptop',
    'Storage Devices',
    'Printers/Peripherals',
    'Power Supply',
    'Hardware Failure',
    'Device Configuration',
    'Replacement/Upgrade',
  ],
  Infrastructure: [
    'Virtualization',
    'Cloud Services',
    'Data Center',
    'Backup Systems',
    'Disaster Recovery',
    'Load Balancing',
    'Monitoring & Alerts',
    'System Integration',
    'Performance Tuning',
    'Capacity Planning',
  ],
};

export const getCategoriesList = (): string[] => {
  return Object.keys(TICKET_CATEGORIES);
};

export const getSubcategoriesForCategory = (category: string): string[] => {
  return TICKET_CATEGORIES[category] || [];
};
