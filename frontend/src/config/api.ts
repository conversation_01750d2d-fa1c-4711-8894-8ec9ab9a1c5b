// Configuration des endpoints API
export const API_CONFIG = {
  // URL de base pour l'API principale (backend Flask)
  BASE_URL: 'http://localhost:5000',

  // URL pour les APIs d'analytics avec IA Ollama
  // Remplacez cette URL par l'adresse de votre serveur Ollama
  ANALYTICS_BASE_URL: 'http://localhost:5000', // Changez cette URL selon votre serveur

  // Endpoints pour les analytics (serveur IA externe)
  ANALYTICS_ENDPOINTS: {
    ANALYZE: '/analyze', // ✅ Disponible - Lance une analyse
    STATUS: '/status', // ✅ Disponible - Vérifie le statut d'une analyse
    REPORT: '/report', // ✅ Disponible - Récupère un rapport
    ANALYSES: '/analyses', // ⚠️ Disponible mais sans pagination
    // STATISTICS: '/analytics/statistics',  // ❌ Non disponible sur le serveur IA
    // EXPORT: '/analytics/export',          // ❌ Non disponible
    // CANCEL: '/analyses/{id}/cancel',      // ❌ Non disponible
    // RETRY: '/analyses/{id}/retry',        // ❌ Non disponible
    // DELETE: '/analyses/{id}'              // ❌ Non disponible
  },

  // Configuration des timeouts
  TIMEOUTS: {
    DEFAULT: 30000, // 30 secondes
    UPLOAD: 600000, // 10 minutes pour les uploads/analyses
    ANALYSIS: 900000, // 15 minutes pour les analyses longues avec IA
  },

  // Configuration des retry
  RETRY: {
    MAX_ATTEMPTS: 3,
    DELAY: 1000, // 1 seconde
  },
};

// Helper pour construire les URLs complètes
export const buildAnalyticsUrl = (endpoint: string, params?: Record<string, string>): string => {
  let url = `${API_CONFIG.ANALYTICS_BASE_URL}${endpoint}`;

  // Remplacer les paramètres dans l'URL (ex: {id} -> valeur réelle)
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      url = url.replace(`{${key}}`, value);
    });
  }

  return url;
};

// Helper pour construire les URLs de l'API principale
export const buildApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.BASE_URL}${endpoint}`;
};

// Configuration simple et directe
export const activeConfig = {
  ...API_CONFIG,
  BASE_URL: import.meta.env?.VITE_API_URL || 'http://localhost:5000',
  ANALYTICS_BASE_URL: import.meta.env?.VITE_ANALYTICS_API_URL || 'http://*************:4001',
};
