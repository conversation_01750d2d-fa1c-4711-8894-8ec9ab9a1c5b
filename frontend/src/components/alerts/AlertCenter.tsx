import { useEffect } from 'react';

interface AlertCenterProps {
  message: string;
  title?: string;
  type?: 'success' | 'error' | 'warning' | 'info';
  duration?: number; // 0 = permanent
  onClose: () => void;
}

export default function AlertCenter({
  message,
  title,
  type = 'success',
  duration = 4000,
  onClose,
}: AlertCenterProps) {
  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(onClose, duration);
      return () => clearTimeout(timer);
    }
  }, [onClose, duration]);

  const getAlertStyles = () => {
    switch (type) {
      case 'success':
        return 'bg-green-500 text-white border-green-600';
      case 'error':
        return 'bg-red-500 text-white border-red-600';
      case 'warning':
        return 'bg-yellow-500 text-black border-yellow-600';
      case 'info':
        return 'bg-blue-500 text-white border-blue-600';
      default:
        return 'bg-green-500 text-white border-green-600';
    }
  };

  return (
    <div
      className={`fixed top-5 right-5 px-4 py-3 rounded-lg shadow-lg z-50 border-l-4 max-w-sm ${getAlertStyles()}`}
    >
      <div className="flex items-start">
        <div className="flex-1">
          {title && <div className="font-semibold text-sm mb-1">{title}</div>}
          <div className="text-sm">{message}</div>
        </div>
        {duration === 0 && (
          <button
            onClick={onClose}
            className="ml-3 flex-shrink-0 text-lg font-bold opacity-70 hover:opacity-100"
          >
            ×
          </button>
        )}
      </div>
    </div>
  );
}
