import React from 'react';
import { useAppSelector, useAppDispatch } from '../../store/hooks';
import { removeAlert } from '../../store/slices/alertSlice';
import AlertCenter from './AlertCenter';

const ReduxAlertDisplay: React.FC = () => {
  const alerts = useAppSelector((state) => state.alerts.alerts);
  const dispatch = useAppDispatch();

  const handleCloseAlert = (alertId: string) => {
    dispatch(removeAlert(alertId));
  };

  return (
    <>
      {alerts.map((alert) => (
        <AlertCenter
          key={alert.id}
          message={alert.message}
          title={alert.title}
          type={alert.type}
          duration={alert.duration}
          onClose={() => handleCloseAlert(alert.id)}
        />
      ))}
    </>
  );
};

export default ReduxAlertDisplay;
