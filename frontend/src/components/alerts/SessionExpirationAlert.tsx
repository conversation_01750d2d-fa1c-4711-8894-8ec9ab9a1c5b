import React, { useEffect, useState } from 'react';
import { useAuthContext } from '../../contexts/AuthContext';

interface SessionExpirationAlertProps {
  onClose: () => void;
}

export default function SessionExpirationAlert({ onClose }: SessionExpirationAlertProps) {
  const { timeRemaining, isAuthenticated } = useAuthContext();
  const [displayTime, setDisplayTime] = useState('');

  useEffect(() => {
    if (timeRemaining > 0 && isAuthenticated) {
      const totalSeconds = Math.floor(timeRemaining);
      const minutes = Math.floor(totalSeconds / 60);
      const seconds = totalSeconds % 60;
      setDisplayTime(`${minutes}:${seconds.toString().padStart(2, '0')}`);
    }
  }, [timeRemaining, isAuthenticated]);

  // Auto-close if session is refreshed or user logs out
  useEffect(() => {
    if (timeRemaining > 300 || !isAuthenticated) {
      onClose();
    }
  }, [timeRemaining, isAuthenticated, onClose]);

  return (
    <div className="fixed top-5 right-5 px-4 py-3 rounded-lg shadow-lg z-50 border-l-4 max-w-sm bg-yellow-500 text-black border-yellow-600">
      <div className="flex items-start">
        <div className="flex-1">
          <div className="font-semibold text-sm mb-1">⚠️ Session Expiration Warning</div>
          <div className="text-sm">
            Your session will expire in <span className="font-mono font-bold">{displayTime}</span>.
            Please save your work.
          </div>
        </div>
        <button
          onClick={onClose}
          className="ml-3 flex-shrink-0 text-lg font-bold opacity-70 hover:opacity-100"
        >
          ×
        </button>
      </div>
    </div>
  );
}
