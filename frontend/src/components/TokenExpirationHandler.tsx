import React, { useEffect, useState } from 'react';
import { useAuthContext } from '../contexts/AuthContext';
import { getToken, isTokenExpired } from '../utils/auth';

export default function TokenExpirationHandler() {
  const { timeRemaining, logout, isAuthenticated } = useAuthContext();
  const [showWarning, setShowWarning] = useState(false);

  useEffect(() => {
    // Vérifier périodiquement si le token est expiré
    const checkTokenExpiration = () => {
      const token = getToken();
      if (token && isTokenExpired(token) && isAuthenticated) {
        console.log('🔒 Token expired, automatic logout triggered');
        logout();
        return;
      }
    };

    // Vérifier immédiatement
    checkTokenExpiration();

    // Vérifier toutes les 30 secondes
    const interval = setInterval(checkTokenExpiration, 30000);

    return () => clearInterval(interval);
  }, [logout, isAuthenticated]);

  useEffect(() => {
    // Afficher un avertissement quand il reste moins de 5 minutes (300 secondes)
    if (timeRemaining <= 300 && timeRemaining > 0 && isAuthenticated) {
      setShowWarning(true);
    } else {
      setShowWarning(false);
    }

    // Déconnexion automatique quand le token expire
    if (timeRemaining <= 0 && isAuthenticated) {
      console.log('🔒 Time remaining reached 0, automatic logout triggered');
      logout();
    }
  }, [timeRemaining, logout, isAuthenticated]);

  // Ne pas afficher l'avertissement si l'utilisateur n'est pas authentifié
  if (!showWarning || !isAuthenticated) return null;

  const minutes = Math.floor(timeRemaining / 60);
  const seconds = timeRemaining % 60;

  return (
    <div className="fixed top-4 right-4 z-50 bg-yellow-500 text-black px-4 py-3 rounded-lg shadow-lg max-w-sm">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
              clipRule="evenodd"
            />
          </svg>
        </div>
        <div className="ml-3">
          <p className="text-sm font-medium">
            Session expire dans {minutes}:{seconds.toString().padStart(2, '0')}
          </p>
          <p className="text-xs">Votre session va expirer bientôt</p>
        </div>
      </div>
    </div>
  );
}
