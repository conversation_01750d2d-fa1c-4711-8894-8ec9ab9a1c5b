import React from 'react';
import { Shield, AlertTriangle, Target, Activity, Zap, FileText, Users, Clock } from 'lucide-react';
import { VulnerabilityInfo, analyticsUtils } from '../../services/analyticsService';

interface VulnerabilityStatsProps {
  vulnerabilities: VulnerabilityInfo[];
  metadata?: {
    total_scans: number;
    total_vulnerabilities: number;
    report_generated: string;
  };
}

const VulnerabilityStats: React.FC<VulnerabilityStatsProps> = ({ vulnerabilities, metadata }) => {
  const stats = analyticsUtils.calculateVulnerabilityStats(vulnerabilities);

  const StatCard: React.FC<{
    title: string;
    value: number | string;
    icon: React.ReactNode;
    color: string;
    subtitle?: string;
  }> = ({ title, value, icon, color, subtitle }) => (
    <div
      className={`bg-gradient-to-br from-gray-700/50 to-gray-800/50 border ${color} rounded-xl p-4 hover:scale-105 transition-all duration-200`}
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-gray-400 text-xs font-medium">{title}</p>
          <p className="text-2xl font-bold text-white mt-1">{value}</p>
          {subtitle && <p className="text-gray-500 text-xs mt-1">{subtitle}</p>}
        </div>
        <div className={`p-3 rounded-xl ${color.replace('border-', 'bg-').replace('/50', '/20')}`}>
          {icon}
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Report metadata */}
      {metadata && (
        <div className="bg-gradient-to-br from-purple-700/30 to-purple-800/40 backdrop-blur-sm border border-purple-500/20 rounded-2xl p-6 shadow-xl">
          <h2 className="text-xl font-bold text-white mb-4 flex items-center">
            <FileText className="w-6 h-6 mr-3 text-purple-400" />
            Vulnerability Report
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center space-x-3">
              <Activity className="w-5 h-5 text-purple-400" />
              <div>
                <p className="text-gray-400">Total Scans</p>
                <p className="text-white font-bold">{metadata.total_scans}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <AlertTriangle className="w-5 h-5 text-orange-400" />
              <div>
                <p className="text-gray-400">Total Vulnerabilities</p>
                <p className="text-white font-bold">{metadata.total_vulnerabilities}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Clock className="w-5 h-5 text-blue-400" />
              <div>
                <p className="text-gray-400">Generated on</p>
                <p className="text-white font-bold">
                  {new Date(metadata.report_generated).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main statistics */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
        <StatCard
          title="Total"
          value={stats.total}
          icon={<Shield className="w-6 h-6 text-blue-400" />}
          color="border-blue-500/50"
          subtitle="vulnerabilities"
        />

        <StatCard
          title="Critical"
          value={stats.critical}
          icon={<AlertTriangle className="w-6 h-6 text-red-400" />}
          color="border-red-500/50"
          subtitle="🔴 Critical"
        />

        <StatCard
          title="High"
          value={stats.high}
          icon={<Zap className="w-6 h-6 text-orange-400" />}
          color="border-orange-500/50"
          subtitle="🟠 High"
        />

        <StatCard
          title="Medium"
          value={stats.medium}
          icon={<Activity className="w-6 h-6 text-yellow-400" />}
          color="border-yellow-500/50"
          subtitle="🟡 Medium"
        />

        <StatCard
          title="Low"
          value={stats.low}
          icon={<Shield className="w-6 h-6 text-green-400" />}
          color="border-green-500/50"
          subtitle="🟢 Low"
        />

        <StatCard
          title="With CVE"
          value={stats.withCVE}
          icon={<FileText className="w-6 h-6 text-purple-400" />}
          color="border-purple-500/50"
          subtitle="identified"
        />
      </div>

      {/* Detailed statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Severity distribution */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <h3 className="text-lg font-bold text-white mb-4 flex items-center">
            <AlertTriangle className="w-5 h-5 mr-2 text-orange-400" />
            Severity Distribution
          </h3>

          <div className="space-y-3">
            {[
              {
                severity: 'CRITICAL',
                count: stats.critical,
                color: 'bg-red-500',
                label: 'Critical',
              },
              { severity: 'HIGH', count: stats.high, color: 'bg-orange-500', label: 'High' },
              { severity: 'MEDIUM', count: stats.medium, color: 'bg-yellow-500', label: 'Medium' },
              { severity: 'LOW', count: stats.low, color: 'bg-green-500', label: 'Low' },
            ].map(({ severity, count, color, label }) => {
              const percentage = stats.total > 0 ? (count / stats.total) * 100 : 0;
              return (
                <div key={severity} className="flex items-center space-x-3">
                  <div className="w-16 text-sm text-gray-300">{label}</div>
                  <div className="flex-1 bg-gray-700 rounded-full h-2">
                    <div
                      className={`${color} h-2 rounded-full transition-all duration-500`}
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                  <div className="w-12 text-sm text-white text-right">{count}</div>
                  <div className="w-12 text-xs text-gray-400 text-right">
                    {percentage.toFixed(0)}%
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Target information */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <h3 className="text-lg font-bold text-white mb-4 flex items-center">
            <Target className="w-5 h-5 mr-2 text-cyan-400" />
            Target Information
          </h3>

          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg">
              <div className="flex items-center space-x-3">
                <Target className="w-5 h-5 text-cyan-400" />
                <span className="text-gray-300">Unique Targets</span>
              </div>
              <span className="text-white font-bold">{stats.uniqueTargets}</span>
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg">
              <div className="flex items-center space-x-3">
                <Activity className="w-5 h-5 text-blue-400" />
                <span className="text-gray-300">Unique Scans</span>
              </div>
              <span className="text-white font-bold">{stats.uniqueScans}</span>
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg">
              <div className="flex items-center space-x-3">
                <FileText className="w-5 h-5 text-purple-400" />
                <span className="text-gray-300">Vulnerabilities with CVE</span>
              </div>
              <span className="text-white font-bold">
                {stats.withCVE} / {stats.total}
              </span>
            </div>

            {stats.total > 0 && (
              <div className="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Users className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">Average per Target</span>
                </div>
                <span className="text-white font-bold">
                  {(stats.total / stats.uniqueTargets).toFixed(1)}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Global risk indicator */}
      <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
        <h3 className="text-lg font-bold text-white mb-4 flex items-center">
          <Shield className="w-5 h-5 mr-2 text-blue-400" />
          Global Risk Assessment
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Risk score */}
          <div className="text-center">
            <div
              className={`w-20 h-20 mx-auto rounded-full flex items-center justify-center text-2xl font-bold ${
                stats.critical > 0 || stats.high > 5
                  ? 'bg-red-500/20 text-red-400 border-2 border-red-500/50'
                  : stats.medium > 10
                    ? 'bg-yellow-500/20 text-yellow-400 border-2 border-yellow-500/50'
                    : 'bg-green-500/20 text-green-400 border-2 border-green-500/50'
              }`}
            >
              {stats.critical > 0 || stats.high > 5 ? '⚠️' : stats.medium > 10 ? '⚡' : '✅'}
            </div>
            <p className="text-gray-400 text-sm mt-2">Risk Level</p>
            <p
              className={`font-bold ${
                stats.critical > 0 || stats.high > 5
                  ? 'text-red-400'
                  : stats.medium > 10
                    ? 'text-yellow-400'
                    : 'text-green-400'
              }`}
            >
              {stats.critical > 0 || stats.high > 5 ? 'HIGH' : stats.medium > 10 ? 'MEDIUM' : 'LOW'}
            </p>
          </div>

          {/* Priority actions */}
          <div className="col-span-2">
            <h4 className="text-white font-semibold mb-3">Recommended Priority Actions</h4>
            <div className="space-y-2 text-sm">
              {stats.critical > 0 && (
                <div className="flex items-center space-x-2 text-red-300">
                  <AlertTriangle className="w-4 h-4" />
                  <span>Immediately address the {stats.critical} critical vulnerabilities</span>
                </div>
              )}
              {stats.high > 0 && (
                <div className="flex items-center space-x-2 text-orange-300">
                  <Zap className="w-4 h-4" />
                  <span>Plan remediation for the {stats.high} high vulnerabilities</span>
                </div>
              )}
              {stats.withCVE > 0 && (
                <div className="flex items-center space-x-2 text-blue-300">
                  <FileText className="w-4 h-4" />
                  <span>Check patches for the {stats.withCVE} identified CVEs</span>
                </div>
              )}
              {stats.total === 0 && (
                <div className="flex items-center space-x-2 text-green-300">
                  <Shield className="w-4 h-4" />
                  <span>No vulnerabilities detected - Maintain monitoring</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VulnerabilityStats;
