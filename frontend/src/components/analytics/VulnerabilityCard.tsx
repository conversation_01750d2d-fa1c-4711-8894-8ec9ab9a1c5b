import React, { useState } from 'react';
import {
  Shield,
  AlertTriangle,
  Eye,
  ChevronDown,
  ChevronUp,
  Clock,
  Target,
  Zap,
  Users,
  CheckCircle,
  AlertCircle,
  Info,
  ExternalLink,
} from 'lucide-react';
import { VulnerabilityInfo, analyticsUtils } from '../../services/analyticsService';

interface VulnerabilityCardProps {
  vulnerability: VulnerabilityInfo;
  onViewDetails?: (vulnerability: VulnerabilityInfo) => void;
}

const VulnerabilityCard: React.FC<VulnerabilityCardProps> = ({ vulnerability, onViewDetails }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { basic_info, ai_analysis } = vulnerability;

  // Safe access to nested properties with fallbacks
  const severityInfo = analyticsUtils.formatSeverity(basic_info?.severity || 'unknown');
  const priority =
    ai_analysis?.vulnerability_analysis?.remediation?.priority ||
    ai_analysis?.remediation?.priority ||
    'medium'; // fallback priority
  const priorityInfo = analyticsUtils.formatPriority(priority);

  // Helper function for safe property access
  const safeGet = (obj: any, path: string, fallback: any = 'N/A') => {
    try {
      return path.split('.').reduce((current, key) => current?.[key], obj) || fallback;
    } catch {
      return fallback;
    }
  };

  // Safe access to vulnerability analysis data
  const vulnAnalysis = ai_analysis?.vulnerability_analysis || {};
  const impactAssessment = vulnAnalysis.impact_assessment || {};
  const remediation = vulnAnalysis.remediation || {};
  const exploitationDetails = vulnAnalysis.exploitation_details || {};
  const responsibilityAssignment = vulnAnalysis.responsibility_assignment || {};

  return (
    <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl hover:border-purple-500/50 transition-all duration-300">
      {/* Card header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="flex items-center space-x-3 mb-2">
            <div className="text-2xl">{severityInfo.icon}</div>
            <div>
              <h3 className="text-lg font-bold text-white line-clamp-2">
                {basic_info?.name || 'Unnamed Vulnerability'}
              </h3>
              <div className="flex items-center space-x-2 mt-1">
                <span
                  className={`px-3 py-1 rounded-full text-xs font-medium border ${severityInfo.bgColor} ${severityInfo.color}`}
                >
                  {severityInfo.text}
                </span>
                {basic_info?.cve && (
                  <span className="px-3 py-1 rounded-full text-xs font-medium bg-blue-500/20 border-blue-500/30 text-blue-300">
                    {basic_info.cve}
                  </span>
                )}
                <span
                  className={`px-3 py-1 rounded-full text-xs font-medium border ${priorityInfo.bgColor} ${priorityInfo.color}`}
                >
                  Priority: {priorityInfo.text}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2 ml-4">
          <button
            onClick={() => onViewDetails?.(vulnerability)}
            className="flex items-center space-x-2 px-3 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white text-sm rounded-xl transition-all duration-200 transform hover:scale-105"
          >
            <Eye className="w-4 h-4" />
            <span>Details</span>
          </button>
        </div>
      </div>

      {/* Basic information */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 text-sm">
        <div className="flex items-center space-x-2">
          <Target className="w-4 h-4 text-gray-400" />
          <div>
            <p className="text-gray-400">Target</p>
            <p className="text-white font-mono">{basic_info?.target || 'N/A'}</p>
          </div>
        </div>

        {basic_info?.port && (
          <div className="flex items-center space-x-2">
            <Zap className="w-4 h-4 text-gray-400" />
            <div>
              <p className="text-gray-400">Port</p>
              <p className="text-white">{basic_info.port}</p>
            </div>
          </div>
        )}

        {basic_info?.service && (
          <div className="flex items-center space-x-2">
            <Shield className="w-4 h-4 text-gray-400" />
            <div>
              <p className="text-gray-400">Service</p>
              <p className="text-white">{basic_info.service}</p>
            </div>
          </div>
        )}

        <div className="flex items-center space-x-2">
          <Clock className="w-4 h-4 text-gray-400" />
          <div>
            <p className="text-gray-400">Security Score</p>
            <p className="text-white font-bold">{basic_info?.security_score || 'N/A'}</p>
          </div>
        </div>
      </div>

      {/* AI analysis summary */}
      <div className="bg-gray-700/30 border border-gray-600 rounded-xl p-4 mb-4">
        <h4 className="text-sm font-semibold text-white mb-2 flex items-center">
          <Info className="w-4 h-4 mr-2 text-blue-400" />
          AI Analysis Summary
        </h4>
        <p className="text-gray-300 text-sm line-clamp-3">
          {vulnAnalysis.summary || 'Analysis summary not available'}
        </p>
      </div>

      {/* Quick impact */}
      <div className="grid grid-cols-3 gap-3 mb-4">
        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3 text-center">
          <AlertTriangle className="w-5 h-5 text-red-400 mx-auto mb-1" />
          <p className="text-xs text-gray-400">Confidentiality</p>
          <p className="text-xs text-red-300 font-medium">
            {(impactAssessment.confidentiality || 'N/A').split(' ')[0]}
          </p>
        </div>
        <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3 text-center">
          <Shield className="w-5 h-5 text-yellow-400 mx-auto mb-1" />
          <p className="text-xs text-gray-400">Integrity</p>
          <p className="text-xs text-yellow-300 font-medium">
            {(impactAssessment.integrity || 'N/A').split(' ')[0]}
          </p>
        </div>
        <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 text-center">
          <CheckCircle className="w-5 h-5 text-blue-400 mx-auto mb-1" />
          <p className="text-xs text-gray-400">Availability</p>
          <p className="text-xs text-blue-300 font-medium">
            {(impactAssessment.availability || 'N/A').split(' ')[0]}
          </p>
        </div>
      </div>

      {/* Immediate actions */}
      <div className="bg-orange-500/10 border border-orange-500/20 rounded-lg p-3 mb-4">
        <h5 className="text-sm font-semibold text-orange-300 mb-2 flex items-center">
          <AlertCircle className="w-4 h-4 mr-2" />
          Recommended Immediate Actions
        </h5>
        <ul className="text-xs text-gray-300 space-y-1">
          {(remediation.immediate_actions || []).slice(0, 2).map((action, index) => (
            <li key={index} className="flex items-start space-x-2">
              <span className="text-orange-400 mt-1">•</span>
              <span className="line-clamp-2">{action}</span>
            </li>
          ))}
          {(remediation.immediate_actions || []).length > 2 && (
            <li className="text-orange-400 text-xs">
              +{(remediation.immediate_actions || []).length - 2} more actions...
            </li>
          )}
          {(!remediation.immediate_actions || remediation.immediate_actions.length === 0) && (
            <li className="text-gray-400 text-xs">No immediate actions available</li>
          )}
        </ul>
      </div>

      {/* Responsibility */}
      <div className="flex items-center justify-between text-sm">
        <div className="flex items-center space-x-2">
          <Users className="w-4 h-4 text-gray-400" />
          <span className="text-gray-400">Responsible:</span>
          <span className="text-white">
            {responsibilityAssignment.primary_responsible || 'Not assigned'}
          </span>
        </div>

        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="flex items-center space-x-1 text-purple-400 hover:text-purple-300 transition-colors"
        >
          <span className="text-xs">{isExpanded ? 'Less details' : 'More details'}</span>
          {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
        </button>
      </div>

      {/* Extended section */}
      {isExpanded && (
        <div className="mt-4 pt-4 border-t border-gray-600 space-y-4">
          {/* Technical description */}
          <div>
            <h5 className="text-sm font-semibold text-white mb-2">Technical Description</h5>
            <p className="text-gray-300 text-sm">
              {vulnAnalysis.technical_description || 'Technical description not available'}
            </p>
          </div>

          {/* Exploitation details */}
          <div>
            <h5 className="text-sm font-semibold text-white mb-2">Exploitation Details</h5>
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div>
                <p className="text-gray-400">Attack Vector:</p>
                <p className="text-gray-300">{exploitationDetails.attack_vector || 'N/A'}</p>
              </div>
              <div>
                <p className="text-gray-400">Difficulty:</p>
                <p className="text-gray-300">{exploitationDetails.difficulty || 'N/A'}</p>
              </div>
            </div>
          </div>

          {/* Long-term solutions */}
          <div>
            <h5 className="text-sm font-semibold text-white mb-2">Long-term Solutions</h5>
            <ul className="text-sm text-gray-300 space-y-1">
              {(remediation.long_term_solutions || []).map((solution, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <span className="text-green-400 mt-1">•</span>
                  <span>{solution}</span>
                </li>
              ))}
              {(!remediation.long_term_solutions ||
                remediation.long_term_solutions.length === 0) && (
                <li className="text-gray-400">No long-term solutions available</li>
              )}
            </ul>
          </div>

          {/* References */}
          {(vulnAnalysis.references || []).length > 0 && (
            <div>
              <h5 className="text-sm font-semibold text-white mb-2">References</h5>
              <div className="space-y-1">
                {(vulnAnalysis.references || []).map((ref, index) => (
                  <a
                    key={index}
                    href={ref}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center space-x-2 text-blue-400 hover:text-blue-300 text-sm transition-colors"
                  >
                    <ExternalLink className="w-3 h-3" />
                    <span className="truncate">{ref}</span>
                  </a>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default VulnerabilityCard;
