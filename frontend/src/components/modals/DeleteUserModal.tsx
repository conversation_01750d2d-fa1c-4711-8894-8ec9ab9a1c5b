import { useState } from 'react';
import { X, Trash2, AlertTriangle, Database, UserX, Shield } from 'lucide-react';

interface DeleteUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  username: string;
  loading?: boolean;
}

export default function DeleteUserModal({
  isOpen,
  onClose,
  onConfirm,
  username,
  loading = false,
}: DeleteUserModalProps) {
  const [confirmText, setConfirmText] = useState('');
  const isConfirmValid = confirmText === username;

  if (!isOpen) return null;

  const handleConfirm = () => {
    if (isConfirmValid) {
      onConfirm();
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 border border-red-500/50 rounded-xl max-w-md w-full shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-red-600/20 rounded-full flex items-center justify-center">
              <Trash2 size={20} className="text-red-400" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">Delete User</h2>
              <p className="text-gray-400 text-sm">Irreversible action</p>
            </div>
          </div>

          <button
            onClick={onClose}
            disabled={loading}
            className="text-gray-400 hover:text-white p-2 rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          {/* Danger Icon */}
          <div className="flex justify-center">
            <div className="w-16 h-16 bg-red-600/20 rounded-full flex items-center justify-center">
              <AlertTriangle size={32} className="text-red-400" />
            </div>
          </div>

          {/* Main Message */}
          <div className="text-center">
            <h3 className="text-lg font-semibold text-white mb-2">
              ⚠️ DANGER - Permanent Deletion
            </h3>
            <p className="text-red-300 font-medium">
              You are about to delete <span className="text-red-400">@{username}</span>
            </p>
          </div>

          {/* Consequences List */}
          <div className="bg-red-900/30 border border-red-500/50 rounded-lg p-4">
            <h4 className="text-red-300 font-medium text-sm mb-3">This action will:</h4>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <UserX size={16} className="text-red-400 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-red-300 font-medium text-sm">
                    Permanently delete the account
                  </p>
                  <p className="text-red-400/80 text-xs">Data cannot be recovered</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Database size={16} className="text-red-400 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-red-300 font-medium text-sm">Erase all data</p>
                  <p className="text-red-400/80 text-xs">Profile, settings, history</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Shield size={16} className="text-red-400 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-red-300 font-medium text-sm">Terminate all sessions</p>
                  <p className="text-red-400/80 text-xs">
                    Immediate logout from all devices
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Critical Warning */}
          <div className="bg-red-900/50 border-2 border-red-500/70 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <AlertTriangle size={16} className="text-red-400" />
              <p className="text-red-300 font-bold text-sm">CRITICAL WARNING</p>
            </div>
            <p className="text-red-200 text-sm">
              This action is <span className="font-bold">IRREVERSIBLE</span>. All data
              will be permanently lost.
            </p>
          </div>

          {/* Confirmation Input */}
          <div className="space-y-2">
            <label className="block text-gray-300 text-sm font-medium">
              To confirm, type the username:{' '}
              <span className="text-red-400 font-mono">{username}</span>
            </label>
            <input
              type="text"
              value={confirmText}
              onChange={(e) => setConfirmText(e.target.value)}
              placeholder={`Type "${username}" to confirm`}
              disabled={loading}
              className="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 disabled:opacity-50"
            />
            {confirmText && !isConfirmValid && (
              <p className="text-red-400 text-xs">Username does not match</p>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-700 bg-gray-800/50">
          <button
            onClick={onClose}
            disabled={loading}
            className="px-4 py-2 text-gray-400 hover:text-white border border-gray-600 rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={handleConfirm}
            disabled={loading || !isConfirmValid}
            className="bg-red-600 hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Trash2 size={16} />
            )}
            <span>{loading ? 'Deleting...' : 'Delete Permanently'}</span>
          </button>
        </div>
      </div>
    </div>
  );
}
