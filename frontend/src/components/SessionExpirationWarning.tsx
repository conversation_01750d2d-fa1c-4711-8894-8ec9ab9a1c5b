import React, { useState, useEffect } from 'react';
import { useAuthContext } from '../contexts/AuthContext';
import SessionExpirationAlert from './alerts/SessionExpirationAlert';

export default function SessionExpirationWarning() {
  const { timeRemaining, isAuthenticated } = useAuthContext();
  const [showWarning, setShowWarning] = useState(false);

  useEffect(() => {
    // Afficher l'avertissement quand il reste 5 minutes ou moins
    if (timeRemaining <= 300 && timeRemaining > 0 && isAuthenticated) {
      if (!showWarning) {
        setShowWarning(true);
      }
    } else {
      // Masquer l'avertissement si la session est rafraîchie ou si l'utilisateur se déconnecte
      if (showWarning) {
        setShowWarning(false);
      }
    }
  }, [timeRemaining, isAuthenticated, showWarning]);

  const handleCloseWarning = () => {
    setShowWarning(false);
  };

  // Afficher l'alerte personnalisée si nécessaire
  if (showWarning && isAuthenticated && timeRemaining <= 300 && timeRemaining > 0) {
    return <SessionExpirationAlert onClose={handleCloseWarning} />;
  }

  return null;
}
