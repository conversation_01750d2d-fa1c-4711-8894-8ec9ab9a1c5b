import React from 'react';

interface SkeletonProps {
  className?: string;
}

const Skeleton: React.FC<SkeletonProps> = ({ className = '' }) => (
  <div className={`animate-pulse bg-gray-700/30 rounded ${className}`}></div>
);

export const StatCardSkeleton: React.FC = () => (
  <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-6 shadow-xl">
    <div className="flex items-center justify-between mb-4">
      <Skeleton className="w-12 h-12 rounded-xl" />
      <div className="flex items-center space-x-1">
        <Skeleton className="w-4 h-4" />
        <Skeleton className="w-8 h-4" />
      </div>
    </div>
    <div>
      <Skeleton className="w-16 h-8 mb-2" />
      <Skeleton className="w-20 h-4 mb-1" />
      <Skeleton className="w-16 h-3" />
    </div>
  </div>
);

export const VulnerabilityStatsSkeleton: React.FC = () => (
  <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
    <div className="flex items-center mb-6">
      <Skeleton className="w-5 h-5 mr-2" />
      <Skeleton className="w-40 h-6" />
    </div>
    
    {/* Total Vulnerabilities */}
    <div className="mb-6 text-center">
      <Skeleton className="w-16 h-10 mx-auto mb-2" />
      <Skeleton className="w-32 h-4 mx-auto" />
    </div>

    {/* Severity Distribution */}
    <div className="mb-6">
      <Skeleton className="w-32 h-5 mb-4" />
      <div className="space-y-3">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="flex items-center justify-between">
            <Skeleton className="w-16 h-6" />
            <div className="flex items-center space-x-2">
              <Skeleton className="w-20 h-2" />
              <Skeleton className="w-6 h-4" />
            </div>
          </div>
        ))}
      </div>
    </div>

    {/* Tool Distribution */}
    <div>
      <Skeleton className="w-28 h-5 mb-4" />
      <div className="grid grid-cols-2 gap-3">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="bg-gray-700/30 rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <Skeleton className="w-4 h-4" />
                <Skeleton className="w-12 h-4" />
              </div>
              <Skeleton className="w-6 h-4" />
            </div>
            <Skeleton className="w-full h-1" />
          </div>
        ))}
      </div>
    </div>
  </div>
);

export const ThreatStatsSkeleton: React.FC = () => (
  <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
    <div className="flex items-center mb-6">
      <Skeleton className="w-5 h-5 mr-2" />
      <Skeleton className="w-32 h-6" />
    </div>

    {/* Phishing Statistics */}
    <div className="mb-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <Skeleton className="w-4 h-4 mr-2" />
          <Skeleton className="w-28 h-5" />
        </div>
        <Skeleton className="w-16 h-4" />
      </div>
      
      <div className="grid grid-cols-3 gap-4 mb-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-gray-700/30 rounded-lg p-3 text-center">
            <Skeleton className="w-8 h-8 mx-auto mb-1" />
            <Skeleton className="w-16 h-3 mx-auto" />
          </div>
        ))}
      </div>

      <div className="flex items-center justify-between mb-2">
        <Skeleton className="w-16 h-4" />
        <Skeleton className="w-8 h-4" />
      </div>
      <Skeleton className="w-full h-2" />
    </div>

    {/* Malware Statistics */}
    <div>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <Skeleton className="w-4 h-4 mr-2" />
          <Skeleton className="w-28 h-5" />
        </div>
        <Skeleton className="w-16 h-4" />
      </div>
      
      <div className="grid grid-cols-3 gap-4 mb-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-gray-700/30 rounded-lg p-3 text-center">
            <Skeleton className="w-8 h-8 mx-auto mb-1" />
            <Skeleton className="w-16 h-3 mx-auto" />
          </div>
        ))}
      </div>

      <div className="flex items-center justify-between mb-2">
        <Skeleton className="w-20 h-4" />
        <Skeleton className="w-8 h-4" />
      </div>
      <Skeleton className="w-full h-2" />
    </div>
  </div>
);

export const ScanActivitySkeleton: React.FC = () => (
  <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
    <div className="flex items-center mb-6">
      <Skeleton className="w-5 h-5 mr-2" />
      <Skeleton className="w-24 h-6" />
    </div>

    {/* Time-based Activity */}
    <div className="mb-6">
      <Skeleton className="w-28 h-5 mb-4" />
      <div className="grid grid-cols-3 gap-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-gray-700/30 rounded-lg p-4 text-center">
            <Skeleton className="w-4 h-4 mx-auto mb-2" />
            <Skeleton className="w-8 h-8 mx-auto mb-1" />
            <Skeleton className="w-16 h-3 mx-auto" />
          </div>
        ))}
      </div>
    </div>

    {/* Scan Type Distribution */}
    <div>
      <Skeleton className="w-36 h-5 mb-4" />
      <div className="space-y-3">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Skeleton className="w-8 h-8 rounded-lg" />
              <Skeleton className="w-20 h-4" />
            </div>
            <div className="flex items-center space-x-3">
              <Skeleton className="w-20 h-2" />
              <Skeleton className="w-6 h-4" />
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
);

export const ChartSkeleton: React.FC = () => (
  <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
    <div className="flex items-center mb-6">
      <Skeleton className="w-5 h-5 mr-2" />
      <Skeleton className="w-32 h-6" />
    </div>
    
    <div className="space-y-4">
      {[1, 2, 3, 4].map((i) => (
        <div key={i} className="flex items-center justify-between">
          <Skeleton className="w-24 h-4" />
          <div className="flex items-center space-x-2">
            <Skeleton className="w-20 h-2" />
            <Skeleton className="w-8 h-4" />
          </div>
        </div>
      ))}
    </div>
  </div>
);

export default Skeleton;
