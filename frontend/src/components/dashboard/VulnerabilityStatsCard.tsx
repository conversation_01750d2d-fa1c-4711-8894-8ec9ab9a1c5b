import React from 'react';
import { Shield, AlertTriangle, Zap, Activity, Target, Globe, Database, Bug } from 'lucide-react';
import { VulnerabilityStats } from '../../services/dashboardService';

interface VulnerabilityStatsCardProps {
  stats: VulnerabilityStats;
}

const VulnerabilityStatsCard: React.FC<VulnerabilityStatsCardProps> = ({ stats }) => {
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-400 bg-red-500/10 border-red-500/30';
      case 'high': return 'text-orange-400 bg-orange-500/10 border-orange-500/30';
      case 'medium': return 'text-yellow-400 bg-yellow-500/10 border-yellow-500/30';
      case 'low': return 'text-green-400 bg-green-500/10 border-green-500/30';
      default: return 'text-gray-400 bg-gray-500/10 border-gray-500/30';
    }
  };

  const getToolIcon = (tool: string) => {
    switch (tool) {
      case 'openvas': return <Shield className="w-4 h-4" />;
      case 'nmap': return <Target className="w-4 h-4" />;
      case 'nikto': return <Globe className="w-4 h-4" />;
      case 'sqlmap': return <Database className="w-4 h-4" />;
      default: return <Bug className="w-4 h-4" />;
    }
  };

  const severityData = [
    { name: 'Critical', count: stats.critical, color: 'critical' },
    { name: 'High', count: stats.high, color: 'high' },
    { name: 'Medium', count: stats.medium, color: 'medium' },
    { name: 'Low', count: stats.low, color: 'low' }
  ];

  const toolData = [
    { name: 'OpenVAS', count: stats.byTool.openvas, tool: 'openvas' },
    { name: 'Nmap', count: stats.byTool.nmap, tool: 'nmap' },
    { name: 'Nikto', count: stats.byTool.nikto, tool: 'nikto' },
    { name: 'SQLMap', count: stats.byTool.sqlmap, tool: 'sqlmap' }
  ];

  return (
    <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
      <h2 className="text-xl font-bold text-white mb-6 flex items-center">
        <AlertTriangle className="w-5 h-5 mr-2 text-purple-400" />
        Vulnerability Analysis
      </h2>

      {/* Total Vulnerabilities */}
      <div className="mb-6 text-center">
        <div className="text-3xl font-bold text-white mb-2">{stats.total}</div>
        <div className="text-gray-400">Total Vulnerabilities Detected</div>
      </div>

      {/* Severity Distribution */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-white mb-4">Severity Distribution</h3>
        <div className="space-y-3">
          {severityData.map((severity) => (
            <div key={severity.name} className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`px-2 py-1 rounded text-xs font-medium border ${getSeverityColor(severity.color)}`}>
                  {severity.name}
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-20 h-2 bg-gray-700 rounded-full">
                  <div 
                    className={`h-2 rounded-full ${
                      severity.color === 'critical' ? 'bg-red-500' :
                      severity.color === 'high' ? 'bg-orange-500' :
                      severity.color === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                    }`}
                    style={{ 
                      width: stats.total > 0 ? `${(severity.count / stats.total) * 100}%` : '0%' 
                    }}
                  ></div>
                </div>
                <span className="text-white font-bold text-sm w-8 text-right">{severity.count}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Tool Distribution */}
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">Detection by Tool</h3>
        <div className="grid grid-cols-2 gap-3">
          {toolData.map((tool) => (
            <div key={tool.name} className="bg-gray-700/30 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <div className="text-purple-400">
                    {getToolIcon(tool.tool)}
                  </div>
                  <span className="text-gray-300 text-sm">{tool.name}</span>
                </div>
                <span className="text-white font-bold">{tool.count}</span>
              </div>
              <div className="w-full h-1 bg-gray-600 rounded-full">
                <div 
                  className="h-1 bg-purple-500 rounded-full"
                  style={{ 
                    width: stats.total > 0 ? `${(tool.count / stats.total) * 100}%` : '0%' 
                  }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default VulnerabilityStatsCard;
