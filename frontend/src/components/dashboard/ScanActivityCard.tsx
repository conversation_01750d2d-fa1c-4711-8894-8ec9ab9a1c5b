import React from 'react';
import { Activity, Target, Globe, Shield, Zap, Calendar, Clock, TrendingUp } from 'lucide-react';
import { ScanActivity } from '../../services/dashboardService';

interface ScanActivityCardProps {
  activity: ScanActivity;
}

const ScanActivityCard: React.FC<ScanActivityCardProps> = ({ activity }) => {
  const getScanTypeIcon = (type: string) => {
    switch (type) {
      case 'network': return <Target className="w-4 h-4" />;
      case 'web': return <Globe className="w-4 h-4" />;
      case 'vulnerability': return <Shield className="w-4 h-4" />;
      case 'deep': return <Zap className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  const getScanTypeColor = (type: string) => {
    switch (type) {
      case 'network': return 'text-blue-400 bg-blue-500/10 border-blue-500/30';
      case 'web': return 'text-green-400 bg-green-500/10 border-green-500/30';
      case 'vulnerability': return 'text-purple-400 bg-purple-500/10 border-purple-500/30';
      case 'deep': return 'text-red-400 bg-red-500/10 border-red-500/30';
      default: return 'text-gray-400 bg-gray-500/10 border-gray-500/30';
    }
  };

  const timeData = [
    { label: 'Today', value: activity.today, icon: <Clock className="w-4 h-4" /> },
    { label: 'This Week', value: activity.week, icon: <Calendar className="w-4 h-4" /> },
    { label: 'This Month', value: activity.month, icon: <TrendingUp className="w-4 h-4" /> }
  ];

  const scanTypes = [
    { name: 'Network', count: activity.byType.network, type: 'network' },
    { name: 'Web', count: activity.byType.web, type: 'web' },
    { name: 'Vulnerability', count: activity.byType.vulnerability, type: 'vulnerability' },
    { name: 'Deep', count: activity.byType.deep, type: 'deep' }
  ];

  const totalScans = Object.values(activity.byType).reduce((sum, count) => sum + count, 0);

  return (
    <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
      <h2 className="text-xl font-bold text-white mb-6 flex items-center">
        <Activity className="w-5 h-5 mr-2 text-purple-400" />
        Scan Activity
      </h2>

      {/* Time-based Activity */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-white mb-4">Activity Timeline</h3>
        <div className="grid grid-cols-3 gap-4">
          {timeData.map((time) => (
            <div key={time.label} className="bg-gray-700/30 rounded-lg p-4 text-center">
              <div className="flex justify-center mb-2 text-purple-400">
                {time.icon}
              </div>
              <div className="text-2xl font-bold text-white mb-1">{time.value}</div>
              <div className="text-gray-400 text-xs">{time.label}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Scan Type Distribution */}
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">Scan Types Distribution</h3>
        <div className="space-y-3">
          {scanTypes.map((scan) => (
            <div key={scan.name} className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-lg ${getScanTypeColor(scan.type)}`}>
                  {getScanTypeIcon(scan.type)}
                </div>
                <span className="text-gray-300">{scan.name}</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-20 h-2 bg-gray-700 rounded-full">
                  <div 
                    className={`h-2 rounded-full ${
                      scan.type === 'network' ? 'bg-blue-500' :
                      scan.type === 'web' ? 'bg-green-500' :
                      scan.type === 'vulnerability' ? 'bg-purple-500' : 'bg-red-500'
                    }`}
                    style={{ 
                      width: totalScans > 0 ? `${(scan.count / totalScans) * 100}%` : '0%' 
                    }}
                  ></div>
                </div>
                <span className="text-white font-bold text-sm w-8 text-right">{scan.count}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Summary Stats */}
      <div className="mt-6 pt-4 border-t border-gray-700/50">
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className="text-lg font-bold text-white">{totalScans}</div>
            <div className="text-gray-400 text-xs">Total Scans</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-green-400">
              {activity.today > 0 ? Math.round((activity.today / activity.week) * 100) : 0}%
            </div>
            <div className="text-gray-400 text-xs">Daily Activity</div>
          </div>
        </div>
      </div>

      {/* Activity Indicator */}
      <div className="mt-4 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${
            activity.today > 0 ? 'bg-green-400 animate-pulse' : 'bg-gray-500'
          }`}></div>
          <span className={`text-sm ${
            activity.today > 0 ? 'text-green-400' : 'text-gray-400'
          }`}>
            {activity.today > 0 ? 'Active Today' : 'No Activity Today'}
          </span>
        </div>
      </div>
    </div>
  );
};

export default ScanActivityCard;
