import React from 'react';
import { BarChart3, Zap, Globe, MapPin } from 'lucide-react';
import { DashboardData } from '../../services/dashboardService';

interface DynamicChartsProps {
  dashboardData: DashboardData;
}

const DynamicCharts: React.FC<DynamicChartsProps> = ({ dashboardData }) => {
  // Calculate incident trends based on real data
  const calculateIncidentTrends = () => {
    const { threatStats, stats } = dashboardData;
    
    if (!threatStats) {
      return [
        { name: 'Malware Detections', percentage: 0, color: 'bg-red-500' },
        { name: 'Phishing Attempts', percentage: 0, color: 'bg-orange-500' },
        { name: 'Network Intrusions', percentage: 0, color: 'bg-yellow-500' },
        { name: 'Data Breaches', percentage: 0, color: 'bg-green-500' }
      ];
    }

    const totalThreats = stats.criticalThreats || 1; // Avoid division by zero
    const malwarePercentage = Math.round((threatStats.malware.quarantined / totalThreats) * 100);
    const phishingPercentage = Math.round((threatStats.phishing.blocked / totalThreats) * 100);
    const networkPercentage = Math.max(0, 100 - malwarePercentage - phishingPercentage - 10); // Estimated
    const breachPercentage = Math.max(0, 100 - malwarePercentage - phishingPercentage - networkPercentage);

    return [
      { name: 'Malware Detections', percentage: malwarePercentage, color: 'bg-red-500' },
      { name: 'Phishing Attempts', percentage: phishingPercentage, color: 'bg-orange-500' },
      { name: 'Network Intrusions', percentage: networkPercentage, color: 'bg-yellow-500' },
      { name: 'Data Breaches', percentage: breachPercentage, color: 'bg-green-500' }
    ];
  };

  // Calculate attack type distribution
  const calculateAttackDistribution = () => {
    const { threatStats, vulnerabilityStats } = dashboardData;
    
    if (!threatStats || !vulnerabilityStats) {
      return [
        { name: 'Malware', percentage: 0, color: 'bg-red-500' },
        { name: 'Phishing', percentage: 0, color: 'bg-orange-500' },
        { name: 'Network', percentage: 0, color: 'bg-yellow-500' },
        { name: 'Other', percentage: 0, color: 'bg-green-500' }
      ];
    }

    const totalDetections = threatStats.malware.total + threatStats.phishing.total + vulnerabilityStats.total || 1;
    
    const malwarePercentage = Math.round((threatStats.malware.total / totalDetections) * 100);
    const phishingPercentage = Math.round((threatStats.phishing.total / totalDetections) * 100);
    const networkPercentage = Math.round((vulnerabilityStats.total / totalDetections) * 100);
    const otherPercentage = Math.max(0, 100 - malwarePercentage - phishingPercentage - networkPercentage);

    return [
      { name: 'Malware', percentage: malwarePercentage, color: 'bg-red-500' },
      { name: 'Phishing', percentage: phishingPercentage, color: 'bg-orange-500' },
      { name: 'Network', percentage: networkPercentage, color: 'bg-yellow-500' },
      { name: 'Other', percentage: otherPercentage, color: 'bg-green-500' }
    ];
  };

  // Calculate threat map data
  const calculateThreatMapData = () => {
    const attackSources = dashboardData.attackSources || [];
    const totalAttacks = attackSources.reduce((sum, source) => sum + source.attacks, 0);
    
    if (totalAttacks === 0) {
      return { highRisk: 0, mediumRisk: 0 };
    }

    // Simple heuristic: sources with >50 attacks are high risk
    const highRiskSources = attackSources.filter(source => source.attacks > 50).length;
    const mediumRiskSources = attackSources.filter(source => source.attacks > 10 && source.attacks <= 50).length;

    return { highRisk: highRiskSources, mediumRisk: mediumRiskSources };
  };

  const incidentTrends = calculateIncidentTrends();
  const attackDistribution = calculateAttackDistribution();
  const threatMapData = calculateThreatMapData();

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Incident Trends */}
      <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
        <h2 className="text-xl font-bold text-white mb-6 flex items-center">
          <BarChart3 className="w-5 h-5 mr-2 text-purple-400" />
          Incident Trends (Last 24h)
        </h2>
        <div className="space-y-4">
          {incidentTrends.map((trend) => (
            <div key={trend.name} className="flex items-center justify-between">
              <span className="text-gray-300">{trend.name}</span>
              <div className="flex items-center space-x-2">
                <div className="w-20 h-2 bg-gray-700 rounded-full">
                  <div 
                    className={`h-2 ${trend.color} rounded-full`}
                    style={{ width: `${trend.percentage}%` }}
                  ></div>
                </div>
                <span className="text-white text-sm">{trend.percentage}%</span>
              </div>
            </div>
          ))}
        </div>
        
        {dashboardData.stats.criticalThreats === 0 && (
          <div className="mt-4 p-3 bg-green-500/10 border border-green-500/30 rounded-lg">
            <p className="text-green-400 text-sm text-center">No active threats detected</p>
          </div>
        )}
      </div>

      {/* Attack Type Distribution */}
      <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
        <h2 className="text-xl font-bold text-white mb-6 flex items-center">
          <Zap className="w-5 h-5 mr-2 text-purple-400" />
          Attack Type Distribution
        </h2>
        <div className="space-y-4">
          <div className="text-center">
            <div className="relative w-32 h-32 mx-auto mb-4">
              {/* Dynamic pie chart representation */}
              <div className="absolute inset-0 rounded-full border-8 border-gray-700"></div>
              {attackDistribution.map((attack, index) => (
                <div
                  key={attack.name}
                  className={`absolute inset-${index * 2} rounded-full border-${8 - index * 2} ${
                    attack.name === 'Malware' ? 'border-red-500' :
                    attack.name === 'Phishing' ? 'border-orange-500' :
                    attack.name === 'Network' ? 'border-yellow-500' : 'border-green-500'
                  } border-t-transparent transform rotate-${45 + index * 90}`}
                  style={{ 
                    borderWidth: `${Math.max(2, attack.percentage / 10)}px`,
                    opacity: attack.percentage > 0 ? 1 : 0.3
                  }}
                ></div>
              ))}
            </div>
          </div>
          <div className="space-y-2">
            {attackDistribution.map((attack) => (
              <div key={attack.name} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className={`w-3 h-3 ${attack.color} rounded-full`}></div>
                  <span className="text-gray-300 text-sm">{attack.name}</span>
                </div>
                <span className="text-white text-sm">{attack.percentage}%</span>
              </div>
            ))}
          </div>
        </div>
        
        {attackDistribution.every(attack => attack.percentage === 0) && (
          <div className="mt-4 p-3 bg-blue-500/10 border border-blue-500/30 rounded-lg">
            <p className="text-blue-400 text-sm text-center">No attack data available</p>
          </div>
        )}
      </div>

      {/* Global Threat Map */}
      <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
        <h2 className="text-xl font-bold text-white mb-6 flex items-center">
          <Globe className="w-5 h-5 mr-2 text-purple-400" />
          Global Threat Map
        </h2>
        <div className="space-y-4">
          <div className="bg-gray-700/30 rounded-lg p-4 text-center">
            <MapPin className="w-16 h-16 text-purple-400 mx-auto mb-2" />
            <p className="text-gray-300 text-sm">
              {dashboardData.attackSources.length > 0 
                ? `Monitoring ${dashboardData.attackSources.length} attack sources`
                : 'No active attack sources detected'
              }
            </p>
            <p className="text-gray-400 text-xs">Real-time attack visualization</p>
          </div>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="bg-red-500/20 p-2 rounded text-center">
              <div className="text-red-400 font-bold">High Risk</div>
              <div className="text-gray-300">{threatMapData.highRisk} sources</div>
            </div>
            <div className="bg-yellow-500/20 p-2 rounded text-center">
              <div className="text-yellow-400 font-bold">Medium Risk</div>
              <div className="text-gray-300">{threatMapData.mediumRisk} sources</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DynamicCharts;
