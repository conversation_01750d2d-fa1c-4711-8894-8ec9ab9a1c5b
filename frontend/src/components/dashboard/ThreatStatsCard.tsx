import React from 'react';
import { Globe, Bug, Shield, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';
import { ThreatStats } from '../../services/dashboardService';

interface ThreatStatsCardProps {
  stats: ThreatStats;
}

const ThreatStatsCard: React.FC<ThreatStatsCardProps> = ({ stats }) => {
  const getRiskColor = (score: number) => {
    if (score >= 80) return 'text-red-400';
    if (score >= 60) return 'text-orange-400';
    if (score >= 40) return 'text-yellow-400';
    return 'text-green-400';
  };

  const getBlockedPercentage = (blocked: number, total: number) => {
    return total > 0 ? Math.round((blocked / total) * 100) : 0;
  };

  return (
    <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
      <h2 className="text-xl font-bold text-white mb-6 flex items-center">
        <Shield className="w-5 h-5 mr-2 text-purple-400" />
        Threat Detection
      </h2>

      {/* Phishing Statistics */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white flex items-center">
            <Globe className="w-4 h-4 mr-2 text-blue-400" />
            Phishing Detection
          </h3>
          <div className={`text-sm font-medium ${getRiskColor(stats.phishing.risk_score_avg)}`}>
            Risk Score: {stats.phishing.risk_score_avg.toFixed(1)}
          </div>
        </div>
        
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="bg-gray-700/30 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-white">{stats.phishing.total}</div>
            <div className="text-gray-400 text-xs">Total Analyzed</div>
          </div>
          <div className="bg-gray-700/30 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-red-400">{stats.phishing.blocked}</div>
            <div className="text-gray-400 text-xs">Blocked</div>
          </div>
          <div className="bg-gray-700/30 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-green-400">
              {stats.phishing.total - stats.phishing.blocked}
            </div>
            <div className="text-gray-400 text-xs">Safe</div>
          </div>
        </div>

        {/* Phishing Block Rate */}
        <div className="flex items-center justify-between mb-2">
          <span className="text-gray-300 text-sm">Block Rate</span>
          <span className="text-white font-bold">
            {getBlockedPercentage(stats.phishing.blocked, stats.phishing.total)}%
          </span>
        </div>
        <div className="w-full h-2 bg-gray-700 rounded-full">
          <div 
            className="h-2 bg-red-500 rounded-full"
            style={{ 
              width: `${getBlockedPercentage(stats.phishing.blocked, stats.phishing.total)}%` 
            }}
          ></div>
        </div>
      </div>

      {/* Malware Statistics */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white flex items-center">
            <Bug className="w-4 h-4 mr-2 text-red-400" />
            Malware Detection
          </h3>
          <div className={`text-sm font-medium ${getRiskColor(stats.malware.threat_score_avg)}`}>
            Threat Score: {stats.malware.threat_score_avg.toFixed(1)}
          </div>
        </div>
        
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="bg-gray-700/30 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-white">{stats.malware.total}</div>
            <div className="text-gray-400 text-xs">Total Scanned</div>
          </div>
          <div className="bg-gray-700/30 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-orange-400">{stats.malware.quarantined}</div>
            <div className="text-gray-400 text-xs">Quarantined</div>
          </div>
          <div className="bg-gray-700/30 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-green-400">
              {stats.malware.total - stats.malware.quarantined}
            </div>
            <div className="text-gray-400 text-xs">Clean</div>
          </div>
        </div>

        {/* Malware Detection Rate */}
        <div className="flex items-center justify-between mb-2">
          <span className="text-gray-300 text-sm">Detection Rate</span>
          <span className="text-white font-bold">
            {getBlockedPercentage(stats.malware.quarantined, stats.malware.total)}%
          </span>
        </div>
        <div className="w-full h-2 bg-gray-700 rounded-full">
          <div 
            className="h-2 bg-orange-500 rounded-full"
            style={{ 
              width: `${getBlockedPercentage(stats.malware.quarantined, stats.malware.total)}%` 
            }}
          ></div>
        </div>
      </div>

      {/* Overall Threat Status */}
      <div className="mt-6 pt-4 border-t border-gray-700/50">
        <div className="flex items-center justify-between">
          <span className="text-gray-300">Overall Threat Level</span>
          <div className="flex items-center space-x-2">
            {(stats.phishing.risk_score_avg + stats.malware.threat_score_avg) / 2 > 60 ? (
              <>
                <AlertTriangle className="w-4 h-4 text-red-400" />
                <span className="text-red-400 font-medium">High</span>
              </>
            ) : (stats.phishing.risk_score_avg + stats.malware.threat_score_avg) / 2 > 30 ? (
              <>
                <XCircle className="w-4 h-4 text-yellow-400" />
                <span className="text-yellow-400 font-medium">Medium</span>
              </>
            ) : (
              <>
                <CheckCircle className="w-4 h-4 text-green-400" />
                <span className="text-green-400 font-medium">Low</span>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThreatStatsCard;
