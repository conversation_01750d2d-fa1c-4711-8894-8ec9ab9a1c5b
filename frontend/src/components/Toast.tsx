import React from 'react';
import { X, CheckCircle, AlertTriangle, AlertCircle, Info } from 'lucide-react';
import { ToastNotification } from '../hooks/useToast';

interface ToastProps {
  toast: ToastNotification;
  onClose: (id: string) => void;
}

const Toast: React.FC<ToastProps> = ({ toast, onClose }) => {
  const getToastStyles = () => {
    switch (toast.type) {
      case 'success':
        return {
          container: 'bg-green-900/90 border-green-500/50 text-green-100',
          icon: <CheckCircle className="w-5 h-5 text-green-400" />,
          dot: 'bg-green-400'
        };
      case 'error':
        return {
          container: 'bg-red-900/90 border-red-500/50 text-red-100',
          icon: <AlertCircle className="w-5 h-5 text-red-400" />,
          dot: 'bg-red-400'
        };
      case 'warning':
        return {
          container: 'bg-yellow-900/90 border-yellow-500/50 text-yellow-100',
          icon: <AlertTriangle className="w-5 h-5 text-yellow-400" />,
          dot: 'bg-yellow-400'
        };
      case 'info':
      default:
        return {
          container: 'bg-blue-900/90 border-blue-500/50 text-blue-100',
          icon: <Info className="w-5 h-5 text-blue-400" />,
          dot: 'bg-blue-400'
        };
    }
  };

  const styles = getToastStyles();

  return (
    <div className={`max-w-md p-4 rounded-xl shadow-2xl backdrop-blur-xl border transition-all duration-300 transform animate-in slide-in-from-right ${styles.container}`}>
      <div className="flex items-start space-x-3">
        {/* Icon */}
        <div className="flex-shrink-0 mt-0.5">
          {styles.icon}
        </div>
        
        {/* Content */}
        <div className="flex-1 min-w-0">
          <p className="font-medium text-sm leading-relaxed">
            {toast.message}
          </p>
          
          {/* Action button if provided */}
          {toast.action && (
            <button
              onClick={toast.action.onClick}
              className="mt-2 text-xs font-semibold underline hover:no-underline transition-all duration-200"
            >
              {toast.action.label}
            </button>
          )}
        </div>
        
        {/* Close button */}
        <button
          onClick={() => onClose(toast.id)}
          className="flex-shrink-0 text-gray-400 hover:text-white transition-colors duration-200 p-1 rounded-lg hover:bg-white/10"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
      
      {/* Progress bar for timed toasts */}
      {toast.duration && toast.duration > 0 && (
        <div className="mt-3 h-1 bg-white/20 rounded-full overflow-hidden">
          <div 
            className={`h-full ${styles.dot} animate-pulse`}
            style={{
              animation: `shrink ${toast.duration}ms linear forwards`
            }}
          />
        </div>
      )}
    </div>
  );
};

interface ToastContainerProps {
  toasts: ToastNotification[];
  onClose: (id: string) => void;
}

export const ToastContainer: React.FC<ToastContainerProps> = ({ toasts, onClose }) => {
  if (toasts.length === 0) return null;

  return (
    <div className="fixed top-4 right-4 z-50 space-y-3">
      {toasts.map((toast) => (
        <Toast key={toast.id} toast={toast} onClose={onClose} />
      ))}
    </div>
  );
};

export default Toast;
