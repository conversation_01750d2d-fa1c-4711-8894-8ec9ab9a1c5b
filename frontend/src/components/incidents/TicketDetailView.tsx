import React, { useState } from 'react';
import {
  ArrowLeft,
  Edit,
  UserPlus,
  MessageSquare,
  Paperclip,
  ArrowRight,
  Clock,
  User,
  Calendar,
  AlertTriangle,
  CheckCircle,
  XCircle,
  FileText,
  Send,
  RotateCcw,
} from 'lucide-react';
import { InvestigationTicket, Incident } from '../../services/incidentService';
import { SeverityBadge, StatusBadge, PriorityBadge } from './InteractiveBadges';

interface TicketDetailViewProps {
  ticket: InvestigationTicket;
  onBack: () => void;
  onEdit: () => void;
  onAssign: () => void;
  onConvertToIncident: () => void;
  onStatusChange: (newStatus: string) => void;
  canEdit: boolean;
  canAssign: boolean;
  canConvert: boolean;
}

interface TimelineEvent {
  id: string;
  timestamp: string;
  type: 'created' | 'assigned' | 'status_change' | 'comment' | 'converted' | 'file_attached';
  user: string;
  description: string;
  details?: any;
}

export const TicketDetailView: React.FC<TicketDetailViewProps> = ({
  ticket,
  onBack,
  onEdit,
  onAssign,
  onConvertToIncident,
  onStatusChange,
  canEdit,
  canAssign,
  canConvert,
}) => {
  const [newComment, setNewComment] = useState('');
  const [isAddingComment, setIsAddingComment] = useState(false);

  // Timeline simulée - en production, cela viendrait de l'API
  const [timeline] = useState<TimelineEvent[]>([
    {
      id: '1',
      timestamp: ticket.created_at,
      type: 'created',
      user: 'Système',
      description: 'Ticket créé',
    },
    {
      id: '2',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      type: 'assigned',
      user: 'Admin',
      description: `Assigné à ${ticket.assigned_to || 'Non assigné'}`,
    },
    {
      id: '3',
      timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
      type: 'status_change',
      user: ticket.assigned_to || 'Utilisateur',
      description: 'Statut changé vers "En cours"',
    },
  ]);

  const getTimelineIcon = (type: string) => {
    switch (type) {
      case 'created':
        return <FileText size={16} className="text-blue-400" />;
      case 'assigned':
        return <UserPlus size={16} className="text-green-400" />;
      case 'status_change':
        return <Clock size={16} className="text-yellow-400" />;
      case 'comment':
        return <MessageSquare size={16} className="text-purple-400" />;
      case 'converted':
        return <ArrowRight size={16} className="text-orange-400" />;
      case 'file_attached':
        return <Paperclip size={16} className="text-gray-400" />;
      default:
        return <Clock size={16} className="text-gray-400" />;
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffHours = Math.floor((now.getTime() - time.getTime()) / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) return `il y a ${diffDays} jour${diffDays > 1 ? 's' : ''}`;
    if (diffHours > 0) return `il y a ${diffHours} heure${diffHours > 1 ? 's' : ''}`;
    return "À l'instant";
  };

  const handleAddComment = () => {
    if (newComment.trim()) {
      // En production, cela ferait un appel API
      console.log('Nouveau commentaire:', newComment);
      setNewComment('');
      setIsAddingComment(false);
    }
  };

  const statusOptions = [
    { value: 'new', label: 'Nouveau', color: 'text-sky-400' },
    { value: 'open', label: 'Ouvert', color: 'text-blue-400' },
    { value: 'in_progress', label: 'En cours', color: 'text-yellow-400' },
    { value: 'on_hold', label: 'En attente', color: 'text-amber-400' },
    { value: 'resolved', label: 'Résolu', color: 'text-emerald-400' },
    { value: 'closed', label: 'Fermé', color: 'text-gray-400' },
  ];

  // Helper function to get priority emoji and text
  const getPriorityDisplay = (priority: number) => {
    switch (priority) {
      case 1:
        return { emoji: '🔴', text: 'Critical' };
      case 2:
        return { emoji: '🟠', text: 'High' };
      case 3:
        return { emoji: '🟡', text: 'Medium' };
      case 4:
        return { emoji: '🟢', text: 'Low' };
      default:
        return { emoji: '⚪', text: 'Unknown' };
    }
  };

  // Helper function to format status
  const getStatusDisplay = (status: string, convertedIncidentId?: string) => {
    if (status === 'converted_to_incident') {
      return `Closed - Converted to incident`;
    }
    return status.replace('_', ' ').replace(/\b\w/g, (l) => l.toUpperCase());
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header with Back Button */}
        <div className="flex items-center justify-between mb-6">
          <button
            onClick={onBack}
            className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors duration-200"
          >
            <ArrowLeft size={20} />
            <span>Retour à la liste</span>
          </button>

          <div className="flex items-center space-x-3">
            {canEdit && (
              <button
                onClick={onEdit}
                className="flex items-center space-x-2 px-4 py-2 bg-yellow-600/20 border border-yellow-500/50 text-yellow-400 rounded-xl hover:bg-yellow-600/30 transition-all duration-200"
              >
                <Edit size={16} />
                <span>Modifier</span>
              </button>
            )}

            {canAssign && (
              <button
                onClick={onAssign}
                className="flex items-center space-x-2 px-4 py-2 bg-green-600/20 border border-green-500/50 text-green-400 rounded-xl hover:bg-green-600/30 transition-all duration-200"
              >
                <UserPlus size={16} />
                <span>Assigner</span>
              </button>
            )}

            {canConvert && (
              <button
                onClick={onConvertToIncident}
                className="flex items-center space-x-2 px-4 py-2 bg-purple-600/20 border border-purple-500/50 text-purple-400 rounded-xl hover:bg-purple-600/30 transition-all duration-200"
              >
                <ArrowRight size={16} />
                <span>Convertir en Incident</span>
              </button>
            )}
          </div>
        </div>

        {/* Professional Ticket Format */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-8">
          {/* Header Border */}
          <div className="border-b border-gray-600 pb-4 mb-6">
            <div className="text-center">
              <div className="text-gray-400 text-sm mb-2">─────────────────────────────</div>
              <h1 className="text-2xl font-bold text-white font-mono">
                Ticket #{ticket.ticket_number}
              </h1>
              <div className="text-gray-400 text-sm mt-2">─────────────────────────────</div>
            </div>
          </div>

          {/* Main Content */}
          <div className="space-y-6">
            {/* Priority and Status */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <span className="text-gray-400 font-medium">Priority:</span>
                <span className="text-lg">
                  {getPriorityDisplay(ticket.priority || 3).emoji}{' '}
                  {getPriorityDisplay(ticket.priority || 3).text}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-gray-400 font-medium">Status:</span>
                <span className="text-white font-medium">
                  {getStatusDisplay(ticket.status, ticket.converted_incident_id)}
                </span>
              </div>
            </div>

            {/* Type */}
            <div className="flex items-center space-x-2">
              <span className="text-gray-400 font-medium">Type:</span>
              <span className="text-white">
                {ticket.category || 'General'} - {ticket.subcategory || 'Support Request'}
              </span>
            </div>

            {/* Title */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <span className="text-blue-400 text-lg">▶</span>
                <span className="text-gray-400 font-medium">Title:</span>
                <span className="text-white font-semibold">
                  {ticket.short_description || ticket.title || 'Sans titre'}
                </span>
              </div>
            </div>

            {/* Description */}
            <div className="space-y-2">
              <div className="flex items-start space-x-2">
                <span className="text-blue-400 text-lg mt-1">▶</span>
                <div className="flex-1">
                  <span className="text-gray-400 font-medium">Description:</span>
                  <div className="text-white mt-2 pl-4 border-l-2 border-gray-600 leading-relaxed">
                    {ticket.description || 'Aucune description disponible.'}
                  </div>
                </div>
              </div>
            </div>

            {/* Assignment and Creation */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-gray-600">
              <div className="flex items-center space-x-2">
                <span className="text-gray-400 font-medium">Assigned to:</span>
                <span className="text-white">{ticket.assigned_to || 'Non assigné'}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-gray-400 font-medium">Creation date:</span>
                <span className="text-white">{formatDate(ticket.created_at)}</span>
              </div>
            </div>

            {/* Creator Information */}
            <div className="flex items-center space-x-2">
              <span className="text-gray-400 font-medium">Created by:</span>
              <span className="text-blue-300">
                {ticket.user_email || ticket.user_id || 'Utilisateur inconnu'}
              </span>
            </div>

            {/* Linked Incident */}
            {ticket.status === 'converted_to_incident' && ticket.converted_incident_id && (
              <div className="pt-4 border-t border-gray-600">
                <div className="flex items-center space-x-2">
                  <span className="text-gray-400 font-medium">Linked Incident:</span>
                  <span className="text-violet-300 font-mono">{ticket.converted_incident_id}</span>
                  <span className="text-gray-400">→</span>
                  <button className="text-violet-400 hover:text-violet-300 underline transition-colors">
                    [View Incident Details]
                  </button>
                </div>
              </div>
            )}

            {/* Bottom Border */}
            <div className="pt-6 mt-6 border-t border-gray-600">
              <div className="text-center text-gray-400 text-sm">─────────────────────────────</div>
            </div>
          </div>

          {/* Timeline Section */}
          <div className="mt-8 bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-8">
            <h2 className="text-xl font-bold text-white mb-6 flex items-center">
              <Clock size={20} className="mr-2 text-blue-400" />
              Timeline
            </h2>

            <div className="space-y-4">
              {ticket.timeline && ticket.timeline.length > 0 ? (
                ticket.timeline.map((entry, index) => (
                  <div key={index} className="flex items-start space-x-3 text-sm">
                    <div className="text-gray-400 font-mono min-w-[120px]">
                      {formatDate(entry.timestamp)}
                    </div>
                    <div className="text-white">
                      {entry.action === 'created' &&
                        `Ticket created by ${entry.user_email || 'System'}`}
                      {entry.action === 'assigned' &&
                        `Assigned to ${entry.details?.assigned_to || 'Unknown'} by ${entry.user_email || 'System'}`}
                      {entry.action === 'status_changed' &&
                        `Status changed to ${entry.details?.new_status || 'Unknown'} by ${entry.user_email || 'System'}`}
                      {entry.action === 'converted_to_incident' &&
                        `Ticket converted to incident ${entry.details?.incident_id || ticket.converted_incident_id} by ${entry.user_email || entry.user_id || 'System'}`}
                      {entry.action === 'comment_added' &&
                        `Comment added by ${entry.user_id || 'User'}`}
                      {![
                        'created',
                        'assigned',
                        'status_changed',
                        'converted_to_incident',
                        'comment_added',
                      ].includes(entry.action) &&
                        `${entry.action.replace('_', ' ').replace(/\b\w/g, (l) => l.toUpperCase())} by ${entry.user_id || 'System'}`}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-gray-400 italic">Aucune activité enregistrée</div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
