import React, { useState } from 'react';
import { Plus, Ticket, AlertTriangle, Shield, FileText } from 'lucide-react';

interface CreateTicketButtonProps {
  onCreateTicket: () => void;
  onCreateIncident: () => void;
  className?: string;
}

export const CreateTicketButton: React.FC<CreateTicketButtonProps> = ({
  onCreateTicket,
  onCreateIncident,
  className = '',
}) => {
  const [showDropdown, setShowDropdown] = useState(false);

  return (
    <div className={`relative ${className}`}>
      {/* Bouton principal */}
      <button
        onClick={() => setShowDropdown(!showDropdown)}
        className="flex items-center space-x-3 px-6 py-4 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 border border-blue-500/50"
      >
        <Plus size={24} className="text-white" />
        <span className="text-lg">Nouveau</span>
        <div className="w-2 h-2 bg-white/30 rounded-full animate-pulse"></div>
      </button>

      {/* Menu déroulant */}
      {showDropdown && (
        <>
          {/* Overlay pour fermer le menu */}
          <div className="fixed inset-0 z-10" onClick={() => setShowDropdown(false)} />

          {/* Menu */}
          <div className="absolute top-full mt-2 right-0 z-20 bg-gray-800/95 backdrop-blur-xl border border-gray-700/50 rounded-2xl shadow-2xl overflow-hidden min-w-64">
            {/* Ticket d'investigation */}
            <button
              onClick={() => {
                onCreateTicket();
                setShowDropdown(false);
              }}
              className="w-full flex items-center space-x-4 px-6 py-4 text-left hover:bg-gray-700/50 transition-colors duration-200 border-b border-gray-700/30"
            >
              <div className="flex-shrink-0 w-12 h-12 bg-blue-600/20 border border-blue-500/30 rounded-xl flex items-center justify-center">
                <Ticket size={24} className="text-blue-400" />
              </div>
              <div className="flex-1">
                <h3 className="text-white font-semibold text-lg">Ticket d'Investigation</h3>
                <p className="text-gray-400 text-sm">
                  Pour les demandes d'analyse, recherches et investigations
                </p>
                <div className="flex items-center space-x-2 mt-2">
                  <span className="text-xs bg-blue-600/20 text-blue-400 px-2 py-1 rounded-lg">
                    🔍 Investigation
                  </span>
                  <span className="text-xs bg-gray-600/20 text-gray-400 px-2 py-1 rounded-lg">
                    📋 Suivi
                  </span>
                </div>
              </div>
            </button>

            {/* Incident de sécurité */}
            <button
              onClick={() => {
                onCreateIncident();
                setShowDropdown(false);
              }}
              className="w-full flex items-center space-x-4 px-6 py-4 text-left hover:bg-gray-700/50 transition-colors duration-200"
            >
              <div className="flex-shrink-0 w-12 h-12 bg-red-600/20 border border-red-500/30 rounded-xl flex items-center justify-center">
                <Shield size={24} className="text-red-400" />
              </div>
              <div className="flex-1">
                <h3 className="text-white font-semibold text-lg">Incident de Sécurité</h3>
                <p className="text-gray-400 text-sm">
                  Pour les incidents critiques nécessitant une réponse immédiate
                </p>
                <div className="flex items-center space-x-2 mt-2">
                  <span className="text-xs bg-red-600/20 text-red-400 px-2 py-1 rounded-lg">
                    🚨 Urgent
                  </span>
                  <span className="text-xs bg-orange-600/20 text-orange-400 px-2 py-1 rounded-lg">
                    🛡️ Sécurité
                  </span>
                </div>
              </div>
            </button>

            {/* Aide rapide */}
            <div className="px-6 py-4 bg-gray-700/30 border-t border-gray-700/30">
              <div className="flex items-start space-x-3">
                <FileText size={16} className="text-gray-400 mt-1 flex-shrink-0" />
                <div>
                  <p className="text-gray-400 text-xs leading-relaxed">
                    <strong className="text-gray-300">Conseil:</strong> Choisissez "Ticket" pour les
                    demandes d'analyse et "Incident" pour les urgences de sécurité.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

// Version compacte pour la barre d'outils
export const CompactCreateButton: React.FC<CreateTicketButtonProps> = ({
  onCreateTicket,
  onCreateIncident,
  className = '',
}) => {
  const [showDropdown, setShowDropdown] = useState(false);

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setShowDropdown(!showDropdown)}
        className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-xl transition-all duration-200"
      >
        <Plus size={18} />
        <span>Nouveau</span>
      </button>

      {showDropdown && (
        <>
          <div className="fixed inset-0 z-10" onClick={() => setShowDropdown(false)} />

          <div className="absolute top-full mt-2 right-0 z-20 bg-gray-800/95 backdrop-blur-xl border border-gray-700/50 rounded-xl shadow-xl overflow-hidden min-w-48">
            <button
              onClick={() => {
                onCreateTicket();
                setShowDropdown(false);
              }}
              className="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-700/50 transition-colors duration-200 border-b border-gray-700/30"
            >
              <Ticket size={18} className="text-blue-400" />
              <span className="text-white">Ticket d'Investigation</span>
            </button>

            <button
              onClick={() => {
                onCreateIncident();
                setShowDropdown(false);
              }}
              className="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-700/50 transition-colors duration-200"
            >
              <Shield size={18} className="text-red-400" />
              <span className="text-white">Incident de Sécurité</span>
            </button>
          </div>
        </>
      )}
    </div>
  );
};
