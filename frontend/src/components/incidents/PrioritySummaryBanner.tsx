import React from 'react';
import { <PERSON><PERSON><PERSON>riangle, Clock, CheckCircle, TrendingUp } from 'lucide-react';
import { InvestigationTicket, Incident } from '../../services/incidentService';

interface PrioritySummaryBannerProps {
  tickets: InvestigationTicket[];
  incidents: Incident[];
}

interface PriorityCount {
  critical: number;
  high: number;
  medium: number;
  low: number;
}

export const PrioritySummaryBanner: React.FC<PrioritySummaryBannerProps> = ({
  tickets,
  incidents,
}) => {
  // Compter les tickets par priorité/sévérité
  const getTicketCounts = (): PriorityCount => {
    const counts = { critical: 0, high: 0, medium: 0, low: 0 };

    tickets.forEach((ticket) => {
      const severity = ticket.severity?.toLowerCase();
      const priority = ticket.priority;

      // Utiliser la sévérité en priorité, sinon la priorité numérique
      if (severity === 'critical' || priority === 1) {
        counts.critical++;
      } else if (severity === 'high' || priority === 2) {
        counts.high++;
      } else if (severity === 'medium' || priority === 3) {
        counts.medium++;
      } else if (severity === 'low' || priority === 4 || priority === 5) {
        counts.low++;
      } else {
        // Par défaut, considérer comme medium si pas de priorité définie
        counts.medium++;
      }
    });

    return counts;
  };

  // Compter les incidents par sévérité
  const getIncidentCounts = (): PriorityCount => {
    const counts = { critical: 0, high: 0, medium: 0, low: 0 };

    incidents.forEach((incident) => {
      const severity = incident.severity?.toLowerCase();

      switch (severity) {
        case 'critical':
          counts.critical++;
          break;
        case 'high':
          counts.high++;
          break;
        case 'medium':
          counts.medium++;
          break;
        case 'low':
          counts.low++;
          break;
        default:
          counts.medium++;
      }
    });

    return counts;
  };

  const ticketCounts = getTicketCounts();
  const incidentCounts = getIncidentCounts();

  // Totaux combinés
  const totalCounts = {
    critical: ticketCounts.critical + incidentCounts.critical,
    high: ticketCounts.high + incidentCounts.high,
    medium: ticketCounts.medium + incidentCounts.medium,
    low: ticketCounts.low + incidentCounts.low,
  };

  const totalItems = totalCounts.critical + totalCounts.high + totalCounts.medium + totalCounts.low;

  // Calculer les éléments extrêmement en retard (10+ jours)
  const getExtremelyOverdueCount = () => {
    const now = new Date();
    let count = 0;

    tickets.forEach((ticket) => {
      const daysSinceCreation = Math.floor(
        (now.getTime() - new Date(ticket.created_at).getTime()) / (1000 * 60 * 60 * 24)
      );
      if (
        daysSinceCreation > 10 &&
        (ticket.status === 'open' || ticket.status === 'new' || ticket.status === 'in_progress')
      ) {
        count++;
      }
    });

    incidents.forEach((incident) => {
      const daysSinceCreation = Math.floor(
        (now.getTime() - new Date(incident.created_at).getTime()) / (1000 * 60 * 60 * 24)
      );
      if (
        daysSinceCreation > 10 &&
        (incident.status === 'open' || incident.status === 'in_progress')
      ) {
        count++;
      }
    });

    return count;
  };

  const extremelyOverdueCount = getExtremelyOverdueCount();

  return (
    <div className="bg-gradient-to-r from-gray-800/80 to-gray-900/80 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 mb-6">
      {/* Titre principal */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <TrendingUp size={24} className="text-blue-400" />
          <h2 className="text-xl font-bold text-white">Vue d'ensemble des priorités</h2>
        </div>
        <div className="text-gray-400 text-sm">
          Total: <span className="text-white font-semibold">{totalItems}</span> éléments
        </div>
      </div>

      {/* Alerte critique si éléments extrêmement en retard */}
      {extremelyOverdueCount > 0 && (
        <div className="bg-red-600/20 border border-red-500/50 rounded-xl p-4 mb-6 animate-pulse">
          <div className="flex items-center space-x-3">
            <AlertTriangle className="text-red-400 animate-bounce" size={20} />
            <span className="text-red-400 font-semibold">
              🚨 URGENT: {extremelyOverdueCount} élément{extremelyOverdueCount > 1 ? 's' : ''} en
              retard de plus de 10 jours !
            </span>
          </div>
        </div>
      )}

      {/* Compteurs par priorité */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {/* Critique */}
        <div
          className={`bg-gradient-to-br from-red-600/20 to-red-700/20 border border-red-500/30 rounded-xl p-4 ${totalCounts.critical > 0 ? 'ring-2 ring-red-500/50' : ''}`}
        >
          <div className="flex items-center justify-between mb-2">
            <span className="text-red-400 font-semibold text-sm">🔴 CRITIQUE</span>
            <AlertTriangle size={16} className="text-red-400" />
          </div>
          <div className="text-2xl font-bold text-red-300">{totalCounts.critical}</div>
          <div className="text-xs text-red-400/80">
            T:{ticketCounts.critical} | I:{incidentCounts.critical}
          </div>
        </div>

        {/* Haute */}
        <div
          className={`bg-gradient-to-br from-orange-600/20 to-orange-700/20 border border-orange-500/30 rounded-xl p-4 ${totalCounts.high > 0 ? 'ring-2 ring-orange-500/50' : ''}`}
        >
          <div className="flex items-center justify-between mb-2">
            <span className="text-orange-400 font-semibold text-sm">🟠 HAUTE</span>
            <Clock size={16} className="text-orange-400" />
          </div>
          <div className="text-2xl font-bold text-orange-300">{totalCounts.high}</div>
          <div className="text-xs text-orange-400/80">
            T:{ticketCounts.high} | I:{incidentCounts.high}
          </div>
        </div>

        {/* Moyenne */}
        <div
          className={`bg-gradient-to-br from-yellow-600/20 to-yellow-700/20 border border-yellow-500/30 rounded-xl p-4 ${totalCounts.medium > 0 ? 'ring-2 ring-yellow-500/50' : ''}`}
        >
          <div className="flex items-center justify-between mb-2">
            <span className="text-yellow-400 font-semibold text-sm">🟡 MOYENNE</span>
            <Clock size={16} className="text-yellow-400" />
          </div>
          <div className="text-2xl font-bold text-yellow-300">{totalCounts.medium}</div>
          <div className="text-xs text-yellow-400/80">
            T:{ticketCounts.medium} | I:{incidentCounts.medium}
          </div>
        </div>

        {/* Basse */}
        <div
          className={`bg-gradient-to-br from-green-600/20 to-green-700/20 border border-green-500/30 rounded-xl p-4 ${totalCounts.low > 0 ? 'ring-2 ring-green-500/50' : ''}`}
        >
          <div className="flex items-center justify-between mb-2">
            <span className="text-green-400 font-semibold text-sm">🟢 BASSE</span>
            <CheckCircle size={16} className="text-green-400" />
          </div>
          <div className="text-2xl font-bold text-green-300">{totalCounts.low}</div>
          <div className="text-xs text-green-400/80">
            T:{ticketCounts.low} | I:{incidentCounts.low}
          </div>
        </div>
      </div>

      {/* Message d'action si pas d'éléments */}
      {totalItems === 0 && (
        <div className="text-center py-8">
          <CheckCircle size={48} className="mx-auto text-green-400 mb-4" />
          <p className="text-green-400 font-semibold text-lg">🎉 Excellent travail !</p>
          <p className="text-gray-400 text-sm">Aucun ticket ou incident en attente</p>
        </div>
      )}

      {/* Message d'encouragement si beaucoup d'éléments critiques */}
      {totalCounts.critical > 5 && (
        <div className="mt-4 p-3 bg-orange-600/20 border border-orange-500/30 rounded-lg">
          <p className="text-orange-400 text-sm font-medium">
            💪 Attention: {totalCounts.critical} éléments critiques nécessitent une action immédiate
          </p>
        </div>
      )}
    </div>
  );
};
