import React from 'react';
import { Eye, Edit, UserPlus, XCircle, Clock, User, Calendar, AlertTriangle } from 'lucide-react';
import { Incident } from '../../services/incidentService';
import { SeverityBadge, StatusBadge, UrgentBadge } from './InteractiveBadges';
import { formatDate, getTimeUrgencyColor } from '../../services/incidentService';
import { TimeIndicator, ActivityPulse, UrgencyAlert } from './AdvancedIndicators';

interface EnhancedIncidentRowProps {
  incident: Incident;
  onView: () => void;
  onEdit: () => void;
  onAssign: () => void;
  onClose: () => void;
  canEdit: boolean;
  canAssign: boolean;
  canClose: boolean;
}

export const EnhancedIncidentRow: React.FC<EnhancedIncidentRowProps> = ({
  incident,
  onView,
  onEdit,
  onAssign,
  onClose,
  canEdit,
  canAssign,
  canClose,
}) => {
  const isUrgent = incident.severity === 'critical' || incident.severity === 'high';
  const timeUrgencyClass = getTimeUrgencyColor(incident.created_at);
  const timeElapsed = Math.floor(
    (new Date().getTime() - new Date(incident.created_at).getTime()) / (1000 * 60 * 60)
  );

  // Calculate time since creation
  const timeSinceCreation = () => {
    if (timeElapsed < 1) return 'Just created';
    if (timeElapsed < 24) return `${timeElapsed}h ago`;
    const diffDays = Math.floor(timeElapsed / 24);
    return `${diffDays}d ago`;
  };

  return (
    <UrgentBadge isUrgent={isUrgent}>
      <tr
        className={`border-b border-gray-700/30 hover:bg-gray-800/40 transition-all duration-300 group ${timeUrgencyClass}`}
      >
        {/* Title and Description */}
        <td className="py-6 px-6 border-r border-gray-700/30">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <h3 className="text-white font-semibold text-lg group-hover:text-purple-400 transition-colors">
                {incident.title}
              </h3>
              <ActivityPulse
                isActive={incident.status === 'open' || incident.status === 'in_progress'}
                color={incident.severity === 'critical' ? 'red' : 'blue'}
                size="sm"
              />
              {isUrgent && <AlertTriangle size={16} className="text-red-400 animate-pulse" />}
            </div>
            <p className="text-gray-400 text-sm line-clamp-2">{incident.description}</p>
            <div className="flex items-center gap-3 text-xs">
              <TimeIndicator
                createdAt={incident.created_at}
                status={incident.status}
                showDetails={false}
              />
              <span className="text-gray-500">•</span>
              <span className="font-mono text-gray-500">{incident.incident_id}</span>
            </div>
            {/* Urgency Alert for critical incidents */}
            {incident.severity === 'critical' && timeElapsed > 2 && (
              <div className="mt-2">
                <UrgencyAlert
                  severity={incident.severity}
                  timeElapsed={timeElapsed}
                  status={incident.status}
                />
              </div>
            )}
          </div>
        </td>

        {/* Severity */}
        <td className="py-6 px-6 border-r border-gray-700/30">
          <div className="flex justify-center">
            <SeverityBadge severity={incident.severity} size="md" />
          </div>
        </td>

        {/* Status */}
        <td className="py-6 px-6 border-r border-gray-700/30">
          <div className="flex justify-center">
            <StatusBadge status={incident.status} size="md" />
          </div>
        </td>

        {/* Created Date */}
        <td className="py-6 px-6 border-r border-gray-700/30">
          <div className="text-center">
            <div className="text-gray-300 text-sm font-medium">
              {formatDate(incident.created_at)}
            </div>
            <div className="text-gray-500 text-xs mt-1">{timeSinceCreation()}</div>
          </div>
        </td>

        {/* Assigned To */}
        <td className="py-6 px-6 border-r border-gray-700/30">
          <div className="text-center">
            {incident.assigned_to ? (
              <div className="flex items-center justify-center">
                <div className="flex items-center px-3 py-1 bg-purple-600/20 text-purple-400 rounded-lg border border-purple-500/30">
                  <User size={14} className="mr-1" />
                  <span className="text-sm">{incident.assigned_to}</span>
                </div>
              </div>
            ) : (
              <span className="text-gray-500 text-sm italic">Unassigned</span>
            )}
          </div>
        </td>

        {/* Type */}
        <td className="py-6 px-6 border-r border-gray-700/30">
          <div className="text-center">
            <span className="px-3 py-1 bg-gradient-to-r from-indigo-600/20 to-indigo-700/20 text-indigo-400 text-sm rounded-lg border border-indigo-500/30">
              {incident.type || 'Security Incident'}
            </span>
          </div>
        </td>

        {/* Actions */}
        <td className="py-6 px-6">
          <div className="flex items-center justify-center space-x-2">
            <button
              onClick={onView}
              className="flex items-center px-3 py-2 bg-blue-600/20 text-blue-400 rounded-lg hover:bg-blue-600/30 transition-all duration-200 text-sm border border-blue-500/30 hover:scale-105"
              title="View Details"
            >
              <Eye size={14} className="mr-1" />
              View
            </button>

            {canEdit && (
              <button
                onClick={onEdit}
                className="flex items-center px-3 py-2 bg-purple-600/20 text-purple-400 rounded-lg hover:bg-purple-600/30 transition-all duration-200 text-sm border border-purple-500/30 hover:scale-105"
                title="Edit Incident"
              >
                <Edit size={14} className="mr-1" />
                Edit
              </button>
            )}

            {canAssign && (
              <button
                onClick={onAssign}
                className="flex items-center px-3 py-2 bg-green-600/20 text-green-400 rounded-lg hover:bg-green-600/30 transition-all duration-200 text-sm border border-green-500/30 hover:scale-105"
                title="Assign Incident"
              >
                <UserPlus size={14} className="mr-1" />
                Assign
              </button>
            )}

            {canClose && incident.status !== 'closed' && (
              <button
                onClick={onClose}
                className="flex items-center px-3 py-2 bg-red-600/20 text-red-400 rounded-lg hover:bg-red-600/30 transition-all duration-200 text-sm border border-red-500/30 hover:scale-105"
                title="Close Incident"
              >
                <XCircle size={14} className="mr-1" />
                Close
              </button>
            )}
          </div>
        </td>
      </tr>
    </UrgentBadge>
  );
};

// Enhanced table header component
export const EnhancedIncidentTableHeader: React.FC = () => {
  return (
    <thead className="bg-gradient-to-r from-gray-800/80 to-gray-900/80 backdrop-blur-sm">
      <tr>
        <th className="text-left py-4 px-6 text-gray-300 font-semibold border-r border-gray-700/30">
          <div className="flex items-center">
            <AlertTriangle size={16} className="mr-2" />
            Incident Details
          </div>
        </th>
        <th className="text-center py-4 px-6 text-gray-300 font-semibold border-r border-gray-700/30">
          Severity
        </th>
        <th className="text-center py-4 px-6 text-gray-300 font-semibold border-r border-gray-700/30">
          Status
        </th>
        <th className="text-center py-4 px-6 text-gray-300 font-semibold border-r border-gray-700/30">
          <div className="flex items-center justify-center">
            <Clock size={16} className="mr-2" />
            Created
          </div>
        </th>
        <th className="text-center py-4 px-6 text-gray-300 font-semibold border-r border-gray-700/30">
          <div className="flex items-center justify-center">
            <User size={16} className="mr-2" />
            Assigned To
          </div>
        </th>
        <th className="text-center py-4 px-6 text-gray-300 font-semibold border-r border-gray-700/30">
          Type
        </th>
        <th className="text-center py-4 px-6 text-gray-300 font-semibold">Actions</th>
      </tr>
    </thead>
  );
};
