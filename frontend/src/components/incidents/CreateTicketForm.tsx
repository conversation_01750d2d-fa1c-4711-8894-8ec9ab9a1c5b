import { useState, useEffect } from 'react';
import { X, AlertTriangle, Save, Loader } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { createTicket, CreateTicketRequest } from '../../services/incidentService';
import { getToken } from '../../utils/auth';
import FileUpload from './FileUpload';
import { getCategoriesList, getSubcategoriesForCategory } from '../../config/categories';

interface CreateTicketFormProps {
  isOpen: boolean;
  onClose: () => void;
  onTicketCreated: () => void;
}

interface ITSMTicketData {
  short_description: string;
  description: string;
  user_id: string;
  category: string;
  subcategory: string;
  impact: 'High' | 'Medium' | 'Low';
  urgency: 'High' | 'Medium' | 'Low';
  assignment_group: string;
  assigned_to: string;
  configuration_item: string;
  contact_type: string;
  location: string;
}

export default function CreateTicketForm({
  isOpen,
  onClose,
  onTicketCreated,
}: CreateTicketFormProps) {
  const { user } = useAuth();
  const [formData, setFormData] = useState<ITSMTicketData>({
    short_description: '',
    description: '',
    user_id: user?.id || '',
    category: getCategoriesList()[0] || 'Database',
    subcategory: '',
    impact: 'Medium',
    urgency: 'Medium',
    assignment_group: '',
    assigned_to: '',
    configuration_item: '',
    contact_type: 'Portal',
    location: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [attachments, setAttachments] = useState<File[]>([]);

  // Calculate priority based on impact and urgency
  const calculatePriority = (impact: string, urgency: string): number => {
    const matrix: { [key: string]: number } = {
      'High-High': 1,
      'High-Medium': 2,
      'High-Low': 3,
      'Medium-High': 2,
      'Medium-Medium': 3,
      'Medium-Low': 4,
      'Low-High': 3,
      'Low-Medium': 4,
      'Low-Low': 5,
    };
    return matrix[`${impact}-${urgency}`] || 4;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.short_description.trim() || !formData.description.trim()) {
      setError('Short description and description are required');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Create FormData for file upload
      const formDataToSend = new FormData();

      // Add ticket data
      Object.entries(formData).forEach(([key, value]) => {
        formDataToSend.append(key, value);
      });

      // Add backward compatibility fields
      formDataToSend.append('title', formData.short_description);
      formDataToSend.append('severity', formData.impact.toLowerCase());

      // Add attachments
      attachments.forEach((file) => {
        formDataToSend.append('attachments', file);
      });

      // Get token properly
      const token = getToken();
      if (!token) {
        throw new Error('No authentication token found');
      }

      // Send request with FormData
      const response = await fetch('http://localhost:5000/api/incident/tickets', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: formDataToSend,
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to create ticket');
      }

      onTicketCreated();
      onClose();
      // Reset form
      setFormData({
        short_description: '',
        description: '',
        user_id: user?.id || '',
        category: getCategoriesList()[0] || 'Database',
        subcategory: '',
        impact: 'Medium',
        urgency: 'Medium',
        assignment_group: '',
        assigned_to: '',
        configuration_item: '',
        contact_type: 'Portal',
        location: '',
      });
      setAttachments([]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create ticket');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof ITSMTicketData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (error) setError(null);
  };

  const handleCategoryChange = (category: string) => {
    setFormData((prev) => ({
      ...prev,
      category,
      subcategory: '', // Reset subcategory when category changes
    }));
    if (error) setError(null);
  };

  // Get available subcategories for current category
  const availableSubcategories = getSubcategoriesForCategory(formData.category);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 border border-gray-700 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center">
            <AlertTriangle className="text-purple-400 mr-3" size={24} />
            <h2 className="text-xl font-semibold text-white">Create Investigation Ticket</h2>
          </div>
          <button onClick={onClose} className="text-gray-400 hover:text-white transition-colors">
            <X size={24} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {error && (
            <div className="bg-red-900/50 border border-red-500 rounded-lg p-4">
              <p className="text-red-200 text-sm">{error}</p>
            </div>
          )}

          {/* Short Description */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Short Description <span className="text-red-400">*</span>
            </label>
            <input
              type="text"
              value={formData.short_description}
              onChange={(e) => handleInputChange('short_description', e.target.value)}
              className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              placeholder="Brief summary of the incident"
              required
            />
          </div>

          {/* Category and Subcategory */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Category</label>
              <select
                value={formData.category}
                onChange={(e) => handleCategoryChange(e.target.value)}
                className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                {getCategoriesList().map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Subcategory</label>
              <select
                value={formData.subcategory}
                onChange={(e) => handleInputChange('subcategory', e.target.value)}
                className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="">Select a subcategory...</option>
                {availableSubcategories.map((subcategory) => (
                  <option key={subcategory} value={subcategory}>
                    {subcategory}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Impact and Urgency */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Impact</label>
              <select
                value={formData.impact}
                onChange={(e) => handleInputChange('impact', e.target.value as any)}
                className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="High">High - Affects many users</option>
                <option value="Medium">Medium - Affects some users</option>
                <option value="Low">Low - Affects few users</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Urgency</label>
              <select
                value={formData.urgency}
                onChange={(e) => handleInputChange('urgency', e.target.value as any)}
                className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="High">High - Immediate attention needed</option>
                <option value="Medium">Medium - Normal timeline</option>
                <option value="Low">Low - Can wait</option>
              </select>
            </div>
          </div>

          {/* Priority Display */}
          <div className="bg-purple-900/30 border border-purple-500 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-300">Calculated Priority:</span>
              <span className="text-white font-semibold">
                P{calculatePriority(formData.impact, formData.urgency)} -
                {calculatePriority(formData.impact, formData.urgency) === 1
                  ? ' Critical'
                  : calculatePriority(formData.impact, formData.urgency) === 2
                    ? ' High'
                    : calculatePriority(formData.impact, formData.urgency) === 3
                      ? ' Medium'
                      : calculatePriority(formData.impact, formData.urgency) === 4
                        ? ' Low'
                        : ' Planning'}
              </span>
            </div>
          </div>

          {/* Assignment and Configuration */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Assignment Group
              </label>
              <input
                type="text"
                value={formData.assignment_group}
                onChange={(e) => handleInputChange('assignment_group', e.target.value)}
                className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="e.g., IT Support, Security Team"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Configuration Item
              </label>
              <input
                type="text"
                value={formData.configuration_item}
                onChange={(e) => handleInputChange('configuration_item', e.target.value)}
                className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="Affected system or component"
              />
            </div>
          </div>

          {/* Contact Type and Location */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Contact Type</label>
              <select
                value={formData.contact_type}
                onChange={(e) => handleInputChange('contact_type', e.target.value)}
                className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="Portal">Portal</option>
                <option value="Phone">Phone</option>
                <option value="Email">Email</option>
                <option value="Walk-in">Walk-in</option>
                <option value="Chat">Chat</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Location</label>
              <input
                type="text"
                value={formData.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="Physical location if relevant"
              />
            </div>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Detailed Description <span className="text-red-400">*</span>
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={6}
              className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
              placeholder="Provide detailed information about the incident:
- What happened and when
- Steps to reproduce (if applicable)
- Error messages or symptoms
- Business impact
- Any troubleshooting already performed"
              required
            />
          </div>

          {/* File Attachments */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Attachments (Optional)
            </label>
            <FileUpload
              onFilesChange={setAttachments}
              maxFiles={5}
              maxFileSize={16}
              disabled={isSubmitting}
            />
          </div>

          {/* Additional Information */}
          <div className="bg-gray-700/30 border border-gray-600 rounded-lg p-4">
            <h3 className="text-sm font-medium text-gray-300 mb-2">Additional Information</h3>
            <ul className="text-sm text-gray-400 space-y-1">
              <li>• This ticket will be visible to all administrators</li>
              <li>• You will receive notifications about status updates</li>
              <li>• Administrators can assign this ticket to specific team members</li>
              <li>• High and critical severity tickets will trigger immediate notifications</li>
              <li>• You can attach files (screenshots, logs, documents) up to 16MB each</li>
            </ul>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-4 pt-4 border-t border-gray-700">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-3 text-gray-400 hover:text-white transition-colors"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={
                isSubmitting || !formData.short_description.trim() || !formData.description.trim()
              }
              className="flex items-center px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isSubmitting ? (
                <>
                  <Loader className="animate-spin mr-2" size={20} />
                  Creating...
                </>
              ) : (
                <>
                  <Save className="mr-2" size={20} />
                  Create Ticket
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
