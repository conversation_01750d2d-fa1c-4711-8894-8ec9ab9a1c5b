import { useState, useEffect } from 'react';
import { X, AlertTriangle, Clock, User, ArrowRight, Edit, Save, Loader } from 'lucide-react';
import { useRole } from '../../hooks/useRole';
import {
  InvestigationTicket,
  updateTicket,
  assignTicket,
  convertTicketToIncident,
  getSeverityColor,
  getStatusColor,
  getPriorityColor,
  getPriorityLabel,
  formatDate,
} from '../../services/incidentService';
import AttachmentViewer from './AttachmentViewer';
import UserSelector from './UserSelector';
import { getCategoriesList, getSubcategoriesForCategory } from '../../config/categories';

interface TicketDetailsModalProps {
  ticket: InvestigationTicket | null;
  isOpen: boolean;
  onClose: () => void;
  onTicketUpdated: () => void;
  initialMode?: 'view' | 'edit' | 'assign' | 'convert';
  currentUserId?: string;
  users?: { [key: string]: any };
}

export default function TicketDetailsModal({
  ticket,
  isOpen,
  onClose,
  onTicketUpdated,
  initialMode = 'view',
  currentUserId,
  users = {},
}: TicketDetailsModalProps) {
  const { isAdmin } = useRole();

  // Check if user can edit this ticket (admin or ticket owner)
  const canEdit = isAdmin || (ticket && currentUserId && ticket.user_id === currentUserId);
  const [isEditing, setIsEditing] = useState(false);
  const [isAssigning, setIsAssigning] = useState(false);
  const [isConverting, setIsConverting] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [editData, setEditData] = useState({
    description: '',
    severity: 'medium' as const,
    status: 'New' as const,
    impact: 'Medium' as const,
    urgency: 'Medium' as const,
    assignment_group: '',
    configuration_item: '',
    location: '',
    category: '',
    subcategory: '',
  });

  const [selectedUser, setSelectedUser] = useState<any>(null);

  // Initialize edit data when ticket changes
  useEffect(() => {
    if (ticket) {
      setEditData({
        description: ticket.description,
        severity: ticket.severity || ticket.impact?.toLowerCase() || 'medium',
        status: ticket.status,
        impact: ticket.impact || 'Medium',
        urgency: ticket.urgency || 'Medium',
        assignment_group: ticket.assignment_group || '',
        configuration_item: ticket.configuration_item || '',
        location: ticket.location || '',
        category: ticket.category || '',
        subcategory: ticket.subcategory || '',
      });
    }
  }, [ticket]);

  // Initialize mode when modal opens
  useEffect(() => {
    if (isOpen && ticket) {
      // Reset all modes first
      setIsEditing(false);
      setIsAssigning(false);
      setIsConverting(false);
      setError(null);

      // Set the initial mode based on permissions
      switch (initialMode) {
        case 'edit':
          if (canEdit) {
            setIsEditing(true);
          }
          break;
        case 'assign':
          if (isAdmin) {
            setIsAssigning(true);
          }
          break;
        case 'convert':
          if (isAdmin) {
            setIsConverting(true);
          }
          break;
        default:
          // 'view' mode - all states remain false
          break;
      }
    }
  }, [isOpen, ticket, initialMode, canEdit, isAdmin]);

  // Function to resolve user email from user_id
  const getUserEmail = (userId: string, userEmail?: string) => {
    if (userEmail) return userEmail;
    if (userId && users[userId]) {
      return users[userId].email || users[userId].username || 'Unknown User';
    }
    return 'System';
  };

  if (!isOpen || !ticket) return null;

  const handleEdit = () => {
    setIsEditing(true);
    // Edit data is already initialized in useEffect
  };

  const handleEditCategoryChange = (category: string) => {
    setEditData((prev) => ({
      ...prev,
      category,
      subcategory: '', // Reset subcategory when category changes
    }));
  };

  const handleSaveEdit = async () => {
    setLoading(true);
    setError(null);

    try {
      await updateTicket(ticket.ticket_number, editData);
      setIsEditing(false);
      onTicketUpdated();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update ticket');
    } finally {
      setLoading(false);
    }
  };

  const handleAssign = async () => {
    if (!selectedUser) {
      setError('Please select a user to assign');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      await assignTicket(ticket.ticket_number, { assigned_to: selectedUser.email });
      setIsAssigning(false);
      setSelectedUser(null);
      onTicketUpdated();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to assign ticket');
    } finally {
      setLoading(false);
    }
  };

  const handleConvert = async () => {
    setLoading(true);
    setError(null);

    try {
      await convertTicketToIncident(ticket.ticket_number);
      setIsConverting(false);
      onTicketUpdated();
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to convert ticket to incident');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 border border-gray-700 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center">
            <AlertTriangle className="text-purple-400 mr-3" size={24} />
            <div>
              <h2 className="text-xl font-semibold text-white">Ticket Details</h2>
              <p className="text-gray-400 text-sm">{ticket.ticket_number}</p>
            </div>
          </div>
          <button onClick={onClose} className="text-gray-400 hover:text-white transition-colors">
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {error && (
            <div className="bg-red-900/50 border border-red-500 rounded-lg p-4">
              <p className="text-red-200 text-sm">{error}</p>
            </div>
          )}

          {/* Status, Priority, and Assignment */}
          <div className="flex items-center flex-wrap gap-3">
            {ticket.priority && (
              <span
                className={`px-3 py-1 rounded-full text-xs font-medium ${getPriorityColor(ticket.priority)}`}
              >
                {getPriorityLabel(ticket.priority)}
              </span>
            )}
            <span
              className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(ticket.status)}`}
            >
              {ticket.status.replace('_', ' ').toUpperCase()}
            </span>
            {ticket.category && (
              <span className="px-3 py-1 bg-gray-700 text-gray-300 text-xs rounded">
                {ticket.category}
              </span>
            )}
            <span className="flex items-center text-sm text-blue-300">
              <User size={16} className="mr-1 text-blue-400" />
              Créé par: {ticket.user_email || 'Utilisateur inconnu'}
            </span>
            {ticket.assigned_to && (
              <span className="flex items-center text-sm text-gray-400">
                <User size={16} className="mr-1" />
                Assigné à: {ticket.assigned_to}
              </span>
            )}
          </div>

          {/* Short Description (Read-only) */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Short Description
              <span className="text-xs text-gray-400 ml-2">
                (Cannot be modified after creation)
              </span>
            </label>
            <p className="text-white text-lg bg-gray-700/20 border border-gray-600 rounded-lg px-4 py-3">
              {ticket.short_description || ticket.title}
            </p>
          </div>

          {/* ITSM Information Grid */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 bg-gray-700/30 border border-gray-600 rounded-lg p-4">
            <div>
              <label className="block text-xs font-medium text-gray-400 mb-1">Category</label>
              <p className="text-white text-sm">{ticket.category || 'Not specified'}</p>
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-400 mb-1">Subcategory</label>
              <p className="text-white text-sm">{ticket.subcategory || 'Not specified'}</p>
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-400 mb-1">Contact Type</label>
              <p className="text-white text-sm">{ticket.contact_type || 'Portal'}</p>
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-400 mb-1">Impact</label>
              <p className="text-white text-sm">{ticket.impact || 'Medium'}</p>
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-400 mb-1">Urgency</label>
              <p className="text-white text-sm">{ticket.urgency || 'Medium'}</p>
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-400 mb-1">Priority</label>
              <p className="text-white text-sm">
                {ticket.priority ? getPriorityLabel(ticket.priority) : 'Not calculated'}
              </p>
            </div>
            {ticket.assignment_group && (
              <div>
                <label className="block text-xs font-medium text-gray-400 mb-1">
                  Assignment Group
                </label>
                <p className="text-white text-sm">{ticket.assignment_group}</p>
              </div>
            )}
            {ticket.configuration_item && (
              <div>
                <label className="block text-xs font-medium text-gray-400 mb-1">
                  Configuration Item
                </label>
                <p className="text-white text-sm">{ticket.configuration_item}</p>
              </div>
            )}
            {ticket.location && (
              <div>
                <label className="block text-xs font-medium text-gray-400 mb-1">Location</label>
                <p className="text-white text-sm">{ticket.location}</p>
              </div>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Description</label>
            {isEditing ? (
              <textarea
                value={editData.description}
                onChange={(e) => setEditData((prev) => ({ ...prev, description: e.target.value }))}
                rows={6}
                className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
              />
            ) : (
              <p className="text-gray-300 whitespace-pre-wrap">{ticket.description}</p>
            )}
          </div>

          {/* Edit Fields (when editing) */}
          {isEditing && (
            <div className="space-y-4 bg-gray-700/20 border border-gray-600 rounded-lg p-4">
              <h3 className="text-white font-medium">Edit Ticket Details</h3>

              {/* Category and Subcategory */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Category</label>
                  <select
                    value={editData.category}
                    onChange={(e) => handleEditCategoryChange(e.target.value)}
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <option value="">Select a category...</option>
                    {getCategoriesList().map((category) => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Subcategory
                  </label>
                  <select
                    value={editData.subcategory}
                    onChange={(e) =>
                      setEditData((prev) => ({ ...prev, subcategory: e.target.value }))
                    }
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                    disabled={!editData.category}
                  >
                    <option value="">Select a subcategory...</option>
                    {editData.category &&
                      getSubcategoriesForCategory(editData.category).map((subcategory) => (
                        <option key={subcategory} value={subcategory}>
                          {subcategory}
                        </option>
                      ))}
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Impact</label>
                  <select
                    value={editData.impact}
                    onChange={(e) =>
                      setEditData((prev) => ({ ...prev, impact: e.target.value as any }))
                    }
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <option value="High">High</option>
                    <option value="Medium">Medium</option>
                    <option value="Low">Low</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Urgency</label>
                  <select
                    value={editData.urgency}
                    onChange={(e) =>
                      setEditData((prev) => ({ ...prev, urgency: e.target.value as any }))
                    }
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <option value="High">High</option>
                    <option value="Medium">Medium</option>
                    <option value="Low">Low</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Status</label>
                  <select
                    value={editData.status}
                    onChange={(e) =>
                      setEditData((prev) => ({ ...prev, status: e.target.value as any }))
                    }
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <option value="New">New</option>
                    <option value="In Progress">In Progress</option>
                    <option value="On Hold">On Hold</option>
                    <option value="Resolved">Resolved</option>
                    <option value="Closed">Closed</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Assignment Group
                  </label>
                  <input
                    type="text"
                    value={editData.assignment_group}
                    onChange={(e) =>
                      setEditData((prev) => ({ ...prev, assignment_group: e.target.value }))
                    }
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="e.g., IT Support, Security Team"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Configuration Item
                  </label>
                  <input
                    type="text"
                    value={editData.configuration_item}
                    onChange={(e) =>
                      setEditData((prev) => ({ ...prev, configuration_item: e.target.value }))
                    }
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="Affected system or component"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Location</label>
                  <input
                    type="text"
                    value={editData.location}
                    onChange={(e) => setEditData((prev) => ({ ...prev, location: e.target.value }))}
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="Physical location if relevant"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Assignment Section (when assigning) */}
          {isAssigning && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Assign to User</label>
              <UserSelector
                onUserSelect={setSelectedUser}
                selectedUserId={selectedUser?._id}
                placeholder="Select a user to assign this ticket"
              />
              {selectedUser && (
                <div className="mt-2 p-3 bg-gray-700/30 border border-gray-600 rounded-lg">
                  <div className="text-sm text-gray-300">
                    <strong>Selected:</strong> {selectedUser.first_name} {selectedUser.last_name}
                  </div>
                  <div className="text-xs text-gray-400">
                    {selectedUser.email} • {selectedUser.role}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Timeline */}
          {ticket.timeline && ticket.timeline.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold text-white mb-4">Timeline</h3>
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {ticket.timeline.map((entry, index) => (
                  <div
                    key={index}
                    className="flex items-start space-x-3 p-3 bg-gray-700/30 rounded-lg"
                  >
                    <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-white font-medium capitalize">
                          {entry.action.replace('_', ' ')}
                          <span className="text-blue-300 text-xs ml-2">
                            by {getUserEmail(entry.user_id, entry.user_email)}
                          </span>
                        </span>
                        <span className="text-gray-400 text-xs">{formatDate(entry.timestamp)}</span>
                      </div>
                      {entry.details && Object.keys(entry.details).length > 0 && (
                        <div className="text-gray-300 text-sm">
                          {entry.details.changes && (
                            <div>
                              {Object.entries(entry.details.changes).map(
                                ([field, change]: [string, any]) => (
                                  <div key={field} className="text-xs">
                                    <span className="capitalize">{field}</span>:
                                    <span className="text-red-300"> {change.from}</span> →
                                    <span className="text-green-300"> {change.to}</span>
                                  </div>
                                )
                              )}
                            </div>
                          )}
                          {entry.details.short_description && (
                            <div className="text-xs">{entry.details.short_description}</div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Attachments */}
          <AttachmentViewer
            attachments={ticket.attachments || []}
            ticketNumber={ticket.ticket_number}
            onAttachmentsUpdated={onTicketUpdated}
            canEdit={isAdmin && !isConverting}
          />

          {/* Metadata */}
          <div className="grid grid-cols-2 gap-4 text-sm text-gray-400">
            <div>
              <span className="flex items-center">
                <Clock size={16} className="mr-1" />
                Created: {formatDate(ticket.created_at)}
              </span>
            </div>
            <div>
              <span className="flex items-center">
                <Clock size={16} className="mr-1" />
                Updated: {formatDate(ticket.updated_at)}
              </span>
            </div>
          </div>

          {/* Convert to Incident Section */}
          {isConverting && ticket.status !== 'converted_to_incident' && (
            <div className="bg-purple-900/30 border border-purple-500 rounded-lg p-4">
              <h3 className="text-white font-medium mb-2">Convert to Incident</h3>
              <p className="text-gray-300 text-sm mb-4">
                This will create a formal incident record and mark this ticket as converted. The
                incident will inherit all information from this ticket and can be managed
                separately.
              </p>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-between items-center p-6 border-t border-gray-700">
          <div className="flex space-x-3">
            {!isEditing && !isAssigning && !isConverting && (
              <>
                {/* Edit button - Available for ticket owner or admin */}
                {canEdit && (
                  <button
                    onClick={handleEdit}
                    className="flex items-center px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
                  >
                    <Edit size={16} className="mr-2" />
                    Edit
                  </button>
                )}

                {/* Admin-only actions */}
                {isAdmin && (
                  <>
                    <button
                      onClick={() => setIsAssigning(true)}
                      className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <User size={16} className="mr-2" />
                      Assign
                    </button>
                    {ticket.status !== 'converted_to_incident' && (
                      <button
                        onClick={() => setIsConverting(true)}
                        className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                      >
                        <ArrowRight size={16} className="mr-2" />
                        Convert to Incident
                      </button>
                    )}
                  </>
                )}
              </>
            )}
          </div>

          <div className="flex space-x-3">
            {(isEditing || isAssigning || isConverting) && (
              <button
                onClick={() => {
                  setIsEditing(false);
                  setIsAssigning(false);
                  setIsConverting(false);
                  setError(null);
                }}
                className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
                disabled={loading}
              >
                Cancel
              </button>
            )}

            {isEditing && (
              <button
                onClick={handleSaveEdit}
                disabled={loading}
                className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 transition-colors"
              >
                {loading ? (
                  <Loader className="animate-spin mr-2" size={16} />
                ) : (
                  <Save className="mr-2" size={16} />
                )}
                Save Changes
              </button>
            )}

            {isAssigning && (
              <button
                onClick={handleAssign}
                disabled={loading || !selectedUser}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
              >
                {loading ? (
                  <Loader className="animate-spin mr-2" size={16} />
                ) : (
                  <User className="mr-2" size={16} />
                )}
                Assign Ticket
              </button>
            )}

            {isConverting && (
              <button
                onClick={handleConvert}
                disabled={loading}
                className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 transition-colors"
              >
                {loading ? (
                  <Loader className="animate-spin mr-2" size={16} />
                ) : (
                  <ArrowRight className="mr-2" size={16} />
                )}
                Convert to Incident
              </button>
            )}

            {!isEditing && !isAssigning && !isConverting && (
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
              >
                Close
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
