import React from 'react';
import {
  Clock,
  User,
  Eye,
  Edit,
  UserPlus,
  ArrowRight,
  Calendar,
  AlertTriangle,
  RotateCcw,
  XCircle,
  RefreshCw,
  Trash2,
} from 'lucide-react';
import { InvestigationTicket } from '../../services/incidentService';
import {
  SeverityBadge,
  StatusBadge,
  PriorityBadge,
  UrgentBadge,
  TicketBadgeGroup,
  CompactPrioritySeverityBadge,
} from './InteractiveBadges';
import { getTimeUrgencyColor, formatDate } from '../../services/incidentService';
import { TimeIndicator, UrgencyAlert, ActivityPulse } from './AdvancedIndicators';

interface EnhancedTicketCardProps {
  ticket: InvestigationTicket;
  createdBy: string;
  onView: () => void;
  onEdit: () => void;
  onAssign: () => void;
  onConvert?: () => void;
  onClose?: () => void;
  onReopen?: () => void;
  onDelete?: () => void;
  canEdit: boolean;
  canAssign: boolean;
  canConvert: boolean;
  canClose: boolean;
  canReopen: boolean;
  canDelete: boolean;
}

export const EnhancedTicketCard: React.FC<EnhancedTicketCardProps> = ({
  ticket,
  createdBy,
  onView,
  onEdit,
  onAssign,
  onConvert,
  onClose,
  onReopen,
  onDelete,
  canEdit,
  canAssign,
  canConvert,
  canClose,
  canReopen,
  canDelete,
}) => {
  const title = ticket.short_description || ticket.title || 'No title';
  const severity = ticket.severity || ticket.impact?.toLowerCase();
  const timeElapsed = Math.floor(
    (new Date().getTime() - new Date(ticket.created_at).getTime()) / (1000 * 60 * 60)
  );
  const daysSinceCreation = Math.floor(timeElapsed / 24);

  // Determine urgency levels
  const isExtremelyOverdue =
    daysSinceCreation > 10 &&
    (ticket.status === 'New' || ticket.status === 'In Progress' || ticket.status === 'On Hold');
  const isUrgent = severity === 'critical' || ticket.priority === 1;
  const timeUrgencyClass = getTimeUrgencyColor(ticket.created_at);

  // Check if ticket is closed
  const isClosed = ticket.status === 'Closed' || ticket.status === 'Resolved';

  const timeSinceCreation = () => {
    if (timeElapsed < 1) return 'Just created';
    if (timeElapsed < 24) return `${timeElapsed}h ago`;
    return `${daysSinceCreation}d ago`;
  };

  return (
    <UrgentBadge isUrgent={isUrgent} isExtremelyOverdue={isExtremelyOverdue}>
      <div
        className={`bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 hover:border-purple-500/50 transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-2xl hover:shadow-purple-500/10 ${timeUrgencyClass}`}
      >
        {/* Urgency Alert */}
        <UrgencyAlert
          severity={severity || 'medium'}
          timeElapsed={timeElapsed}
          status={ticket.status}
        />

        {/* Header Section */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-3 mb-2">
              <h3 className="text-lg font-semibold text-white truncate">{title}</h3>
              {ticket.category && (
                <span className="px-3 py-1 bg-gradient-to-r from-gray-700 to-gray-800 text-gray-300 text-xs rounded-lg border border-gray-600/30 flex-shrink-0">
                  {ticket.category}
                </span>
              )}
              <ActivityPulse
                isActive={ticket.status === 'In Progress' || ticket.status === 'New'}
                color={severity === 'critical' ? 'red' : 'blue'}
                size="sm"
              />
            </div>
            <div className="flex items-center gap-2 mb-2">
              <p className="text-gray-400 text-sm font-mono">{ticket.ticket_number}</p>
              <span className="text-gray-500 text-xs">•</span>
              <TimeIndicator
                createdAt={ticket.created_at}
                status={ticket.status}
                showDetails={false}
              />
            </div>

            <p className="text-gray-300 text-sm line-clamp-2 mb-3">{ticket.description}</p>

            {/* Converted to Incident Link */}
            {ticket.status === 'converted_to_incident' && ticket.converted_incident_id && (
              <div className="flex items-center gap-2 p-2 bg-violet-900/30 border border-violet-500/50 rounded-lg">
                <RotateCcw size={14} className="text-violet-400" />
                <span className="text-violet-300 text-xs">
                  Converted into incident:
                  <span className="font-mono ml-1 text-violet-200">
                    {ticket.converted_incident_id}
                  </span>
                </span>
              </div>
            )}
          </div>

          {/* Status Badges - Priorité et Sévérité sur même ligne */}
          <div className="flex flex-col items-end space-y-2 ml-4 flex-shrink-0">
            {/* Priorité et Sévérité sur la même ligne */}
            <div className="flex items-center space-x-1">
              {severity && <SeverityBadge severity={severity} size="sm" />}
              {ticket.priority && <PriorityBadge priority={ticket.priority} size="sm" />}
            </div>
            {/* Statut séparé pour plus de clarté */}
            <StatusBadge status={ticket.status} size="sm" />
          </div>
        </div>

        {/* Metadata Section */}
        <div className="flex items-center justify-between mb-4 text-sm text-gray-400">
          <div className="flex items-center space-x-4">
            <span className="flex items-center text-blue-300">
              <User size={14} className="mr-1 text-blue-400" />
              {ticket.user_email || createdBy || 'Utilisateur inconnu'}
            </span>
            {ticket.assigned_to && (
              <span className="flex items-center text-purple-400">
                <UserPlus size={14} className="mr-1" />
                {ticket.assigned_to}
              </span>
            )}
          </div>
          <div className="flex items-center text-xs text-gray-500">
            <Clock size={12} className="mr-1" />
            {formatDate(ticket.created_at)}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between">
          <div className="flex space-x-2">
            <button
              onClick={onView}
              className="flex items-center px-3 py-2 bg-blue-600/20 text-blue-400 rounded-lg hover:bg-blue-600/30 transition-colors text-sm border border-blue-500/30"
            >
              <Eye size={14} className="mr-1" />
              View
            </button>

            {/* Show Edit/Assign buttons only if ticket is not closed */}
            {!isClosed && canEdit && (
              <button
                onClick={onEdit}
                className="flex items-center px-3 py-2 bg-purple-600/20 text-purple-400 rounded-lg hover:bg-purple-600/30 transition-colors text-sm border border-purple-500/30"
              >
                <Edit size={14} className="mr-1" />
                Edit
              </button>
            )}
            {!isClosed && canAssign && (
              <button
                onClick={onAssign}
                className="flex items-center px-3 py-2 bg-green-600/20 text-green-400 rounded-lg hover:bg-green-600/30 transition-colors text-sm border border-green-500/30"
              >
                <UserPlus size={14} className="mr-1" />
                Assign
              </button>
            )}

            {/* Show Reopen button if ticket is closed */}
            {isClosed && canReopen && onReopen && (
              <button
                onClick={onReopen}
                className="flex items-center px-3 py-2 bg-green-600/20 text-green-400 rounded-lg hover:bg-green-600/30 transition-colors text-sm border border-green-500/30"
              >
                <RefreshCw size={14} className="mr-1" />
                Reopen
              </button>
            )}
          </div>

          <div className="flex space-x-2">
            {/* Show Convert button only if ticket is not closed */}
            {!isClosed && canConvert && onConvert && (
              <button
                onClick={onConvert}
                className="flex items-center px-3 py-2 bg-red-600/20 text-red-400 rounded-lg hover:bg-red-600/30 transition-colors text-sm border border-red-500/30"
              >
                <ArrowRight size={14} className="mr-1" />
                Convert
              </button>
            )}

            {/* Show Close button only if ticket is not closed */}
            {!isClosed && canClose && onClose && (
              <button
                onClick={onClose}
                className="flex items-center px-3 py-2 bg-gray-600/20 text-gray-400 rounded-lg hover:bg-gray-600/30 transition-colors text-sm border border-gray-500/30"
              >
                <XCircle size={14} className="mr-1" />
                Close
              </button>
            )}

            {/* Show Delete button - only for admins */}
            {canDelete && onDelete && (
              <button
                onClick={onDelete}
                className="flex items-center px-3 py-2 bg-orange-600/20 text-orange-400 rounded-lg hover:bg-orange-600/30 transition-colors text-sm border border-orange-500/30"
              >
                <Trash2 size={14} className="mr-1" />
                Delete
              </button>
            )}
          </div>
        </div>

        {/* Urgency Indicator */}
        {isUrgent && (
          <div className="mt-3 flex items-center justify-center">
            <div className="flex items-center px-3 py-1 bg-red-600/20 text-red-400 rounded-full text-xs border border-red-500/30 animate-pulse">
              <AlertTriangle size={12} className="mr-1" />
              URGENT - Requires immediate attention
            </div>
          </div>
        )}
      </div>
    </UrgentBadge>
  );
};
