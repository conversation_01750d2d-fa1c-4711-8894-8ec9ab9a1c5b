import { useState, useEffect } from 'react';
import { Search, User, Mail, Shield, CheckCircle, XCircle } from 'lucide-react';
import { getToken } from '../../utils/auth';

interface User {
  _id: string;
  first_name: string;
  last_name: string;
  username: string;
  email: string;
  role: 'admin' | 'user';
  active: boolean;
  banned: boolean;
  email_verified: boolean;
  created_at: string;
}

interface UserSelectorProps {
  onUserSelect: (user: User) => void;
  selectedUserId?: string;
  placeholder?: string;
  showEmailOnly?: boolean;
}

export default function UserSelector({
  onUserSelect,
  selectedUserId,
  placeholder = 'Select a user',
  showEmailOnly = false,
}: UserSelectorProps) {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const token = getToken();
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch('http://localhost:5000/admin/users', {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }

      const userData = await response.json();
      // Filter out banned users and sort by name
      const activeUsers = userData
        .filter((user: User) => !user.banned && user.active && user.email_verified)
        .sort((a: User, b: User) =>
          `${a.first_name} ${a.last_name}`.localeCompare(`${b.first_name} ${b.last_name}`)
        );

      setUsers(activeUsers);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const filteredUsers = users.filter((user) => {
    const searchLower = searchTerm.toLowerCase();
    return (
      user.first_name.toLowerCase().includes(searchLower) ||
      user.last_name.toLowerCase().includes(searchLower) ||
      user.username.toLowerCase().includes(searchLower) ||
      user.email.toLowerCase().includes(searchLower)
    );
  });

  const selectedUser = users.find((user) => user._id === selectedUserId);

  const handleUserSelect = (user: User) => {
    onUserSelect(user);
    setIsOpen(false);
    setSearchTerm('');
  };

  const getUserDisplayName = (user: User) => {
    if (showEmailOnly) {
      return user.email;
    }
    return `${user.first_name} ${user.last_name} (${user.username})`;
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'text-purple-400';
      case 'user':
        return 'text-blue-400';
      default:
        return 'text-gray-400';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Shield size={14} />;
      case 'user':
        return <User size={14} />;
      default:
        return <User size={14} />;
    }
  };

  if (loading) {
    return (
      <div className="relative">
        <div className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-gray-400">
          Loading users...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="relative">
        <div className="w-full px-4 py-3 bg-red-900/50 border border-red-500 rounded-lg text-red-200">
          Error: {error}
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      {/* Selected User Display / Dropdown Trigger */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white text-left hover:border-purple-500 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors"
      >
        {selectedUser ? (
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className={`flex items-center ${getRoleColor(selectedUser.role)}`}>
                {getRoleIcon(selectedUser.role)}
              </div>
              <span>{getUserDisplayName(selectedUser)}</span>
              {selectedUser.email_verified && <CheckCircle size={14} className="text-green-400" />}
            </div>
            <span className="text-gray-400">▼</span>
          </div>
        ) : (
          <div className="flex items-center justify-between">
            <span className="text-gray-400">{placeholder}</span>
            <span className="text-gray-400">▼</span>
          </div>
        )}
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-gray-800 border border-gray-600 rounded-lg shadow-xl z-50 max-h-80 overflow-hidden">
          {/* Search */}
          <div className="p-3 border-b border-gray-600">
            <div className="relative">
              <Search
                size={16}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search users..."
                className="w-full pl-10 pr-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                autoFocus
              />
            </div>
          </div>

          {/* User List */}
          <div className="max-h-60 overflow-y-auto">
            {filteredUsers.length === 0 ? (
              <div className="p-4 text-center text-gray-400">
                {searchTerm ? 'No users found matching your search' : 'No users available'}
              </div>
            ) : (
              filteredUsers.map((user) => (
                <button
                  key={user._id}
                  onClick={() => handleUserSelect(user)}
                  className={`w-full p-3 text-left hover:bg-gray-700/50 transition-colors border-b border-gray-700/50 last:border-b-0 ${
                    selectedUserId === user._id ? 'bg-purple-900/30' : ''
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`flex items-center ${getRoleColor(user.role)}`}>
                        {getRoleIcon(user.role)}
                      </div>
                      <div>
                        <div className="text-white font-medium">
                          {user.first_name} {user.last_name}
                        </div>
                        <div className="text-gray-400 text-sm">@{user.username}</div>
                        {!showEmailOnly && (
                          <div className="flex items-center space-x-2 text-xs text-gray-500">
                            <Mail size={12} />
                            <span>{user.email}</span>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {user.email_verified && (
                        <CheckCircle size={14} className="text-green-400" title="Email Verified" />
                      )}
                      <span
                        className={`text-xs px-2 py-1 rounded ${
                          user.role === 'admin'
                            ? 'bg-purple-900/50 text-purple-300'
                            : 'bg-blue-900/50 text-blue-300'
                        }`}
                      >
                        {user.role}
                      </span>
                    </div>
                  </div>
                </button>
              ))
            )}
          </div>

          {/* Footer */}
          <div className="p-2 border-t border-gray-600 bg-gray-800/50">
            <div className="text-xs text-gray-400 text-center">
              {filteredUsers.length} user{filteredUsers.length !== 1 ? 's' : ''} available
            </div>
          </div>
        </div>
      )}

      {/* Overlay to close dropdown */}
      {isOpen && <div className="fixed inset-0 z-40" onClick={() => setIsOpen(false)} />}
    </div>
  );
}
