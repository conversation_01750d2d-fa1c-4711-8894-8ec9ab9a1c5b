import React, { useState, useEffect } from 'react';
import { X, AlertTriangle, Clock, User, Edit, Save, Loader, Plus, Trash2 } from 'lucide-react';
import { Incident, formatDate } from '../../services/incidentService';
import AttachmentViewer from './AttachmentViewer';

interface IncidentDetailsModalProps {
  incident: Incident;
  isOpen: boolean;
  onClose: () => void;
  onIncidentUpdated?: () => void;
  canEdit?: boolean;
  users?: { [key: string]: any };
  onViewTicket?: (ticketNumber: string) => void;
}

export const IncidentDetailsModal: React.FC<IncidentDetailsModalProps> = ({
  incident,
  isOpen,
  onClose,
  onIncidentUpdated,
  canEdit = true,
  users = {},
  onViewTicket,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Edit state for incident fields
  const [editData, setEditData] = useState({
    title: '',
    description: '',
    severity: 'medium' as 'critical' | 'high' | 'medium' | 'low',
    status: 'open' as 'open' | 'in_progress' | 'resolved' | 'closed',
    impact_assessment: '',
    business_impact: 'medium' as 'critical' | 'high' | 'medium' | 'low' | 'none',
    root_cause: '',
    mitigation_steps: [] as string[],
    next_steps: [] as string[],
    linked_assets: [] as string[],
    assigned_to: '',
    assigned_team: '',
    references: [] as { type: string; value: string; description?: string }[],
  });

  // Initialize edit data when incident changes
  useEffect(() => {
    if (incident) {
      setEditData({
        title: incident.title || '',
        description: incident.description || '',
        severity: incident.severity || 'medium',
        status: incident.status || 'open',
        impact_assessment: incident.impact_assessment || '',
        business_impact: incident.business_impact || 'medium',
        root_cause: incident.root_cause || '',
        mitigation_steps: incident.mitigation_steps || [],
        next_steps: incident.next_steps || [],
        linked_assets: incident.linked_assets || [],
        assigned_to: incident.assigned_to || '',
        assigned_team: incident.assigned_team || '',
        references: incident.references || [],
      });
    }
  }, [incident]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSaveEdit = async () => {
    setLoading(true);
    setError(null);

    try {
      // TODO: Implement updateIncident API call
      console.log('Saving incident:', editData);
      setIsEditing(false);
      if (onIncidentUpdated) {
        onIncidentUpdated();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update incident');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setError(null);
    // Reset edit data to original values
    if (incident) {
      setEditData({
        title: incident.title || '',
        description: incident.description || '',
        severity: incident.severity || 'medium',
        status: incident.status || 'open',
        impact_assessment: incident.impact_assessment || '',
        business_impact: incident.business_impact || 'medium',
        root_cause: incident.root_cause || '',
        mitigation_steps: incident.mitigation_steps || [],
        next_steps: incident.next_steps || [],
        linked_assets: incident.linked_assets || [],
        assigned_to: incident.assigned_to || '',
        assigned_team: incident.assigned_team || '',
        references: incident.references || [],
      });
    }
  };

  // Helper functions for managing arrays
  const addToArray = (
    field: 'mitigation_steps' | 'next_steps' | 'linked_assets',
    value: string
  ) => {
    if (value.trim()) {
      setEditData((prev) => ({
        ...prev,
        [field]: [...prev[field], value.trim()],
      }));
    }
  };

  const removeFromArray = (
    field: 'mitigation_steps' | 'next_steps' | 'linked_assets',
    index: number
  ) => {
    setEditData((prev) => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index),
    }));
  };

  const addReference = (type: string, value: string, description?: string) => {
    if (value.trim()) {
      setEditData((prev) => ({
        ...prev,
        references: [...prev.references, { type, value: value.trim(), description }],
      }));
    }
  };

  const removeReference = (index: number) => {
    setEditData((prev) => ({
      ...prev,
      references: prev.references.filter((_, i) => i !== index),
    }));
  };

  // Function to resolve user email from user_id
  const getUserEmail = (userId: string, userEmail?: string) => {
    if (userEmail) return userEmail;
    if (userId && users[userId]) {
      return users[userId].email || users[userId].username || 'Unknown User';
    }
    return 'System';
  };

  if (!isOpen) return null;

  // Helper functions for display
  const getSeverityDisplay = (severity: string) => {
    const severityMap = {
      critical: { text: 'Critical' },
      high: { text: 'High' },
      medium: { text: 'Medium' },
      low: { text: 'Low' },
    };
    return severityMap[severity as keyof typeof severityMap] || { text: 'Unknown' };
  };

  const getStatusDisplay = (status: string) => {
    const statusMap = {
      open: 'Open',
      in_progress: 'In Progress',
      resolved: 'Resolved',
      closed: 'Closed',
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  const getIncidentType = () => {
    if (incident.escalated) return 'Escalated Incident';
    if (incident.false_positive) return 'False Positive';
    return 'Security Incident';
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 border border-gray-700 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header - EXACT same as TicketDetailsModal */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center">
            <AlertTriangle className="text-red-400 mr-3" size={24} />
            <div>
              <h2 className="text-xl font-semibold text-white">Incident Details</h2>
              <p className="text-gray-400 text-sm">#{incident.incident_id}</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            {canEdit && !isEditing && (
              <button
                onClick={handleEdit}
                className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                <Edit size={16} className="mr-2" />
                Edit
              </button>
            )}
            <button onClick={onClose} className="text-gray-400 hover:text-white transition-colors">
              <X size={24} />
            </button>
          </div>
        </div>

        {/* Content - EXACT same structure as TicketDetailsModal */}
        <div className="p-6 space-y-6">
          {/* Status, Priority, and Assignment - EXACT same as TicketDetailsModal */}
          <div className="flex items-center flex-wrap gap-3">
            <span
              className={`px-3 py-1 rounded-full text-xs font-medium ${
                incident.severity === 'critical'
                  ? 'bg-red-900 text-red-200'
                  : incident.severity === 'high'
                    ? 'bg-orange-900 text-orange-200'
                    : incident.severity === 'medium'
                      ? 'bg-yellow-900 text-yellow-200'
                      : 'bg-green-900 text-green-200'
              }`}
            >
              {getSeverityDisplay(incident.severity).text}
            </span>
            <span
              className={`px-3 py-1 rounded-full text-xs font-medium ${
                incident.status === 'open'
                  ? 'bg-green-900 text-green-200'
                  : incident.status === 'in_progress'
                    ? 'bg-blue-900 text-blue-200'
                    : incident.status === 'resolved'
                      ? 'bg-purple-900 text-purple-200'
                      : 'bg-gray-900 text-gray-200'
              }`}
            >
              {getStatusDisplay(incident.status).replace('_', ' ').toUpperCase()}
            </span>
            <span className="px-3 py-1 bg-gray-700 text-gray-300 text-xs rounded">
              {getIncidentType()}
            </span>
            <span className="flex items-center text-sm text-blue-300">
              <User size={16} className="mr-1 text-blue-400" />
              Created by: {incident.user_email || incident.user_id || 'Unknown'}
            </span>
            {incident.assigned_to && (
              <span className="flex items-center text-sm text-gray-400">
                <User size={16} className="mr-1" />
                Assigned to: {incident.assigned_to}
              </span>
            )}
          </div>

          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Title</label>
            {isEditing ? (
              <input
                type="text"
                value={editData.title}
                onChange={(e) => setEditData((prev) => ({ ...prev, title: e.target.value }))}
                className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="Incident title"
              />
            ) : (
              <p className="text-white text-lg bg-gray-700/20 border border-gray-600 rounded-lg px-4 py-3">
                {incident.title}
              </p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Description</label>
            {isEditing ? (
              <textarea
                value={editData.description}
                onChange={(e) => setEditData((prev) => ({ ...prev, description: e.target.value }))}
                rows={4}
                className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
                placeholder="Detailed description of the incident"
              />
            ) : (
              <p className="text-gray-300 whitespace-pre-wrap">{incident.description}</p>
            )}
          </div>

          {/* Impact Assessment */}
          {(isEditing || incident.impact_assessment) && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Impact Assessment
              </label>
              {isEditing ? (
                <textarea
                  value={editData.impact_assessment}
                  onChange={(e) =>
                    setEditData((prev) => ({ ...prev, impact_assessment: e.target.value }))
                  }
                  rows={3}
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
                  placeholder="What systems, users, or processes are affected?"
                />
              ) : (
                <div className="bg-gray-700/20 border border-gray-600 rounded-lg p-4">
                  <p className="text-gray-300 whitespace-pre-wrap">{incident.impact_assessment}</p>
                </div>
              )}
            </div>
          )}

          {/* Business Impact */}
          {(isEditing || incident.business_impact) && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Business Impact
              </label>
              {isEditing ? (
                <select
                  value={editData.business_impact}
                  onChange={(e) =>
                    setEditData((prev) => ({ ...prev, business_impact: e.target.value as any }))
                  }
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option value="none">None</option>
                  <option value="low">Low Business Impact</option>
                  <option value="medium">Medium Business Impact</option>
                  <option value="high">High Business Impact</option>
                  <option value="critical">Critical Business Impact</option>
                </select>
              ) : (
                <span
                  className={`px-3 py-1 rounded-full text-xs font-medium ${
                    incident.business_impact === 'critical'
                      ? 'bg-red-900 text-red-200'
                      : incident.business_impact === 'high'
                        ? 'bg-orange-900 text-orange-200'
                        : incident.business_impact === 'medium'
                          ? 'bg-yellow-900 text-yellow-200'
                          : incident.business_impact === 'low'
                            ? 'bg-blue-900 text-blue-200'
                            : 'bg-gray-900 text-gray-200'
                  }`}
                >
                  {(incident.business_impact || 'none').charAt(0).toUpperCase() +
                    (incident.business_impact || 'none').slice(1)}{' '}
                  Business Impact
                </span>
              )}
            </div>
          )}

          {/* Root Cause */}
          {(isEditing || incident.root_cause) && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Root Cause</label>
              {isEditing ? (
                <textarea
                  value={editData.root_cause}
                  onChange={(e) => setEditData((prev) => ({ ...prev, root_cause: e.target.value }))}
                  rows={3}
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
                  placeholder="What was the underlying cause of this incident?"
                />
              ) : (
                <div className="bg-red-900/20 border border-red-600 rounded-lg p-4">
                  <p className="text-red-200 whitespace-pre-wrap">{incident.root_cause}</p>
                </div>
              )}
            </div>
          )}

          {/* Mitigation Steps */}
          {(isEditing || (incident.mitigation_steps && incident.mitigation_steps.length > 0)) && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Mitigation Steps Taken
              </label>
              {isEditing ? (
                <div className="bg-green-900/20 border border-green-600 rounded-lg p-4">
                  <div className="space-y-2 mb-3">
                    {editData.mitigation_steps.map((step, index) => (
                      <div key={index} className="flex items-start space-x-2">
                        <span className="text-green-400 mt-1">✓</span>
                        <span className="text-green-200 flex-1">{step}</span>
                        <button
                          onClick={() => removeFromArray('mitigation_steps', index)}
                          className="text-red-400 hover:text-red-300"
                        >
                          <Trash2 size={14} />
                        </button>
                      </div>
                    ))}
                  </div>
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      placeholder="Add mitigation step..."
                      className="flex-1 px-3 py-2 bg-gray-700/50 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          addToArray('mitigation_steps', e.currentTarget.value);
                          e.currentTarget.value = '';
                        }
                      }}
                    />
                    <button
                      onClick={(e) => {
                        const input = e.currentTarget.previousElementSibling as HTMLInputElement;
                        addToArray('mitigation_steps', input.value);
                        input.value = '';
                      }}
                      className="px-3 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                    >
                      <Plus size={14} />
                    </button>
                  </div>
                </div>
              ) : (
                <div className="bg-green-900/20 border border-green-600 rounded-lg p-4">
                  <ul className="space-y-2">
                    {incident.mitigation_steps?.map((step, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <span className="text-green-400 mt-1">✓</span>
                        <span className="text-green-200">{step}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}

          {/* Next Steps */}
          {(isEditing || (incident.next_steps && incident.next_steps.length > 0)) && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Next Steps</label>
              {isEditing ? (
                <div className="bg-blue-900/20 border border-blue-600 rounded-lg p-4">
                  <div className="space-y-2 mb-3">
                    {editData.next_steps.map((step, index) => (
                      <div key={index} className="flex items-start space-x-2">
                        <span className="text-blue-400 mt-1">→</span>
                        <span className="text-blue-200 flex-1">{step}</span>
                        <button
                          onClick={() => removeFromArray('next_steps', index)}
                          className="text-red-400 hover:text-red-300"
                        >
                          <Trash2 size={14} />
                        </button>
                      </div>
                    ))}
                  </div>
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      placeholder="Add next step..."
                      className="flex-1 px-3 py-2 bg-gray-700/50 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          addToArray('next_steps', e.currentTarget.value);
                          e.currentTarget.value = '';
                        }
                      }}
                    />
                    <button
                      onClick={(e) => {
                        const input = e.currentTarget.previousElementSibling as HTMLInputElement;
                        addToArray('next_steps', input.value);
                        input.value = '';
                      }}
                      className="px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                    >
                      <Plus size={14} />
                    </button>
                  </div>
                </div>
              ) : (
                <div className="bg-blue-900/20 border border-blue-600 rounded-lg p-4">
                  <ul className="space-y-2">
                    {incident.next_steps?.map((step, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <span className="text-blue-400 mt-1">→</span>
                        <span className="text-blue-200">{step}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}

          {/* Linked Assets/Systems */}
          {incident.linked_assets && incident.linked_assets.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Linked Assets/Systems
              </label>
              <div className="bg-purple-900/20 border border-purple-600 rounded-lg p-4">
                <div className="flex flex-wrap gap-2">
                  {incident.linked_assets.map((asset, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-purple-700 text-purple-200 text-sm rounded-full"
                    >
                      {asset}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Recommendations */}
          {incident.recommendations && incident.recommendations.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Recommendations
              </label>
              <div className="bg-yellow-900/20 border border-yellow-600 rounded-lg p-4">
                <ul className="space-y-2">
                  {incident.recommendations.map((recommendation, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <span className="text-yellow-400 mt-1">💡</span>
                      <span className="text-yellow-200">{recommendation}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}

          {/* References */}
          {incident.references && incident.references.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">References</label>
              <div className="bg-gray-700/30 border border-gray-600 rounded-lg p-4">
                <div className="space-y-3">
                  {incident.references.map((ref, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <span
                        className={`px-2 py-1 text-xs rounded ${
                          ref.type === 'cve'
                            ? 'bg-red-700 text-red-200'
                            : ref.type === 'threat_id'
                              ? 'bg-orange-700 text-orange-200'
                              : ref.type === 'external_report'
                                ? 'bg-blue-700 text-blue-200'
                                : 'bg-gray-700 text-gray-200'
                        }`}
                      >
                        {ref.type.toUpperCase()}
                      </span>
                      <div className="flex-1">
                        <div className="text-white font-mono text-sm">{ref.value}</div>
                        {ref.description && (
                          <div className="text-gray-400 text-xs mt-1">{ref.description}</div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Evidence */}
          {incident.evidence && incident.evidence.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Evidence</label>
              <div className="bg-gray-700/30 border border-gray-600 rounded-lg p-4">
                <div className="space-y-2">
                  {incident.evidence.map((evidence, index) => (
                    <div
                      key={index}
                      className="flex items-center space-x-2 p-2 bg-gray-800/50 rounded"
                    >
                      <span className="text-gray-400 text-sm">📄</span>
                      <span className="text-gray-300 text-sm">
                        {evidence.name || evidence.description || `Evidence ${index + 1}`}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Tags */}
          {incident.tags && incident.tags.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Tags</label>
              <div className="flex flex-wrap gap-2">
                {incident.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-gray-700 text-gray-300 text-sm rounded-full"
                  >
                    #{tag}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Assignment Information */}
          {(isEditing || incident.assigned_to || incident.assigned_team) && (
            <div className="bg-blue-900/20 border border-blue-600 rounded-lg p-4">
              <label className="block text-sm font-medium text-blue-300 mb-2">Assignment</label>
              {isEditing ? (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-xs font-medium text-blue-300 mb-1">
                      Assigned to
                    </label>
                    <input
                      type="text"
                      value={editData.assigned_to}
                      onChange={(e) =>
                        setEditData((prev) => ({ ...prev, assigned_to: e.target.value }))
                      }
                      className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="User email or name"
                    />
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-blue-300 mb-1">Team</label>
                    <input
                      type="text"
                      value={editData.assigned_team}
                      onChange={(e) =>
                        setEditData((prev) => ({ ...prev, assigned_team: e.target.value }))
                      }
                      className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Team name"
                    />
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  {incident.assigned_to && (
                    <div className="flex items-center space-x-2">
                      <User size={16} className="text-blue-400" />
                      <span className="text-blue-200">Assigned to: {incident.assigned_to}</span>
                    </div>
                  )}
                  {incident.assigned_team && (
                    <div className="flex items-center space-x-2">
                      <User size={16} className="text-blue-400" />
                      <span className="text-blue-200">Team: {incident.assigned_team}</span>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Timeline - EXACT same as TicketDetailsModal */}
          {incident.timeline && incident.timeline.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold text-white mb-4">Timeline</h3>
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {incident.timeline.map((entry, index) => (
                  <div
                    key={index}
                    className="flex items-start space-x-3 p-3 bg-gray-700/30 rounded-lg"
                  >
                    <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-white font-medium capitalize">
                          {entry.action.replace('_', ' ')}
                          <span className="text-blue-300 text-xs ml-2">
                            by {getUserEmail(entry.user_id, entry.user_email)}
                          </span>
                        </span>
                        <span className="text-gray-400 text-xs">{formatDate(entry.timestamp)}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Linked Ticket - EXACT same as TicketDetailsModal */}
          {incident.original_ticket_number && (
            <div className="bg-gray-700/30 border border-gray-600 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <span className="text-gray-400 font-medium">Created from ticket:</span>
                <span className="text-violet-300 font-mono">{incident.original_ticket_number}</span>
                <span className="text-gray-400">→</span>
                <button
                  onClick={() =>
                    onViewTicket &&
                    incident.original_ticket_number &&
                    onViewTicket(incident.original_ticket_number)
                  }
                  className="text-violet-400 hover:text-violet-300 underline transition-colors"
                >
                  [View Ticket Details]
                </button>
              </div>
            </div>
          )}

          {/* Attachments */}
          <AttachmentViewer
            attachments={incident.attachments || []}
            ticketNumber={incident.incident_id}
            onAttachmentsUpdated={onIncidentUpdated || (() => {})}
            canEdit={true}
          />

          {/* Metadata - EXACT same as TicketDetailsModal */}
          <div className="grid grid-cols-2 gap-4 text-sm text-gray-400">
            <div>
              <span className="flex items-center">
                <Clock size={16} className="mr-1" />
                Created: {formatDate(incident.created_at)}
              </span>
            </div>
            <div>
              <span className="flex items-center">
                <Clock size={16} className="mr-1" />
                Updated: {formatDate(incident.updated_at)}
              </span>
            </div>
            {incident.resolved_at && (
              <div>
                <span className="flex items-center">
                  <Clock size={16} className="mr-1" />
                  Resolved: {formatDate(incident.resolved_at)}
                </span>
              </div>
            )}
            {incident.closed_at && (
              <div>
                <span className="flex items-center">
                  <Clock size={16} className="mr-1" />
                  Closed: {formatDate(incident.closed_at)}
                </span>
              </div>
            )}
          </div>
          {/* Error Message */}
          {error && (
            <div className="bg-red-900/50 border border-red-500 rounded-lg p-4">
              <p className="text-red-200 text-sm">{error}</p>
            </div>
          )}
        </div>

        {/* Actions */}
        {isEditing && (
          <div className="flex justify-end items-center p-6 border-t border-gray-700 space-x-3">
            <button
              onClick={handleCancel}
              className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              onClick={handleSaveEdit}
              disabled={loading}
              className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 transition-colors"
            >
              {loading ? (
                <Loader className="animate-spin mr-2" size={16} />
              ) : (
                <Save className="mr-2" size={16} />
              )}
              Save Changes
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default IncidentDetailsModal;
