import React from 'react';
import {
  Clock,
  User,
  Eye,
  Edit,
  UserPlus,
  XCircle,
  AlertTriangle,
  FileText,
  RefreshCw,
  Trash2,
} from 'lucide-react';
import { Incident } from '../../services/incidentService';
import { SeverityBadge, StatusBadge, UrgentBadge } from './InteractiveBadges';
import { getTimeUrgencyColor, formatDate } from '../../services/incidentService';
import { TimeIndicator, UrgencyAlert, ActivityPulse } from './AdvancedIndicators';

interface EnhancedIncidentCardProps {
  incident: Incident;
  createdBy: string;
  onView: () => void;
  onEdit: () => void;
  onAssign: () => void;
  onClose?: () => void;
  onReopen?: () => void;
  onDelete?: () => void;
  canEdit: boolean;
  canAssign: boolean;
  canClose: boolean;
  canReopen: boolean;
  canDelete: boolean;
}

export const EnhancedIncidentCard: React.FC<EnhancedIncidentCardProps> = ({
  incident,
  createdBy,
  onView,
  onEdit,
  onAssign,
  onClose,
  onReopen,
  onDelete,
  canEdit,
  canAssign,
  canClose,
  canReopen,
  canDelete,
}) => {
  const timeElapsed = Math.floor(
    (new Date().getTime() - new Date(incident.created_at).getTime()) / (1000 * 60 * 60)
  );
  const daysSinceCreation = Math.floor(timeElapsed / 24);

  // Determine urgency levels
  const isExtremelyOverdue =
    daysSinceCreation > 10 && (incident.status === 'open' || incident.status === 'in_progress');
  const isUrgent = incident.severity === 'critical';
  const timeUrgencyClass = getTimeUrgencyColor(incident.created_at);

  // Check if incident is closed
  const isClosed = incident.status === 'closed' || incident.status === 'resolved';

  // Get incident type display
  const getIncidentType = () => {
    if (incident.escalated) return 'Escalated';
    if (incident.false_positive) return 'False Positive';
    return 'Security Incident';
  };

  return (
    <UrgentBadge isUrgent={isUrgent} isExtremelyOverdue={isExtremelyOverdue}>
      <div
        className={`bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 hover:border-purple-500/50 transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-2xl hover:shadow-purple-500/10 ${timeUrgencyClass}`}
      >
        {/* Urgency Alert */}
        <UrgencyAlert
          severity={incident.severity}
          timeElapsed={timeElapsed}
          status={incident.status}
        />

        {/* Header Section */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-3 mb-2">
              <h3 className="text-lg font-semibold text-white truncate">{incident.title}</h3>
              <span className="px-3 py-1 bg-gradient-to-r from-indigo-700 to-indigo-800 text-indigo-300 text-xs rounded-lg border border-indigo-600/30 flex-shrink-0">
                {getIncidentType()}
              </span>
              <ActivityPulse
                isActive={incident.status === 'open' || incident.status === 'in_progress'}
                color={incident.severity === 'critical' ? 'red' : 'blue'}
                size="sm"
              />
            </div>
            <div className="flex items-center gap-2 mb-2">
              <p className="text-gray-400 text-sm font-mono">{incident.incident_id}</p>
              <span className="text-gray-500 text-xs">•</span>
              <TimeIndicator
                createdAt={incident.created_at}
                status={incident.status}
                showDetails={false}
              />
            </div>
            <p className="text-gray-300 text-sm line-clamp-2 mb-3">{incident.description}</p>

            {/* Original Ticket Information */}
            {incident.original_ticket_number && (
              <div className="flex items-center gap-2 p-2 bg-blue-900/30 border border-blue-500/50 rounded-lg">
                <FileText size={14} className="text-blue-400" />
                <span className="text-blue-300 text-xs">
                  Créé depuis le ticket:
                  <span className="font-mono ml-1 text-blue-200">
                    {incident.original_ticket_number}
                  </span>
                </span>
              </div>
            )}
          </div>

          {/* Status Badges - Sévérité et Statut séparés */}
          <div className="flex flex-col items-end space-y-2 ml-4 flex-shrink-0">
            {/* Sévérité */}
            <SeverityBadge severity={incident.severity} size="sm" />
            {/* Statut séparé */}
            <StatusBadge status={incident.status} size="sm" />
          </div>
        </div>

        {/* Metadata Section */}
        <div className="flex items-center justify-between mb-4 text-sm text-gray-400">
          <div className="flex items-center space-x-4">
            <span className="flex items-center">
              <User size={14} className="mr-1" />
              {createdBy}
            </span>
            {incident.assigned_to && (
              <span className="flex items-center text-purple-400">
                <UserPlus size={14} className="mr-1" />
                {incident.assigned_to}
              </span>
            )}
          </div>
          <div className="flex items-center text-xs text-gray-500">
            <Clock size={12} className="mr-1" />
            {formatDate(incident.created_at)}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between">
          <div className="flex space-x-2">
            <button
              onClick={onView}
              className="flex items-center px-3 py-2 bg-blue-600/20 text-blue-400 rounded-lg hover:bg-blue-600/30 transition-colors text-sm border border-blue-500/30"
            >
              <Eye size={14} className="mr-1" />
              View
            </button>
            {/* Show Edit/Assign buttons only if incident is not closed */}
            {!isClosed && canEdit && (
              <button
                onClick={onEdit}
                className="flex items-center px-3 py-2 bg-purple-600/20 text-purple-400 rounded-lg hover:bg-purple-600/30 transition-colors text-sm border border-purple-500/30"
              >
                <Edit size={14} className="mr-1" />
                Edit
              </button>
            )}
            {!isClosed && canAssign && (
              <button
                onClick={onAssign}
                className="flex items-center px-3 py-2 bg-green-600/20 text-green-400 rounded-lg hover:bg-green-600/30 transition-colors text-sm border border-green-500/30"
              >
                <UserPlus size={14} className="mr-1" />
                Assign
              </button>
            )}

            {/* Show Reopen button if incident is closed */}
            {isClosed && canReopen && onReopen && (
              <button
                onClick={onReopen}
                className="flex items-center px-3 py-2 bg-green-600/20 text-green-400 rounded-lg hover:bg-green-600/30 transition-colors text-sm border border-green-500/30"
              >
                <RefreshCw size={14} className="mr-1" />
                Reopen
              </button>
            )}
          </div>

          <div className="flex space-x-2">
            {/* Show Close button only if incident is not closed */}
            {!isClosed && canClose && onClose && (
              <button
                onClick={onClose}
                className="flex items-center px-3 py-2 bg-gray-600/20 text-gray-400 rounded-lg hover:bg-gray-600/30 transition-colors text-sm border border-gray-500/30"
              >
                <XCircle size={14} className="mr-1" />
                Close
              </button>
            )}

            {/* Show Delete button - only for admins */}
            {canDelete && onDelete && (
              <button
                onClick={onDelete}
                className="flex items-center px-3 py-2 bg-orange-600/20 text-orange-400 rounded-lg hover:bg-orange-600/30 transition-colors text-sm border border-orange-500/30"
              >
                <Trash2 size={14} className="mr-1" />
                Delete
              </button>
            )}
          </div>
        </div>

        {/* Urgency Indicator */}
        {isExtremelyOverdue && (
          <div className="mt-3 flex items-center justify-center">
            <div className="flex items-center px-3 py-1 bg-red-600/30 text-red-300 rounded-full text-xs border border-red-500/50 animate-pulse">
              <AlertTriangle size={12} className="mr-1 animate-bounce" />
              EXTREMELY OVERDUE - {daysSinceCreation} days without resolution!
            </div>
          </div>
        )}

        {isUrgent && !isExtremelyOverdue && (
          <div className="mt-3 flex items-center justify-center">
            <div className="flex items-center px-3 py-1 bg-orange-600/20 text-orange-400 rounded-full text-xs border border-orange-500/30">
              <AlertTriangle size={12} className="mr-1" />
              CRITICAL - Immediate attention required
            </div>
          </div>
        )}

        {/* Special Status Indicators */}
        {incident.escalated && (
          <div className="mt-2 flex items-center justify-center">
            <div className="flex items-center px-3 py-1 bg-orange-600/20 text-orange-400 rounded-full text-xs border border-orange-500/30">
              <AlertTriangle size={12} className="mr-1" />
              ESCALATED - Management attention required
            </div>
          </div>
        )}

        {incident.false_positive && (
          <div className="mt-2 flex items-center justify-center">
            <div className="flex items-center px-3 py-1 bg-gray-600/20 text-gray-400 rounded-full text-xs border border-gray-500/30">
              <XCircle size={12} className="mr-1" />
              FALSE POSITIVE - No action required
            </div>
          </div>
        )}
      </div>
    </UrgentBadge>
  );
};
