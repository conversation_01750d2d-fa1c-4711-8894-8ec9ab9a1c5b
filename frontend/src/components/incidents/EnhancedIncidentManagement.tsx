import React, { useState, useEffect } from 'react';
import { Grid, List, Eye, Edit, UserPlus, ArrowRight, XCircle, RefreshCw } from 'lucide-react';
import { incidentService, InvestigationTicket, Incident } from '../../services/incidentService';
import { PrioritySummaryBanner } from './PrioritySummaryBanner';
import { FilterAndSearch, FilterOptions } from './FilterAndSearch';
import { CreateTicketButton } from './CreateTicketButton';
import { TicketTable } from './TicketTable';
import { IncidentTable } from './IncidentTable';
import { EnhancedTicketCard } from './EnhancedTicketCard';
import { EnhancedIncidentCard } from './EnhancedIncidentCard';
import { TicketDetailView } from './TicketDetailView';
import { IncidentDashboard } from './IncidentDashboard';

type ViewMode = 'table' | 'cards';
type ActiveTab = 'all' | 'tickets' | 'incidents';

export const EnhancedIncidentManagement: React.FC = () => {
  const [tickets, setTickets] = useState<InvestigationTicket[]>([]);
  const [incidents, setIncidents] = useState<Incident[]>([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<ViewMode>('table');
  const [activeTab, setActiveTab] = useState<ActiveTab>('all');
  const [selectedTicket, setSelectedTicket] = useState<InvestigationTicket | null>(null);
  const [selectedIncident, setSelectedIncident] = useState<Incident | null>(null);

  // Filtres
  const [filters, setFilters] = useState<FilterOptions>({
    searchText: '',
    priority: [],
    status: [],
    type: [],
    assignedTo: [],
    dateRange: { start: '', end: '' },
  });

  // Charger les données
  const loadData = async () => {
    try {
      setLoading(true);
      const [ticketsData, incidentsData] = await Promise.all([
        incidentService.getInvestigationTickets(),
        incidentService.getIncidents(),
      ]);
      setTickets(ticketsData);
      setIncidents(incidentsData);
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  // Fonction de rafraîchissement
  const handleRefresh = async () => {
    await loadData();
  };

  // Filtrer les données
  const filteredTickets = tickets.filter((ticket) => {
    // Recherche textuelle
    if (filters.searchText) {
      const searchLower = filters.searchText.toLowerCase();
      const matchesSearch =
        ticket.ticket_number?.toLowerCase().includes(searchLower) ||
        ticket.title?.toLowerCase().includes(searchLower) ||
        ticket.description?.toLowerCase().includes(searchLower) ||
        ticket.short_description?.toLowerCase().includes(searchLower);
      if (!matchesSearch) return false;
    }

    // Filtre par priorité/sévérité
    if (filters.priority.length > 0) {
      const ticketPriority =
        ticket.severity?.toLowerCase() ||
        (ticket.priority === 1
          ? 'critical'
          : ticket.priority === 2
            ? 'high'
            : ticket.priority === 3
              ? 'medium'
              : 'low');
      if (!filters.priority.includes(ticketPriority)) return false;
    }

    // Filtre par statut
    if (filters.status.length > 0 && !filters.status.includes(ticket.status)) {
      return false;
    }

    // Filtre par assigné
    if (
      filters.assignedTo.length > 0 &&
      ticket.assigned_to &&
      !filters.assignedTo.includes(ticket.assigned_to)
    ) {
      return false;
    }

    // Filtre par date
    if (filters.dateRange.start) {
      const ticketDate = new Date(ticket.created_at);
      const startDate = new Date(filters.dateRange.start);
      if (ticketDate < startDate) return false;
    }
    if (filters.dateRange.end) {
      const ticketDate = new Date(ticket.created_at);
      const endDate = new Date(filters.dateRange.end);
      if (ticketDate > endDate) return false;
    }

    return true;
  });

  const filteredIncidents = incidents.filter((incident) => {
    // Recherche textuelle
    if (filters.searchText) {
      const searchLower = filters.searchText.toLowerCase();
      const matchesSearch =
        incident.incident_number?.toLowerCase().includes(searchLower) ||
        incident.title?.toLowerCase().includes(searchLower) ||
        incident.description?.toLowerCase().includes(searchLower);
      if (!matchesSearch) return false;
    }

    // Filtre par priorité/sévérité
    if (filters.priority.length > 0) {
      const incidentPriority = incident.severity?.toLowerCase() || 'medium';
      if (!filters.priority.includes(incidentPriority)) return false;
    }

    // Filtre par statut
    if (filters.status.length > 0 && !filters.status.includes(incident.status)) {
      return false;
    }

    // Filtre par type
    if (
      filters.type.length > 0 &&
      incident.incident_type &&
      !filters.type.includes(incident.incident_type)
    ) {
      return false;
    }

    // Filtre par assigné
    if (
      filters.assignedTo.length > 0 &&
      incident.assigned_to &&
      !filters.assignedTo.includes(incident.assigned_to)
    ) {
      return false;
    }

    // Filtre par date
    if (filters.dateRange.start) {
      const incidentDate = new Date(incident.created_at);
      const startDate = new Date(filters.dateRange.start);
      if (incidentDate < startDate) return false;
    }
    if (filters.dateRange.end) {
      const incidentDate = new Date(incident.created_at);
      const endDate = new Date(filters.dateRange.end);
      if (incidentDate > endDate) return false;
    }

    return true;
  });

  // Obtenir les listes pour les filtres
  const availableAssignees = Array.from(
    new Set([
      ...tickets.map((t) => t.assigned_to).filter(Boolean),
      ...incidents.map((i) => i.assigned_to).filter(Boolean),
    ])
  ) as string[];

  const availableTypes = Array.from(
    new Set(incidents.map((i) => i.incident_type).filter(Boolean))
  ) as string[];

  // Handlers
  const handleViewTicket = (ticket: InvestigationTicket) => {
    setSelectedTicket(ticket);
  };

  const handleViewIncident = (incident: Incident) => {
    setSelectedIncident(incident);
  };

  const handleEditTicket = (ticket: InvestigationTicket) => {
    console.log('Modifier ticket:', ticket.id);
  };

  const handleEditIncident = (incident: Incident) => {
    console.log('Modifier incident:', incident.id);
  };

  const handleAssignTicket = (ticket: InvestigationTicket) => {
    console.log('Assigner ticket:', ticket.id);
  };

  const handleAssignIncident = (incident: Incident) => {
    console.log('Assigner incident:', incident.id);
  };

  const handleConvertToIncident = (ticket: InvestigationTicket) => {
    console.log('Convertir en incident:', ticket.id);
  };

  const handleCloseIncident = (incident: Incident) => {
    console.log('Clôturer incident:', incident.id);
  };

  const handleCreateTicket = () => {
    console.log('Créer nouveau ticket');
  };

  const handleCreateIncident = () => {
    console.log('Créer nouvel incident');
  };

  const handleStatusChange = (newStatus: string) => {
    console.log('Changer statut vers:', newStatus);
  };

  // Si on affiche les détails d'un ticket
  if (selectedTicket) {
    return (
      <TicketDetailView
        ticket={selectedTicket}
        onBack={() => setSelectedTicket(null)}
        onEdit={() => handleEditTicket(selectedTicket)}
        onAssign={() => handleAssignTicket(selectedTicket)}
        onConvertToIncident={() => handleConvertToIncident(selectedTicket)}
        onStatusChange={handleStatusChange}
        canEdit={true}
        canAssign={true}
        canConvert={true}
      />
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
        <div className="text-white text-xl">Chargement...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* En-tête avec bouton de création */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Gestion des Incidents et Tickets</h1>
            <p className="text-gray-400">
              Vue d'ensemble et gestion centralisée de tous vos tickets et incidents
            </p>
          </div>

          <CreateTicketButton
            onCreateTicket={handleCreateTicket}
            onCreateIncident={handleCreateIncident}
          />
        </div>

        {/* Résumé des priorités */}
        <PrioritySummaryBanner tickets={filteredTickets} incidents={filteredIncidents} />

        {/* Dashboard complet */}
        <IncidentDashboard tickets={filteredTickets} incidents={filteredIncidents} />

        {/* Filtres et recherche */}
        <FilterAndSearch
          filters={filters}
          onFiltersChange={setFilters}
          availableAssignees={availableAssignees}
          availableTypes={availableTypes}
        />

        {/* Contrôles d'affichage */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* Onglets */}
            <div className="flex items-center space-x-2 bg-gray-800/60 rounded-xl p-1">
              <button
                onClick={() => setActiveTab('all')}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                  activeTab === 'all' ? 'bg-blue-600 text-white' : 'text-gray-400 hover:text-white'
                }`}
              >
                Tout ({filteredTickets.length + filteredIncidents.length})
              </button>
              <button
                onClick={() => setActiveTab('tickets')}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                  activeTab === 'tickets'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                Tickets ({filteredTickets.length})
              </button>
              <button
                onClick={() => setActiveTab('incidents')}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                  activeTab === 'incidents'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                Incidents ({filteredIncidents.length})
              </button>
            </div>
          </div>

          {/* Contrôles de vue et actions */}
          <div className="flex items-center space-x-3">
            {/* Bouton de rafraîchissement */}
            <button
              onClick={handleRefresh}
              disabled={loading}
              className={`flex items-center space-x-2 px-4 py-2 rounded-xl transition-all duration-200 ${
                loading
                  ? 'bg-gray-700/50 text-gray-500 cursor-not-allowed'
                  : 'bg-green-600/20 border border-green-500/50 text-green-400 hover:bg-green-600/30'
              }`}
              title="Actualiser la liste"
            >
              <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
              <span className="text-sm font-medium">
                {loading ? 'Actualisation...' : 'Actualiser'}
              </span>
            </button>

            {/* Sélecteur de vue */}
            <div className="flex items-center space-x-2 bg-gray-800/60 rounded-xl p-1">
              <button
                onClick={() => setViewMode('table')}
                className={`p-2 rounded-lg transition-all duration-200 ${
                  viewMode === 'table' ? 'bg-blue-600 text-white' : 'text-gray-400 hover:text-white'
                }`}
                title="Vue tableau"
              >
                <List size={18} />
              </button>
              <button
                onClick={() => setViewMode('cards')}
                className={`p-2 rounded-lg transition-all duration-200 ${
                  viewMode === 'cards' ? 'bg-blue-600 text-white' : 'text-gray-400 hover:text-white'
                }`}
                title="Vue cartes"
              >
                <Grid size={18} />
              </button>
            </div>
          </div>
        </div>

        {/* Contenu principal */}
        <div className="space-y-6">
          {/* Tickets */}
          {(activeTab === 'all' || activeTab === 'tickets') && (
            <div>
              {activeTab === 'all' && (
                <h2 className="text-xl font-semibold text-white mb-4">
                  Tickets d'Investigation ({filteredTickets.length})
                </h2>
              )}

              {viewMode === 'table' ? (
                <TicketTable
                  tickets={filteredTickets}
                  onView={handleViewTicket}
                  onEdit={handleEditTicket}
                  onAssign={handleAssignTicket}
                  onConvert={handleConvertToIncident}
                  onRefresh={handleRefresh}
                  canEdit={true}
                  canAssign={true}
                  canConvert={true}
                  isLoading={loading}
                />
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredTickets.map((ticket) => (
                    <EnhancedTicketCard
                      key={ticket.id}
                      ticket={ticket}
                      createdBy={ticket.user_email || ticket.user_id || 'Unknown'}
                      onView={() => handleViewTicket(ticket)}
                      onEdit={() => handleEditTicket(ticket)}
                      onAssign={() => handleAssignTicket(ticket)}
                      onConvert={() => handleConvertToIncident(ticket)}
                      onClose={() => {
                        /* TODO: Implement close */
                      }}
                      onReopen={() => {
                        /* TODO: Implement reopen */
                      }}
                      onDelete={() => {
                        /* TODO: Implement delete */
                      }}
                      canEdit={true}
                      canAssign={true}
                      canConvert={true}
                      canClose={true}
                      canReopen={true}
                      canDelete={true}
                    />
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Incidents */}
          {(activeTab === 'all' || activeTab === 'incidents') && (
            <div>
              {activeTab === 'all' && (
                <h2 className="text-xl font-semibold text-white mb-4">
                  Incidents de Sécurité ({filteredIncidents.length})
                </h2>
              )}

              {viewMode === 'table' ? (
                <IncidentTable
                  incidents={filteredIncidents}
                  onView={handleViewIncident}
                  onEdit={handleEditIncident}
                  onAssign={handleAssignIncident}
                  onClose={handleCloseIncident}
                  onRefresh={handleRefresh}
                  canEdit={true}
                  canAssign={true}
                  canClose={true}
                  isLoading={loading}
                />
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredIncidents.map((incident) => (
                    <EnhancedIncidentCard
                      key={incident.id}
                      incident={incident}
                      onView={() => handleViewIncident(incident)}
                      onEdit={() => handleEditIncident(incident)}
                      onAssign={() => handleAssignIncident(incident)}
                      onClose={() => handleCloseIncident(incident)}
                      canEdit={true}
                      canAssign={true}
                      canClose={true}
                    />
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
