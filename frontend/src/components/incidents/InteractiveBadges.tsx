import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>gle,
  Clock,
  User,
  Zap,
  CheckCircle,
  XCircle,
  Pause,
  RotateCcw,
} from 'lucide-react';
import {
  getSeverityColor,
  getStatusColor,
  getPriorityColor,
  getPriorityLabel,
  getSeverityIcon,
  getStatusIcon,
  getPriorityIcon,
  getBadgeHoverEffect,
} from '../../services/incidentService';

interface SeverityBadgeProps {
  severity: string;
  showIcon?: boolean;
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
}

interface StatusBadgeProps {
  status: string;
  showIcon?: boolean;
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
}

interface PriorityBadgeProps {
  priority: number;
  showIcon?: boolean;
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
}

const getSizeClasses = (size: 'sm' | 'md' | 'lg') => {
  switch (size) {
    case 'sm':
      return 'px-2 py-1 text-xs';
    case 'lg':
      return 'px-4 py-2 text-sm';
    default:
      return 'px-3 py-1 text-xs';
  }
};

const getIconSize = (size: 'sm' | 'md' | 'lg') => {
  switch (size) {
    case 'sm':
      return 12;
    case 'lg':
      return 18;
    default:
      return 14;
  }
};

const getStatusIconComponent = (status: string, size: number) => {
  switch (status?.toLowerCase()) {
    case 'new':
      return <Zap size={size} className="mr-1" />;
    case 'open':
      return <AlertTriangle size={size} className="mr-1" />;
    case 'in progress':
    case 'in_progress':
      return <Clock size={size} className="mr-1 animate-spin" />;
    case 'on hold':
    case 'on_hold':
      return <Pause size={size} className="mr-1" />;
    case 'resolved':
      return <CheckCircle size={size} className="mr-1" />;
    case 'closed':
      return <XCircle size={size} className="mr-1" />;
    case 'converted_to_incident':
      return <RotateCcw size={size} className="mr-1" />;
    default:
      return <AlertTriangle size={size} className="mr-1" />;
  }
};

const getSeverityIconComponent = (severity: string, size: number) => {
  switch (severity?.toLowerCase()) {
    case 'critical':
    case 'high':
      return <AlertTriangle size={size} className="mr-1" />;
    case 'medium':
      return <Clock size={size} className="mr-1" />;
    case 'low':
      return <CheckCircle size={size} className="mr-1" />;
    default:
      return <AlertTriangle size={size} className="mr-1" />;
  }
};

export const SeverityBadge: React.FC<SeverityBadgeProps> = ({
  severity,
  showIcon = true,
  size = 'md',
  onClick,
}) => {
  const baseClasses = `inline-flex items-center font-medium rounded-xl ${getSizeClasses(size)} ${getSeverityColor(severity)} ${getBadgeHoverEffect()}`;
  const iconSize = getIconSize(size);

  return (
    <span
      className={baseClasses}
      onClick={onClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
    >
      {showIcon && getSeverityIconComponent(severity, iconSize)}
      <span className="font-semibold">{severity.toUpperCase()}</span>
      {showIcon && <span className="ml-1">{getSeverityIcon(severity)}</span>}
    </span>
  );
};

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  showIcon = true,
  size = 'md',
  onClick,
}) => {
  const baseClasses = `inline-flex items-center font-medium rounded-xl ${getSizeClasses(size)} ${getStatusColor(status)} ${getBadgeHoverEffect()}`;
  const iconSize = getIconSize(size);

  return (
    <span
      className={baseClasses}
      onClick={onClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
    >
      {showIcon && getStatusIconComponent(status, iconSize)}
      <span className="font-semibold">{status.replace('_', ' ').toUpperCase()}</span>
      {showIcon && <span className="ml-1">{getStatusIcon(status)}</span>}
    </span>
  );
};

export const PriorityBadge: React.FC<PriorityBadgeProps> = ({
  priority,
  showIcon = true,
  size = 'md',
  onClick,
}) => {
  const baseClasses = `inline-flex items-center font-medium rounded-xl ${getSizeClasses(size)} ${getPriorityColor(priority)} ${getBadgeHoverEffect()}`;
  const iconSize = getIconSize(size);

  return (
    <span
      className={baseClasses}
      onClick={onClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
    >
      {showIcon && <AlertTriangle size={iconSize} className="mr-1" />}
      <span className="font-semibold">{getPriorityLabel(priority)}</span>
      {showIcon && <span className="ml-1">{getPriorityIcon(priority)}</span>}
    </span>
  );
};

// Combined badge component for tickets
interface TicketBadgeGroupProps {
  severity?: string;
  status: string;
  priority?: number;
  size?: 'sm' | 'md' | 'lg';
  layout?: 'horizontal' | 'vertical';
  onSeverityClick?: () => void;
  onStatusClick?: () => void;
  onPriorityClick?: () => void;
}

export const TicketBadgeGroup: React.FC<TicketBadgeGroupProps> = ({
  severity,
  status,
  priority,
  size = 'md',
  layout = 'horizontal',
  onSeverityClick,
  onStatusClick,
  onPriorityClick,
}) => {
  const containerClasses =
    layout === 'horizontal' ? 'flex items-center space-x-2' : 'flex flex-col space-y-2';

  return (
    <div className={containerClasses}>
      {/* Priorité et Sévérité ensemble si layout horizontal */}
      {layout === 'horizontal' && (severity || priority) && (
        <div className="flex items-center space-x-1">
          {severity && <SeverityBadge severity={severity} size={size} onClick={onSeverityClick} />}
          {priority && <PriorityBadge priority={priority} size={size} onClick={onPriorityClick} />}
        </div>
      )}

      {/* Layout vertical - badges séparés */}
      {layout === 'vertical' && (
        <>
          {severity && <SeverityBadge severity={severity} size={size} onClick={onSeverityClick} />}
          {priority && <PriorityBadge priority={priority} size={size} onClick={onPriorityClick} />}
        </>
      )}

      {/* Statut toujours séparé */}
      <StatusBadge status={status} size={size} onClick={onStatusClick} />
    </div>
  );
};

// Compact badge combining priority and severity
interface CompactPrioritySeverityBadgeProps {
  priority?: number;
  severity?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const CompactPrioritySeverityBadge: React.FC<CompactPrioritySeverityBadgeProps> = ({
  priority,
  severity,
  size = 'sm',
}) => {
  if (!priority && !severity) return null;

  const sizeClasses = getSizeClasses(size);

  return (
    <div className={`inline-flex items-center space-x-1 ${sizeClasses}`}>
      {severity && (
        <span
          className={`inline-flex items-center font-medium rounded-lg px-2 py-1 text-xs ${getSeverityColor(severity)}`}
        >
          {severity.toUpperCase()}
        </span>
      )}
      {priority && (
        <span
          className={`inline-flex items-center font-medium rounded-lg px-2 py-1 text-xs ${getPriorityColor(priority)}`}
        >
          {getPriorityLabel(priority)}
        </span>
      )}
    </div>
  );
};

// Animated pulse badge for urgent items
interface UrgentBadgeProps {
  children: React.ReactNode;
  isUrgent?: boolean;
  isExtremelyOverdue?: boolean;
}

export const UrgentBadge: React.FC<UrgentBadgeProps> = ({
  children,
  isUrgent = false,
  isExtremelyOverdue = false,
}) => {
  return (
    <div
      className={`relative ${isExtremelyOverdue ? 'animate-pulse' : isUrgent ? 'animate-pulse' : ''}`}
    >
      {children}
      {isExtremelyOverdue && (
        <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full animate-ping flex items-center justify-center">
          <span className="text-white text-xs font-bold">!</span>
        </div>
      )}
      {isUrgent && !isExtremelyOverdue && (
        <div className="absolute -top-1 -right-1 w-3 h-3 bg-orange-500 rounded-full animate-ping"></div>
      )}
    </div>
  );
};
