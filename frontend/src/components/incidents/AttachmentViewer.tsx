import { useState } from 'react';
import { Download, Trash2, Upload, Plus, X, File, AlertCircle } from 'lucide-react';
import { useRole } from '../../hooks/useRole';
import { getToken } from '../../utils/auth';
import FileUpload from './FileUpload';

interface Attachment {
  id: string;
  original_name: string;
  stored_name: string;
  file_path: string;
  file_size: number;
  upload_date: string;
  file_type: string;
}

interface AttachmentViewerProps {
  attachments: Attachment[];
  ticketNumber: string;
  onAttachmentsUpdated: () => void;
  canEdit?: boolean;
}

export default function AttachmentViewer({
  attachments = [],
  ticketNumber,
  onAttachmentsUpdated,
  canEdit = false,
}: AttachmentViewerProps) {
  const { isAdmin } = useRole();
  const [showUpload, setShowUpload] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString();
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    const imageTypes = ['png', 'jpg', 'jpeg', 'gif'];

    if (imageTypes.includes(extension || '')) {
      return '🖼️';
    } else if (extension === 'pdf') {
      return '📄';
    } else if (['doc', 'docx'].includes(extension || '')) {
      return '📝';
    } else if (['xls', 'xlsx'].includes(extension || '')) {
      return '📊';
    } else if (extension === 'zip') {
      return '🗜️';
    } else if (['log', 'txt'].includes(extension || '')) {
      return '📋';
    } else {
      return '📁';
    }
  };

  const handleUploadFiles = async (files: File[]) => {
    if (files.length === 0) return;

    setUploading(true);
    setError(null);

    try {
      const formData = new FormData();
      files.forEach((file) => {
        formData.append('attachments', file);
      });
      formData.append('user_id', 'current_user'); // TODO: Get actual user ID

      const token = getToken();
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(
        `http://localhost:5000/api/incident/tickets/${ticketNumber}/attachments`,
        {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: formData,
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to upload files');
      }

      setShowUpload(false);
      onAttachmentsUpdated();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to upload files');
    } finally {
      setUploading(false);
    }
  };

  const handleDeleteAttachment = async (attachmentId: string) => {
    if (!confirm('Are you sure you want to delete this attachment?')) {
      return;
    }

    try {
      const token = getToken();
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(
        `http://localhost:5000/api/incident/tickets/${ticketNumber}/attachments/${attachmentId}`,
        {
          method: 'DELETE',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ user_id: 'current_user' }), // TODO: Get actual user ID
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to delete attachment');
      }

      onAttachmentsUpdated();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete attachment');
    }
  };

  const handleDownload = (attachment: Attachment) => {
    // TODO: Implement file download
    console.log('Download attachment:', attachment);
  };

  if (attachments.length === 0 && !canEdit) {
    return (
      <div className="text-center py-6 text-gray-400">
        <File size={48} className="mx-auto mb-2 opacity-50" />
        <p>No attachments</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-white">
          Attachments {attachments.length > 0 && `(${attachments.length})`}
        </h3>
        {canEdit && isAdmin && (
          <button
            onClick={() => setShowUpload(!showUpload)}
            className="flex items-center px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm"
          >
            <Plus size={16} className="mr-1" />
            Add Files
          </button>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-900/50 border border-red-500 rounded-lg p-3">
          <div className="flex items-center">
            <AlertCircle className="text-red-400 mr-2" size={16} />
            <span className="text-red-200 text-sm">{error}</span>
          </div>
        </div>
      )}

      {/* Upload Section */}
      {showUpload && canEdit && (
        <div className="bg-gray-700/30 border border-gray-600 rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-white font-medium">Upload New Files</h4>
            <button onClick={() => setShowUpload(false)} className="text-gray-400 hover:text-white">
              <X size={16} />
            </button>
          </div>
          <FileUpload
            onFilesChange={handleUploadFiles}
            maxFiles={5}
            maxFileSize={16}
            disabled={uploading}
          />
          {uploading && (
            <div className="mt-4 text-center">
              <div className="inline-flex items-center text-purple-400">
                <Upload className="animate-pulse mr-2" size={16} />
                Uploading files...
              </div>
            </div>
          )}
        </div>
      )}

      {/* Attachments List */}
      {attachments.length > 0 && (
        <div className="space-y-3">
          {attachments.map((attachment) => (
            <div
              key={attachment.id}
              className="flex items-center justify-between bg-gray-700/50 border border-gray-600 rounded-lg p-4"
            >
              <div className="flex items-center space-x-3">
                <span className="text-2xl">{getFileIcon(attachment.original_name)}</span>
                <div>
                  <p className="text-white font-medium">{attachment.original_name}</p>
                  <div className="flex items-center space-x-4 text-xs text-gray-400">
                    <span>{formatFileSize(attachment.file_size)}</span>
                    <span>•</span>
                    <span>{formatDate(attachment.upload_date)}</span>
                    <span>•</span>
                    <span className="uppercase">{attachment.file_type}</span>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleDownload(attachment)}
                  className="p-2 text-gray-400 hover:text-blue-400 hover:bg-blue-900/30 rounded-lg transition-colors"
                  title="Download"
                >
                  <Download size={16} />
                </button>
                {canEdit && isAdmin && (
                  <button
                    onClick={() => handleDeleteAttachment(attachment.id)}
                    className="p-2 text-gray-400 hover:text-red-400 hover:bg-red-900/30 rounded-lg transition-colors"
                    title="Delete"
                  >
                    <Trash2 size={16} />
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Empty State for Editable */}
      {attachments.length === 0 && canEdit && (
        <div className="text-center py-8 border-2 border-dashed border-gray-600 rounded-lg">
          <File size={48} className="mx-auto mb-3 text-gray-400 opacity-50" />
          <p className="text-gray-400 mb-2">No attachments yet</p>
          <p className="text-gray-500 text-sm">
            Click "Add Files" to attach documents, screenshots, or logs
          </p>
        </div>
      )}
    </div>
  );
}
