import React from 'react';
import {
  Eye,
  Edit,
  UserPlus,
  ArrowRight,
  Clock,
  User,
  AlertTriangle,
  RotateCcw,
  RefreshCw,
} from 'lucide-react';
import { InvestigationTicket } from '../../services/incidentService';
import { SeverityBadge, StatusBadge, PriorityBadge } from './InteractiveBadges';
import { formatDate } from '../../services/incidentService';

interface TicketTableProps {
  tickets: InvestigationTicket[];
  onView: (ticket: InvestigationTicket) => void;
  onEdit: (ticket: InvestigationTicket) => void;
  onAssign: (ticket: InvestigationTicket) => void;
  onConvert?: (ticket: InvestigationTicket) => void;
  onRefresh?: () => void;
  canEdit: boolean;
  canAssign: boolean;
  canConvert: boolean;
  isLoading?: boolean;
}

export const TicketTable: React.FC<TicketTableProps> = ({
  tickets,
  onView,
  onEdit,
  onAssign,
  onConvert,
  onRefresh,
  canEdit,
  canAssign,
  canConvert,
  isLoading = false,
}) => {
  const getTimeElapsed = (createdAt: string) => {
    const now = new Date();
    const created = new Date(createdAt);
    const diffHours = Math.floor((now.getTime() - created.getTime()) / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) return `${diffDays}j`;
    if (diffHours > 0) return `${diffHours}h`;
    return 'Nouveau';
  };

  const getUrgencyIndicator = (ticket: InvestigationTicket) => {
    const daysSinceCreation = Math.floor(
      (new Date().getTime() - new Date(ticket.created_at).getTime()) / (1000 * 60 * 60 * 24)
    );
    const isExtremelyOverdue =
      daysSinceCreation > 10 &&
      (ticket.status === 'open' || ticket.status === 'new' || ticket.status === 'in_progress');
    const isCritical = ticket.severity === 'critical' || ticket.priority === 1;

    if (isExtremelyOverdue) {
      return <AlertTriangle size={16} className="text-red-500 animate-pulse" />;
    } else if (isCritical) {
      return <AlertTriangle size={16} className="text-orange-500" />;
    }
    return null;
  };

  return (
    <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl overflow-hidden">
      {/* En-tête avec bouton de rafraîchissement */}
      {onRefresh && (
        <div className="flex items-center justify-between p-4 border-b border-gray-700/50">
          <h3 className="text-lg font-semibold text-white">Tickets ({tickets.length})</h3>
          <button
            onClick={onRefresh}
            disabled={isLoading}
            className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 ${
              isLoading
                ? 'bg-gray-700/50 text-gray-500 cursor-not-allowed'
                : 'bg-green-600/20 border border-green-500/50 text-green-400 hover:bg-green-600/30'
            }`}
            title="Actualiser les tickets"
          >
            <RefreshCw size={14} className={isLoading ? 'animate-spin' : ''} />
            <span className="text-sm">{isLoading ? 'Actualisation...' : 'Actualiser'}</span>
          </button>
        </div>
      )}

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-700/50 bg-gray-800/40">
              <th className="text-left p-4 text-gray-300 font-semibold text-sm">🚨</th>
              <th className="text-left p-4 text-gray-300 font-semibold text-sm">ID Ticket</th>
              <th className="text-left p-4 text-gray-300 font-semibold text-sm">Statut</th>
              <th className="text-left p-4 text-gray-300 font-semibold text-sm">Type</th>
              <th className="text-left p-4 text-gray-300 font-semibold text-sm">Titre</th>
              <th className="text-left p-4 text-gray-300 font-semibold text-sm">Créé par</th>
              <th className="text-left p-4 text-gray-300 font-semibold text-sm">Créé</th>
              <th className="text-left p-4 text-gray-300 font-semibold text-sm">Assigné à</th>
              <th className="text-left p-4 text-gray-300 font-semibold text-sm">Priorité</th>
              <th className="text-left p-4 text-gray-300 font-semibold text-sm">Actions</th>
            </tr>
          </thead>
          <tbody>
            {tickets.map((ticket) => (
              <tr
                key={ticket.id}
                className="border-b border-gray-700/30 hover:bg-gray-700/20 transition-colors duration-200"
              >
                {/* Urgency Indicator */}
                <td className="p-4">{getUrgencyIndicator(ticket)}</td>

                {/* Ticket ID */}
                <td className="p-4">
                  <span className="text-blue-400 font-mono text-sm font-semibold">
                    {ticket.ticket_number}
                  </span>
                </td>

                {/* Status */}
                <td className="p-4">
                  <StatusBadge status={ticket.status} size="sm" />
                </td>

                {/* Type */}
                <td className="p-4">
                  <span className="text-gray-300 text-sm">
                    {ticket.category || 'Investigation'}
                  </span>
                </td>

                {/* Title */}
                <td className="p-4 max-w-xs">
                  <div className="text-white text-sm font-medium truncate">
                    {ticket.short_description || ticket.title || 'Sans titre'}
                  </div>
                  <div className="text-gray-400 text-xs mt-1 truncate">{ticket.description}</div>
                  {/* Converted to Incident Link */}
                  {ticket.status === 'converted_to_incident' && ticket.converted_incident_id && (
                    <div className="flex items-center gap-1 mt-2">
                      <RotateCcw size={12} className="text-violet-400" />
                      <span className="text-violet-300 text-xs">
                        → {ticket.converted_incident_id}
                      </span>
                    </div>
                  )}
                </td>

                {/* Created By */}
                <td className="p-4">
                  <div className="flex items-center space-x-2">
                    <User size={14} className="text-blue-400" />
                    <span className="text-blue-300 text-sm">
                      {ticket.user_email || ticket.user_id || 'Utilisateur inconnu'}
                    </span>
                  </div>
                </td>

                {/* Created Date */}
                <td className="p-4">
                  <div className="text-gray-300 text-sm">
                    {new Date(ticket.created_at).toLocaleDateString('fr-FR')}
                  </div>
                  <div className="text-gray-500 text-xs">{getTimeElapsed(ticket.created_at)}</div>
                </td>

                {/* Assigned To */}
                <td className="p-4">
                  <div className="flex items-center space-x-2">
                    <User size={14} className="text-gray-400" />
                    <span className="text-gray-300 text-sm">
                      {ticket.assigned_to || 'Non assigné'}
                    </span>
                  </div>
                </td>

                {/* Priority */}
                <td className="p-4">
                  <div className="flex items-center space-x-1">
                    {ticket.severity && <SeverityBadge severity={ticket.severity} size="sm" />}
                    {ticket.priority && <PriorityBadge priority={ticket.priority} size="sm" />}
                  </div>
                </td>

                {/* Actions */}
                <td className="p-4">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => onView(ticket)}
                      className="p-2 text-blue-400 hover:text-blue-300 hover:bg-blue-500/20 rounded-lg transition-colors duration-200"
                      title="Voir les détails"
                    >
                      <Eye size={16} />
                    </button>

                    {canEdit && (
                      <button
                        onClick={() => onEdit(ticket)}
                        className="p-2 text-yellow-400 hover:text-yellow-300 hover:bg-yellow-500/20 rounded-lg transition-colors duration-200"
                        title="Modifier"
                      >
                        <Edit size={16} />
                      </button>
                    )}

                    {canAssign && (
                      <button
                        onClick={() => onAssign(ticket)}
                        className="p-2 text-green-400 hover:text-green-300 hover:bg-green-500/20 rounded-lg transition-colors duration-200"
                        title="Assigner"
                      >
                        <UserPlus size={16} />
                      </button>
                    )}

                    {canConvert && onConvert && (
                      <button
                        onClick={() => onConvert(ticket)}
                        className="p-2 text-purple-400 hover:text-purple-300 hover:bg-purple-500/20 rounded-lg transition-colors duration-200"
                        title="Convertir en incident"
                      >
                        <ArrowRight size={16} />
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {tickets.length === 0 && (
        <div className="p-8 text-center text-gray-400">
          <AlertTriangle size={48} className="mx-auto mb-4 opacity-50" />
          <p className="text-lg">Aucun ticket trouvé</p>
          <p className="text-sm">Créez un nouveau ticket ou ajustez vos filtres</p>
        </div>
      )}
    </div>
  );
};
