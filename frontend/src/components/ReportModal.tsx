import React from 'react';
import {
  FileText,
  Download,
  AlertTriangle,
  CheckCircle,
  Clock,
  Shield,
  Target,
  Zap,
} from 'lucide-react';

interface ReportModalProps {
  report: any;
  onClose: () => void;
}

const ReportModal: React.FC<ReportModalProps> = ({ report, onClose }) => {
  const downloadReport = () => {
    const dataStr = JSON.stringify(report, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `rapport_${report.analysis_info?.analysis_id || 'analyse'}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity?.toUpperCase()) {
      case 'HIGH':
        return 'bg-red-500/20 text-red-300 border-red-500/30';
      case 'MEDIUM':
        return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30';
      case 'LOW':
        return 'bg-green-500/20 text-green-300 border-green-500/30';
      default:
        return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'ÉLEVÉ':
        return 'bg-red-500/20 text-red-300';
      case 'MODÉRÉ':
        return 'bg-yellow-500/20 text-yellow-300';
      default:
        return 'bg-blue-500/20 text-blue-300';
    }
  };

  if (!report?.report?.vulnerability_assessment_report) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
        <div className="bg-gray-800 rounded-2xl p-8 max-w-md">
          <div className="text-center">
            <AlertTriangle className="w-16 h-16 text-yellow-400 mx-auto mb-4" />
            <p className="text-gray-300">Format de rapport non reconnu</p>
            <button
              onClick={onClose}
              className="mt-4 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-xl"
            >
              Fermer
            </button>
          </div>
        </div>
      </div>
    );
  }

  const reportData = report.report.vulnerability_assessment_report;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl border border-gray-700 max-w-7xl w-full max-h-[95vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700 bg-gradient-to-r from-blue-900/30 to-purple-900/30">
          <div>
            <h2 className="text-2xl font-bold text-white flex items-center">
              <Shield className="w-7 h-7 mr-3 text-blue-400" />
              Rapport de Sécurité Complet
            </h2>
            <p className="text-gray-400 mt-1">
              Analyse IA • {reportData.metadata.total_scans} scans •{' '}
              {reportData.metadata.total_vulnerabilities} vulnérabilités
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-700 rounded-lg transition-colors text-gray-400 hover:text-white"
          >
            ✕
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(95vh-180px)]">
          {/* Résumé exécutif */}
          <div className="bg-gradient-to-r from-blue-800/30 to-purple-800/30 rounded-xl p-6 border border-blue-700/50 mb-8">
            <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
              📊 Résumé Exécutif
              <span className="ml-3 px-3 py-1 bg-green-500/20 text-green-300 text-sm rounded-full">
                {report.analysis_info?.options?.generate_full_report
                  ? 'Analyse Complète'
                  : 'Analyse Basique'}
              </span>
            </h3>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-6">
              <div className="text-center bg-blue-500/10 rounded-lg p-4 border border-blue-500/20">
                <Target className="w-8 h-8 text-blue-400 mx-auto mb-2" />
                <div className="text-2xl font-bold text-blue-400">
                  {reportData.metadata.total_scans}
                </div>
                <div className="text-sm text-blue-300">Cibles analysées</div>
              </div>
              <div className="text-center bg-red-500/10 rounded-lg p-4 border border-red-500/20">
                <AlertTriangle className="w-8 h-8 text-red-400 mx-auto mb-2" />
                <div className="text-2xl font-bold text-red-400">
                  {reportData.metadata.total_vulnerabilities}
                </div>
                <div className="text-sm text-red-300">Vulnérabilités</div>
              </div>
              <div className="text-center bg-purple-500/10 rounded-lg p-4 border border-purple-500/20">
                <Zap className="w-8 h-8 text-purple-400 mx-auto mb-2" />
                <div className="text-lg font-bold text-purple-400 font-mono">
                  {report.analysis_info?.analysis_id?.slice(0, 8)}...
                </div>
                <div className="text-sm text-purple-300">ID d'analyse</div>
              </div>
              <div className="text-center bg-green-500/10 rounded-lg p-4 border border-green-500/20">
                <Clock className="w-8 h-8 text-green-400 mx-auto mb-2" />
                <div className="text-sm font-bold text-green-400">
                  {report.analysis_info?.completed_at
                    ? new Date(report.analysis_info.completed_at).toLocaleDateString()
                    : 'N/A'}
                </div>
                <div className="text-sm text-green-300">Complété le</div>
              </div>
            </div>

            <div className="bg-gray-800/30 rounded-lg p-4">
              <h4 className="text-sm font-semibold text-gray-300 mb-2">📁 Fichier source</h4>
              <p className="text-white font-mono text-sm">
                {report.analysis_info?.filename || 'N/A'}
              </p>
            </div>
          </div>

          {/* Analyses détaillées par cible */}
          <div className="space-y-8">
            <h3 className="text-xl font-semibold text-white flex items-center">
              🎯 Analyses Détaillées par Cible
              <span className="ml-3 text-sm text-gray-400">
                ({Object.keys(reportData.scan_reports).length} cibles)
              </span>
            </h3>

            {Object.entries(reportData.scan_reports).map(
              ([scanId, scanReport]: [string, any], scanIndex) => (
                <div
                  key={scanId}
                  className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 rounded-xl border border-gray-700/50 overflow-hidden"
                >
                  {/* En-tête de la cible */}
                  <div className="bg-gradient-to-r from-gray-700/50 to-gray-800/50 p-6 border-b border-gray-700/50">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-xl font-semibold text-white flex items-center">
                          🎯 {scanReport.scan_metadata?.target || 'Cible inconnue'}
                          {scanReport.vulnerabilities?.[0]?.basic_info?.security_score && (
                            <span className="ml-3 px-3 py-1 bg-blue-500/20 text-blue-300 text-sm rounded-full border border-blue-500/30">
                              Score: {scanReport.vulnerabilities[0].basic_info.security_score}/100
                            </span>
                          )}
                        </h4>
                        <p className="text-sm text-gray-400 mt-1">
                          Scan #{scanIndex + 1} • ID: {scanId.slice(0, 8)}...
                        </p>
                      </div>
                      <div className="text-right">
                        <span
                          className={`px-4 py-2 rounded-full text-sm font-medium border ${
                            scanReport.vulnerabilities?.length > 0
                              ? 'bg-red-500/20 text-red-300 border-red-500/30'
                              : 'bg-green-500/20 text-green-300 border-green-500/30'
                          }`}
                        >
                          {scanReport.vulnerabilities?.length || 0} vulnérabilité(s)
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Contenu des vulnérabilités */}
                  <div className="p-6">
                    {scanReport.vulnerabilities && scanReport.vulnerabilities.length > 0 ? (
                      <div className="space-y-6">
                        {scanReport.vulnerabilities.map((vuln: any, vulnIndex: number) => (
                          <div
                            key={vulnIndex}
                            className="bg-gray-800/40 rounded-xl border-l-4 border-red-500 overflow-hidden"
                          >
                            {/* En-tête de la vulnérabilité */}
                            <div className="p-6 bg-gradient-to-r from-red-900/20 to-red-800/20">
                              <div className="flex items-start justify-between mb-4">
                                <div className="flex-1">
                                  <h5 className="text-lg font-semibold text-white mb-2">
                                    {vuln.basic_info?.name?.replace(/^\|\s*/, '') ||
                                      'Vulnérabilité sans nom'}
                                  </h5>
                                  <div className="flex items-center space-x-3 flex-wrap gap-2">
                                    {vuln.basic_info?.cve && (
                                      <span className="px-3 py-1 bg-blue-500/20 text-blue-300 text-sm rounded-full border border-blue-500/30">
                                        {vuln.basic_info.cve}
                                      </span>
                                    )}
                                    <span
                                      className={`px-3 py-1 rounded-full text-sm font-medium border ${getSeverityColor(vuln.basic_info?.severity)}`}
                                    >
                                      {vuln.basic_info?.severity || 'Inconnue'}
                                    </span>
                                    <span
                                      className={`px-3 py-1 rounded-full text-sm ${getRiskColor(vuln.basic_info?.risk_level)}`}
                                    >
                                      Risque: {vuln.basic_info?.risk_level || 'N/A'}
                                    </span>
                                  </div>
                                </div>
                              </div>

                              {/* Description */}
                              {vuln.basic_info?.description && (
                                <div className="mb-4">
                                  <h6 className="text-sm font-medium text-gray-300 mb-2">
                                    📝 Description
                                  </h6>
                                  <p className="text-gray-300 text-sm bg-gray-900/30 p-4 rounded-lg">
                                    {vuln.basic_info.description.replace(/^\|\s*/, '')}
                                  </p>
                                </div>
                              )}
                            </div>

                            {/* Analyse IA */}
                            {vuln.ai_analysis?.vulnerability_analysis && (
                              <div className="p-6 bg-gradient-to-r from-purple-900/20 to-indigo-900/20 border-t border-gray-700/50">
                                <h6 className="text-lg font-semibold text-purple-300 mb-4 flex items-center">
                                  🤖 Analyse IA Complète
                                </h6>

                                {/* Résumé */}
                                <div className="mb-6">
                                  <h7 className="text-sm font-medium text-purple-300 mb-2 block">
                                    📋 Résumé
                                  </h7>
                                  <p className="text-gray-300 text-sm bg-purple-500/10 p-4 rounded border-l-2 border-purple-500">
                                    {vuln.ai_analysis.vulnerability_analysis.summary}
                                  </p>
                                </div>

                                {/* Description technique */}
                                <div className="mb-6">
                                  <h7 className="text-sm font-medium text-blue-300 mb-2 block">
                                    🔧 Description technique
                                  </h7>
                                  <p className="text-gray-300 text-sm bg-blue-500/10 p-4 rounded border-l-2 border-blue-500">
                                    {vuln.ai_analysis.vulnerability_analysis.technical_description}
                                  </p>
                                </div>

                                {/* Évaluation d'impact */}
                                {vuln.ai_analysis.vulnerability_analysis.impact_assessment && (
                                  <div className="mb-6">
                                    <h7 className="text-sm font-medium text-red-300 mb-3 block">
                                      ⚠️ Évaluation d'impact
                                    </h7>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                      {Object.entries(
                                        vuln.ai_analysis.vulnerability_analysis.impact_assessment
                                      ).map(([key, value]: [string, any]) => (
                                        <div
                                          key={key}
                                          className="bg-red-500/10 p-3 rounded border-l-2 border-red-500"
                                        >
                                          <div className="text-sm">
                                            <span className="text-red-300 font-medium capitalize">
                                              {key.replace('_', ' ')}:
                                            </span>
                                            <p className="text-gray-300 mt-1">{value}</p>
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                )}

                                {/* Plan de remédiation */}
                                {vuln.ai_analysis.vulnerability_analysis.remediation && (
                                  <div className="mb-6">
                                    <h7 className="text-sm font-medium text-green-300 mb-3 block">
                                      💡 Plan de remédiation
                                    </h7>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                      {vuln.ai_analysis.vulnerability_analysis.remediation
                                        .immediate_actions && (
                                        <div className="bg-green-500/10 p-4 rounded border-l-2 border-green-500">
                                          <h8 className="text-sm font-medium text-green-300 mb-2">
                                            🚨 Actions immédiates
                                          </h8>
                                          <ul className="text-sm text-gray-300 space-y-1">
                                            {vuln.ai_analysis.vulnerability_analysis.remediation.immediate_actions.map(
                                              (action: string, i: number) => (
                                                <li key={i} className="flex items-start">
                                                  <span className="text-green-400 mr-2 mt-1">
                                                    •
                                                  </span>
                                                  <span>{action}</span>
                                                </li>
                                              )
                                            )}
                                          </ul>
                                        </div>
                                      )}

                                      {vuln.ai_analysis.vulnerability_analysis.remediation
                                        .long_term_solutions && (
                                        <div className="bg-blue-500/10 p-4 rounded border-l-2 border-blue-500">
                                          <h8 className="text-sm font-medium text-blue-300 mb-2">
                                            🔄 Solutions long terme
                                          </h8>
                                          <ul className="text-sm text-gray-300 space-y-1">
                                            {vuln.ai_analysis.vulnerability_analysis.remediation.long_term_solutions.map(
                                              (solution: string, i: number) => (
                                                <li key={i} className="flex items-start">
                                                  <span className="text-blue-400 mr-2 mt-1">•</span>
                                                  <span>{solution}</span>
                                                </li>
                                              )
                                            )}
                                          </ul>
                                        </div>
                                      )}
                                    </div>

                                    <div className="flex items-center space-x-6 text-sm bg-gray-800/30 p-3 rounded">
                                      <div className="flex items-center space-x-2">
                                        <span className="text-gray-400">Priorité:</span>
                                        <span
                                          className={`px-2 py-1 rounded text-xs ${
                                            vuln.ai_analysis.vulnerability_analysis.remediation
                                              .priority === 'High'
                                              ? 'bg-red-500/20 text-red-300'
                                              : vuln.ai_analysis.vulnerability_analysis.remediation
                                                    .priority === 'Medium'
                                                ? 'bg-yellow-500/20 text-yellow-300'
                                                : 'bg-green-500/20 text-green-300'
                                          }`}
                                        >
                                          {
                                            vuln.ai_analysis.vulnerability_analysis.remediation
                                              .priority
                                          }
                                        </span>
                                      </div>
                                      <div className="flex items-center space-x-2">
                                        <span className="text-gray-400">Effort estimé:</span>
                                        <span className="text-white text-xs">
                                          {
                                            vuln.ai_analysis.vulnerability_analysis.remediation
                                              .estimated_effort
                                          }
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                )}

                                {/* Responsabilités */}
                                {vuln.ai_analysis.vulnerability_analysis
                                  .responsibility_assignment && (
                                  <div className="bg-gray-800/30 p-4 rounded">
                                    <h7 className="text-sm font-medium text-orange-300 mb-3 block">
                                      👥 Attribution des responsabilités
                                    </h7>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                      <div>
                                        <span className="text-orange-300">
                                          Responsable principal:
                                        </span>
                                        <p className="text-white mt-1">
                                          {
                                            vuln.ai_analysis.vulnerability_analysis
                                              .responsibility_assignment.primary_responsible
                                          }
                                        </p>
                                      </div>
                                      <div>
                                        <span className="text-orange-300">
                                          Responsable secondaire:
                                        </span>
                                        <p className="text-white mt-1">
                                          {
                                            vuln.ai_analysis.vulnerability_analysis
                                              .responsibility_assignment.secondary_responsible
                                          }
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </div>
                            )}

                            {/* Informations techniques */}
                            <div className="p-6 bg-gray-900/30 border-t border-gray-700/50">
                              <h6 className="text-sm font-medium text-gray-300 mb-3">
                                🔧 Informations techniques
                              </h6>
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                <div>
                                  <span className="text-gray-400">Début scan:</span>
                                  <div className="text-white mt-1 text-xs">
                                    {vuln.basic_info?.start_date
                                      ? new Date(vuln.basic_info.start_date).toLocaleString()
                                      : 'N/A'}
                                  </div>
                                </div>
                                <div>
                                  <span className="text-gray-400">Fin scan:</span>
                                  <div className="text-white mt-1 text-xs">
                                    {vuln.basic_info?.end_date
                                      ? new Date(vuln.basic_info.end_date).toLocaleString()
                                      : 'N/A'}
                                  </div>
                                </div>
                                <div>
                                  <span className="text-gray-400">Durée:</span>
                                  <div className="text-white mt-1 text-xs">
                                    {vuln.basic_info?.start_date && vuln.basic_info?.end_date
                                      ? `${Math.round((new Date(vuln.basic_info.end_date).getTime() - new Date(vuln.basic_info.start_date).getTime()) / 60000)} min`
                                      : 'N/A'}
                                  </div>
                                </div>
                                <div>
                                  <span className="text-gray-400">Outils:</span>
                                  <div className="text-white mt-1 text-xs">
                                    {vuln.basic_info?.tools_used
                                      ? vuln.basic_info.tools_used.split(',').length + ' outils'
                                      : 'N/A'}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <CheckCircle className="w-20 h-20 text-green-400 mx-auto mb-4" />
                        <h4 className="text-xl font-semibold text-green-300 mb-2">
                          Aucune vulnérabilité détectée
                        </h4>
                        <p className="text-gray-400">
                          Cette cible ne présente aucun problème de sécurité identifié
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-700 bg-gray-800/30">
          <div className="text-sm text-gray-400">
            Rapport généré le{' '}
            {report.analysis_info?.completed_at
              ? new Date(report.analysis_info.completed_at).toLocaleString()
              : 'Date inconnue'}
          </div>
          <div className="flex space-x-3">
            <button
              onClick={downloadReport}
              className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl transition-all duration-200"
            >
              <Download className="w-4 h-4" />
              <span>Télécharger JSON</span>
            </button>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-xl transition-colors"
            >
              Fermer
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReportModal;
