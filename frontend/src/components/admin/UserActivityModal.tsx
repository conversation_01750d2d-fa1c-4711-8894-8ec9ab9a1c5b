import { useState, useEffect } from 'react';
import {
  X,
  Activity,
  Calendar,
  Clock,
  User,
  Shield,
  Scan,
  Download,
  Settings,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Filter,
  Search,
  RefreshCw,
  ExternalLink,
} from 'lucide-react';
import { activityService, ActivityLog, ActivityResponse } from '../../services/activityService';

interface UserActivityModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  userName: string;
}

export default function UserActivityModal({
  isOpen,
  onClose,
  userId,
  userName,
}: UserActivityModalProps) {
  const [activities, setActivities] = useState<ActivityLog[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [offset, setOffset] = useState(0);
  const [filters, setFilters] = useState({
    category: '',
    activity_type: '',
    success_only: '',
    start_date: '',
    end_date: '',
  });
  const [searchQuery, setSearchQuery] = useState('');

  const limit = 50;

  const categoryIcons: { [key: string]: any } = {
    AUTH: Shield,
    SCAN: Scan,
    EXPORT: Download,
    USER_MGMT: User,
    INCIDENT: AlertTriangle,
    CONFIG: Settings,
    SECURITY: Shield,
    SYSTEM: Activity,
  };

  const categoryColors: { [key: string]: string } = {
    AUTH: 'text-purple-400 border-purple-500/30',
    SCAN: 'text-blue-400 border-blue-500/30',
    EXPORT: 'text-green-400 border-green-500/30',
    USER_MGMT: 'text-orange-400 border-orange-500/30',
    INCIDENT: 'text-red-400 border-red-500/30',
    CONFIG: 'text-yellow-400 border-yellow-500/30',
    SECURITY: 'text-red-500 border-red-500/30',
    SYSTEM: 'text-gray-400 border-gray-500/30',
  };

  const fetchActivities = async (reset = false) => {
    try {
      setLoading(true);
      const currentOffset = reset ? 0 : offset;

      const filterParams = {
        limit,
        offset: currentOffset,
        ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v !== '')),
      };

      const data = await activityService.getUserActivities(userId, filterParams);

      if (reset) {
        setActivities(data.activities);
        setOffset(data.limit);
      } else {
        setActivities((prev) => [...prev, ...data.activities]);
        setOffset((prev) => prev + data.limit);
      }

      setTotalCount(data.total_count);
      setHasMore(data.has_more);
    } catch (error) {
      console.error('Error fetching activities:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen && userId) {
      fetchActivities(true);
    }
  }, [isOpen, userId, filters]);

  const handleFilterChange = (key: string, value: string) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
    setOffset(0);
  };

  const handleLoadMore = () => {
    if (!loading && hasMore) {
      fetchActivities(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  const getRelativeTime = (dateString: string) => {
    const now = new Date();
    const activityDate = new Date(dateString);
    const diffInSeconds = Math.floor((now.getTime() - activityDate.getTime()) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
    return formatDate(dateString);
  };

  const getActivityIcon = (category: string) => {
    const IconComponent = categoryIcons[category] || Activity;
    const colorClass = categoryColors[category] || 'text-gray-400';
    return <IconComponent size={16} className={colorClass} />;
  };

  const filteredActivities = activities.filter((activity) => {
    if (!searchQuery) return true;
    const searchLower = searchQuery.toLowerCase();
    return (
      activity.activity_description.toLowerCase().includes(searchLower) ||
      activity.username.toLowerCase().includes(searchLower) ||
      JSON.stringify(activity.details).toLowerCase().includes(searchLower)
    );
  });

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800/95 backdrop-blur-xl border border-gray-700/50 rounded-2xl max-w-6xl w-full h-[90vh] shadow-2xl flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-purple-600/20 border border-purple-500/30 rounded-xl flex items-center justify-center">
              <Activity size={24} className="text-purple-400" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white">User Activity Log</h2>
              <p className="text-gray-400 text-sm">
                Activity history for <span className="text-purple-400 font-medium">{userName}</span>
              </p>
            </div>
          </div>

          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white p-2 rounded-lg hover:bg-gray-700/50 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Filters */}
        <div className="p-6 border-b border-gray-700 bg-gray-800/50">
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {/* Search */}
            <div className="lg:col-span-2">
              <div className="relative">
                <Search
                  size={16}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                />
                <input
                  type="text"
                  placeholder="Search activities..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
            </div>

            {/* Category Filter */}
            <select
              value={filters.category}
              onChange={(e) => handleFilterChange('category', e.target.value)}
              className="px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="">All Categories</option>
              <option value="AUTH">Authentication</option>
              <option value="SCAN">Security Scanning</option>
              <option value="EXPORT">Data Export</option>
              <option value="USER_MGMT">User Management</option>
              <option value="INCIDENT">Incident Management</option>
              <option value="CONFIG">Configuration</option>
              <option value="SECURITY">Security Events</option>
              <option value="SYSTEM">System Operations</option>
            </select>

            {/* Success Filter */}
            <select
              value={filters.success_only}
              onChange={(e) => handleFilterChange('success_only', e.target.value)}
              className="px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="">All Results</option>
              <option value="true">Successful Only</option>
              <option value="false">Failed Only</option>
            </select>

            {/* Date Range */}
            <input
              type="date"
              value={filters.start_date}
              onChange={(e) => handleFilterChange('start_date', e.target.value)}
              className="px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            />

            <input
              type="date"
              value={filters.end_date}
              onChange={(e) => handleFilterChange('end_date', e.target.value)}
              className="px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>

          {/* Stats */}
          <div className="mt-4 flex items-center justify-between">
            <div className="text-sm text-gray-400">
              Showing {filteredActivities.length} of {totalCount} activities
            </div>
            <button
              onClick={() => fetchActivities(true)}
              disabled={loading}
              className="flex items-center space-x-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 text-white rounded-lg transition-colors"
            >
              <RefreshCw size={14} className={loading ? 'animate-spin' : ''} />
              <span>Refresh</span>
            </button>
          </div>
        </div>

        {/* Activity List */}
        <div className="flex-1 overflow-y-auto p-6">
          {loading && activities.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64">
              <div className="relative">
                <div className="animate-spin rounded-full h-12 w-12 border-4 border-gray-600 border-t-purple-400"></div>
                <div className="absolute inset-0 rounded-full border-4 border-transparent border-r-purple-400 animate-pulse"></div>
              </div>
              <p className="text-gray-400 mt-4 text-sm">Loading activities...</p>
            </div>
          ) : filteredActivities.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-gray-400">
              <div className="w-20 h-20 bg-gray-700/50 rounded-full flex items-center justify-center mb-6 border border-gray-600/30">
                <Activity size={32} className="text-gray-500" />
              </div>
              <h3 className="text-lg font-medium text-gray-300 mb-2">No Activities Found</h3>
              <p className="text-sm text-gray-500 text-center max-w-md">
                {Object.values(filters).some((v) => v !== '')
                  ? 'No activities match your current filters. Try adjusting your search criteria.'
                  : 'This user has no recorded activities yet.'}
              </p>
            </div>
          ) : (
            <div className="space-y-6">
              {filteredActivities.map((activity, index) => (
                <div
                  key={activity._id}
                  className={`bg-gray-800/50 border border-gray-700 rounded-lg p-6 hover:bg-gray-700/50 transition-all duration-300 transform hover:scale-[1.01] shadow-lg hover:shadow-xl animate-in slide-in-from-bottom-4 ${
                    activity.success ? 'hover:border-green-500/50' : 'hover:border-red-500/50'
                  }`}
                  style={{ animationDelay: `${index * 50}ms` }}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4 flex-1">
                      {/* Activity Icon */}
                      <div
                        className={`w-12 h-12 rounded-xl flex items-center justify-center flex-shrink-0 mt-1 ${
                          activity.success
                            ? 'bg-purple-500/20 border border-purple-500/30'
                            : 'bg-red-500/20 border border-red-500/30'
                        }`}
                      >
                        {getActivityIcon(activity.category)}
                      </div>

                      {/* Activity Details */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-3 mb-2">
                          <h4 className="text-white font-semibold text-lg">
                            {activity.activity_description}
                          </h4>
                          <div className="flex items-center space-x-2">
                            {activity.success ? (
                              <div className="flex items-center space-x-1 px-3 py-1 bg-green-500/20 border border-green-500/30 rounded-lg">
                                <CheckCircle size={14} className="text-green-400" />
                                <span className="text-green-400 text-xs font-medium">Success</span>
                              </div>
                            ) : (
                              <div className="flex items-center space-x-1 px-3 py-1 bg-red-500/20 border border-red-500/30 rounded-lg">
                                <XCircle size={14} className="text-red-400" />
                                <span className="text-red-400 text-xs font-medium">Failed</span>
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="flex flex-wrap items-center gap-3 text-sm mb-3">
                          <div
                            className="flex items-center space-x-2 px-3 py-1 bg-purple-500/10 border border-purple-500/30 rounded-lg"
                            title={formatDate(activity.timestamp)}
                          >
                            <Clock size={14} className="text-purple-400" />
                            <span className="text-purple-300 font-medium">
                              {getRelativeTime(activity.timestamp)}
                            </span>
                          </div>

                          <div className="flex items-center space-x-2 px-3 py-1 bg-gray-700/50 border border-gray-600/30 rounded-lg">
                            <User size={14} className="text-gray-400" />
                            <span className="text-gray-300 font-medium">{activity.username}</span>
                          </div>

                          <div
                            className={`px-3 py-1 rounded-lg text-xs font-medium border ${categoryColors[activity.category]} bg-gray-700/30`}
                          >
                            {activity.category_description}
                          </div>

                          {activity.ip_address && (
                            <div className="flex items-center space-x-2 px-3 py-1 bg-gray-700/50 border border-gray-600/30 rounded-lg">
                              <span className="text-gray-400 text-xs">IP:</span>
                              <span className="font-mono text-xs text-gray-300">
                                {activity.ip_address}
                              </span>
                            </div>
                          )}

                          {activity.user_agent && (
                            <div className="flex items-center space-x-2 px-3 py-1 bg-gray-700/50 border border-gray-600/30 rounded-lg max-w-xs">
                              <span className="text-gray-400 text-xs">Browser:</span>
                              <span
                                className="text-xs text-gray-300 truncate"
                                title={activity.user_agent}
                              >
                                {activity.user_agent.split(' ')[0]}
                              </span>
                            </div>
                          )}
                        </div>

                        {/* Activity Details */}
                        {Object.keys(activity.details).length > 0 && (
                          <div className="bg-gradient-to-r from-gray-800/40 to-gray-700/20 border border-gray-600/30 rounded-lg p-4 mt-4">
                            <div className="flex items-center space-x-2 mb-3">
                              <Settings size={14} className="text-gray-400" />
                              <span className="text-sm text-gray-400 font-medium">
                                Activity Details
                              </span>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              {Object.entries(activity.details).map(([key, value]) => (
                                <div
                                  key={key}
                                  className="bg-gray-800/30 border border-gray-600/20 rounded-lg p-3"
                                >
                                  <div className="flex items-center space-x-2 mb-1">
                                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                    <span className="text-xs font-medium text-blue-400 capitalize">
                                      {key.replace(/_/g, ' ')}
                                    </span>
                                  </div>
                                  <div className="text-sm text-gray-300 break-all pl-4">
                                    {typeof value === 'object' ? (
                                      <pre className="text-xs bg-gray-900/50 p-2 rounded border border-gray-600/20 overflow-x-auto">
                                        {JSON.stringify(value, null, 2)}
                                      </pre>
                                    ) : (
                                      <span className="font-mono">{String(value)}</span>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {/* Load More Button */}
              {hasMore && (
                <div className="flex justify-center pt-6">
                  <button
                    onClick={handleLoadMore}
                    disabled={loading}
                    className="flex items-center space-x-2 px-8 py-3 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                  >
                    {loading ? (
                      <>
                        <RefreshCw size={16} className="animate-spin" />
                        <span>Loading more activities...</span>
                      </>
                    ) : (
                      <>
                        <ExternalLink size={16} />
                        <span>Load More Activities</span>
                      </>
                    )}
                  </button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-700 bg-gray-800/50">
          <div className="text-sm text-gray-400">Total activities: {totalCount}</div>
          <button
            onClick={onClose}
            className="px-6 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}
