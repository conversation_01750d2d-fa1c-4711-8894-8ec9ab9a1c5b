/* Complex and sophisticated AI animations for the Analytics page */

/* 3D Cyberpunk utility classes */
.perspective-1000 {
  perspective: 1000px;
}

.transform-gpu {
  transform: translateZ(0);
  backface-visibility: hidden;
}

.rotateX-1 {
  transform: rotateX(1deg);
}

.rotateX-2 {
  transform: rotateX(2deg);
}

.rotateY-1 {
  transform: rotateY(1deg);
}

.rotateY-90 {
  transform: rotateY(90deg);
}

.rotateY-180 {
  transform: rotateY(180deg);
}

.rotateY-270 {
  transform: rotateY(270deg);
}

.rotateY-360 {
  transform: rotateY(360deg);
}

.translateZ-2 {
  transform: translateZ(2px);
}

.translateZ-4 {
  transform: translateZ(4px);
}

.translateZ-8 {
  transform: translateZ(8px);
}

.translateY-full {
  transform: translateY(100%);
}

/* Animation de morphing organique */
@keyframes organic-morph {
  0%,
  100% {
    border-radius: 2rem 2rem 2rem 2rem;
    transform: scale(1) rotate(0deg);
  }
  25% {
    border-radius: 3rem 1rem 3rem 1rem;
    transform: scale(1.02) rotate(0.5deg);
  }
  50% {
    border-radius: 1rem 3rem 1rem 3rem;
    transform: scale(1.01) rotate(-0.5deg);
  }
  75% {
    border-radius: 2.5rem 1.5rem 2.5rem 1.5rem;
    transform: scale(1.03) rotate(0.3deg);
  }
}

/* Data flow animation */
@keyframes data-flow {
  0% {
    stroke-dasharray: 0 100;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 50 50;
    stroke-dashoffset: -25;
  }
  100% {
    stroke-dasharray: 100 0;
    stroke-dashoffset: -100;
  }
}

/* Animation de pulsation complexe */
@keyframes complex-pulse {
  0%,
  100% {
    opacity: 0.4;
    transform: scale(1) rotate(0deg);
    filter: hue-rotate(0deg);
  }
  25% {
    opacity: 0.8;
    transform: scale(1.1) rotate(90deg);
    filter: hue-rotate(90deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.2) rotate(180deg);
    filter: hue-rotate(180deg);
  }
  75% {
    opacity: 0.6;
    transform: scale(1.05) rotate(270deg);
    filter: hue-rotate(270deg);
  }
}

/* Animation de scan holographique subtile */
@keyframes holographic-scan {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Animation de brillance subtile pour les boutons */
@keyframes subtle-shine {
  0% {
    transform: translateX(-100%) skewX(-12deg);
    opacity: 0;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    transform: translateX(100%) skewX(-12deg);
    opacity: 0;
  }
}

/* Animation de glow doux */
@keyframes soft-glow {
  0%,
  100% {
    box-shadow: 0 0 5px rgba(6, 182, 212, 0.2);
  }
  50% {
    box-shadow:
      0 0 15px rgba(6, 182, 212, 0.4),
      0 0 25px rgba(139, 92, 246, 0.2);
  }
}

/* Animation de pulsation IA */
@keyframes ai-pulse {
  0%,
  100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

/* Animation de particules flottantes */
@keyframes float-particles {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  33% {
    transform: translateY(-10px) rotate(120deg);
    opacity: 1;
  }
  66% {
    transform: translateY(5px) rotate(240deg);
    opacity: 0.7;
  }
}

/* Electric circuit animation */
@keyframes electric-flow {
  0% {
    stroke-dashoffset: 100;
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
  100% {
    stroke-dashoffset: 0;
    opacity: 0.3;
  }
}

/* Animation de glow pulsant */
@keyframes glow-pulse {
  0%,
  100% {
    box-shadow: 0 0 5px rgba(6, 182, 212, 0.3);
  }
  50% {
    box-shadow:
      0 0 20px rgba(6, 182, 212, 0.6),
      0 0 30px rgba(139, 92, 246, 0.4);
  }
}

/* Animation de rotation 3D */
@keyframes rotate-3d {
  0% {
    transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
  }
  25% {
    transform: perspective(1000px) rotateY(90deg) rotateX(5deg);
  }
  50% {
    transform: perspective(1000px) rotateY(180deg) rotateX(0deg);
  }
  75% {
    transform: perspective(1000px) rotateY(270deg) rotateX(-5deg);
  }
  100% {
    transform: perspective(1000px) rotateY(360deg) rotateX(0deg);
  }
}

/* Data scan animation */
@keyframes data-scan {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Animation de matrice */
@keyframes matrix-rain {
  0% {
    transform: translateY(-100vh);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh);
    opacity: 0;
  }
}

/* Utility classes for subtle AI animations */
.ai-holographic-scan {
  animation: holographic-scan 4s infinite;
}

.ai-pulse {
  animation: ai-pulse 3s infinite ease-in-out;
}

.ai-float {
  animation: float-particles 6s infinite ease-in-out;
}

.ai-electric {
  stroke-dasharray: 10;
  animation: electric-flow 3s infinite linear;
}

.ai-glow {
  animation: soft-glow 4s infinite ease-in-out;
}

.ai-rotate-3d {
  animation: rotate-3d 15s infinite linear;
}

.ai-data-scan {
  background: linear-gradient(90deg, transparent, rgba(6, 182, 212, 0.2), transparent);
  background-size: 200% 100%;
  animation: data-scan 3s infinite;
}

.ai-matrix {
  animation: matrix-rain 12s infinite linear;
}

/* Animation de brillance subtile */
.ai-subtle-shine {
  animation: subtle-shine 3s infinite;
}

/* Animations complexes pour formes organiques */
.ai-organic-morph {
  animation: organic-morph 8s infinite ease-in-out;
}

.ai-data-flow {
  animation: data-flow 4s infinite linear;
}

.ai-complex-pulse {
  animation: complex-pulse 6s infinite ease-in-out;
}

/* Neural network animation */
@keyframes neural-network {
  0% {
    stroke-dasharray: 0 20;
    opacity: 0.3;
  }
  50% {
    stroke-dasharray: 10 10;
    opacity: 1;
  }
  100% {
    stroke-dasharray: 20 0;
    opacity: 0.3;
  }
}

.ai-neural-network {
  animation: neural-network 3s infinite ease-in-out;
}

/* Animation de particules quantiques */
@keyframes quantum-particle {
  0%,
  100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.6;
  }
  25% {
    transform: translate(10px, -5px) scale(1.2);
    opacity: 1;
  }
  50% {
    transform: translate(-5px, 10px) scale(0.8);
    opacity: 0.8;
  }
  75% {
    transform: translate(-10px, -10px) scale(1.1);
    opacity: 0.9;
  }
}

.ai-quantum-particle {
  animation: quantum-particle 4s infinite ease-in-out;
}

/* Animation de scan radar 3D */
@keyframes radar-3d {
  0% {
    transform: rotateY(0deg) rotateX(0deg);
    opacity: 0.3;
  }
  25% {
    transform: rotateY(90deg) rotateX(5deg);
    opacity: 0.8;
  }
  50% {
    transform: rotateY(180deg) rotateX(0deg);
    opacity: 1;
  }
  75% {
    transform: rotateY(270deg) rotateX(-5deg);
    opacity: 0.6;
  }
  100% {
    transform: rotateY(360deg) rotateX(0deg);
    opacity: 0.3;
  }
}

.ai-radar-3d {
  animation: radar-3d 6s infinite linear;
  transform-style: preserve-3d;
}

/* Animation de hologramme */
@keyframes hologram {
  0%,
  100% {
    opacity: 0.7;
    transform: perspective(1000px) rotateY(0deg) translateZ(0px);
    filter: hue-rotate(0deg) brightness(1);
  }
  25% {
    opacity: 0.9;
    transform: perspective(1000px) rotateY(5deg) translateZ(10px);
    filter: hue-rotate(90deg) brightness(1.2);
  }
  50% {
    opacity: 1;
    transform: perspective(1000px) rotateY(0deg) translateZ(20px);
    filter: hue-rotate(180deg) brightness(1.4);
  }
  75% {
    opacity: 0.8;
    transform: perspective(1000px) rotateY(-5deg) translateZ(10px);
    filter: hue-rotate(270deg) brightness(1.1);
  }
}

.ai-hologram {
  animation: hologram 5s infinite ease-in-out;
}

/* Advanced Cyberpunk animations */
@keyframes cyberpunk-glow {
  0%,
  100% {
    box-shadow:
      0 0 5px rgba(6, 182, 212, 0.5),
      0 0 10px rgba(6, 182, 212, 0.3),
      0 0 15px rgba(6, 182, 212, 0.2);
  }
  25% {
    box-shadow:
      0 0 10px rgba(139, 92, 246, 0.5),
      0 0 20px rgba(139, 92, 246, 0.3),
      0 0 30px rgba(139, 92, 246, 0.2);
  }
  50% {
    box-shadow:
      0 0 15px rgba(236, 72, 153, 0.5),
      0 0 25px rgba(236, 72, 153, 0.3),
      0 0 35px rgba(236, 72, 153, 0.2);
  }
  75% {
    box-shadow:
      0 0 10px rgba(16, 185, 129, 0.5),
      0 0 20px rgba(16, 185, 129, 0.3),
      0 0 30px rgba(16, 185, 129, 0.2);
  }
}

.ai-cyberpunk-glow {
  animation: cyberpunk-glow 4s infinite ease-in-out;
}

/* Animation de cube 3D rotatif */
@keyframes cube-rotate {
  0% {
    transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg);
  }
  25% {
    transform: rotateX(90deg) rotateY(0deg) rotateZ(0deg);
  }
  50% {
    transform: rotateX(90deg) rotateY(90deg) rotateZ(0deg);
  }
  75% {
    transform: rotateX(90deg) rotateY(90deg) rotateZ(90deg);
  }
  100% {
    transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg);
  }
}

.ai-cube-rotate {
  animation: cube-rotate 8s infinite linear;
  transform-style: preserve-3d;
}

/* Animation de scan vertical Matrix */
@keyframes matrix-scan {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100%);
    opacity: 0;
  }
}

.ai-matrix-scan {
  animation: matrix-scan 3s infinite ease-in-out;
}

/* Animation de typing cyberpunk */
@keyframes cyberpunk-typing {
  0% {
    width: 0;
    border-right: 2px solid #06b6d4;
  }
  50% {
    border-right: 2px solid #06b6d4;
  }
  100% {
    width: 100%;
    border-right: 2px solid transparent;
  }
}

.ai-cyberpunk-typing {
  overflow: hidden;
  white-space: nowrap;
  animation: cyberpunk-typing 2s steps(20, end) infinite;
}

/* Orbital animation */
@keyframes orbital-rotation {
  0% {
    transform: rotateZ(0deg);
  }
  100% {
    transform: rotateZ(360deg);
  }
}

/* Animation orbitale pour les particules */
@keyframes orbit {
  from {
    transform: rotate(0deg) translateX(40px) rotate(0deg);
  }
  to {
    transform: rotate(360deg) translateX(40px) rotate(-360deg);
  }
}

.ai-orbital {
  animation: orbital-rotation 4s infinite linear;
}

.ai-orbital-reverse {
  animation: orbital-rotation 3s infinite linear reverse;
}

.ai-orbit {
  animation: orbit 8s infinite linear;
}

/* Energy flow animation */
@keyframes energy-flow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.ai-energy-flow {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(6, 182, 212, 0.3) 25%,
    rgba(139, 92, 246, 0.5) 50%,
    rgba(236, 72, 153, 0.3) 75%,
    transparent 100%
  );
  background-size: 200% 100%;
  animation: energy-flow 2s infinite ease-in-out;
}

/* Animation de glitch cyberpunk */
@keyframes cyberpunk-glitch {
  0%,
  100% {
    transform: translate(0);
    filter: hue-rotate(0deg);
  }
  10% {
    transform: translate(-2px, 1px);
    filter: hue-rotate(90deg);
  }
  20% {
    transform: translate(2px, -1px);
    filter: hue-rotate(180deg);
  }
  30% {
    transform: translate(-1px, 2px);
    filter: hue-rotate(270deg);
  }
  40% {
    transform: translate(1px, -2px);
    filter: hue-rotate(360deg);
  }
  50% {
    transform: translate(-2px, -1px);
    filter: hue-rotate(180deg);
  }
  60% {
    transform: translate(2px, 1px);
    filter: hue-rotate(90deg);
  }
  70% {
    transform: translate(-1px, -2px);
    filter: hue-rotate(270deg);
  }
  80% {
    transform: translate(1px, 2px);
    filter: hue-rotate(45deg);
  }
  90% {
    transform: translate(-2px, 1px);
    filter: hue-rotate(315deg);
  }
}

.ai-cyberpunk-glitch {
  animation: cyberpunk-glitch 0.3s infinite;
}

/* Neon pulse animation */
@keyframes neon-pulse {
  0%,
  100% {
    text-shadow:
      0 0 5px currentColor,
      0 0 10px currentColor,
      0 0 15px currentColor;
    opacity: 1;
  }
  50% {
    text-shadow:
      0 0 10px currentColor,
      0 0 20px currentColor,
      0 0 30px currentColor,
      0 0 40px currentColor;
    opacity: 0.8;
  }
}

.ai-neon-pulse {
  animation: neon-pulse 2s infinite ease-in-out;
}

/* 3D hover effect for cards */
.ai-card-3d {
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
}

.ai-card-3d:hover {
  transform: perspective(1000px) rotateX(5deg) rotateY(5deg) translateZ(20px);
}

/* Button shine effect */
.ai-button-shine {
  position: relative;
  overflow: hidden;
}

.ai-button-shine::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.ai-button-shine:hover::before {
  left: 100%;
}

/* AI loading animation */
@keyframes ai-loading {
  0%,
  20% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  80%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.ai-loading {
  animation: ai-loading 1.5s infinite;
}

/* Radar scan effect */
@keyframes radar-scan {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.ai-radar {
  animation: radar-scan 4s infinite linear;
}

/* Text typing effect */
@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

.ai-typing {
  overflow: hidden;
  white-space: nowrap;
  border-right: 2px solid rgba(6, 182, 212, 0.7);
  animation:
    typing 3s steps(40, end),
    blink-caret 0.75s step-end infinite;
}

@keyframes blink-caret {
  from,
  to {
    border-color: transparent;
  }
  50% {
    border-color: rgba(6, 182, 212, 0.7);
  }
}

/* Toast animations */
@keyframes slide-in-from-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes shrink {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

.animate-in {
  animation-duration: 0.3s;
  animation-timing-function: ease-out;
  animation-fill-mode: both;
}

.slide-in-from-right {
  animation-name: slide-in-from-right;
}
