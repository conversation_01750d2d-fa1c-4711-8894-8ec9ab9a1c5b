import { useState, useEffect } from 'react';
import { AlertTriangle, Plus, Search, RefreshCw, FileDown, ShieldAlert } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useRole } from '../hooks/useRole';
import { useModalContext } from '../contexts/ModalContext';
import { useConfirmation } from '../hooks/useConfirmation';
import Cookies from 'js-cookie';
import {
  InvestigationTicket,
  Incident,
  getTickets,
  getIncidents,
  closeTicket,
  reopenTicket,
  deleteTicket,
  closeIncident,
  reopenIncident,
  deleteIncident,
} from '../services/incidentService';
import CreateTicketForm from '../components/incidents/CreateTicketForm';
import TicketDetailsModal from '../components/incidents/TicketDetailsModal';
import { EnhancedTicketCard } from '../components/incidents/EnhancedTicketCard';

import { EnhancedIncidentCard } from '../components/incidents/EnhancedIncidentCard';
import { IncidentDetailsModal } from '../components/incidents/IncidentDetailsModal';
import { IncidentDashboard } from '../components/incidents/IncidentDashboard';

export default function Incidents() {
  const { user, token } = useAuth();
  const { isAdmin } = useRole();
  const { showAlert } = useModalContext();
  const { confirm, ConfirmationComponent } = useConfirmation();
  const [activeTab, setActiveTab] = useState<'tickets' | 'incidents'>('tickets');
  const [tickets, setTickets] = useState<InvestigationTicket[]>([]);
  const [incidents, setIncidents] = useState<Incident[]>([]);
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState<{ [key: string]: any }>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [severityFilter, setSeverityFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState<InvestigationTicket | null>(null);
  const [showTicketDetails, setShowTicketDetails] = useState(false);
  const [selectedIncident, setSelectedIncident] = useState<Incident | null>(null);
  const [showIncidentDetails, setShowIncidentDetails] = useState(false);
  const [ticketModalMode, setTicketModalMode] = useState<'view' | 'edit' | 'assign' | 'convert'>(
    'view'
  );

  // Load data on component mount
  useEffect(() => {
    console.log('🚀 useEffect triggered with:', { user, isAdmin, token: !!token });
    loadTickets();
    loadIncidents();
    loadUsers();
  }, [user, isAdmin, token]);

  const loadTickets = async () => {
    try {
      setLoading(true);
      const data = await getTickets();
      setTickets(data);
    } catch (error) {
      console.error('Failed to load tickets:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadIncidents = async () => {
    try {
      const data = await getIncidents();
      setIncidents(data);
    } catch (error) {
      console.error('Failed to load incidents:', error);
    }
  };

  const loadUsers = async () => {
    console.log('🔍 loadUsers called');
    console.log('🔍 token:', token);
    console.log('🔍 isAdmin:', isAdmin);
    console.log('🔍 user:', user);

    try {
      if (user) {
        console.log('✅ User exists, setting user info...');
        const userId = user.id || user._id;
        console.log('🆔 User ID found:', userId);

        if (userId) {
          const userMap = {
            [userId]: {
              _id: userId,
              email: user.email,
              username: user.username,
              role: user.role,
            },
          };
          setUsers(userMap);
          console.log('✅ User map set:', userMap);
        } else {
          console.error('❌ No user ID found in user object');
        }
      } else {
        console.error('❌ No user object found');
      }
    } catch (error) {
      console.error('💥 Error loading users:', error);
    }
  };

  // Fonction de rafraîchissement
  const handleRefresh = async () => {
    setLoading(true);
    try {
      await Promise.all([loadTickets(), loadIncidents(), loadUsers()]);

      // Debug: Log data for statistics verification
      console.log('📊 DEBUG - Data loaded:');
      console.log('Total tickets:', tickets.length);
      console.log('Total incidents:', incidents.length);
      console.log(
        'Tickets by status:',
        tickets.reduce(
          (acc, t) => {
            acc[t.status] = (acc[t.status] || 0) + 1;
            return acc;
          },
          {} as Record<string, number>
        )
      );
      console.log(
        'Tickets by severity:',
        tickets.reduce(
          (acc, t) => {
            const severity = t.severity || t.impact || 'unknown';
            acc[severity] = (acc[severity] || 0) + 1;
            return acc;
          },
          {} as Record<string, number>
        )
      );
      console.log(
        'Incidents by status:',
        incidents.reduce(
          (acc, i) => {
            acc[i.status] = (acc[i.status] || 0) + 1;
            return acc;
          },
          {} as Record<string, number>
        )
      );
      console.log(
        'Incidents by severity:',
        incidents.reduce(
          (acc, i) => {
            acc[i.severity] = (acc[i.severity] || 0) + 1;
            return acc;
          },
          {} as Record<string, number>
        )
      );
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setLoading(false);
    }
  };

  const exportTicketsToCSV = () => {
    try {
      // Prepare CSV headers
      const headers = [
        'Ticket Number',
        'Title',
        'Description',
        'Category',
        'Subcategory',
        'Priority',
        'Status',
        'Impact',
        'Urgency',
        'Created By',
        'Assigned To',
        'Created Date',
        'Updated Date',
        'Configuration Item',
        'Location',
      ];

      // Prepare CSV data
      const csvData = filteredTickets.map((ticket) => [
        ticket.ticket_number || '',
        ticket.short_description || ticket.title || '',
        (ticket.description || '').replace(/"/g, '""'), // Escape quotes
        ticket.category || '',
        ticket.subcategory || '',
        ticket.priority || '',
        ticket.status || '',
        ticket.impact || '',
        ticket.urgency || '',
        ticket.user_email || 'Unknown',
        ticket.assigned_to || '',
        ticket.created_at ? new Date(ticket.created_at).toLocaleString() : '',
        ticket.updated_at ? new Date(ticket.updated_at).toLocaleString() : '',
        ticket.configuration_item || '',
        ticket.location || '',
      ]);

      // Combine headers and data
      const csvContent = [headers, ...csvData]
        .map((row) => row.map((field) => `"${field}"`).join(','))
        .join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute(
        'download',
        `investigation_tickets_${new Date().toISOString().split('T')[0]}.csv`
      );
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      showAlert({
        type: 'success',
        title: 'Export Successful',
        message: `Exported ${filteredTickets.length} tickets to CSV file.`,
      });
    } catch (error) {
      console.error('Error exporting tickets:', error);
      showAlert({
        type: 'error',
        title: 'Export Failed',
        message: 'Failed to export tickets. Please try again.',
      });
    }
  };

  const exportIncidentsToCSV = () => {
    try {
      // Prepare CSV headers
      const headers = [
        'Incident ID',
        'Title',
        'Description',
        'Severity',
        'Status',
        'Created By',
        'Assigned To',
        'Assigned Team',
        'Created Date',
        'Updated Date',
        'Resolved Date',
        'Impact Assessment',
        'Business Impact',
        'Root Cause',
        'Original Ticket',
      ];

      // Prepare CSV data
      const csvData = filteredIncidents.map((incident) => [
        incident.incident_id || '',
        incident.title || '',
        (incident.description || '').replace(/"/g, '""'), // Escape quotes
        incident.severity || '',
        incident.status || '',
        incident.user_email || 'Unknown',
        incident.assigned_to || '',
        incident.assigned_team || '',
        incident.created_at ? new Date(incident.created_at).toLocaleString() : '',
        incident.updated_at ? new Date(incident.updated_at).toLocaleString() : '',
        incident.resolved_at ? new Date(incident.resolved_at).toLocaleString() : '',
        (incident.impact_assessment || '').replace(/"/g, '""'),
        incident.business_impact || '',
        (incident.root_cause || '').replace(/"/g, '""'),
        incident.original_ticket_number || '',
      ]);

      // Combine headers and data
      const csvContent = [headers, ...csvData]
        .map((row) => row.map((field) => `"${field}"`).join(','))
        .join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute(
        'download',
        `security_incidents_${new Date().toISOString().split('T')[0]}.csv`
      );
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      showAlert({
        type: 'success',
        title: 'Export Successful',
        message: `Exported ${filteredIncidents.length} incidents to CSV file.`,
      });
    } catch (error) {
      console.error('Error exporting incidents:', error);
      showAlert({
        type: 'error',
        title: 'Export Failed',
        message: 'Failed to export incidents. Please try again.',
      });
    }
  };

  const handleViewTicket = (ticket: InvestigationTicket) => {
    setSelectedTicket(ticket);
    setTicketModalMode('view');
    setShowTicketDetails(true);
  };

  const handleEditTicket = (ticket: InvestigationTicket) => {
    setSelectedTicket(ticket);
    setTicketModalMode('edit');
    setShowTicketDetails(true);
  };

  const handleAssignTicket = (ticket: InvestigationTicket) => {
    setSelectedTicket(ticket);
    setTicketModalMode('assign');
    setShowTicketDetails(true);
  };

  const handleConvertTicket = (ticket: InvestigationTicket) => {
    setSelectedTicket(ticket);
    setTicketModalMode('convert');
    setShowTicketDetails(true);
  };

  const handleCloseTicket = async (ticket: InvestigationTicket) => {
    try {
      console.log('Closing ticket:', ticket.ticket_number);

      // Call API to close ticket
      await closeTicket(ticket.ticket_number);

      // Reload tickets to get updated data
      await loadTickets();

      showAlert({
        type: 'success',
        title: 'Ticket Closed',
        message: `Ticket ${ticket.ticket_number} has been closed successfully.`,
      });
    } catch (error) {
      console.error('Error closing ticket:', error);
      showAlert({
        type: 'error',
        title: 'Error',
        message:
          error instanceof Error ? error.message : 'Failed to close ticket. Please try again.',
      });
    }
  };

  const handleReopenTicket = async (ticket: InvestigationTicket) => {
    try {
      console.log('Reopening ticket:', ticket.ticket_number);

      // Call API to reopen ticket
      await reopenTicket(ticket.ticket_number);

      // Reload tickets to get updated data
      await loadTickets();

      showAlert({
        type: 'success',
        title: 'Ticket Reopened',
        message: `Ticket ${ticket.ticket_number} has been reopened successfully.`,
      });
    } catch (error) {
      console.error('Error reopening ticket:', error);
      showAlert({
        type: 'error',
        title: 'Error',
        message:
          error instanceof Error ? error.message : 'Failed to reopen ticket. Please try again.',
      });
    }
  };

  const handleDeleteTicket = async (ticket: InvestigationTicket) => {
    // Show confirmation dialog
    const confirmed = await confirm({
      title: 'Delete Ticket',
      message: `Are you sure you want to delete ticket ${ticket.ticket_number}?\n\nThis action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      type: 'danger',
    });

    if (!confirmed) {
      return;
    }

    try {
      console.log('Deleting ticket:', ticket.ticket_number);

      // Call API to delete ticket
      await deleteTicket(ticket.ticket_number);

      // Reload tickets to get updated data
      await loadTickets();

      showAlert({
        type: 'success',
        title: 'Ticket Deleted',
        message: `Ticket ${ticket.ticket_number} has been deleted successfully.`,
      });
    } catch (error) {
      console.error('Error deleting ticket:', error);
      showAlert({
        type: 'error',
        title: 'Error',
        message:
          error instanceof Error ? error.message : 'Failed to delete ticket. Please try again.',
      });
    }
  };

  const handleTicketUpdated = () => {
    loadTickets();
    loadIncidents();
  };

  const handleViewIncident = (incident: Incident) => {
    setSelectedIncident(incident);
    setShowIncidentDetails(true);
  };

  const handleEditIncident = (incident: Incident) => {
    setSelectedIncident(incident);
    setShowIncidentDetails(true);
    // The modal will handle edit mode internally
  };

  const handleAssignIncident = (incident: Incident) => {
    // Open the incident details modal in edit mode for assignment
    setSelectedIncident(incident);
    setShowIncidentDetails(true);
  };

  const handleIncidentUpdated = () => {
    loadIncidents();
    setSelectedIncident(null);
    setShowIncidentDetails(false);
  };

  // Function to export all incidents and tickets to CSV
  const handleExportIncidentsCSV = async () => {
    try {
      setExportingCSV(true);
      console.log('📊 Exporting all incidents and tickets to CSV');

      const token = Cookies.get('token');
      if (!token) {
        showAlert({
          type: 'error',
          title: 'Authentication required',
          message: 'Please log in to export data',
        });
        return;
      }

      const response = await fetch(`http://localhost:5000/api/export/incidents/csv`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        // Generate filename
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
        link.download = `PICA_Incidents_Export_${timestamp}.csv`;

        // Trigger download
        document.body.appendChild(link);
        link.click();

        // Cleanup
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        console.log('✅ Incidents CSV exported successfully');

        showAlert({
          type: 'success',
          title: 'Export successful',
          message: 'All incidents and tickets exported successfully to CSV.',
        });
      } else {
        const errorData = await response.json();
        console.error('❌ Failed to export incidents CSV:', response.status, errorData);
        showAlert({
          type: 'error',
          title: 'Export error',
          message: 'CSV export error: ' + (errorData.message || 'Unknown error'),
        });
      }
    } catch (error) {
      console.error('❌ Error exporting incidents CSV:', error);
      showAlert({
        type: 'error',
        title: 'Export error',
        message: 'An error occurred during CSV export',
      });
    } finally {
      setExportingCSV(false);
    }
  };

  // Filter tickets based on search and filters
  const filteredTickets = tickets.filter((ticket) => {
    const title = ticket.short_description || ticket.title || '';
    const matchesSearch =
      title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.ticket_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (ticket.category && ticket.category.toLowerCase().includes(searchTerm.toLowerCase()));

    // Handle both old severity and new impact/priority filtering
    const ticketSeverity = ticket.severity || ticket.impact?.toLowerCase();
    const matchesSeverity = severityFilter === 'all' || ticketSeverity === severityFilter;
    const matchesStatus =
      statusFilter === 'all' || ticket.status.toLowerCase() === statusFilter.toLowerCase();
    return matchesSearch && matchesSeverity && matchesStatus;
  });

  // Filter incidents based on search and filters
  const filteredIncidents = incidents.filter((incident) => {
    const matchesSearch =
      incident.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      incident.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      incident.incident_id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesSeverity = severityFilter === 'all' || incident.severity === severityFilter;
    const matchesStatus = statusFilter === 'all' || incident.status === statusFilter;
    return matchesSearch && matchesSeverity && matchesStatus;
  });

  const renderTabButton = (tab: 'tickets' | 'incidents', label: string, icon: any) => {
    const Icon = icon;
    return (
      <button
        onClick={() => setActiveTab(tab)}
        className={`px-6 py-3 rounded-lg font-medium transition-all ${
          activeTab === tab
            ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
            : 'text-gray-300 hover:text-white hover:bg-gray-700/50'
        }`}
      >
        <Icon size={20} className="inline mr-2" />
        {label}
      </button>
    );
  };

  const renderFilters = () => (
    <div className="flex flex-wrap gap-4 mb-8">
      <div className="flex-1 min-w-64">
        <div className="relative">
          <Search
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            size={20}
          />
          <input
            type="text"
            placeholder="Search tickets, incidents..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
          />
        </div>
      </div>

      <select
        value={severityFilter}
        onChange={(e) => setSeverityFilter(e.target.value)}
        className="px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all duration-200"
      >
        <option value="all">All Severities</option>
        <option value="critical">Critical</option>
        <option value="high">High</option>
        <option value="medium">Medium</option>
        <option value="low">Low</option>
      </select>

      <select
        value={statusFilter}
        onChange={(e) => setStatusFilter(e.target.value)}
        className="px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all duration-200"
      >
        <option value="all">All Statuses</option>
        <option value="open">Open</option>
        <option value="in_progress">In Progress</option>
        <option value="converted_to_incident">Converted</option>
        <option value="resolved">Resolved</option>
        <option value="closed">Closed</option>
      </select>

      <button
        onClick={() => {
          loadTickets();
          loadIncidents();
        }}
        className="flex items-center space-x-2 px-4 py-3 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg"
      >
        <RefreshCw size={20} />
        <span>Refresh</span>
      </button>
    </div>
  );

  const renderTicketRow = (ticket: InvestigationTicket) => {
    // Fallback: si l'utilisateur actuel est le créateur du ticket, utiliser ses infos
    let createdBy = users[ticket.user_id]?.email || users[ticket.user_id]?.username;

    if (!createdBy && user && (user.id === ticket.user_id || user._id === ticket.user_id)) {
      createdBy = user.email || user.username;
    }

    createdBy = createdBy || ticket.user_id || '-';

    return (
      <EnhancedTicketCard
        key={ticket.id}
        ticket={ticket}
        createdBy={createdBy}
        onView={() => handleViewTicket(ticket)}
        onEdit={() => handleEditTicket(ticket)}
        onAssign={() => handleAssignTicket(ticket)}
        onConvert={() => handleConvertTicket(ticket)}
        onClose={() => handleCloseTicket(ticket)}
        onReopen={() => handleReopenTicket(ticket)}
        onDelete={() => handleDeleteTicket(ticket)}
        canEdit={isAdmin || ticket.user_id === user?.id}
        canAssign={isAdmin}
        canConvert={isAdmin && ticket.status !== 'converted_to_incident'}
        canClose={isAdmin && ticket.status !== 'Closed' && ticket.status !== 'Resolved'}
        canReopen={isAdmin && (ticket.status === 'Closed' || ticket.status === 'Resolved')}
        canDelete={isAdmin}
      />
    );
  };

  const renderIncidentCard = (incident: Incident) => {
    // Calculer le créateur de l'incident avec gestion des cas vides
    let createdBy = '';

    // 1. Essayer d'abord user_email (vient de l'agrégation backend)
    if (incident.user_email && incident.user_email.trim()) {
      createdBy = incident.user_email;
    }
    // 2. Essayer le cache local des utilisateurs
    else if (incident.user_id && users[incident.user_id]) {
      createdBy = users[incident.user_id].email || users[incident.user_id].username;
    }
    // 3. Vérifier si l'utilisateur actuel est le créateur
    else if (
      user &&
      incident.user_id &&
      (user.id === incident.user_id || user._id === incident.user_id)
    ) {
      createdBy = user.email || user.username || 'Utilisateur actuel';
    }
    // 4. Utiliser user_id comme fallback s'il n'est pas vide
    else if (incident.user_id && incident.user_id.trim && incident.user_id.trim()) {
      createdBy = incident.user_id;
    }
    // 5. Vérifier les informations de conversion (pour les incidents créés à partir de tickets)
    else if (incident.original_creator_email && incident.original_creator_email.trim()) {
      createdBy = incident.original_creator_email + ' (via ticket)';
    }
    // 6. Cas par défaut pour les anciens incidents sans créateur
    else {
      createdBy = 'Système';
    }

    return (
      <EnhancedIncidentCard
        key={incident.id}
        incident={incident}
        createdBy={createdBy}
        onView={() => handleViewIncident(incident)}
        onEdit={() => handleEditIncident(incident)}
        onAssign={() => handleAssignIncident(incident)}
        onClose={() => handleCloseIncident(incident)}
        onReopen={() => handleReopenIncident(incident)}
        onDelete={() => handleDeleteIncident(incident)}
        canEdit={isAdmin}
        canAssign={isAdmin}
        canClose={isAdmin}
        canReopen={isAdmin}
        canDelete={isAdmin}
      />
    );
  };

  const handleCloseIncident = async (incident: Incident) => {
    try {
      console.log('Closing incident:', incident.incident_id);

      // Call API to close incident
      await closeIncident(incident.incident_id);

      // Reload incidents to get updated data
      await loadIncidents();

      showAlert({
        type: 'success',
        title: 'Incident Closed',
        message: `Incident ${incident.incident_id} has been closed successfully.`,
      });
    } catch (error) {
      console.error('Error closing incident:', error);
      showAlert({
        type: 'error',
        title: 'Error',
        message:
          error instanceof Error ? error.message : 'Failed to close incident. Please try again.',
      });
    }
  };

  const handleReopenIncident = async (incident: Incident) => {
    try {
      console.log('Reopening incident:', incident.incident_id);

      // Call API to reopen incident
      await reopenIncident(incident.incident_id);

      // Reload incidents to get updated data
      await loadIncidents();

      showAlert({
        type: 'success',
        title: 'Incident Reopened',
        message: `Incident ${incident.incident_id} has been reopened successfully.`,
      });
    } catch (error) {
      console.error('Error reopening incident:', error);
      showAlert({
        type: 'error',
        title: 'Error',
        message:
          error instanceof Error ? error.message : 'Failed to reopen incident. Please try again.',
      });
    }
  };

  const handleDeleteIncident = async (incident: Incident) => {
    // Show confirmation dialog
    const confirmed = await confirm({
      title: 'Delete Incident',
      message: `Are you sure you want to delete incident ${incident.incident_id}?\n\nThis action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      type: 'danger',
    });

    if (!confirmed) {
      return;
    }

    try {
      console.log('Deleting incident:', incident.incident_id);

      // Call API to delete incident
      await deleteIncident(incident.incident_id);

      // Reload incidents to get updated data
      await loadIncidents();

      showAlert({
        type: 'success',
        title: 'Incident Deleted',
        message: `Incident ${incident.incident_id} has been deleted successfully.`,
      });
    } catch (error) {
      console.error('Error deleting incident:', error);
      showAlert({
        type: 'error',
        title: 'Error',
        message:
          error instanceof Error ? error.message : 'Failed to delete incident. Please try again.',
      });
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-8 shadow-2xl">
        <div className="flex items-center space-x-4 mb-6">
          <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg">
            <AlertTriangle className="w-8 h-8 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-white">Incident Management</h1>
            <p className="text-gray-300">Manage security incidents and investigation tickets</p>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex justify-center mt-8">
          <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-1 border border-gray-700/50">
            {renderTabButton('tickets', 'Investigation Tickets', AlertTriangle)}
            {renderTabButton('incidents', 'Security Incidents', ShieldAlert)}
          </div>
        </div>
      </div>

      {/* Dashboard Overview - Full */}
      <div className="px-8 py-4">
        <IncidentDashboard tickets={tickets} incidents={incidents} />
      </div>

      {/* Content */}
      <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl shadow-2xl">
        {activeTab === 'tickets' && (
          <div className="p-8">
            {/* Header with Create Button */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-white">Investigation Tickets</h2>
              <button
                onClick={() => setShowCreateForm(true)}
                className="flex items-center space-x-2 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg"
              >
                <Plus size={20} />
                <span>Create Ticket</span>
              </button>
            </div>

            {/* Search and Filters */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <Search
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                    size={20}
                  />
                  <input
                    type="text"
                    placeholder="Search tickets..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <select
                  value={severityFilter}
                  onChange={(e) => setSeverityFilter(e.target.value)}
                  className="px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all duration-200"
                >
                  <option value="all">All Severities</option>
                  <option value="critical">Critical</option>
                  <option value="high">High</option>
                  <option value="medium">Medium</option>
                  <option value="low">Low</option>
                </select>

                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all duration-200"
                >
                  <option value="all">All Statuses</option>
                  <option value="open">Open</option>
                  <option value="in_progress">In Progress</option>
                  <option value="converted_to_incident">Converted</option>
                  <option value="resolved">Resolved</option>
                  <option value="closed">Closed</option>
                </select>

                <button
                  onClick={exportTicketsToCSV}
                  className="flex items-center space-x-2 px-4 py-3 bg-green-600 hover:bg-green-700 rounded-xl text-white transition-all duration-200"
                  title="Export all tickets to CSV"
                >
                  <FileDown size={16} />
                  <span>Export All</span>
                </button>

                {/* Refresh Button */}
                <button
                  onClick={handleRefresh}
                  disabled={loading}
                  className={`flex items-center space-x-2 px-4 py-3 rounded-xl transition-all duration-200 ${
                    loading
                      ? 'bg-gray-700/50 text-gray-500 cursor-not-allowed'
                      : 'bg-pink-600 hover:bg-pink-700 text-white'
                  }`}
                  title="Refresh tickets"
                >
                  <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
                  <span className="text-sm font-medium">
                    {loading ? 'Refreshing...' : 'Refresh'}
                  </span>
                </button>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'tickets' && (
          <div className="px-8 pb-8">
            {/* Tickets Grid */}
            <div className="space-y-4">
              {loading ? (
                <div className="text-center py-12">
                  <RefreshCw className="animate-spin mx-auto mb-4 text-purple-400" size={48} />
                  <p className="text-gray-400">Loading tickets...</p>
                </div>
              ) : filteredTickets.length > 0 ? (
                filteredTickets.map(renderTicketRow)
              ) : (
                <div className="text-center py-12">
                  <AlertTriangle className="mx-auto mb-4 text-gray-400" size={48} />
                  <div>
                    <p className="text-gray-400 text-lg mb-2">
                      {isAdmin ? 'No tickets found' : "You haven't created any tickets yet"}
                    </p>
                    <p className="text-gray-500 text-sm">
                      {isAdmin
                        ? 'Tickets will appear here when users submit them'
                        : 'Create your first ticket to report an issue or request assistance'}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'incidents' && (
          <div className="p-8">
            {/* Header with Create Button */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-white">Security Incidents</h2>
              <button
                onClick={() => setShowCreateForm(true)}
                className="flex items-center space-x-2 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg"
              >
                <Plus size={20} />
                <span>Create Incident</span>
              </button>
            </div>

            {/* Search and Filters */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <Search
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                    size={20}
                  />
                  <input
                    type="text"
                    placeholder="Search incidents..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <select
                  value={severityFilter}
                  onChange={(e) => setSeverityFilter(e.target.value)}
                  className="px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all duration-200"
                >
                  <option value="all">All Severities</option>
                  <option value="critical">Critical</option>
                  <option value="high">High</option>
                  <option value="medium">Medium</option>
                  <option value="low">Low</option>
                </select>

                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all duration-200"
                >
                  <option value="all">All Statuses</option>
                  <option value="open">Open</option>
                  <option value="in_progress">In Progress</option>
                  <option value="resolved">Resolved</option>
                  <option value="closed">Closed</option>
                </select>

                <button
                  onClick={exportIncidentsToCSV}
                  className="flex items-center space-x-2 px-4 py-3 bg-green-600 hover:bg-green-700 rounded-xl text-white transition-all duration-200"
                  title="Export all incidents to CSV"
                >
                  <FileDown size={16} />
                  <span>Export All</span>
                </button>

                {/* Refresh Button */}
                <button
                  onClick={handleRefresh}
                  disabled={loading}
                  className={`flex items-center space-x-2 px-4 py-3 rounded-xl transition-all duration-200 ${
                    loading
                      ? 'bg-gray-700/50 text-gray-500 cursor-not-allowed'
                      : 'bg-pink-600 hover:bg-pink-700 text-white'
                  }`}
                  title="Refresh incidents"
                >
                  <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
                  <span className="text-sm font-medium">
                    {loading ? 'Refreshing...' : 'Refresh'}
                  </span>
                </button>
              </div>
            </div>

            {/* Incidents Grid */}
            <div className="space-y-4">
              {loading ? (
                <div className="text-center py-12">
                  <RefreshCw className="animate-spin mx-auto mb-4 text-purple-400" size={48} />
                  <p className="text-gray-400">Loading incidents...</p>
                </div>
              ) : filteredIncidents.length > 0 ? (
                filteredIncidents.map(renderIncidentCard)
              ) : (
                <div className="text-center py-12">
                  <ShieldAlert className="mx-auto mb-4 text-gray-400" size={48} />
                  <div>
                    <p className="text-gray-400 text-lg mb-2">No incidents found</p>
                    <p className="text-gray-500 text-sm">
                      Incidents will appear here when security events are detected
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Create Ticket Form Modal */}
      <CreateTicketForm
        isOpen={showCreateForm}
        onClose={() => setShowCreateForm(false)}
        onTicketCreated={() => {
          loadTickets();
          setShowCreateForm(false);
        }}
      />

      {/* Ticket Details Modal */}
      <TicketDetailsModal
        ticket={selectedTicket}
        isOpen={showTicketDetails}
        onClose={() => {
          setShowTicketDetails(false);
          setSelectedTicket(null);
          setTicketModalMode('view');
        }}
        onTicketUpdated={handleTicketUpdated}
        initialMode={ticketModalMode}
        currentUserId={user?.id}
        users={users}
      />

      {/* Incident Details Modal */}
      {selectedIncident && (
        <IncidentDetailsModal
          incident={selectedIncident}
          isOpen={showIncidentDetails}
          onClose={() => {
            setShowIncidentDetails(false);
            setSelectedIncident(null);
          }}
          onIncidentUpdated={handleIncidentUpdated}
          users={users}
          onViewTicket={(ticketNumber: string) => {
            // Find the ticket and open it in the ticket modal
            const ticket = tickets.find((t) => t.ticket_number === ticketNumber);
            if (ticket) {
              setSelectedTicket(ticket);
              setTicketModalMode('view');
              setShowTicketDetails(true);
              // Close incident modal
              setShowIncidentDetails(false);
              setSelectedIncident(null);
            }
          }}
        />
      )}

      {/* Confirmation Modal */}
      <ConfirmationComponent />
    </div>
  );
}
