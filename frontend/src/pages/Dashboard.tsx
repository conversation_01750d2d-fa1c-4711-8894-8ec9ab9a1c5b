import { useState, useEffect, useRef } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useRole } from '../hooks/useRole';
import {
  Shield,
  Activity,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Clock,
  Zap,
  RefreshCw,
  Bell,
  Users,
  Database,
  Server,
  Download,
  Eye,
  Globe,
  Bug,
  Target
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

// Données simulées réalistes pour le dashboard
const generateRealisticData = () => {
  const now = new Date();
  const last24Hours = Array.from({ length: 24 }, (_, i) => {
    const time = new Date(now.getTime() - (23 - i) * 60 * 60 * 1000);
    return {
      time: time.getHours() + 'h',
      threats: Math.floor(Math.random() * 50) + 10,
      scans: Math.floor(Math.random() * 30) + 5,
      blocked: Math.floor(Math.random() * 20) + 2,
      incidents: Math.floor(Math.random() * 10)
    };
  });

  const last7Days = Array.from({ length: 7 }, (_, i) => {
    const date = new Date(now.getTime() - (6 - i) * 24 * 60 * 60 * 1000);
    return {
      day: date.toLocaleDateString('fr-FR', { weekday: 'short' }),
      vulnerabilities: Math.floor(Math.random() * 100) + 20,
      malware: Math.floor(Math.random() * 40) + 5,
      phishing: Math.floor(Math.random() * 60) + 10,
      network: Math.floor(Math.random() * 30) + 3
    };
  });

  const threatTypes = [
    { name: 'Malware', value: 156, color: '#ef4444' },
    { name: 'Phishing', value: 89, color: '#f97316' },
    { name: 'Vulnérabilités', value: 234, color: '#eab308' },
    { name: 'Intrusions', value: 67, color: '#22c55e' },
    { name: 'Autres', value: 43, color: '#8b5cf6' }
  ];

  const systemHealth = [
    { service: 'Pentesting', status: 99.8, incidents: 2 },
    { service: 'Phishing Detection', status: 98.5, incidents: 5 },
    { service: 'Malware Scanner', status: 99.2, incidents: 3 },
    { service: 'Network Monitor', status: 97.8, incidents: 8 },
    { service: 'Analytics Engine', status: 99.9, incidents: 1 }
  ];

  return { last24Hours, last7Days, threatTypes, systemHealth };
};

export default function Dashboard() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [data] = useState(generateRealisticData());

  // Statistiques calculées
  const stats = {
    totalThreats: data.threatTypes.reduce((sum, t) => sum + t.value, 0),
    activeScans: data.last24Hours[data.last24Hours.length - 1]?.scans || 0,
    criticalIncidents: data.systemHealth.reduce((sum, s) => sum + s.incidents, 0),
    securityScore: Math.round(data.systemHealth.reduce((sum, s) => sum + s.status, 0) / data.systemHealth.length),
    todayThreats: data.last24Hours.reduce((sum, h) => sum + h.threats, 0),
    todayBlocked: data.last24Hours.reduce((sum, h) => sum + h.blocked, 0)
  };

  useEffect(() => {
    // Update timestamp every minute
    const interval = setInterval(() => {
      setLastUpdated(new Date());
    }, 60000);

    return () => clearInterval(interval);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };



  return (
    <div className="space-y-6">
      {/* Header avec métriques principales */}
      <div className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2 flex items-center">
              <Shield className="w-8 h-8 text-purple-400 mr-3" />
              Dashboard Sécurité PICA
            </h1>
            <p className="text-gray-300">
              Surveillance temps réel • Dernière mise à jour: {formatTime(lastUpdated)}
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <button className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors flex items-center space-x-2">
              <RefreshCw className="w-4 h-4" />
              <span>Actualiser</span>
            </button>
            <button className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center space-x-2">
              <Download className="w-4 h-4" />
              <span>Exporter</span>
            </button>
          </div>
        </div>

        {/* Métriques principales */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          <div className="bg-gradient-to-br from-red-500/20 to-red-600/20 border border-red-500/30 rounded-xl p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-red-300 text-sm font-medium">Menaces Totales</p>
                <p className="text-3xl font-bold text-white">{stats.totalThreats}</p>
                <p className="text-red-400 text-xs">+{stats.todayThreats} aujourd'hui</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-400" />
            </div>
          </div>

          <div className="bg-gradient-to-br from-blue-500/20 to-blue-600/20 border border-blue-500/30 rounded-xl p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-300 text-sm font-medium">Scans Actifs</p>
                <p className="text-3xl font-bold text-white">{stats.activeScans}</p>
                <p className="text-blue-400 text-xs">En cours</p>
              </div>
              <Activity className="w-8 h-8 text-blue-400" />
            </div>
          </div>

          <div className="bg-gradient-to-br from-green-500/20 to-green-600/20 border border-green-500/30 rounded-xl p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-300 text-sm font-medium">Menaces Bloquées</p>
                <p className="text-3xl font-bold text-white">{stats.todayBlocked}</p>
                <p className="text-green-400 text-xs">Aujourd'hui</p>
              </div>
              <Shield className="w-8 h-8 text-green-400" />
            </div>
          </div>

          <div className="bg-gradient-to-br from-purple-500/20 to-purple-600/20 border border-purple-500/30 rounded-xl p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-300 text-sm font-medium">Score Sécurité</p>
                <p className="text-3xl font-bold text-white">{stats.securityScore}%</p>
                <p className="text-purple-400 text-xs">Santé système</p>
              </div>
              <CheckCircle className="w-8 h-8 text-purple-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Graphiques principaux */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Graphique des menaces sur 24h */}
        <div className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <h2 className="text-xl font-bold text-white mb-6 flex items-center">
            <Activity className="w-5 h-5 mr-2 text-purple-400" />
            Activité des Menaces (24h)
          </h2>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={data.last24Hours}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis dataKey="time" stroke="#9CA3AF" />
              <YAxis stroke="#9CA3AF" />
              <Tooltip
                contentStyle={{
                  backgroundColor: '#1F2937',
                  border: '1px solid #374151',
                  borderRadius: '8px',
                  color: '#F9FAFB'
                }}
              />
              <Legend />
              <Area
                type="monotone"
                dataKey="threats"
                stackId="1"
                stroke="#EF4444"
                fill="#EF4444"
                fillOpacity={0.3}
                name="Menaces détectées"
              />
              <Area
                type="monotone"
                dataKey="blocked"
                stackId="1"
                stroke="#10B981"
                fill="#10B981"
                fillOpacity={0.3}
                name="Menaces bloquées"
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Graphique des scans */}
        <div className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <h2 className="text-xl font-bold text-white mb-6 flex items-center">
            <Target className="w-5 h-5 mr-2 text-purple-400" />
            Activité des Scans (24h)
          </h2>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={data.last24Hours}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis dataKey="time" stroke="#9CA3AF" />
              <YAxis stroke="#9CA3AF" />
              <Tooltip
                contentStyle={{
                  backgroundColor: '#1F2937',
                  border: '1px solid #374151',
                  borderRadius: '8px',
                  color: '#F9FAFB'
                }}
              />
              <Legend />
              <Line
                type="monotone"
                dataKey="scans"
                stroke="#3B82F6"
                strokeWidth={3}
                dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
                name="Scans effectués"
              />
              <Line
                type="monotone"
                dataKey="incidents"
                stroke="#F59E0B"
                strokeWidth={2}
                dot={{ fill: '#F59E0B', strokeWidth: 2, r: 3 }}
                name="Incidents détectés"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Graphiques secondaires */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Répartition des types de menaces */}
        <div className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <h2 className="text-xl font-bold text-white mb-6 flex items-center">
            <Zap className="w-5 h-5 mr-2 text-purple-400" />
            Types de Menaces
          </h2>
          <ResponsiveContainer width="100%" height={250}>
            <PieChart>
              <Pie
                data={data.threatTypes}
                cx="50%"
                cy="50%"
                innerRadius={40}
                outerRadius={80}
                paddingAngle={5}
                dataKey="value"
              >
                {data.threatTypes.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip
                contentStyle={{
                  backgroundColor: '#1F2937',
                  border: '1px solid #374151',
                  borderRadius: '8px',
                  color: '#F9FAFB'
                }}
              />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Évolution hebdomadaire */}
        <div className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <h2 className="text-xl font-bold text-white mb-6 flex items-center">
            <TrendingUp className="w-5 h-5 mr-2 text-purple-400" />
            Tendances (7 jours)
          </h2>
          <ResponsiveContainer width="100%" height={250}>
            <BarChart data={data.last7Days}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis dataKey="day" stroke="#9CA3AF" />
              <YAxis stroke="#9CA3AF" />
              <Tooltip
                contentStyle={{
                  backgroundColor: '#1F2937',
                  border: '1px solid #374151',
                  borderRadius: '8px',
                  color: '#F9FAFB'
                }}
              />
              <Legend />
              <Bar dataKey="vulnerabilities" fill="#EF4444" name="Vulnérabilités" />
              <Bar dataKey="malware" fill="#F97316" name="Malware" />
              <Bar dataKey="phishing" fill="#EAB308" name="Phishing" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Santé du système */}
        <div className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <h2 className="text-xl font-bold text-white mb-6 flex items-center">
            <Server className="w-5 h-5 mr-2 text-purple-400" />
            Santé Système
          </h2>
          <div className="space-y-4">
            {data.systemHealth.map((service, index) => (
              <div key={index} className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-white text-sm font-medium">{service.service}</span>
                  <span className="text-green-400 text-sm font-bold">{service.status}%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-green-500 to-green-400 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${service.status}%` }}
                  ></div>
                </div>
                <div className="flex justify-between text-xs text-gray-400">
                  <span>Uptime</span>
                  <span>{service.incidents} incidents</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Alertes et incidents récents */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Alertes critiques */}
        <div className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-white flex items-center">
              <Bell className="w-5 h-5 mr-2 text-purple-400" />
              Alertes Critiques
            </h2>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
              <span className="text-red-400 text-sm">Live</span>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-red-500/10 border border-red-500/30 rounded-lg">
              <div className="flex items-center space-x-3">
                <AlertTriangle className="w-5 h-5 text-red-400" />
                <div>
                  <div className="text-white font-medium">Tentative d'intrusion détectée</div>
                  <div className="text-gray-400 text-sm">IP: ************* - Multiple failed attempts</div>
                </div>
              </div>
              <span className="text-red-400 text-sm">Maintenant</span>
            </div>

            <div className="flex items-center justify-between p-4 bg-orange-500/10 border border-orange-500/30 rounded-lg">
              <div className="flex items-center space-x-3">
                <Bug className="w-5 h-5 text-orange-400" />
                <div>
                  <div className="text-white font-medium">Malware détecté</div>
                  <div className="text-gray-400 text-sm">Fichier suspect en quarantaine</div>
                </div>
              </div>
              <span className="text-orange-400 text-sm">Il y a 5 min</span>
            </div>

            <div className="flex items-center justify-between p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
              <div className="flex items-center space-x-3">
                <Globe className="w-5 h-5 text-yellow-400" />
                <div>
                  <div className="text-white font-medium">Phishing bloqué</div>
                  <div className="text-gray-400 text-sm">URL malveillante interceptée</div>
                </div>
              </div>
              <span className="text-yellow-400 text-sm">Il y a 12 min</span>
            </div>
          </div>
        </div>

        {/* Activité récente */}
        <div className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <h2 className="text-xl font-bold text-white mb-6 flex items-center">
            <Clock className="w-5 h-5 mr-2 text-purple-400" />
            Activité Récente
          </h2>

          <div className="space-y-4">
            <div className="flex items-center space-x-4 p-3 bg-gray-700/30 rounded-lg">
              <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
              <div className="flex-1">
                <div className="text-white text-sm">Scan de vulnérabilités terminé</div>
                <div className="text-gray-400 text-xs">234 vulnérabilités trouvées • Il y a 2 min</div>
              </div>
              <Target className="w-4 h-4 text-blue-400" />
            </div>

            <div className="flex items-center space-x-4 p-3 bg-gray-700/30 rounded-lg">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <div className="flex-1">
                <div className="text-white text-sm">Base de données mise à jour</div>
                <div className="text-gray-400 text-xs">Nouvelles signatures malware • Il y a 15 min</div>
              </div>
              <Database className="w-4 h-4 text-green-400" />
            </div>

            <div className="flex items-center space-x-4 p-3 bg-gray-700/30 rounded-lg">
              <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
              <div className="flex-1">
                <div className="text-white text-sm">Rapport d'analyse généré</div>
                <div className="text-gray-400 text-xs">Analyse IA terminée • Il y a 1h</div>
              </div>
              <Eye className="w-4 h-4 text-purple-400" />
            </div>

            <div className="flex items-center space-x-4 p-3 bg-gray-700/30 rounded-lg">
              <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
              <div className="flex-1">
                <div className="text-white text-sm">Connexion utilisateur</div>
                <div className="text-gray-400 text-xs">{user?.username || 'Utilisateur'} connecté • Il y a 2h</div>
              </div>
              <Users className="w-4 h-4 text-yellow-400" />
            </div>
          </div>

          <div className="mt-6 pt-4 border-t border-gray-700/50">
            <button
              onClick={() => navigate('/admin/activity')}
              className="w-full text-purple-400 hover:text-purple-300 text-sm font-medium transition-colors"
            >
              Voir tous les logs d'activité
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
