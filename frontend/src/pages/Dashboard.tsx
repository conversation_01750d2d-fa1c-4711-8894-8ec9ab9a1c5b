import { useState, useEffect, useRef } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useRole } from '../hooks/useRole';
import {
  Shield,
  Activity,
  AlertTriangle,
  CheckCircle,
  Target,
  BarChart3,
  TrendingUp,
  TrendingDown,
  Clock,
  Zap,
  RefreshCw,
  Search,
  Bell,
  Users,
  Database,
  Network,
  Server,
  Download,
  XCircle,
  Filter
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { dashboardService, DashboardData } from '../services/dashboardService';
import { FallbackDataService } from '../services/fallbackDataService';
import VulnerabilityStatsCard from '../components/dashboard/VulnerabilityStatsCard';
import ThreatStatsCard from '../components/dashboard/ThreatStatsCard';
import ScanActivityCard from '../components/dashboard/ScanActivityCard';
import DynamicCharts from '../components/dashboard/DynamicCharts';
import {
  StatCardSkeleton,
  VulnerabilityStatsSkeleton,
  ThreatStatsSkeleton,
  ScanActivitySkeleton,
  ChartSkeleton
} from '../components/dashboard/SkeletonLoader';

export default function Dashboard() {
  const { user } = useAuth();
  const { role } = useRole();
  const navigate = useNavigate();
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [searchQuery, setSearchQuery] = useState('');
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dataQuality, setDataQuality] = useState<'real' | 'partial' | 'fallback' | 'emergency'>('fallback');

  // Refs for interval cleanup
  const timestampIntervalRef = useRef<number | null>(null);
  const dataIntervalRef = useRef<number | null>(null);
  const failureCountRef = useRef(0);

  // Robust dashboard data fetching with retry mechanism
  const fetchDashboardData = async (retryCount = 0) => {
    const maxRetries = 3;
    const baseDelay = 1000;

    try {
      setLoading(true);
      if (retryCount === 0) setError(null);

      console.log(`🔄 Fetching dashboard data (attempt ${retryCount + 1}/${maxRetries + 1})...`);

      const data = await dashboardService.getDashboardDataWithRetry(1, 500); // Single attempt here, retries handled by this function

      setDashboardData(data);
      setLastUpdated(new Date());
      setError(null);
      failureCountRef.current = 0; // Reset failure count on success

      // Determine data quality
      const quality = FallbackDataService.getDataQuality(data);
      setDataQuality(quality);

      console.log(`✅ Dashboard data loaded with quality: ${quality}`);

    } catch (err) {
      console.error(`❌ Failed to fetch dashboard data (attempt ${retryCount + 1}):`, err);
      failureCountRef.current++;

      if (retryCount < maxRetries) {
        const delay = baseDelay * Math.pow(2, retryCount);
        console.log(`⏳ Retrying in ${delay}ms...`);

        setTimeout(() => {
          fetchDashboardData(retryCount + 1);
        }, delay);
        return;
      }

      // All retries exhausted
      setError('Failed to load dashboard data after multiple attempts');
      console.log('🚨 All retries exhausted, using fallback data');

      // Use fallback data service
      const fallbackData = await dashboardService.getFallbackData();
      setDashboardData(fallbackData);
      setDataQuality('fallback');

    } finally {
      if (retryCount === 0 || retryCount >= maxRetries) {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchDashboardData();

    // Update timestamp every minute
    timestampIntervalRef.current = setInterval(() => {
      setLastUpdated(new Date());
    }, 60000);

    // Refresh data every 5 minutes with failure handling
    dataIntervalRef.current = setInterval(() => {
      if (failureCountRef.current < 5) { // Stop auto-refresh after 5 consecutive failures
        fetchDashboardData();
      } else {
        console.log('🛑 Auto-refresh disabled due to consecutive failures');
      }
    }, 300000);

    return () => {
      if (timestampIntervalRef.current) clearInterval(timestampIntervalRef.current);
      if (dataIntervalRef.current) clearInterval(dataIntervalRef.current);
    };
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour12: true,
      hour: 'numeric',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'text-green-400';
      case 'warning': return 'text-yellow-400';
      case 'offline': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-400 bg-red-500/10 border-red-500/30';
      case 'high': return 'text-orange-400 bg-orange-500/10 border-orange-500/30';
      case 'medium': return 'text-yellow-400 bg-yellow-500/10 border-yellow-500/30';
      case 'low': return 'text-green-400 bg-green-500/10 border-green-500/30';
      default: return 'text-gray-400 bg-gray-500/10 border-gray-500/30';
    }
  };

  const getTrendIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="w-4 h-4 text-green-400" />;
    if (change < 0) return <TrendingDown className="w-4 h-4 text-red-400" />;
    return <Activity className="w-4 h-4 text-gray-400" />;
  };

  const getTrendColor = (change: number) => {
    if (change > 0) return 'text-green-400';
    if (change < 0) return 'text-red-400';
    return 'text-gray-400';
  };

  if (loading && !dashboardData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="w-12 h-12 text-purple-400 mx-auto mb-4 animate-spin" />
          <h2 className="text-xl font-bold text-white mb-2">Loading Dashboard</h2>
          <p className="text-gray-400">Fetching security data...</p>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <XCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-white mb-2">Failed to Load Dashboard</h2>
          <p className="text-gray-400 mb-4">Unable to fetch dashboard data</p>
          <button
            onClick={() => fetchDashboardData()}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2 flex items-center">
              <Shield className="w-8 h-8 text-purple-400 mr-3" />
              Security Dashboard
            </h1>
            <div className="flex items-center space-x-4">
              <p className="text-gray-300">
                Real-time monitoring • Last updated: {formatTime(lastUpdated)}
              </p>
              {/* Data Quality Indicator */}
              <div className={`px-2 py-1 rounded text-xs font-medium ${
                dataQuality === 'real' ? 'bg-green-500/20 text-green-400 border border-green-500/30' :
                dataQuality === 'partial' ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30' :
                dataQuality === 'fallback' ? 'bg-orange-500/20 text-orange-400 border border-orange-500/30' :
                'bg-red-500/20 text-red-400 border border-red-500/30'
              }`}>
                {dataQuality === 'real' ? '🟢 Live Data' :
                 dataQuality === 'partial' ? '🟡 Partial Data' :
                 dataQuality === 'fallback' ? '🟠 Fallback Data' :
                 '🔴 Emergency Mode'}
              </div>
              {error && (
                <span className="text-red-400 text-sm">• {error}</span>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors"
              />
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={fetchDashboardData}
                disabled={loading}
                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 text-white rounded-lg transition-colors flex items-center space-x-2"
              >
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                <span>Refresh</span>
              </button>

              {/* Export Button */}
              <div className="relative">
                <button className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center space-x-2">
                  <Download className="w-4 h-4" />
                  <span>Export</span>
                </button>
              </div>

              {/* Filter Button */}
              <div className="relative">
                <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center space-x-2">
                  <Filter className="w-4 h-4" />
                  <span>Filter</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        {!dashboardData ? (
          // Show skeleton loaders while loading
          <>
            <StatCardSkeleton />
            <StatCardSkeleton />
            <StatCardSkeleton />
            <StatCardSkeleton />
            <StatCardSkeleton />
          </>
        ) : (
          // Show actual data
          <>
        {/* Total Scans */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-6 shadow-xl hover:border-purple-500/50 transition-all duration-300">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-blue-500/20 rounded-xl">
              <Database className="w-6 h-6 text-blue-400" />
            </div>
            <div className="flex items-center space-x-1">
              {getTrendIcon(dashboardData.stats.scanTrends.totalChange)}
              <span className={`text-sm ${getTrendColor(dashboardData.stats.scanTrends.totalChange)}`}>
                {dashboardData.stats.scanTrends.totalChange}%
              </span>
            </div>
          </div>
          <div>
            <h3 className="text-2xl font-bold text-white">{dashboardData.stats.totalScans}</h3>
            <p className="text-gray-400 text-sm">Total Scans</p>
            <p className="text-xs text-gray-500 mt-1">All time</p>
          </div>
        </div>

        {/* Active Scans */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-6 shadow-xl hover:border-yellow-500/50 transition-all duration-300">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-yellow-500/20 rounded-xl">
              <Activity className="w-6 h-6 text-yellow-400" />
            </div>
            <div className="flex items-center space-x-1">
              {getTrendIcon(dashboardData.stats.scanTrends.activeChange)}
              <span className={`text-sm ${getTrendColor(dashboardData.stats.scanTrends.activeChange)}`}>
                {dashboardData.stats.scanTrends.activeChange}%
              </span>
            </div>
          </div>
          <div>
            <h3 className="text-2xl font-bold text-white">{dashboardData.stats.activeScans}</h3>
            <p className="text-gray-400 text-sm">Active Scans</p>
            <p className="text-xs text-gray-500 mt-1">Last 24 hours</p>
          </div>
        </div>

        {/* Critical Threats */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-6 shadow-xl hover:border-red-500/50 transition-all duration-300">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-red-500/20 rounded-xl">
              <AlertTriangle className="w-6 h-6 text-red-400" />
            </div>
            <div className="flex items-center space-x-1">
              {getTrendIcon(dashboardData.stats.scanTrends.threatsChange)}
              <span className={`text-sm ${getTrendColor(dashboardData.stats.scanTrends.threatsChange)}`}>
                {Math.abs(dashboardData.stats.scanTrends.threatsChange)}%
              </span>
            </div>
          </div>
          <div>
            <h3 className="text-2xl font-bold text-white">{dashboardData.stats.criticalThreats}</h3>
            <p className="text-gray-400 text-sm">Critical Threats</p>
            <p className="text-xs text-gray-500 mt-1">High priority</p>
          </div>
        </div>

        {/* Security Alerts */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-6 shadow-xl hover:border-orange-500/50 transition-all duration-300">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-orange-500/20 rounded-xl">
              <Bell className="w-6 h-6 text-orange-400" />
            </div>
            <div className="flex items-center space-x-1">
              {getTrendIcon(dashboardData.stats.scanTrends.alertsChange)}
              <span className={`text-sm ${getTrendColor(dashboardData.stats.scanTrends.alertsChange)}`}>
                {dashboardData.stats.scanTrends.alertsChange}%
              </span>
            </div>
          </div>
          <div>
            <h3 className="text-2xl font-bold text-white">{dashboardData.stats.securityAlerts}</h3>
            <p className="text-gray-400 text-sm">Security Alerts</p>
            <p className="text-xs text-gray-500 mt-1">Unread</p>
          </div>
        </div>

        {/* Security Score */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-6 shadow-xl hover:border-green-500/50 transition-all duration-300">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-green-500/20 rounded-xl">
              <Shield className="w-6 h-6 text-green-400" />
            </div>
            <div className="text-xs text-gray-400">/ 100</div>
          </div>
          <div>
            <h3 className="text-2xl font-bold text-white">{dashboardData.stats.securityScore}</h3>
            <p className="text-gray-400 text-sm">Security Score</p>
            <p className="text-xs text-gray-500 mt-1">Based on threat detection and system health</p>
          </div>
        </div>
        </>
        )}
      </div>

      {/* User Info & System Status */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Profile Card */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <h2 className="text-xl font-bold text-white mb-6 flex items-center">
            <Users className="w-5 h-5 mr-2 text-purple-400" />
            User Profile
          </h2>
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              {user?.avatar ? (
                <img
                  src={`http://localhost:5000${user.avatar}`}
                  alt="User Avatar"
                  className="w-16 h-16 rounded-xl object-cover border-2 border-purple-500/30"
                />
              ) : (
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-cyan-500 rounded-xl flex items-center justify-center">
                  {user?.first_name && user?.last_name
                    ? <span className="text-white text-xl font-bold">{user.first_name[0]}{user.last_name[0]}</span>
                    : <Users className="w-8 h-8 text-white" />
                  }
                </div>
              )}
              <div>
                <h3 className="text-white font-semibold text-lg">
                  {user?.first_name && user?.last_name
                    ? `${user.first_name} ${user.last_name}`
                    : 'User'
                  }
                </h3>
                <p className="text-gray-300">{user?.username || user?.email}</p>
                <p className="text-purple-400 text-sm capitalize">{role}</p>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-700/50">
              <div className="text-center">
                <div className="text-white font-bold text-lg">{dashboardData.stats.totalScans}</div>
                <div className="text-gray-400 text-xs">Total Scans</div>
              </div>
              <div className="text-center">
                <div className="text-green-400 font-bold text-lg">{dashboardData.stats.securityScore}</div>
                <div className="text-gray-400 text-xs">Security Score</div>
              </div>
            </div>
          </div>
        </div>

        {/* System Status */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <h2 className="text-xl font-bold text-white mb-6 flex items-center">
            <Server className="w-5 h-5 mr-2 text-purple-400" />
            System Status
          </h2>
          <div className="space-y-4">
            {Object.entries(dashboardData.systemStatus).map(([service, status]) => (
              <div key={service} className="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${
                    status === 'online' ? 'bg-green-400' :
                    status === 'warning' ? 'bg-yellow-400' : 'bg-red-400'
                  }`}></div>
                  <span className="text-white capitalize">{service}</span>
                </div>
                <span className={`text-sm font-medium ${getStatusColor(status)} capitalize`}>
                  {status}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Charts and Analytics */}
      {dashboardData ? (
        <DynamicCharts dashboardData={dashboardData} />
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <ChartSkeleton />
          <ChartSkeleton />
          <ChartSkeleton />
        </div>
      )}

      {/* Detailed Statistics Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Vulnerability Statistics */}
        {dashboardData?.vulnerabilityStats ? (
          <VulnerabilityStatsCard stats={dashboardData.vulnerabilityStats} />
        ) : (
          <VulnerabilityStatsSkeleton />
        )}

        {/* Threat Detection Statistics */}
        {dashboardData?.threatStats ? (
          <ThreatStatsCard stats={dashboardData.threatStats} />
        ) : (
          <ThreatStatsSkeleton />
        )}

        {/* Scan Activity */}
        {dashboardData?.scanActivity ? (
          <ScanActivityCard activity={dashboardData.scanActivity} />
        ) : (
          <ScanActivitySkeleton />
        )}
      </div>

      {/* Bottom Section - Attack Sources and Recent Incidents */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Attack Sources */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <h2 className="text-xl font-bold text-white mb-6 flex items-center">
            <Network className="w-5 h-5 mr-2 text-purple-400" />
            Top Attack Sources
            <span className="ml-auto text-sm text-gray-400">Last 7 days</span>
          </h2>
          <div className="space-y-4">
            {dashboardData.attackSources.map((source, index) => (
              <div key={source.ip} className="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg hover:bg-gray-700/50 transition-colors">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-red-500/20 rounded-lg flex items-center justify-center">
                    <span className="text-red-400 font-bold text-sm">{index + 1}</span>
                  </div>
                  <div>
                    <div className="text-white font-medium">{source.ip}</div>
                    <div className="text-gray-400 text-sm">{source.country}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-white font-bold">{source.attacks}</div>
                  <div className="text-gray-400 text-sm">attacks</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Incidents */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-white flex items-center">
              <AlertTriangle className="w-5 h-5 mr-2 text-purple-400" />
              Recent Incidents
            </h2>
            <button
              onClick={() => navigate('/incidents')}
              className="text-purple-400 hover:text-purple-300 text-sm font-medium transition-colors"
            >
              View all
            </button>
          </div>

          {dashboardData.recentIncidents.length > 0 ? (
            <div className="space-y-4">
              {dashboardData.recentIncidents.map((incident) => (
                <div key={incident.id} className="p-4 bg-gray-700/30 rounded-lg hover:bg-gray-700/50 transition-colors">
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="text-white font-medium">{incident.title}</h3>
                    <span className={`px-2 py-1 rounded text-xs font-medium border ${getSeverityColor(incident.severity)}`}>
                      {incident.severity}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-4">
                      <span className="text-gray-400">Status: <span className="text-white">{incident.status}</span></span>
                      <span className="text-gray-400">Assigned: <span className="text-white">{incident.assigned}</span></span>
                    </div>
                    <span className="text-gray-400">{incident.created}</span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <CheckCircle className="w-12 h-12 text-green-400 mx-auto mb-3" />
              <h3 className="text-white font-medium mb-2">No incidents found</h3>
              <p className="text-gray-400 text-sm">When new incidents occur, they will appear here</p>
            </div>
          )}
        </div>
      </div>

      {/* Real-time Alerts & Activity Timeline */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Real-time Alerts */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-white flex items-center">
              <Bell className="w-5 h-5 mr-2 text-purple-400" />
              Real-time Alerts
            </h2>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-green-400 text-sm">Live</span>
            </div>
          </div>

          {dashboardData.stats.securityAlerts > 0 ? (
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-red-500/10 border border-red-500/30 rounded-lg">
                <div className="flex items-center space-x-3">
                  <AlertTriangle className="w-5 h-5 text-red-400" />
                  <div>
                    <div className="text-white font-medium">Security Alert</div>
                    <div className="text-gray-400 text-sm">{dashboardData.stats.securityAlerts} failed activities detected</div>
                  </div>
                </div>
                <span className="text-red-400 text-sm">Now</span>
              </div>

              {dashboardData.stats.criticalThreats > 0 && (
                <div className="flex items-center justify-between p-3 bg-orange-500/10 border border-orange-500/30 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Zap className="w-5 h-5 text-orange-400" />
                    <div>
                      <div className="text-white font-medium">Threat Detection</div>
                      <div className="text-gray-400 text-sm">{dashboardData.stats.criticalThreats} threats identified</div>
                    </div>
                  </div>
                  <span className="text-orange-400 text-sm">Active</span>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8">
              <CheckCircle className="w-12 h-12 text-green-400 mx-auto mb-3" />
              <h3 className="text-white font-medium mb-2">All Clear</h3>
              <p className="text-gray-400 text-sm">No active security alerts</p>
            </div>
          )}
        </div>

        {/* Activity Timeline */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <h2 className="text-xl font-bold text-white mb-6 flex items-center">
            <Clock className="w-5 h-5 mr-2 text-purple-400" />
            Recent Activity
          </h2>

          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
              <div className="flex-1">
                <div className="text-white text-sm">System scan completed</div>
                <div className="text-gray-400 text-xs">2 minutes ago</div>
              </div>
              <Target className="w-4 h-4 text-blue-400" />
            </div>

            <div className="flex items-center space-x-4">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <div className="flex-1">
                <div className="text-white text-sm">Vulnerability database updated</div>
                <div className="text-gray-400 text-xs">15 minutes ago</div>
              </div>
              <Database className="w-4 h-4 text-green-400" />
            </div>

            <div className="flex items-center space-x-4">
              <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
              <div className="flex-1">
                <div className="text-white text-sm">Analytics report generated</div>
                <div className="text-gray-400 text-xs">1 hour ago</div>
              </div>
              <BarChart3 className="w-4 h-4 text-purple-400" />
            </div>

            <div className="flex items-center space-x-4">
              <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
              <div className="flex-1">
                <div className="text-white text-sm">User logged in</div>
                <div className="text-gray-400 text-xs">2 hours ago</div>
              </div>
              <Users className="w-4 h-4 text-yellow-400" />
            </div>
          </div>

          <div className="mt-6 pt-4 border-t border-gray-700/50">
            <button className="w-full text-purple-400 hover:text-purple-300 text-sm font-medium transition-colors">
              View Full Activity Log
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
