import { useState, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import { PICARealDataService, PICARealData } from '../services/picaDashboardService';
import {
  Shield,
  AlertTriangle,
  TrendingUp,
  Clock,
  RefreshCw,
  Users,
  Database,
  Download,
  Eye,
  Globe,
  Bug,
  Target
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

// Données de fallback en cas d'erreur
const getFallbackData = (): PICARealData => ({
  securityOverview: {
    securityScore: 0,
    totalVulnerabilities: 0,
    activeThreats: 0,
    incidentsToday: 0,
    incidentsResolved: 0,
    incidentsPending: 0
  },
  scannerStatus: [
    { name: 'OpenVAS', status: 'offline', lastScan: 'N/A', vulnerabilities: 0 },
    { name: '<PERSON><PERSON><PERSON>', status: 'offline', lastScan: 'N/A', vulnerabilities: 0 },
    { name: 'Nikto', status: 'offline', lastScan: 'N/A', vulnerabilities: 0 },
    { name: 'SQLMap', status: 'offline', lastScan: 'N/A', vulnerabilities: 0 },
    { name: 'Phishing Detection', status: 'offline', lastScan: 'N/A', vulnerabilities: 0 }
  ],
  scanStats: {
    total: 0,
    running: 0,
    completed: 0,
    failed: 0,
    byType: []
  },
  vulnerabilityTrend: [],
  phishingData: {
    domainsBlocked: 0,
    ipsSuspicious: 0,
    emailsAnalyzed: 0,
    phishingDetected: 0,
    reputationChecks: 0
  },
  aiAnalytics: {
    alertsClassified: 0,
    confidenceRate: 0,
    falsePositives: 0,
    correlatedEvents: 0,
    reportsGenerated: 0
  },
  realTimeActivity: [],
  threatGeolocation: []
});

export default function Dashboard() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [data, setData] = useState<PICARealData>(getFallbackData());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fonction pour charger les vraies données PICA
  const loadRealData = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('🔄 Loading real PICA dashboard data...');

      const realData = await PICARealDataService.getRealDashboardData();
      setData(realData);
      setLastUpdated(new Date());

      console.log('✅ Real PICA data loaded successfully');
    } catch (err) {
      console.error('❌ Failed to load real PICA data:', err);
      setError('Impossible de charger les données en temps réel. Affichage des données de base.');
      // Garder les données de fallback
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Charger les données au montage
    loadRealData();

    // Update timestamp every minute
    const timestampInterval = setInterval(() => {
      setLastUpdated(new Date());
    }, 60000);

    // Refresh data every 5 minutes
    const dataInterval = setInterval(() => {
      loadRealData();
    }, 300000);

    return () => {
      clearInterval(timestampInterval);
      clearInterval(dataInterval);
    };
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };



  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'text-green-400';
      case 'running': return 'text-yellow-400';
      case 'offline': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusBg = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-500/20 border-green-500/30';
      case 'running': return 'bg-yellow-500/20 border-yellow-500/30';
      case 'offline': return 'bg-red-500/20 border-red-500/30';
      default: return 'bg-gray-500/20 border-gray-500/30';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header PICA */}
      <div className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2 flex items-center">
              <Shield className="w-8 h-8 text-purple-400 mr-3" />
              PICA - Plateforme Intégrée de Cybersécurité Automatisée
            </h1>
            <div className="space-y-1">
              <p className="text-gray-300">
                Surveillance temps réel • Dernière mise à jour: {formatTime(lastUpdated)}
              </p>
              {error && (
                <p className="text-orange-400 text-sm">
                  ⚠️ {error}
                </p>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-4">
            {/* Indicateur de statut des données */}
            <div className={`px-3 py-1 rounded-lg text-xs font-medium ${
              loading ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30' :
              error ? 'bg-red-500/20 text-red-400 border border-red-500/30' :
              'bg-green-500/20 text-green-400 border border-green-500/30'
            }`}>
              {loading ? '🔄 Chargement...' :
               error ? '⚠️ Données limitées' :
               '✅ Données temps réel'}
            </div>

            <button
              onClick={loadRealData}
              disabled={loading}
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 text-white rounded-lg transition-colors flex items-center space-x-2"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              <span>Actualiser</span>
            </button>
            <button className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center space-x-2">
              <Download className="w-4 h-4" />
              <span>Rapport</span>
            </button>
          </div>
        </div>

        {/* 🔐 1. Vue d'ensemble de la sécurité */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <div className="bg-gradient-to-br from-purple-500/20 to-purple-600/20 border border-purple-500/30 rounded-xl p-4">
            <div className="text-center">
              <p className="text-purple-300 text-sm font-medium">Score Sécurité</p>
              <p className="text-3xl font-bold text-white">{data.securityOverview.securityScore}%</p>
              <p className="text-purple-400 text-xs">Global</p>
            </div>
          </div>

          <div className="bg-gradient-to-br from-red-500/20 to-red-600/20 border border-red-500/30 rounded-xl p-4">
            <div className="text-center">
              <p className="text-red-300 text-sm font-medium">Vulnérabilités</p>
              <p className="text-3xl font-bold text-white">{data.securityOverview.totalVulnerabilities}</p>
              <p className="text-red-400 text-xs">Détectées</p>
            </div>
          </div>

          <div className="bg-gradient-to-br from-orange-500/20 to-orange-600/20 border border-orange-500/30 rounded-xl p-4">
            <div className="text-center">
              <p className="text-orange-300 text-sm font-medium">Menaces Actives</p>
              <p className="text-3xl font-bold text-white">{data.securityOverview.activeThreats}</p>
              <p className="text-orange-400 text-xs">En cours</p>
            </div>
          </div>

          <div className="bg-gradient-to-br from-yellow-500/20 to-yellow-600/20 border border-yellow-500/30 rounded-xl p-4">
            <div className="text-center">
              <p className="text-yellow-300 text-sm font-medium">Incidents</p>
              <p className="text-3xl font-bold text-white">{data.securityOverview.incidentsToday}</p>
              <p className="text-yellow-400 text-xs">Aujourd'hui</p>
            </div>
          </div>

          <div className="bg-gradient-to-br from-green-500/20 to-green-600/20 border border-green-500/30 rounded-xl p-4">
            <div className="text-center">
              <p className="text-green-300 text-sm font-medium">Résolus</p>
              <p className="text-3xl font-bold text-white">{data.securityOverview.incidentsResolved}</p>
              <p className="text-green-400 text-xs">Total</p>
            </div>
          </div>

          <div className="bg-gradient-to-br from-blue-500/20 to-blue-600/20 border border-blue-500/30 rounded-xl p-4">
            <div className="text-center">
              <p className="text-blue-300 text-sm font-medium">En Attente</p>
              <p className="text-3xl font-bold text-white">{data.securityOverview.incidentsPending}</p>
              <p className="text-blue-400 text-xs">À traiter</p>
            </div>
          </div>
        </div>
      </div>

      {/* État des scanners - Section spécifique PICA */}
      <div className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
        <h2 className="text-xl font-bold text-white mb-6 flex items-center">
          <Target className="w-5 h-5 mr-2 text-purple-400" />
          État des Scanners PICA
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {data.scannerStatus.map((scanner, index) => (
            <div key={index} className={`border rounded-xl p-4 ${getStatusBg(scanner.status)}`}>
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-white font-semibold">{scanner.name}</h3>
                <div className={`w-3 h-3 rounded-full ${
                  scanner.status === 'online' ? 'bg-green-400' :
                  scanner.status === 'running' ? 'bg-yellow-400 animate-pulse' : 'bg-red-400'
                }`}></div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-300">Statut:</span>
                  <span className={getStatusColor(scanner.status)}>{scanner.status}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-300">Dernier scan:</span>
                  <span className="text-white">{scanner.lastScan}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-300">Vulnérabilités:</span>
                  <span className="text-red-400 font-bold">{scanner.vulnerabilities}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 🕵️‍♀️ 2. Analyses et scans */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Statistiques des scans */}
        <div className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <h2 className="text-xl font-bold text-white mb-6 flex items-center">
            <Target className="w-5 h-5 mr-2 text-purple-400" />
            Statistiques des Scans
          </h2>
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="bg-gray-700/30 rounded-lg p-4 text-center">
              <p className="text-3xl font-bold text-white">{data.scanStats.total}</p>
              <p className="text-gray-300 text-sm">Total</p>
            </div>
            <div className="bg-yellow-500/20 rounded-lg p-4 text-center">
              <p className="text-3xl font-bold text-yellow-400">{data.scanStats.running}</p>
              <p className="text-gray-300 text-sm">En cours</p>
            </div>
            <div className="bg-green-500/20 rounded-lg p-4 text-center">
              <p className="text-3xl font-bold text-green-400">{data.scanStats.completed}</p>
              <p className="text-gray-300 text-sm">Terminés</p>
            </div>
            <div className="bg-red-500/20 rounded-lg p-4 text-center">
              <p className="text-3xl font-bold text-red-400">{data.scanStats.failed}</p>
              <p className="text-gray-300 text-sm">Échecs</p>
            </div>
          </div>

          {/* Répartition par type */}
          <div className="space-y-3">
            <h3 className="text-white font-semibold">Répartition par type</h3>
            {data.scanStats.byType.map((type, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 rounded" style={{ backgroundColor: type.color }}></div>
                  <span className="text-gray-300">{type.type}</span>
                </div>
                <span className="text-white font-bold">{type.count}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Évolution des vulnérabilités */}
        <div className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <h2 className="text-xl font-bold text-white mb-6 flex items-center">
            <TrendingUp className="w-5 h-5 mr-2 text-purple-400" />
            Évolution des Vulnérabilités (7j)
          </h2>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={data.vulnerabilityTrend}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis dataKey="day" stroke="#9CA3AF" />
              <YAxis stroke="#9CA3AF" />
              <Tooltip
                contentStyle={{
                  backgroundColor: '#1F2937',
                  border: '1px solid #374151',
                  borderRadius: '8px',
                  color: '#F9FAFB'
                }}
              />
              <Legend />
              <Bar dataKey="critical" stackId="a" fill="#DC2626" name="Critique" />
              <Bar dataKey="high" stackId="a" fill="#EA580C" name="Élevé" />
              <Bar dataKey="medium" stackId="a" fill="#D97706" name="Moyen" />
              <Bar dataKey="low" stackId="a" fill="#65A30D" name="Faible" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* 📊 3. IA et Analyses avancées + 🧠 4. Phishing et Réputation */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* IA et Analyses avancées */}
        <div className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <h2 className="text-xl font-bold text-white mb-6 flex items-center">
            <Eye className="w-5 h-5 mr-2 text-purple-400" />
            IA et Analyses Avancées
          </h2>

          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="bg-purple-500/20 rounded-lg p-4 text-center">
              <p className="text-2xl font-bold text-purple-400">{data.aiAnalytics.alertsClassified}</p>
              <p className="text-gray-300 text-sm">Alertes classifiées</p>
            </div>
            <div className="bg-green-500/20 rounded-lg p-4 text-center">
              <p className="text-2xl font-bold text-green-400">{data.aiAnalytics.confidenceRate}%</p>
              <p className="text-gray-300 text-sm">Taux de confiance</p>
            </div>
            <div className="bg-red-500/20 rounded-lg p-4 text-center">
              <p className="text-2xl font-bold text-red-400">{data.aiAnalytics.falsePositives}</p>
              <p className="text-gray-300 text-sm">Faux positifs</p>
            </div>
            <div className="bg-blue-500/20 rounded-lg p-4 text-center">
              <p className="text-2xl font-bold text-blue-400">{data.aiAnalytics.correlatedEvents}</p>
              <p className="text-gray-300 text-sm">Événements corrélés</p>
            </div>
          </div>

          <div className="bg-gray-700/30 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-300">Rapports IA générés</span>
              <span className="text-white font-bold">{data.aiAnalytics.reportsGenerated}</span>
            </div>
          </div>
        </div>

        {/* Phishing et Réputation */}
        <div className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <h2 className="text-xl font-bold text-white mb-6 flex items-center">
            <Globe className="w-5 h-5 mr-2 text-purple-400" />
            Phishing et Réputation
          </h2>

          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-red-500/10 border border-red-500/30 rounded-lg">
              <div>
                <p className="text-white font-medium">Domaines bloqués</p>
                <p className="text-gray-400 text-sm">Dernières 24h</p>
              </div>
              <p className="text-2xl font-bold text-red-400">{data.phishingData.domainsBlocked}</p>
            </div>

            <div className="flex items-center justify-between p-3 bg-orange-500/10 border border-orange-500/30 rounded-lg">
              <div>
                <p className="text-white font-medium">IPs suspectes</p>
                <p className="text-gray-400 text-sm">En surveillance</p>
              </div>
              <p className="text-2xl font-bold text-orange-400">{data.phishingData.ipsSuspicious}</p>
            </div>

            <div className="flex items-center justify-between p-3 bg-blue-500/10 border border-blue-500/30 rounded-lg">
              <div>
                <p className="text-white font-medium">Emails analysés</p>
                <p className="text-gray-400 text-sm">Aujourd'hui</p>
              </div>
              <p className="text-2xl font-bold text-blue-400">{data.phishingData.emailsAnalyzed}</p>
            </div>

            <div className="flex items-center justify-between p-3 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
              <div>
                <p className="text-white font-medium">Phishing détecté</p>
                <p className="text-gray-400 text-sm">Bloqués automatiquement</p>
              </div>
              <p className="text-2xl font-bold text-yellow-400">{data.phishingData.phishingDetected}</p>
            </div>

            <div className="bg-gray-700/30 rounded-lg p-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Vérifications réputation</span>
                <span className="text-white font-bold">{data.phishingData.reputationChecks}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* ⚠️ 7. Alertes temps réel et activité récente */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Alertes temps réel */}
        <div className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-white flex items-center">
              <AlertTriangle className="w-5 h-5 mr-2 text-purple-400" />
              Alertes Temps Réel
            </h2>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
              <span className="text-red-400 text-sm">Live</span>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-red-500/10 border border-red-500/30 rounded-lg">
              <div className="flex items-center space-x-3">
                <AlertTriangle className="w-5 h-5 text-red-400" />
                <div>
                  <div className="text-white font-medium">Tentative d'intrusion détectée</div>
                  <div className="text-gray-400 text-sm">IP: ************* - Échecs multiples</div>
                </div>
              </div>
              <span className="text-red-400 text-sm">Maintenant</span>
            </div>

            <div className="flex items-center justify-between p-4 bg-orange-500/10 border border-orange-500/30 rounded-lg">
              <div className="flex items-center space-x-3">
                <Bug className="w-5 h-5 text-orange-400" />
                <div>
                  <div className="text-white font-medium">Malware détecté</div>
                  <div className="text-gray-400 text-sm">Fichier en quarantaine</div>
                </div>
              </div>
              <span className="text-orange-400 text-sm">Il y a 5 min</span>
            </div>

            <div className="flex items-center justify-between p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
              <div className="flex items-center space-x-3">
                <Globe className="w-5 h-5 text-yellow-400" />
                <div>
                  <div className="text-white font-medium">Phishing bloqué</div>
                  <div className="text-gray-400 text-sm">URL malveillante interceptée</div>
                </div>
              </div>
              <span className="text-yellow-400 text-sm">Il y a 12 min</span>
            </div>
          </div>
        </div>

        {/* Activité récente PICA */}
        <div className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <h2 className="text-xl font-bold text-white mb-6 flex items-center">
            <Clock className="w-5 h-5 mr-2 text-purple-400" />
            Journal d'Activité PICA
          </h2>

          <div className="space-y-4">
            <div className="flex items-center space-x-4 p-3 bg-gray-700/30 rounded-lg">
              <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
              <div className="flex-1">
                <div className="text-white text-sm">Scan OpenVAS terminé</div>
                <div className="text-gray-400 text-xs">89 vulnérabilités trouvées • Il y a 2 min</div>
              </div>
              <Target className="w-4 h-4 text-blue-400" />
            </div>

            <div className="flex items-center space-x-4 p-3 bg-gray-700/30 rounded-lg">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <div className="flex-1">
                <div className="text-white text-sm">Base signatures mise à jour</div>
                <div className="text-gray-400 text-xs">Nouvelles règles malware • Il y a 15 min</div>
              </div>
              <Database className="w-4 h-4 text-green-400" />
            </div>

            <div className="flex items-center space-x-4 p-3 bg-gray-700/30 rounded-lg">
              <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
              <div className="flex-1">
                <div className="text-white text-sm">Rapport IA généré</div>
                <div className="text-gray-400 text-xs">Analyse automatique terminée • Il y a 1h</div>
              </div>
              <Eye className="w-4 h-4 text-purple-400" />
            </div>

            <div className="flex items-center space-x-4 p-3 bg-gray-700/30 rounded-lg">
              <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
              <div className="flex-1">
                <div className="text-white text-sm">Connexion administrateur</div>
                <div className="text-gray-400 text-xs">{user?.username || 'Admin'} connecté • Il y a 2h</div>
              </div>
              <Users className="w-4 h-4 text-yellow-400" />
            </div>
          </div>

          <div className="mt-6 pt-4 border-t border-gray-700/50">
            <button
              onClick={() => navigate('/admin/activity')}
              className="w-full text-purple-400 hover:text-purple-300 text-sm font-medium transition-colors"
            >
              Voir le journal complet d'activité
            </button>
          </div>
        </div>
      </div>

      {/* 📁 5. Accès rapide aux rapports */}
      <div className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-white flex items-center">
            <Download className="w-5 h-5 mr-2 text-purple-400" />
            Rapports et Exports
          </h2>
          <button
            onClick={() => navigate('/reports')}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors text-sm"
          >
            Voir tous les rapports
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <button className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-4 text-center hover:bg-blue-500/30 transition-colors">
            <Download className="w-6 h-6 text-blue-400 mx-auto mb-2" />
            <p className="text-white font-medium">Rapport Vulnérabilités</p>
            <p className="text-gray-400 text-xs">PDF/CSV</p>
          </button>

          <button className="bg-green-500/20 border border-green-500/30 rounded-lg p-4 text-center hover:bg-green-500/30 transition-colors">
            <Download className="w-6 h-6 text-green-400 mx-auto mb-2" />
            <p className="text-white font-medium">Rapport IA</p>
            <p className="text-gray-400 text-xs">JSON/PDF</p>
          </button>

          <button className="bg-orange-500/20 border border-orange-500/30 rounded-lg p-4 text-center hover:bg-orange-500/30 transition-colors">
            <Download className="w-6 h-6 text-orange-400 mx-auto mb-2" />
            <p className="text-white font-medium">Rapport Phishing</p>
            <p className="text-gray-400 text-xs">CSV/JSON</p>
          </button>

          <button className="bg-purple-500/20 border border-purple-500/30 rounded-lg p-4 text-center hover:bg-purple-500/30 transition-colors">
            <Download className="w-6 h-6 text-purple-400 mx-auto mb-2" />
            <p className="text-white font-medium">Rapport Complet</p>
            <p className="text-gray-400 text-xs">PDF</p>
          </button>
        </div>
      </div>
    </div>
  );
}
