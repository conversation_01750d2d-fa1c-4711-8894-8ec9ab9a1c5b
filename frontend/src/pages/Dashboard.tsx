import React, { useState, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useRole } from '../hooks/useRole';
import {
  Shield,
  Activity,
  AlertTriangle,
  CheckCircle,
  Target,
  Bug,
  Globe,
  BarChart3,
  TrendingUp,
  TrendingDown,
  Clock,
  Zap,
  Eye,
  RefreshCw,
  Search,
  Bell,
  Users,
  Database,
  Network,
  Lock,
  Wifi,
  Server,
  FileText,
  Download,
  Settings,
  Play,
  Pause,
  XCircle,
  MapPin,
  Calendar,
  Filter
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { dashboardService, DashboardData } from '../services/dashboardService';

export default function Dashboard() {
  const { user } = useAuth();
  const { role } = useRole();
  const navigate = useNavigate();
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [searchQuery, setSearchQuery] = useState('');
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await dashboardService.getDashboardData();
      setDashboardData(data);
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Failed to fetch dashboard data:', err);
      setError('Failed to load dashboard data');
      // Set fallback mock data
      setDashboardData({
        stats: {
          totalScans: 127,
          activeScans: 42,
          criticalThreats: 8,
          securityAlerts: 18,
          securityScore: 94,
          scanTrends: {
            totalChange: 8,
            activeChange: 5,
            threatsChange: -2,
            alertsChange: 3
          }
        },
        systemStatus: {
          pentesting: 'online',
          phishing: 'online',
          malware: 'online',
          analytics: 'online',
          incidents: 'warning'
        },
        attackSources: [
          { ip: '*************', country: 'United States', attacks: 127 },
          { ip: '**************', country: 'Russia', attacks: 98 },
          { ip: '************', country: 'China', attacks: 76 },
          { ip: '*************', country: 'Brazil', attacks: 54 },
          { ip: '*************', country: 'India', attacks: 32 }
        ],
        recentIncidents: [
          { id: 1, title: 'Suspicious Login Attempt', severity: 'high', status: 'investigating', created: '2 hours ago', assigned: 'Security Team' },
          { id: 2, title: 'Malware Detection on Endpoint', severity: 'critical', status: 'resolved', created: '4 hours ago', assigned: 'IT Team' },
          { id: 3, title: 'Phishing Email Detected', severity: 'medium', status: 'monitoring', created: '6 hours ago', assigned: 'SOC Team' }
        ]
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();

    // Update timestamp every minute
    const interval = setInterval(() => {
      setLastUpdated(new Date());
    }, 60000);

    // Refresh data every 5 minutes
    const dataInterval = setInterval(() => {
      fetchDashboardData();
    }, 300000);

    return () => {
      clearInterval(interval);
      clearInterval(dataInterval);
    };
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour12: true,
      hour: 'numeric',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'text-green-400';
      case 'warning': return 'text-yellow-400';
      case 'offline': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-400 bg-red-500/10 border-red-500/30';
      case 'high': return 'text-orange-400 bg-orange-500/10 border-orange-500/30';
      case 'medium': return 'text-yellow-400 bg-yellow-500/10 border-yellow-500/30';
      case 'low': return 'text-green-400 bg-green-500/10 border-green-500/30';
      default: return 'text-gray-400 bg-gray-500/10 border-gray-500/30';
    }
  };

  const getTrendIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="w-4 h-4 text-green-400" />;
    if (change < 0) return <TrendingDown className="w-4 h-4 text-red-400" />;
    return <Activity className="w-4 h-4 text-gray-400" />;
  };

  const getTrendColor = (change: number) => {
    if (change > 0) return 'text-green-400';
    if (change < 0) return 'text-red-400';
    return 'text-gray-400';
  };

  if (loading && !dashboardData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="w-12 h-12 text-purple-400 mx-auto mb-4 animate-spin" />
          <h2 className="text-xl font-bold text-white mb-2">Loading Dashboard</h2>
          <p className="text-gray-400">Fetching security data...</p>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <XCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-white mb-2">Failed to Load Dashboard</h2>
          <p className="text-gray-400 mb-4">Unable to fetch dashboard data</p>
          <button
            onClick={fetchDashboardData}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2 flex items-center">
              <Shield className="w-8 h-8 text-purple-400 mr-3" />
              Security Dashboard
            </h1>
            <p className="text-gray-300">
              Real-time monitoring • Last updated: {formatTime(lastUpdated)}
              {error && <span className="text-yellow-400 ml-2">• Using cached data</span>}
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors"
              />
            </div>
            <button
              onClick={fetchDashboardData}
              disabled={loading}
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 text-white rounded-lg transition-colors flex items-center space-x-2"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        {/* Total Scans */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-6 shadow-xl hover:border-purple-500/50 transition-all duration-300">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-blue-500/20 rounded-xl">
              <Database className="w-6 h-6 text-blue-400" />
            </div>
            <div className="flex items-center space-x-1">
              {getTrendIcon(dashboardData.stats.scanTrends.totalChange)}
              <span className={`text-sm ${getTrendColor(dashboardData.stats.scanTrends.totalChange)}`}>
                {dashboardData.stats.scanTrends.totalChange}%
              </span>
            </div>
          </div>
          <div>
            <h3 className="text-2xl font-bold text-white">{dashboardData.stats.totalScans}</h3>
            <p className="text-gray-400 text-sm">Total Scans</p>
            <p className="text-xs text-gray-500 mt-1">All time</p>
          </div>
        </div>

        {/* Active Scans */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-6 shadow-xl hover:border-yellow-500/50 transition-all duration-300">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-yellow-500/20 rounded-xl">
              <Activity className="w-6 h-6 text-yellow-400" />
            </div>
            <div className="flex items-center space-x-1">
              {getTrendIcon(dashboardData.stats.scanTrends.activeChange)}
              <span className={`text-sm ${getTrendColor(dashboardData.stats.scanTrends.activeChange)}`}>
                {dashboardData.stats.scanTrends.activeChange}%
              </span>
            </div>
          </div>
          <div>
            <h3 className="text-2xl font-bold text-white">{dashboardData.stats.activeScans}</h3>
            <p className="text-gray-400 text-sm">Active Scans</p>
            <p className="text-xs text-gray-500 mt-1">Last 24 hours</p>
          </div>
        </div>

        {/* Critical Threats */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-6 shadow-xl hover:border-red-500/50 transition-all duration-300">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-red-500/20 rounded-xl">
              <AlertTriangle className="w-6 h-6 text-red-400" />
            </div>
            <div className="flex items-center space-x-1">
              {getTrendIcon(dashboardData.stats.scanTrends.threatsChange)}
              <span className={`text-sm ${getTrendColor(dashboardData.stats.scanTrends.threatsChange)}`}>
                {Math.abs(dashboardData.stats.scanTrends.threatsChange)}%
              </span>
            </div>
          </div>
          <div>
            <h3 className="text-2xl font-bold text-white">{dashboardData.stats.criticalThreats}</h3>
            <p className="text-gray-400 text-sm">Critical Threats</p>
            <p className="text-xs text-gray-500 mt-1">High priority</p>
          </div>
        </div>

        {/* Security Alerts */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-6 shadow-xl hover:border-orange-500/50 transition-all duration-300">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-orange-500/20 rounded-xl">
              <Bell className="w-6 h-6 text-orange-400" />
            </div>
            <div className="flex items-center space-x-1">
              {getTrendIcon(dashboardData.stats.scanTrends.alertsChange)}
              <span className={`text-sm ${getTrendColor(dashboardData.stats.scanTrends.alertsChange)}`}>
                {dashboardData.stats.scanTrends.alertsChange}%
              </span>
            </div>
          </div>
          <div>
            <h3 className="text-2xl font-bold text-white">{dashboardData.stats.securityAlerts}</h3>
            <p className="text-gray-400 text-sm">Security Alerts</p>
            <p className="text-xs text-gray-500 mt-1">Unread</p>
          </div>
        </div>

        {/* Security Score */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-6 shadow-xl hover:border-green-500/50 transition-all duration-300">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-green-500/20 rounded-xl">
              <Shield className="w-6 h-6 text-green-400" />
            </div>
            <div className="text-xs text-gray-400">/ 100</div>
          </div>
          <div>
            <h3 className="text-2xl font-bold text-white">{dashboardData.stats.securityScore}</h3>
            <p className="text-gray-400 text-sm">Security Score</p>
            <p className="text-xs text-gray-500 mt-1">Based on threat detection and system health</p>
          </div>
        </div>
      </div>

      {/* System Status & Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* System Status */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <h2 className="text-xl font-bold text-white mb-6 flex items-center">
            <Server className="w-5 h-5 mr-2 text-purple-400" />
            System Status
          </h2>
          <div className="space-y-4">
            {Object.entries(dashboardData.systemStatus).map(([service, status]) => (
              <div key={service} className="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${
                    status === 'online' ? 'bg-green-400' :
                    status === 'warning' ? 'bg-yellow-400' : 'bg-red-400'
                  }`}></div>
                  <span className="text-white capitalize">{service}</span>
                </div>
                <span className={`text-sm font-medium ${getStatusColor(status)} capitalize`}>
                  {status}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <h2 className="text-xl font-bold text-white mb-6 flex items-center">
            <Zap className="w-5 h-5 mr-2 text-purple-400" />
            Quick Actions
          </h2>
          <div className="grid grid-cols-2 gap-4">
            <button
              onClick={() => navigate('/app')}
              className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white p-4 rounded-xl transition-all duration-200 transform hover:scale-105 flex items-center space-x-2"
            >
              <Target className="w-5 h-5" />
              <span>Pentesting</span>
            </button>
            <button
              onClick={() => navigate('/phishing')}
              className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white p-4 rounded-xl transition-all duration-200 transform hover:scale-105 flex items-center space-x-2"
            >
              <Globe className="w-5 h-5" />
              <span>Phishing Detection</span>
            </button>
            <button
              onClick={() => navigate('/malware')}
              className="bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white p-4 rounded-xl transition-all duration-200 transform hover:scale-105 flex items-center space-x-2"
            >
              <Bug className="w-5 h-5" />
              <span>Malware Detection</span>
            </button>
            <button
              onClick={() => navigate('/analytics')}
              className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white p-4 rounded-xl transition-all duration-200 transform hover:scale-105 flex items-center space-x-2"
            >
              <BarChart3 className="w-5 h-5" />
              <span>Analytics</span>
            </button>
            <button
              onClick={() => navigate('/incidents')}
              className="bg-gradient-to-r from-orange-600 to-yellow-600 hover:from-orange-700 hover:to-yellow-700 text-white p-4 rounded-xl transition-all duration-200 transform hover:scale-105 flex items-center space-x-2"
            >
              <AlertTriangle className="w-5 h-5" />
              <span>Incidents</span>
            </button>
            <button
              onClick={() => navigate('/settings')}
              className="bg-gradient-to-r from-gray-600 to-slate-600 hover:from-gray-700 hover:to-slate-700 text-white p-4 rounded-xl transition-all duration-200 transform hover:scale-105 flex items-center space-x-2"
            >
              <Settings className="w-5 h-5" />
              <span>Settings</span>
            </button>
          </div>
        </div>
      </div>

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Incident Trends */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <h2 className="text-xl font-bold text-white mb-6 flex items-center">
            <BarChart3 className="w-5 h-5 mr-2 text-purple-400" />
            Incident Trends (Last 24h)
          </h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-300">Malware Detections</span>
              <div className="flex items-center space-x-2">
                <div className="w-20 h-2 bg-gray-700 rounded-full">
                  <div className="w-3/4 h-2 bg-red-500 rounded-full"></div>
                </div>
                <span className="text-white text-sm">75%</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-300">Phishing Attempts</span>
              <div className="flex items-center space-x-2">
                <div className="w-20 h-2 bg-gray-700 rounded-full">
                  <div className="w-1/2 h-2 bg-orange-500 rounded-full"></div>
                </div>
                <span className="text-white text-sm">50%</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-300">Network Intrusions</span>
              <div className="flex items-center space-x-2">
                <div className="w-20 h-2 bg-gray-700 rounded-full">
                  <div className="w-1/3 h-2 bg-yellow-500 rounded-full"></div>
                </div>
                <span className="text-white text-sm">33%</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-300">Data Breaches</span>
              <div className="flex items-center space-x-2">
                <div className="w-20 h-2 bg-gray-700 rounded-full">
                  <div className="w-1/4 h-2 bg-green-500 rounded-full"></div>
                </div>
                <span className="text-white text-sm">25%</span>
              </div>
            </div>
          </div>
        </div>

        {/* Attack Type Distribution */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <h2 className="text-xl font-bold text-white mb-6 flex items-center">
            <Zap className="w-5 h-5 mr-2 text-purple-400" />
            Attack Type Distribution
          </h2>
          <div className="space-y-4">
            <div className="text-center">
              <div className="relative w-32 h-32 mx-auto mb-4">
                <div className="absolute inset-0 rounded-full border-8 border-gray-700"></div>
                <div className="absolute inset-0 rounded-full border-8 border-red-500 border-t-transparent transform rotate-45"></div>
                <div className="absolute inset-2 rounded-full border-6 border-orange-500 border-t-transparent transform rotate-90"></div>
                <div className="absolute inset-4 rounded-full border-4 border-yellow-500 border-t-transparent transform rotate-180"></div>
                <div className="absolute inset-6 rounded-full border-2 border-green-500 border-t-transparent transform rotate-270"></div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-gray-300 text-sm">Malware</span>
                </div>
                <span className="text-white text-sm">35%</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                  <span className="text-gray-300 text-sm">Phishing</span>
                </div>
                <span className="text-white text-sm">28%</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <span className="text-gray-300 text-sm">Network</span>
                </div>
                <span className="text-white text-sm">22%</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-gray-300 text-sm">Other</span>
                </div>
                <span className="text-white text-sm">15%</span>
              </div>
            </div>
          </div>
        </div>

        {/* Global Threat Map */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <h2 className="text-xl font-bold text-white mb-6 flex items-center">
            <Globe className="w-5 h-5 mr-2 text-purple-400" />
            Global Threat Map
          </h2>
          <div className="space-y-4">
            <div className="bg-gray-700/30 rounded-lg p-4 text-center">
              <MapPin className="w-16 h-16 text-purple-400 mx-auto mb-2" />
              <p className="text-gray-300 text-sm">Interactive threat map</p>
              <p className="text-gray-400 text-xs">Real-time attack visualization</p>
            </div>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="bg-red-500/20 p-2 rounded text-center">
                <div className="text-red-400 font-bold">High Risk</div>
                <div className="text-gray-300">15 regions</div>
              </div>
              <div className="bg-yellow-500/20 p-2 rounded text-center">
                <div className="text-yellow-400 font-bold">Medium Risk</div>
                <div className="text-gray-300">8 regions</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Section - Attack Sources and Recent Incidents */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Attack Sources */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <h2 className="text-xl font-bold text-white mb-6 flex items-center">
            <Network className="w-5 h-5 mr-2 text-purple-400" />
            Top Attack Sources
            <span className="ml-auto text-sm text-gray-400">Last 7 days</span>
          </h2>
          <div className="space-y-4">
            {dashboardData.attackSources.map((source, index) => (
              <div key={source.ip} className="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg hover:bg-gray-700/50 transition-colors">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-red-500/20 rounded-lg flex items-center justify-center">
                    <span className="text-red-400 font-bold text-sm">{index + 1}</span>
                  </div>
                  <div>
                    <div className="text-white font-medium">{source.ip}</div>
                    <div className="text-gray-400 text-sm">{source.country}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-white font-bold">{source.attacks}</div>
                  <div className="text-gray-400 text-sm">attacks</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Incidents */}
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-white flex items-center">
              <AlertTriangle className="w-5 h-5 mr-2 text-purple-400" />
              Recent Incidents
            </h2>
            <button
              onClick={() => navigate('/incidents')}
              className="text-purple-400 hover:text-purple-300 text-sm font-medium transition-colors"
            >
              View all
            </button>
          </div>

          {dashboardData.recentIncidents.length > 0 ? (
            <div className="space-y-4">
              {dashboardData.recentIncidents.map((incident) => (
                <div key={incident.id} className="p-4 bg-gray-700/30 rounded-lg hover:bg-gray-700/50 transition-colors">
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="text-white font-medium">{incident.title}</h3>
                    <span className={`px-2 py-1 rounded text-xs font-medium border ${getSeverityColor(incident.severity)}`}>
                      {incident.severity}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-4">
                      <span className="text-gray-400">Status: <span className="text-white">{incident.status}</span></span>
                      <span className="text-gray-400">Assigned: <span className="text-white">{incident.assigned}</span></span>
                    </div>
                    <span className="text-gray-400">{incident.created}</span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <CheckCircle className="w-12 h-12 text-green-400 mx-auto mb-3" />
              <h3 className="text-white font-medium mb-2">No incidents found</h3>
              <p className="text-gray-400 text-sm">When new incidents occur, they will appear here</p>
            </div>
          )}
        </div>
      </div>

      {/* Alerts Section */}
      <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
        <h2 className="text-xl font-bold text-white mb-6 flex items-center">
          <Bell className="w-5 h-5 mr-2 text-purple-400" />
          Alerts
        </h2>
        <div className="text-center py-8">
          <Bell className="w-12 h-12 text-gray-400 mx-auto mb-3" />
          <h3 className="text-white font-medium mb-2">No alerts</h3>
          <p className="text-gray-400 text-sm">When new alerts arrive, they will appear here</p>
        </div>
      </div>
    </div>
  );
}
