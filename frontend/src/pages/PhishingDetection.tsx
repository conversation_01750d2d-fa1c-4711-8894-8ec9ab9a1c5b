import { useState, useEffect } from 'react';
import {
  Shield,
  Globe,
  Search,
  AlertTriangle,
  CheckCircle,
  Clock,
  RefreshCw,
  Activity,
  Zap,
  AlertCircle,
  Ban,
  FileDown,
} from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useModalContext } from '../contexts/ModalContext';
import Cookies from 'js-cookie';
import {
  quickPhishingCheck,
  getAnalysisHistory,
  getSuspiciousLinks,
  PhishingAnalysis,
  SuspiciousLink,
} from '../services/phishingService';

// Interfaces are now imported from the service

export default function PhishingDetection() {
  const { user } = useAuth();
  const { showAlert } = useModalContext();
  const [url, setUrl] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [currentAnalysis, setCurrentAnalysis] = useState<PhishingAnalysis | null>(null);
  const [analysisHistory, setAnalysisHistory] = useState<PhishingAnalysis[]>([]);
  const [suspiciousLinks, setSuspiciousLinks] = useState<SuspiciousLink[]>([]);
  const [activeTab, setActiveTab] = useState<'analyze' | 'history' | 'suspicious'>('analyze');
  const [loadingHistory, setLoadingHistory] = useState(false);
  const [loadingSuspicious, setLoadingSuspicious] = useState(false);
  const [exportingCSV, setExportingCSV] = useState(false);

  // Load analysis history
  const loadAnalysisHistory = async () => {
    setLoadingHistory(true);
    try {
      const data = await getAnalysisHistory(1, 50);
      setAnalysisHistory(data.analyses || []);
    } catch (error) {
      console.error('Error loading history:', error);
    } finally {
      setLoadingHistory(false);
    }
  };

  // Load suspicious links
  const loadSuspiciousLinks = async () => {
    setLoadingSuspicious(true);
    try {
      const data = await getSuspiciousLinks(1, 50);
      setSuspiciousLinks(data.suspicious_links || []);
    } catch (error) {
      console.error('Error loading suspicious links:', error);
    } finally {
      setLoadingSuspicious(false);
    }
  };

  // Function to export all phishing analyses to CSV
  const handleExportPhishingCSV = async () => {
    try {
      setExportingCSV(true);
      console.log('📊 Exporting all phishing analyses to CSV');

      const token = Cookies.get('token');
      if (!token) {
        showAlert({
          type: 'error',
          title: 'Authentication required',
          message: 'Please log in to export data',
        });
        return;
      }

      const response = await fetch(`http://localhost:5000/api/export/phishing/csv`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        // Créer un blob à partir de la réponse
        const blob = await response.blob();

        // Créer un URL temporaire pour le blob
        const url = window.URL.createObjectURL(blob);

        // Créer un lien de téléchargement
        const link = document.createElement('a');
        link.href = url;

        // Générer le nom du fichier
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
        link.download = `PICA_Phishing_Export_${timestamp}.csv`;

        // Déclencher le téléchargement
        document.body.appendChild(link);
        link.click();

        // Nettoyer
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        console.log('✅ Phishing CSV exported successfully');

        showAlert({
          type: 'success',
          title: 'Export successful',
          message: `${analysisHistory.length} phishing analysis(es) exported successfully to CSV.`,
        });
      } else {
        const errorData = await response.json();
        console.error('❌ Failed to export phishing CSV:', response.status, errorData);
        showAlert({
          type: 'error',
          title: 'Export error',
          message: 'CSV export error: ' + (errorData.message || 'Unknown error'),
        });
      }
    } catch (error) {
      console.error('❌ Error exporting phishing CSV:', error);
      showAlert({
        type: 'error',
        title: 'Export error',
        message: 'An error occurred during CSV export',
      });
    } finally {
      setExportingCSV(false);
    }
  };

  // Analyze a URL (quick check)
  const analyzeUrl = async () => {
    if (!url.trim()) return;

    setIsAnalyzing(true);
    setCurrentAnalysis(null);

    try {
      const data = await quickPhishingCheck(url.trim());
      setCurrentAnalysis({
        _id: data.analysis_id,
        analysis_id: data.analysis_id,
        url: data.url,
        status: 'completed',
        created_at: new Date().toISOString(),
        completed_at: new Date().toISOString(),
        result: data.result,
      });

      // Reload history and suspicious links
      loadAnalysisHistory();
      loadSuspiciousLinks();
    } catch (error) {
      console.error('Analysis error:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Get color according to risk level
  const getRiskColor = (score: number) => {
    if (score >= 70) return 'text-red-600';
    if (score >= 40) return 'text-orange-600';
    return 'text-green-600';
  };

  // Get icon according to risk level
  const getRiskIcon = (score: number) => {
    if (score >= 70) return <AlertTriangle className="w-5 h-5 text-red-600" />;
    if (score >= 40) return <AlertCircle className="w-5 h-5 text-orange-600" />;
    return <CheckCircle className="w-5 h-5 text-green-600" />;
  };

  // Load data on component mount
  useEffect(() => {
    loadAnalysisHistory();
    loadSuspiciousLinks();
  }, []);

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-8 shadow-2xl">
        <div className="flex items-center space-x-4 mb-6">
          <div className="w-16 h-16 bg-gradient-to-br from-green-400 via-green-500 to-green-700 rounded-xl flex items-center justify-center shadow-lg">
            <Globe className="w-8 h-8 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Phishing Detection</h1>
            <p className="text-gray-300">
              Analyze suspicious URLs and detect phishing attempts in real time
            </p>
          </div>
        </div>

        {/* Tab navigation */}
        <div className="flex space-x-2">
          <button
            onClick={() => setActiveTab('analyze')}
            className={`px-6 py-3 rounded-lg font-medium transition-all ${
              activeTab === 'analyze'
                ? 'bg-gradient-to-r from-green-400 via-green-500 to-green-700 text-white shadow-lg'
                : 'text-gray-300 hover:text-white hover:bg-gray-700/50'
            }`}
          >
            <Search className="w-5 h-5 inline mr-2" />
            Analyze
          </button>
          <button
            onClick={() => setActiveTab('history')}
            className={`px-6 py-3 rounded-lg font-medium transition-all ${
              activeTab === 'history'
                ? 'bg-gradient-to-r from-green-400 via-green-500 to-green-700 text-white shadow-lg'
                : 'text-gray-300 hover:text-white hover:bg-gray-700/50'
            }`}
          >
            <Clock className="w-5 h-5 inline mr-2" />
            History
          </button>
          <button
            onClick={() => setActiveTab('suspicious')}
            className={`px-6 py-3 rounded-lg font-medium transition-all ${
              activeTab === 'suspicious'
                ? 'bg-gradient-to-r from-green-400 via-green-500 to-green-700 text-white shadow-lg'
                : 'text-gray-300 hover:text-white hover:bg-gray-700/50'
            }`}
          >
            <AlertTriangle className="w-5 h-5 inline mr-2" />
            Suspicious Links
          </button>
        </div>
      </div>

      {/* Content according to active tab */}
      {activeTab === 'analyze' && (
        <div className="space-y-8">
          {/* Analysis form */}
          <div className="bg-gradient-to-br from-gray-800/40 to-gray-900/60 backdrop-blur-sm border border-gray-600/30 rounded-2xl p-8 shadow-xl">
            <div className="flex items-center space-x-4 mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-green-400 via-green-500 to-green-700 rounded-xl flex items-center justify-center shadow-lg">
                <Search className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">Analyze URL</h2>
                <p className="text-gray-400">Enter the URL to check for phishing attempts</p>
              </div>
            </div>

            <div className="flex gap-4">
              <div className="flex-1">
                <input
                  type="url"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                  placeholder="Enter URL to analyze (e.g. https://example.com)"
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                  disabled={isAnalyzing}
                />
              </div>
              <button
                onClick={analyzeUrl}
                disabled={isAnalyzing || !url.trim()}
                className="px-8 py-3 bg-gradient-to-r from-green-400 via-green-500 to-green-700 hover:from-green-500 hover:via-green-600 hover:to-green-800 disabled:from-gray-600 disabled:to-gray-700 disabled:cursor-not-allowed text-white font-medium rounded-xl transition-all duration-200 flex items-center shadow-lg"
              >
                {isAnalyzing ? (
                  <>
                    <RefreshCw className="w-5 h-5 mr-2 animate-spin" />
                    Analyzing...
                  </>
                ) : (
                  <>
                    <Zap className="w-5 h-5 mr-2" />
                    Analyze
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Analysis result */}
          {currentAnalysis && currentAnalysis.result && (
            <div className="bg-gradient-to-br from-gray-800/40 to-gray-900/60 backdrop-blur-sm border border-gray-600/30 rounded-2xl p-8 shadow-xl">
              <div className="flex items-center space-x-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-500 rounded-xl flex items-center justify-center shadow-lg">
                  <Activity className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white">Analysis Result</h3>
                  <p className="text-gray-400">Security verification details</p>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Summary */}
                <div className="space-y-4">
                  <div className="bg-gradient-to-br from-gray-700/50 to-gray-800/50 rounded-xl p-6 border border-gray-600/30">
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-green-200 font-medium">Analyzed URL:</span>
                    </div>
                    <p className="text-white font-mono text-sm break-all bg-gray-900/50 p-3 rounded-lg">
                      {currentAnalysis.url}
                    </p>
                  </div>

                  <div className="bg-gradient-to-br from-gray-700/50 to-gray-800/50 rounded-xl p-6 border border-gray-600/30">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-green-200 font-medium">Risk score:</span>
                      {getRiskIcon(currentAnalysis.result.risk_score)}
                    </div>
                    <div className="flex items-center">
                      <span
                        className={`text-3xl font-bold ${getRiskColor(currentAnalysis.result.risk_score)}`}
                      >
                        {currentAnalysis.result.risk_score}%
                      </span>
                      <span
                        className={`ml-3 px-3 py-1 rounded-full text-sm font-medium ${
                          currentAnalysis.result.risk_score >= 70
                            ? 'bg-red-600/30 text-red-300 border border-red-500/30'
                            : currentAnalysis.result.risk_score >= 40
                              ? 'bg-orange-600/30 text-orange-300 border border-orange-500/30'
                              : 'bg-green-600/30 text-green-300 border border-green-500/30'
                        }`}
                      >
                        {currentAnalysis.result.likelihood}
                      </span>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-gray-700/50 to-gray-800/50 rounded-xl p-6 border border-gray-600/30">
                    <span className="text-green-200 font-medium block mb-3">Verdict:</span>
                    <div className="flex items-center">
                      {currentAnalysis.result.is_phishing ? (
                        <>
                          <Ban className="w-6 h-6 text-red-500 mr-3" />
                          <span className="text-red-300 font-medium">
                            Potential Phishing Detected
                          </span>
                        </>
                      ) : (
                        <>
                          <CheckCircle className="w-6 h-6 text-green-500 mr-3" />
                          <span className="text-green-300 font-medium">URL Appears Safe</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>

                {/* Verification details */}
                <div>
                  <h4 className="text-lg font-semibold text-white mb-4 flex items-center">
                    <Search className="w-5 h-5 mr-2 text-green-400" />
                    Verification Details
                  </h4>
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {currentAnalysis.result.checks.map((check, index) => (
                      <div
                        key={index}
                        className={`p-4 rounded-xl border ${
                          check.result === 'Failed'
                            ? 'bg-gradient-to-br from-red-900/30 to-red-800/30 border-red-500/30'
                            : check.result === 'Warning'
                              ? 'bg-gradient-to-br from-orange-900/30 to-orange-800/30 border-orange-500/30'
                              : 'bg-gradient-to-br from-green-900/30 to-green-800/30 border-green-500/30'
                        }`}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium text-white">{check.name}</span>
                          <span
                            className={`text-sm px-3 py-1 rounded-full font-medium ${
                              check.result === 'Failed'
                                ? 'bg-red-600/50 text-red-200 border border-red-500/30'
                                : check.result === 'Warning'
                                  ? 'bg-orange-600/50 text-orange-200 border border-orange-500/30'
                                  : 'bg-green-600/50 text-green-200 border border-green-500/30'
                            }`}
                          >
                            {check.result}
                          </span>
                        </div>
                        <p className="text-sm text-gray-300">{check.description}</p>
                        {check.high_risk && (
                          <div className="mt-3 flex items-center text-red-400 text-xs bg-red-900/20 p-2 rounded-lg">
                            <AlertTriangle className="w-3 h-3 mr-1" />
                            High risk factor
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* History Tab */}
      {activeTab === 'history' && (
        <div className="bg-gradient-to-br from-gray-800/40 to-gray-900/60 backdrop-blur-sm border border-gray-600/30 rounded-2xl p-8 shadow-xl">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-green-400 via-green-500 to-green-700 rounded-xl flex items-center justify-center shadow-lg">
                <Clock className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">Analysis History</h2>
                <p className="text-gray-400">View your previous analyses</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              {/* Export CSV Button */}
              {analysisHistory.length > 0 && (
                <button
                  onClick={handleExportPhishingCSV}
                  disabled={exportingCSV}
                  className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg"
                  title="Export all analyses to CSV"
                >
                  {exportingCSV ? (
                    <>
                      <RefreshCw className="w-4 h-4 animate-spin" />
                      <span>Exporting...</span>
                    </>
                  ) : (
                    <>
                      <FileDown className="w-4 h-4" />
                      <span>Export CSV</span>
                    </>
                  )}
                </button>
              )}

              <button
                onClick={loadAnalysisHistory}
                disabled={loadingHistory}
                className="px-4 py-2 bg-gradient-to-r from-green-400 via-green-500 to-green-700 hover:from-green-500 hover:via-green-600 hover:to-green-800 disabled:from-gray-600 disabled:to-gray-700 text-white rounded-xl transition-all duration-200 flex items-center shadow-lg"
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${loadingHistory ? 'animate-spin' : ''}`} />
                Refresh
              </button>
            </div>
          </div>

          {loadingHistory ? (
            <div className="text-center py-12">
              <RefreshCw className="w-12 h-12 text-green-400 animate-spin mx-auto mb-4" />
              <p className="text-gray-300">Loading history...</p>
            </div>
          ) : analysisHistory.length === 0 ? (
            <div className="text-center py-12">
              <Search className="w-16 h-16 text-gray-500 mx-auto mb-4" />
              <p className="text-gray-400 text-lg">No analysis performed</p>
              <p className="text-gray-500 text-sm">Start by analyzing a URL in the "Analyze" tab</p>
            </div>
          ) : (
            <div className="space-y-4">
              {analysisHistory.map((analysis) => (
                <div
                  key={analysis._id}
                  className="bg-gradient-to-br from-gray-700/30 to-gray-800/30 rounded-xl p-6 border border-gray-600/30 hover:border-gray-500/50 transition-all duration-200"
                >
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      {analysis.result && getRiskIcon(analysis.result.risk_score)}
                      <span className="ml-3 font-medium text-white break-all">{analysis.url}</span>
                    </div>
                    <div className="flex items-center space-x-4">
                      {analysis.result && (
                        <span
                          className={`px-3 py-1 rounded-full text-sm font-medium border ${
                            analysis.result.risk_score >= 70
                              ? 'bg-red-600/30 text-red-300 border-red-500/30'
                              : analysis.result.risk_score >= 40
                                ? 'bg-orange-600/30 text-orange-300 border-orange-500/30'
                                : 'bg-green-600/30 text-green-300 border-green-500/30'
                          }`}
                        >
                          {analysis.result.risk_score}%
                        </span>
                      )}
                      <span className="text-gray-400 text-sm">
                        {new Date(analysis.created_at).toLocaleDateString('en-US', {
                          day: '2-digit',
                          month: '2-digit',
                          year: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit',
                        })}
                      </span>
                    </div>
                  </div>

                  {analysis.result && (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div className="text-gray-400">
                        Niveau:{' '}
                        <span className="text-white font-medium">{analysis.result.likelihood}</span>
                      </div>
                      <div className="text-gray-400">
                        Checks:{' '}
                        <span className="text-white font-medium">
                          {analysis.result.checks.length}
                        </span>
                      </div>
                      <div className="text-gray-400">
                        Failures:{' '}
                        <span className="text-red-300 font-medium">
                          {analysis.result.checks.filter((c) => c.result === 'Failed').length}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Suspicious Links Tab */}
      {activeTab === 'suspicious' && (
        <div className="bg-gradient-to-br from-gray-800/40 to-gray-900/60 backdrop-blur-sm border border-gray-600/30 rounded-2xl p-8 shadow-xl">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-500 rounded-xl flex items-center justify-center shadow-lg">
                <AlertTriangle className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">Suspicious Links Detected</h2>
                <p className="text-gray-400">URLs identified as potentially dangerous</p>
              </div>
            </div>
            <button
              onClick={loadSuspiciousLinks}
              disabled={loadingSuspicious}
              className="px-4 py-2 bg-gradient-to-r from-green-400 via-green-500 to-green-700 hover:from-green-500 hover:via-green-600 hover:to-green-800 disabled:from-gray-600 disabled:to-gray-700 text-white rounded-xl transition-all duration-200 flex items-center shadow-lg"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${loadingSuspicious ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>

          {loadingSuspicious ? (
            <div className="text-center py-12">
              <RefreshCw className="w-12 h-12 text-green-400 animate-spin mx-auto mb-4" />
              <p className="text-gray-300">Loading suspicious links...</p>
            </div>
          ) : suspiciousLinks.length === 0 ? (
            <div className="text-center py-12">
              <Globe className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <p className="text-gray-400 text-lg">No suspicious links detected</p>
              <p className="text-gray-500 text-sm">Your detection system is working correctly</p>
            </div>
          ) : (
            <div className="space-y-4">
              {suspiciousLinks.map((link) => (
                <div
                  key={link._id}
                  className="bg-gradient-to-br from-gray-700/30 to-gray-800/30 rounded-xl p-6 border border-gray-600/30 hover:border-gray-500/50 transition-all duration-200"
                >
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      {getRiskIcon(link.risk_score)}
                      <div className="ml-3">
                        <div className="font-medium text-white break-all">{link.url}</div>
                        <div className="text-sm text-gray-400">{link.domain}</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <span
                        className={`px-3 py-1 rounded-full text-sm font-medium border ${
                          link.status === 'verified'
                            ? 'bg-red-600/30 text-red-300 border-red-500/30'
                            : link.status === 'false_positive'
                              ? 'bg-green-600/30 text-green-300 border-green-500/30'
                              : 'bg-orange-600/30 text-orange-300 border-orange-500/30'
                        }`}
                      >
                        {link.status === 'verified'
                          ? 'Verified'
                          : link.status === 'false_positive'
                            ? 'False positive'
                            : 'Active'}
                      </span>
                      <span
                        className={`px-3 py-1 rounded-full text-sm font-medium border ${
                          link.risk_score >= 70
                            ? 'bg-red-600/30 text-red-300 border-red-500/30'
                            : link.risk_score >= 40
                              ? 'bg-orange-600/30 text-orange-300 border-orange-500/30'
                              : 'bg-green-600/30 text-green-300 border-green-500/30'
                        }`}
                      >
                        {link.risk_score}%
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div className="text-gray-400">
                      Detections:{' '}
                      <span className="text-white font-medium">{link.detection_count}</span>
                    </div>
                    <div className="text-gray-400">
                      Failures:{' '}
                      <span className="text-red-300 font-medium">{link.failed_checks_count}</span>
                    </div>
                    <div className="text-gray-400">
                      Warnings:{' '}
                      <span className="text-orange-300 font-medium">
                        {link.warning_checks_count}
                      </span>
                    </div>
                    <div className="text-gray-400">
                      First detection:{' '}
                      <span className="text-white font-medium">
                        {new Date(link.first_detected).toLocaleDateString('en-US')}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
