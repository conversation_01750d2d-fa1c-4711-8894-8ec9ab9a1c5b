import { useState, useEffect, useRef } from 'react';
import {
  Upload,
  FileText,
  AlertTriangle,
  CheckCircle,
  Clock,
  Eye,
  RefreshCw,
  Activity,
  Zap,
  X,
  Info,
  AlertCircle,
  Ban,
  FileX,
  Database,
  BarChart3,
  Search,
  Shield,
  Bug,
  BugPlay,
  Globe,
  ExternalLink,
  Mail,
  Target,
  Download,
  FileDown,
} from 'lucide-react';
import Cookies from 'js-cookie';
import { useModalContext } from '../contexts/ModalContext';
import {
  malwareService,
  malwareUtils,
  MalwareAnalysis,
  QuickScanResult,
  PackingAnalysis,
  MalwareStats,
} from '../services/malwareService';

// Transform backend response to match frontend interface
const transformBackendResponse = (backendData: any): MalwareAnalysis => {
  console.log('Backend data received:', backendData); // Debug log

  // Determine threat level based on malware_score or threat_score
  const getThreatLevel = (score: number): 'CLEAN' | 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' => {
    if (score >= 80) return 'CRITICAL';
    if (score >= 60) return 'HIGH';
    if (score >= 40) return 'MEDIUM';
    if (score >= 20) return 'LOW';
    return 'CLEAN';
  };

  // Get the actual malware score and max score from backend data
  const malware_score = backendData.malware_score || backendData.threat_score || 0;
  const max_score = backendData.max_score || 100;

  // Calculate confidence percentage (normalize malware_score to 0-100)
  const confidence_pct =
    backendData.confidence_pct || Math.min(100, Math.max(0, (malware_score / max_score) * 100));

  // Extract detection details from backend data
  let detection_details: string[] = [];

  // If detection_details already exists in backend data, use it
  if (backendData.detection_details && Array.isArray(backendData.detection_details)) {
    detection_details = backendData.detection_details;
  } else {
    // Otherwise, extract from suspicious strings and other indicators
    if (backendData.suspicious_strings?.suspicious_strings) {
      backendData.suspicious_strings.suspicious_strings.forEach((item: any) => {
        detection_details.push(`${item.type}: ${item.string}`);
      });
    }

    if (backendData.advanced_analysis?.behavioral_indicators) {
      backendData.advanced_analysis.behavioral_indicators.forEach((indicator: any) => {
        detection_details.push(`Behavioral: ${indicator.description}`);
      });
    }
  }

  const transformedData = {
    _id: backendData._id,
    mongo_id: backendData.mongo_id,
    filename: backendData.file_name || backendData.filename || 'Unknown',
    original_filename: backendData.original_filename || backendData.file_name || 'Unknown',
    file_size: backendData.file_size || backendData.file_info?.size || 0,
    analysis_timestamp:
      backendData.analysis_timestamp || backendData.start_time || new Date().toISOString(),
    user_id: backendData.user_id,
    analysis_type: backendData.analysis_type,
    threat_level: backendData.threat_level || getThreatLevel(malware_score),
    confidence_pct: confidence_pct,
    malware_score: malware_score,
    max_score: max_score,
    is_malware: backendData.is_malware || false,
    file_info: {
      filename: backendData.file_name || backendData.filename || 'Unknown',
      file_type:
        backendData.file_type?.mime_type || backendData.file_type?.description || 'Unknown',
      hashes: backendData.hashes || {},
    },
    analysis_details: backendData.analysis_details || {
      sections:
        backendData.advanced_analysis?.entropy_analysis?.sections ||
        backendData.analysis_details?.sections ||
        [],
      suspicious_strings:
        backendData.analysis_details?.suspicious_strings ||
        backendData.suspicious_strings?.suspicious_strings ||
        [],
      packing_analysis: backendData.analysis_details?.packing_analysis || {
        status: backendData.advanced_analysis?.packer_detected ? 'DETECTED' : 'NOT DETECTED',
        is_packed: backendData.advanced_analysis?.packer_detected || false,
        packer_name: undefined,
        confidence: undefined,
        indicators: [],
      },
      entropy_analysis: backendData.analysis_details?.entropy_analysis || [],
      exports: backendData.analysis_details?.exports || [],
      imports: backendData.analysis_details?.imports || [],
      indicators: backendData.analysis_details?.indicators || [],
      macros: backendData.analysis_details?.macros || {},
    },
    suspicious_apis: backendData.suspicious_apis || [],
    yara_matches: backendData.yara_matches || [],
    iocs: {
      ips: backendData.iocs?.ips || backendData.iocs?.ip_addresses || [],
      domains: backendData.iocs?.domains || [],
      urls: backendData.iocs?.urls || [],
      emails: backendData.iocs?.emails || backendData.iocs?.email_addresses || [],
    },
    detection_details: detection_details,
    recommendations: backendData.recommendations || [],
    created_at: backendData.created_at,
    updated_at: backendData.updated_at,
  };

  console.log('Transformed data:', transformedData); // Debug log
  return transformedData;
};

export default function MalwareDetection() {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { showAlert } = useModalContext();

  // États pour l'analyse
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [currentAnalysis, setCurrentAnalysis] = useState<MalwareAnalysis | null>(null);
  const [quickScanResult, setQuickScanResult] = useState<QuickScanResult | null>(null);
  const [packingAnalysis, setPackingAnalysis] = useState<PackingAnalysis | null>(null);

  // States for history and statistics
  const [analysisHistory, setAnalysisHistory] = useState<MalwareAnalysis[]>([]);
  const [statistics, setStatistics] = useState<MalwareStats | null>(null);
  const [activeTab, setActiveTab] = useState<'analyze' | 'history' | 'help'>('analyze');
  const [analysisType, setAnalysisType] = useState<
    'quick' | 'standard' | 'comprehensive' | 'aggressive' | 'packing'
  >('standard');

  // États de chargement
  const [loadingHistory, setLoadingHistory] = useState(false);
  const [loadingStats, setLoadingStats] = useState(false);

  // States for details popup
  const [selectedAnalysisForView, setSelectedAnalysisForView] = useState<any>(null);
  const [showAnalysisDetails, setShowAnalysisDetails] = useState(false);
  const [exportingPDF, setExportingPDF] = useState(false);
  const [exportingCSV, setExportingCSV] = useState(false);

  // Debug: Log when currentAnalysis changes
  useEffect(() => {
    console.log('currentAnalysis state changed:', currentAnalysis);
  }, [currentAnalysis]);

  // Debug: Log when packingAnalysis changes
  useEffect(() => {
    console.log('packingAnalysis state changed:', packingAnalysis);
  }, [packingAnalysis]);

  // Functions to manage details popup
  const openAnalysisDetails = (analysis: any) => {
    setSelectedAnalysisForView(analysis);
    setShowAnalysisDetails(true);
  };

  const closeAnalysisDetails = () => {
    setSelectedAnalysisForView(null);
    setShowAnalysisDetails(false);
  };

  // Fonction pour exporter le rapport PDF de malware
  const handleExportMalwarePDF = async (analysisId: string) => {
    try {
      setExportingPDF(true);
      console.log('📄 Exporting malware PDF for analysis:', analysisId);

      const token = Cookies.get('token');
      if (!token) {
        showAlert({
          type: 'error',
          title: 'Authentication required',
          message: 'Please log in to export reports',
        });
        return;
      }

      const response = await fetch(`http://localhost:5000/api/export/malware/${analysisId}/pdf`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        // Créer un blob à partir de la réponse
        const blob = await response.blob();

        // Créer un URL temporaire pour le blob
        const url = window.URL.createObjectURL(blob);

        // Créer un lien de téléchargement
        const link = document.createElement('a');
        link.href = url;

        // Generate filename
        const filename = selectedAnalysisForView?.file_info?.filename || 'malware_analysis';
        const threatLevel = selectedAnalysisForView?.threat_level || 'unknown';
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');

        link.download = `PICA_Malware_Report_${filename}_${threatLevel}_${timestamp}.pdf`;

        // Déclencher le téléchargement
        document.body.appendChild(link);
        link.click();

        // Nettoyer
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        console.log('✅ Malware PDF exported successfully');

        showAlert({
          type: 'success',
          title: 'Export successful',
          message: 'PDF report exported successfully.',
        });
      } else {
        const errorData = await response.json();
        console.error('❌ Failed to export malware PDF:', response.status, errorData);
        showAlert({
          type: 'error',
          title: 'Export error',
          message: 'PDF export error: ' + (errorData.message || 'Unknown error'),
        });
      }
    } catch (error) {
      console.error('❌ Error exporting malware PDF:', error);
      showAlert({
        type: 'error',
        title: 'Export error',
        message: 'An error occurred during PDF export',
      });
    } finally {
      setExportingPDF(false);
    }
  };

  // Function to export all malware analyses to CSV
  const handleExportMalwareCSV = async () => {
    try {
      setExportingCSV(true);
      console.log('📊 Exporting all malware analyses to CSV');

      const token = Cookies.get('token');
      if (!token) {
        showAlert({
          type: 'error',
          title: 'Authentication required',
          message: 'Please log in to export data',
        });
        return;
      }

      const response = await fetch(`http://localhost:5000/api/export/malware/csv`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        // Créer un blob à partir de la réponse
        const blob = await response.blob();

        // Créer un URL temporaire pour le blob
        const url = window.URL.createObjectURL(blob);

        // Créer un lien de téléchargement
        const link = document.createElement('a');
        link.href = url;

        // Generate filename
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
        link.download = `PICA_Malware_Export_${timestamp}.csv`;

        // Déclencher le téléchargement
        document.body.appendChild(link);
        link.click();

        // Nettoyer
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        console.log('✅ Malware CSV exported successfully');

        showAlert({
          type: 'success',
          title: 'Export successful',
          message: `${analysisHistory.length} malware analysis(es) exported successfully to CSV.`,
        });
      } else {
        const errorData = await response.json();
        console.error('❌ Failed to export malware CSV:', response.status, errorData);
        showAlert({
          type: 'error',
          title: 'Export error',
          message: 'CSV export error: ' + (errorData.message || 'Unknown error'),
        });
      }
    } catch (error) {
      console.error('❌ Error exporting malware CSV:', error);
      showAlert({
        type: 'error',
        title: 'Export error',
        message: 'An error occurred during CSV export',
      });
    } finally {
      setExportingCSV(false);
    }
  };

  // Function to load analysis history
  const loadAnalysisHistory = async () => {
    try {
      setLoadingHistory(true);
      const response = await malwareService.getAnalysisHistory(50);
      setAnalysisHistory(response.data.analyses || []);
    } catch (error) {
      console.error('Error loading history:', error);
    } finally {
      setLoadingHistory(false);
    }
  };

  // Fonction pour charger les statistiques
  const loadStatistics = async () => {
    try {
      setLoadingStats(true);
      const response = await malwareService.getStatistics();
      setStatistics(response.data.statistics || null);
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques:', error);
    } finally {
      setLoadingStats(false);
    }
  };

  // Function to get threat icon
  const getThreatIcon = (threatLevel: string) => {
    switch (threatLevel) {
      case 'CRITICAL':
        return <Ban className="w-5 h-5 text-red-600" />;
      case 'HIGH':
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
      case 'MEDIUM':
        return <AlertCircle className="w-5 h-5 text-orange-500" />;
      case 'LOW':
        return <Info className="w-5 h-5 text-yellow-500" />;
      default:
        return <CheckCircle className="w-5 h-5 text-green-500" />;
    }
  };

  // Charger les données au montage du composant
  useEffect(() => {
    if (activeTab === 'history') {
      loadAnalysisHistory();
      loadStatistics(); // Also load stats for history
    }
  }, [activeTab]);

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      // Reset previous results
      setCurrentAnalysis(null);
      setQuickScanResult(null);
      setPackingAnalysis(null);
    }
  };

  // Analyze file according to selected type
  const analyzeFile = async () => {
    if (!selectedFile) return;

    setIsAnalyzing(true);
    setCurrentAnalysis(null);
    setQuickScanResult(null);
    setPackingAnalysis(null);

    try {
      switch (analysisType) {
        case 'quick':
          const quickResult = await malwareService.quickScan(selectedFile);
          setQuickScanResult(quickResult.data);
          break;

        case 'standard':
          const standardResult = await malwareService.analyzeFile(selectedFile);
          setCurrentAnalysis(transformBackendResponse(standardResult.data));
          break;

        case 'comprehensive':
          const comprehensiveResult = await malwareService.analyzeFileComprehensive(selectedFile);
          const analysisData = comprehensiveResult.data.analysis || comprehensiveResult.data;
          console.log('Raw comprehensive result:', comprehensiveResult);
          console.log('Analysis data extracted:', analysisData);

          // Use transformBackendResponse to ensure consistent data structure
          setCurrentAnalysis(transformBackendResponse(analysisData));
          break;

        case 'aggressive':
          const aggressiveResult = await malwareService.analyzeFileAggressive(selectedFile);
          const aggressiveData = aggressiveResult.data.analysis || aggressiveResult.data;
          console.log('Raw aggressive result:', aggressiveResult);
          console.log('Aggressive analysis data extracted:', aggressiveData);

          // Use transformBackendResponse to ensure consistent data structure
          setCurrentAnalysis(transformBackendResponse(aggressiveData));
          break;

        case 'packing':
          const packingResult = await malwareService.analyzeFilePacking(selectedFile);
          console.log('Packing result received:', packingResult);
          console.log('Packing analysis data:', packingResult.data.packing_analysis);
          setPackingAnalysis(packingResult.data.packing_analysis);
          break;
      }

      // Reload history after analysis
      loadAnalysisHistory();
    } catch (error) {
      console.error('Error during analysis:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-8 shadow-2xl">
        <div className="flex items-center space-x-4 mb-6">
          <div className="w-16 h-16 bg-gradient-to-br from-amber-500 to-yellow-500 rounded-2xl flex items-center justify-center shadow-lg">
            <Bug className="w-8 h-8 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-white">Malware Detection</h1>
            <p className="text-gray-300">Analyze your files to detect potential threats</p>
          </div>
        </div>

        {/* Navigation par onglets */}
        <div className="flex justify-center mb-8">
          <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-1 border border-gray-700/50">
            <button
              onClick={() => setActiveTab('analyze')}
              className={`px-6 py-3 rounded-lg font-medium transition-all ${
                activeTab === 'analyze'
                  ? 'bg-gradient-to-r from-red-500 to-orange-500 text-white shadow-lg'
                  : 'text-gray-300 hover:text-white hover:bg-gray-700/50'
              }`}
            >
              <BugPlay className="w-5 h-5 inline mr-2" />
              Analyze
            </button>
            <button
              onClick={() => setActiveTab('history')}
              className={`px-6 py-3 rounded-lg font-medium transition-all ${
                activeTab === 'history'
                  ? 'bg-gradient-to-r from-red-500 to-orange-500 text-white shadow-lg'
                  : 'text-gray-300 hover:text-white hover:bg-gray-700/50'
              }`}
            >
              <Clock className="w-5 h-5 inline mr-2" />
              History
            </button>

            <button
              onClick={() => setActiveTab('help')}
              className={`px-6 py-3 rounded-lg font-medium transition-all ${
                activeTab === 'help'
                  ? 'bg-gradient-to-r from-red-500 to-orange-500 text-white shadow-lg'
                  : 'text-gray-300 hover:text-white hover:bg-gray-700/50'
              }`}
            >
              <Info className="w-5 h-5 inline mr-2" />
              Scans Guide
            </button>
          </div>
        </div>
      </div>

      {/* Contenu selon l'onglet actif */}
      <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl shadow-2xl">
        {activeTab === 'analyze' && (
          <div className="p-8 space-y-8">
            <div className="flex items-center space-x-4 mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center shadow-lg">
                <BugPlay className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">Analyze a file</h2>
                <p className="text-gray-400">Select a file and choose the analysis type</p>
              </div>
            </div>

            {/* Sélection du type d'analyse */}
            <div className="mb-8">
              <h3 className="text-xl font-semibold text-white mb-4">Analysis type</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                {[
                  {
                    value: 'quick',
                    label: 'Quick',
                    icon: Zap,
                    desc: 'Hashes + IOCs',
                    color: 'from-blue-500 to-cyan-500',
                  },
                  {
                    value: 'standard',
                    label: 'Standard',
                    icon: Search,
                    desc: 'Complete analysis',
                    color: 'from-green-500 to-emerald-500',
                  },
                  {
                    value: 'comprehensive',
                    label: 'Comprehensive',
                    icon: Activity,
                    desc: 'Detailed analysis',
                    color: 'from-purple-500 to-pink-500',
                  },
                  {
                    value: 'aggressive',
                    label: 'Aggressive',
                    icon: AlertTriangle,
                    desc: 'All tools',
                    color: 'from-red-500 to-orange-500',
                  },
                  {
                    value: 'packing',
                    label: 'Packing',
                    icon: Database,
                    desc: 'Packer detection',
                    color: 'from-yellow-500 to-amber-500',
                  },
                ].map((type) => (
                  <button
                    key={type.value}
                    onClick={() => setAnalysisType(type.value as any)}
                    className={`p-4 rounded-xl border-2 transition-all duration-200 text-left ${
                      analysisType === type.value
                        ? 'border-cyan-500 bg-cyan-600/20'
                        : 'border-gray-600/50 bg-gray-800/30 hover:border-gray-500'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <div
                        className={`w-10 h-10 bg-gradient-to-br ${type.color} rounded-lg flex items-center justify-center`}
                      >
                        <type.icon className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h4 className="text-white font-medium">{type.label}</h4>
                        <p className="text-gray-400 text-sm">{type.desc}</p>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Alert for aggressive scan */}
            {analysisType === 'aggressive' && (
              <div className="mb-6 bg-red-900/20 border border-red-500/30 rounded-lg p-4">
                <div className="flex items-center">
                  <AlertTriangle className="w-5 h-5 text-red-400 mr-3" />
                  <div>
                    <h4 className="text-red-300 font-semibold">Aggressive Scan Selected</h4>
                    <p className="text-red-200 text-sm mt-1">
                      This scan uses all available tools and may take longer. It provides the most
                      complete analysis with improved scoring.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* File drop zone */}
            <div className="mb-6">
              <div
                onClick={() => fileInputRef.current?.click()}
                className="border-2 border-dashed border-white/30 rounded-lg p-8 text-center cursor-pointer hover:border-purple-400 transition-colors"
              >
                {selectedFile ? (
                  <div className="space-y-2">
                    <FileText className="w-12 h-12 text-green-400 mx-auto" />
                    <p className="text-white font-medium">{selectedFile.name}</p>
                    <p className="text-purple-200 text-sm">
                      {malwareUtils.formatFileSize(selectedFile.size)}
                    </p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <Upload className="w-12 h-12 text-purple-400 mx-auto" />
                    <p className="text-white">Click to select a file</p>
                    <p className="text-purple-200 text-sm">
                      Supported formats: EXE, DLL, PDF, DOC, ZIP, etc.
                    </p>
                  </div>
                )}
              </div>
              <input
                ref={fileInputRef}
                type="file"
                onChange={handleFileSelect}
                className="hidden"
                accept="*/*"
              />
            </div>

            {/* Analysis button */}
            <button
              onClick={analyzeFile}
              disabled={isAnalyzing || !selectedFile}
              className="w-full px-8 py-4 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-medium rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg flex items-center justify-center"
            >
              {isAnalyzing ? (
                <>
                  <RefreshCw className="w-5 h-5 mr-2 animate-spin" />
                  Analysis in progress...
                </>
              ) : (
                <>
                  <Zap className="w-5 h-5 mr-2" />
                  Start {analysisType} analysis
                </>
              )}
            </button>

            {/* Analysis results */}
            {(currentAnalysis || quickScanResult || packingAnalysis) && (
              <div className="bg-gradient-to-br from-gray-800/40 to-gray-900/60 backdrop-blur-sm border border-gray-600/30 rounded-2xl p-8 shadow-xl">
                <div className="flex items-center space-x-4 mb-8">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-cyan-500 rounded-xl flex items-center justify-center shadow-lg">
                    <Activity className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-white">Analysis Results</h3>
                    <p className="text-gray-400 text-sm">Complete security analysis details</p>
                  </div>
                </div>

                {/* Standard/complete analysis results */}
                {currentAnalysis && (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* COLONNE GAUCHE - Informations générales */}
                    <div className="space-y-6">
                      <div className="bg-gradient-to-br from-blue-500/10 to-blue-600/20 border border-blue-500/30 rounded-2xl p-6 shadow-lg">
                        <div className="flex items-center space-x-3 mb-4">
                          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                            <FileText className="w-5 h-5 text-white" />
                          </div>
                          <div>
                            <h4 className="text-white font-semibold">Analyzed File</h4>
                            <p className="text-blue-300 text-sm">File information</p>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div className="text-white font-mono text-sm bg-gray-800/50 rounded-lg p-3">
                            {currentAnalysis.original_filename}
                          </div>
                          <div className="text-blue-200 text-sm">
                            Taille: {malwareUtils.formatFileSize(currentAnalysis.file_size)}
                          </div>
                        </div>
                      </div>

                      <div className="bg-gradient-to-br from-purple-500/10 to-purple-600/20 border border-purple-500/30 rounded-2xl p-6 shadow-lg">
                        <div className="flex items-center space-x-3 mb-4">
                          <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                            {getThreatIcon(currentAnalysis.threat_level)}
                          </div>
                          <div>
                            <h4 className="text-white font-semibold">Threat Level</h4>
                            <p className="text-purple-300 text-sm">Confidence score</p>
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <span
                            className={`text-4xl font-bold ${malwareUtils.getThreatColor(currentAnalysis.threat_level)}`}
                          >
                            {currentAnalysis.confidence_pct}%
                          </span>
                          <span
                            className={`px-4 py-2 rounded-xl text-sm font-medium ${malwareUtils.getThreatBgColor(currentAnalysis.threat_level)}`}
                          >
                            {currentAnalysis.threat_level}
                          </span>
                        </div>
                      </div>

                      <div className="bg-gradient-to-br from-gray-700/50 to-gray-800/70 border border-gray-600/30 rounded-2xl p-6 shadow-lg">
                        <div className="flex items-center space-x-3 mb-4">
                          <div className="w-10 h-10 bg-gradient-to-br from-gray-600 to-gray-700 rounded-xl flex items-center justify-center shadow-lg">
                            <Shield className="w-5 h-5 text-white" />
                          </div>
                          <div>
                            <h4 className="text-white font-semibold">Verdict</h4>
                            <p className="text-gray-300 text-sm">Final evaluation</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          {(() => {
                            const threatLevel = currentAnalysis.threat_level;
                            const score =
                              currentAnalysis.confidence_pct || currentAnalysis.malware_score || 0;

                            if (
                              threatLevel === 'CRITICAL' ||
                              threatLevel === 'HIGH' ||
                              score >= 70
                            ) {
                              return (
                                <>
                                  <Ban className="w-8 h-8 text-red-500" />
                                  <span className="text-red-300 font-semibold text-lg">
                                    Malware Detected
                                  </span>
                                </>
                              );
                            } else if (threatLevel === 'MEDIUM' || score >= 30) {
                              return (
                                <>
                                  <AlertTriangle className="w-8 h-8 text-orange-500" />
                                  <span className="text-orange-300 font-semibold text-lg">
                                    Suspicious File
                                  </span>
                                </>
                              );
                            } else if (threatLevel === 'LOW' || score >= 10) {
                              return (
                                <>
                                  <Info className="w-8 h-8 text-yellow-500" />
                                  <span className="text-yellow-300 font-semibold text-lg">
                                    Low Risk
                                  </span>
                                </>
                              );
                            } else {
                              return (
                                <>
                                  <CheckCircle className="w-8 h-8 text-green-500" />
                                  <span className="text-green-300 font-semibold text-lg">
                                    File Appears Safe
                                  </span>
                                </>
                              );
                            }
                          })()}
                        </div>
                      </div>

                      {/* Detection details in left column */}
                      {currentAnalysis.detection_details &&
                        currentAnalysis.detection_details.length > 0 && (
                          <div className="bg-gradient-to-br from-orange-500/10 to-red-500/20 border border-orange-500/30 rounded-2xl p-6 shadow-lg">
                            <div className="flex items-center space-x-3 mb-4">
                              <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center shadow-lg">
                                <AlertTriangle className="w-5 h-5 text-white" />
                              </div>
                              <div>
                                <h5 className="text-white font-semibold">Detections</h5>
                                <p className="text-orange-300 text-sm">
                                  {currentAnalysis.detection_details.length} detected elements
                                </p>
                              </div>
                            </div>
                            <div className="space-y-3 max-h-48 overflow-y-auto">
                              {currentAnalysis.detection_details.map((detail, index) => (
                                <div
                                  key={index}
                                  className="bg-gradient-to-r from-orange-900/30 to-red-900/30 border border-orange-500/30 rounded-xl p-4"
                                >
                                  <span className="text-orange-100 text-sm">{detail}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                      {/* Recommandations dans la colonne gauche */}
                      {currentAnalysis.recommendations &&
                        currentAnalysis.recommendations.length > 0 && (
                          <div className="bg-gradient-to-br from-blue-500/10 to-indigo-500/20 border border-blue-500/30 rounded-2xl p-6 shadow-lg">
                            <div className="flex items-center space-x-3 mb-4">
                              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center shadow-lg">
                                <Info className="w-5 h-5 text-white" />
                              </div>
                              <div>
                                <h5 className="text-white font-semibold">Recommandations</h5>
                                <p className="text-blue-300 text-sm">Suggested actions</p>
                              </div>
                            </div>
                            <div className="space-y-3">
                              {currentAnalysis.recommendations.map((recommendation, index) => (
                                <div
                                  key={index}
                                  className="bg-gradient-to-r from-blue-900/30 to-indigo-900/30 border border-blue-500/30 rounded-xl p-4"
                                >
                                  <span className="text-blue-100 text-sm">{recommendation}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                    </div>

                    {/* RIGHT COLUMN - Technical details and IOCs */}
                    <div className="space-y-6">
                      {/* Hashes */}
                      {currentAnalysis.file_info.hashes &&
                        Object.keys(currentAnalysis.file_info.hashes).length > 0 && (
                          <div className="bg-gradient-to-br from-gray-700/50 to-gray-800/70 border border-gray-600/30 rounded-2xl p-6 shadow-lg">
                            <div className="flex items-center space-x-3 mb-4">
                              <div className="w-10 h-10 bg-gradient-to-br from-gray-600 to-gray-700 rounded-xl flex items-center justify-center shadow-lg">
                                <Database className="w-5 h-5 text-white" />
                              </div>
                              <div>
                                <h5 className="text-white font-semibold">Hashes</h5>
                                <p className="text-gray-300 text-sm">Cryptographic fingerprints</p>
                              </div>
                            </div>
                            <div className="space-y-3">
                              {Object.entries(currentAnalysis.file_info.hashes).map(
                                ([type, hash]) =>
                                  hash && (
                                    <div key={type} className="bg-gray-800/50 rounded-xl p-3">
                                      <div className="flex justify-between items-center mb-1">
                                        <span className="text-gray-300 uppercase text-xs font-medium">
                                          {type}:
                                        </span>
                                      </div>
                                      <span className="text-white font-mono text-xs break-all bg-gray-900/50 rounded-lg p-2 block">
                                        {hash}
                                      </span>
                                    </div>
                                  )
                              )}
                            </div>
                          </div>
                        )}

                      {/* Threat score */}
                      <div className="bg-gradient-to-br from-purple-500/10 to-pink-500/20 border border-purple-500/30 rounded-2xl p-6 shadow-lg">
                        <div className="flex items-center space-x-3 mb-4">
                          <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg">
                            <BarChart3 className="w-5 h-5 text-white" />
                          </div>
                          <div>
                            <h5 className="text-white font-semibold">Threat Score</h5>
                            <p className="text-purple-300 text-sm">Numerical assessment</p>
                          </div>
                        </div>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <span className="text-white text-2xl font-bold">
                              {currentAnalysis.malware_score} / {currentAnalysis.max_score}
                            </span>
                            <span
                              className={`px-3 py-1 rounded-xl text-sm font-medium ${
                                currentAnalysis.malware_score >= 80
                                  ? 'bg-red-500/20 text-red-300 border border-red-500/30'
                                  : currentAnalysis.malware_score >= 60
                                    ? 'bg-orange-500/20 text-orange-300 border border-orange-500/30'
                                    : currentAnalysis.malware_score >= 40
                                      ? 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30'
                                      : currentAnalysis.malware_score >= 20
                                        ? 'bg-blue-500/20 text-blue-300 border border-blue-500/30'
                                        : 'bg-green-500/20 text-green-300 border border-green-500/30'
                              }`}
                            >
                              {Math.round(
                                (currentAnalysis.malware_score / currentAnalysis.max_score) * 100
                              )}
                              %
                            </span>
                          </div>
                          <div className="w-full bg-gray-700/50 rounded-full h-3 overflow-hidden">
                            <div
                              className={`h-3 rounded-full transition-all duration-500 ${
                                currentAnalysis.malware_score >= 80
                                  ? 'bg-gradient-to-r from-red-500 to-red-600'
                                  : currentAnalysis.malware_score >= 60
                                    ? 'bg-gradient-to-r from-orange-500 to-orange-600'
                                    : currentAnalysis.malware_score >= 40
                                      ? 'bg-gradient-to-r from-yellow-500 to-yellow-600'
                                      : currentAnalysis.malware_score >= 20
                                        ? 'bg-gradient-to-r from-blue-500 to-blue-600'
                                        : 'bg-gradient-to-r from-green-500 to-green-600'
                              }`}
                              style={{
                                width: `${(currentAnalysis.malware_score / currentAnalysis.max_score) * 100}%`,
                              }}
                            ></div>
                          </div>
                        </div>
                      </div>

                      {/* IOCs - Indicators of Compromise */}
                      {((currentAnalysis.iocs?.ips && currentAnalysis.iocs.ips.length > 0) ||
                        (currentAnalysis.iocs?.domains &&
                          currentAnalysis.iocs.domains.length > 0) ||
                        (currentAnalysis.iocs?.urls && currentAnalysis.iocs.urls.length > 0) ||
                        (currentAnalysis.iocs?.emails &&
                          currentAnalysis.iocs.emails.length > 0)) && (
                        <div className="bg-gradient-to-br from-orange-500/10 to-red-500/20 border border-orange-500/30 rounded-2xl p-6 shadow-lg">
                          <div className="flex items-center space-x-3 mb-6">
                            <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center shadow-lg">
                              <AlertTriangle className="w-5 h-5 text-white" />
                            </div>
                            <div>
                              <h5 className="text-white font-semibold">
                                Indicators of Compromise (IOCs)
                              </h5>
                              <p className="text-orange-300 text-sm">
                                Suspicious elements detected
                              </p>
                            </div>
                          </div>

                          <div className="space-y-4">
                            {/* Adresses IP */}
                            {currentAnalysis.iocs?.ips && currentAnalysis.iocs.ips.length > 0 && (
                              <div className="bg-gradient-to-br from-red-500/10 to-red-600/20 border border-red-500/30 rounded-xl p-4">
                                <h6 className="text-red-300 font-semibold mb-3 flex items-center">
                                  🌐 Adresses IP ({currentAnalysis.iocs.ips.length})
                                </h6>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                  {currentAnalysis.iocs.ips.map((ip, i) => (
                                    <div
                                      key={i}
                                      className="bg-red-800/30 rounded-lg px-3 py-2 border border-red-600/30"
                                    >
                                      <span className="text-red-200 font-mono text-sm">{ip}</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}

                            {/* Domaines */}
                            {currentAnalysis.iocs?.domains &&
                              currentAnalysis.iocs.domains.length > 0 && (
                                <div className="bg-orange-900/20 border border-orange-500/30 rounded-lg p-4">
                                  <h6 className="text-orange-300 font-semibold mb-3 flex items-center">
                                    🌍 Domaines ({currentAnalysis.iocs.domains.length})
                                  </h6>
                                  <div className="space-y-2">
                                    {currentAnalysis.iocs.domains.map((domain, i) => (
                                      <div
                                        key={i}
                                        className="bg-orange-800/20 rounded px-3 py-2 border border-orange-600/20"
                                      >
                                        <span className="text-orange-200 font-mono text-sm break-all">
                                          {domain}
                                        </span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}

                            {/* URLs */}
                            {currentAnalysis.iocs?.urls && currentAnalysis.iocs.urls.length > 0 && (
                              <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-4">
                                <h6 className="text-yellow-300 font-semibold mb-3 flex items-center">
                                  🔗 URLs ({currentAnalysis.iocs.urls.length})
                                </h6>
                                <div className="space-y-2">
                                  {currentAnalysis.iocs.urls.map((url, i) => (
                                    <div
                                      key={i}
                                      className="bg-yellow-800/20 rounded px-3 py-2 border border-yellow-600/20"
                                    >
                                      <span className="text-yellow-200 font-mono text-xs break-all">
                                        {url}
                                      </span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}

                            {/* Emails */}
                            {currentAnalysis.iocs?.emails &&
                              currentAnalysis.iocs.emails.length > 0 && (
                                <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
                                  <h6 className="text-blue-300 font-semibold mb-3 flex items-center">
                                    📧 Email Adresses ({currentAnalysis.iocs.emails.length})
                                  </h6>
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                    {currentAnalysis.iocs.emails.map((email, i) => (
                                      <div
                                        key={i}
                                        className="bg-blue-800/20 rounded px-3 py-2 border border-blue-600/20"
                                      >
                                        <span className="text-blue-200 font-mono text-sm">
                                          {email}
                                        </span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Quick scan results */}
                {quickScanResult && (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* COLONNE GAUCHE - Informations générales */}
                    <div className="space-y-4">
                      <div className="bg-white/5 rounded-lg p-6">
                        <h5 className="text-purple-200 mb-3">File information</h5>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-purple-300">Name:</span>
                            <span className="text-white">{quickScanResult.filename}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-purple-300">Size:</span>
                            <span className="text-white">
                              {malwareUtils.formatFileSize(quickScanResult.file_size)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-purple-300">Type:</span>
                            <span className="text-white">{quickScanResult.file_type}</span>
                          </div>
                        </div>
                      </div>

                      <div className="bg-white/5 rounded-lg p-6">
                        <h5 className="text-purple-200 mb-3">Threat score</h5>
                        <div className="text-center">
                          <div
                            className={`text-4xl font-bold ${quickScanResult.is_suspicious ? 'text-orange-500' : 'text-green-500'}`}
                          >
                            {quickScanResult.threat_score}%
                          </div>
                          <div
                            className={`mt-2 px-3 py-1 rounded-full text-sm ${
                              quickScanResult.is_suspicious
                                ? 'bg-orange-600/20 text-orange-300'
                                : 'bg-green-600/20 text-green-300'
                            }`}
                          >
                            {quickScanResult.is_suspicious ? 'Suspect' : 'Propre'}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* COLONNE DROITE - IOCs détaillés */}
                    <div className="space-y-4">
                      {/* IOCs détaillés pour scan rapide */}
                      {(quickScanResult.iocs?.ip_addresses &&
                        quickScanResult.iocs.ip_addresses.length > 0) ||
                      (quickScanResult.iocs?.domains && quickScanResult.iocs.domains.length > 0) ||
                      (quickScanResult.iocs?.urls && quickScanResult.iocs.urls.length > 0) ||
                      (quickScanResult.iocs?.email_addresses &&
                        quickScanResult.iocs.email_addresses.length > 0) ? (
                        <div className="bg-white/5 rounded-lg p-6">
                          <h5 className="text-purple-200 mb-4 flex items-center">
                            <AlertTriangle className="w-5 h-5 mr-2 text-orange-500" />
                            Detected IOCs
                          </h5>

                          <div className="space-y-4">
                            {/* IPs pour scan rapide */}
                            {quickScanResult.iocs?.ip_addresses &&
                              quickScanResult.iocs.ip_addresses.length > 0 && (
                                <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-3">
                                  <h6 className="text-red-300 font-semibold mb-2">
                                    🌐 IP Adresses ({quickScanResult.iocs.ip_addresses.length})
                                  </h6>
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                    {quickScanResult.iocs.ip_addresses.map((ip, i) => (
                                      <div key={i} className="bg-red-800/20 rounded px-2 py-1">
                                        <span className="text-red-200 font-mono text-sm">{ip}</span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}

                            {/* Domaines pour scan rapide */}
                            {quickScanResult.iocs?.domains &&
                              quickScanResult.iocs.domains.length > 0 && (
                                <div className="bg-orange-900/20 border border-orange-500/30 rounded-lg p-3">
                                  <h6 className="text-orange-300 font-semibold mb-2">
                                    🌍 Domaines ({quickScanResult.iocs.domains.length})
                                  </h6>
                                  <div className="space-y-1">
                                    {quickScanResult.iocs.domains.map((domain, i) => (
                                      <div key={i} className="bg-orange-800/20 rounded px-2 py-1">
                                        <span className="text-orange-200 font-mono text-sm break-all">
                                          {domain}
                                        </span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}

                            {/* URLs pour scan rapide */}
                            {quickScanResult.iocs?.urls && quickScanResult.iocs.urls.length > 0 && (
                              <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-3">
                                <h6 className="text-yellow-300 font-semibold mb-2">
                                  🔗 URLs ({quickScanResult.iocs.urls.length})
                                </h6>
                                <div className="space-y-1">
                                  {quickScanResult.iocs.urls.map((url, i) => (
                                    <div key={i} className="bg-yellow-800/20 rounded px-2 py-1">
                                      <span className="text-yellow-200 font-mono text-xs break-all">
                                        {url}
                                      </span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}

                            {/* Emails pour scan rapide */}
                            {quickScanResult.iocs?.email_addresses &&
                              quickScanResult.iocs.email_addresses.length > 0 && (
                                <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-3">
                                  <h6 className="text-blue-300 font-semibold mb-2">
                                    📧 Emails ({quickScanResult.iocs.email_addresses.length})
                                  </h6>
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                    {quickScanResult.iocs.email_addresses.map((email, i) => (
                                      <div key={i} className="bg-blue-800/20 rounded px-2 py-1">
                                        <span className="text-blue-200 font-mono text-sm">
                                          {email}
                                        </span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                          </div>
                        </div>
                      ) : (
                        <div className="bg-white/5 rounded-lg p-6">
                          <h5 className="text-purple-200 mb-3">Detected IOCs</h5>
                          <div className="text-center py-4">
                            <CheckCircle className="w-8 h-8 text-green-500 mx-auto mb-2" />
                            <p className="text-green-300">No IOC detected</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Packing analysis results */}
                {packingAnalysis && (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* COLONNE GAUCHE - Informations de packing */}
                    <div className="space-y-4">
                      <div className="bg-white/5 rounded-lg p-6">
                        <h5 className="text-purple-200 mb-3">Packing status</h5>
                        <div className="text-center">
                          <div
                            className={`text-2xl font-bold ${
                              packingAnalysis.status === 'DETECTED'
                                ? 'text-red-500'
                                : packingAnalysis.status === 'WHITELISTED'
                                  ? 'text-blue-500'
                                  : 'text-green-500'
                            }`}
                          >
                            {packingAnalysis.status}
                          </div>
                          {packingAnalysis.packer_name && (
                            <div className="mt-2 text-white">{packingAnalysis.packer_name}</div>
                          )}
                          <div className="mt-1 text-sm text-purple-300">
                            Confidence: {packingAnalysis.confidence}
                          </div>
                        </div>
                      </div>

                      <div className="bg-white/5 rounded-lg p-6">
                        <h5 className="text-purple-200 mb-3">Analyzed sections</h5>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-white">
                            {packingAnalysis.sections?.length || 0}
                          </div>
                          <div className="text-sm text-purple-300">
                            {packingAnalysis.sections?.filter((s) => s.suspicious).length || 0}{' '}
                            suspicious
                          </div>
                        </div>
                      </div>

                      <div className="bg-white/5 rounded-lg p-6">
                        <h5 className="text-purple-200 mb-3">Indicators</h5>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-orange-500">
                            {packingAnalysis.indicators?.length || 0}
                          </div>
                          <div className="text-sm text-purple-300">detected</div>
                        </div>
                      </div>
                    </div>

                    {/* RIGHT COLUMN - Indicator details */}
                    <div className="space-y-4">
                      {packingAnalysis.indicators && packingAnalysis.indicators.length > 0 ? (
                        <div className="bg-white/5 rounded-lg p-6">
                          <h5 className="text-purple-200 mb-3">Packing indicators:</h5>
                          <div className="space-y-2 max-h-96 overflow-y-auto">
                            {packingAnalysis.indicators.map((indicator, index) => (
                              <div
                                key={index}
                                className="p-3 bg-orange-900/20 border-l-4 border-orange-500 rounded"
                              >
                                <span className="text-orange-200 text-sm">{indicator}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      ) : (
                        <div className="bg-white/5 rounded-lg p-6">
                          <h5 className="text-purple-200 mb-3">Packing indicators:</h5>
                          <div className="text-center py-8">
                            <CheckCircle className="w-8 h-8 text-green-500 mx-auto mb-2" />
                            <p className="text-green-300">No packing indicators detected</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Onglet Historique */}
        {activeTab === 'history' && (
          <div className="space-y-8">
            {/* Statistiques générales */}
            {statistics && (
              <div className="bg-gradient-to-br from-purple-700/30 to-purple-800/40 backdrop-blur-sm border border-purple-500/20 rounded-2xl p-6 shadow-xl">
                <h2 className="text-xl font-bold text-white mb-6 flex items-center">
                  <BarChart3 className="w-6 h-6 mr-3 text-purple-400" />
                  General statistics
                </h2>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-gradient-to-br from-gray-700/50 to-gray-800/50 rounded-xl p-4 text-center border border-gray-600/30">
                    <div className="text-2xl font-bold text-white mb-1">
                      {statistics.total_analyses}
                    </div>
                    <div className="text-gray-300 text-sm">Overall analyses</div>
                  </div>

                  <div className="bg-gradient-to-br from-gray-700/50 to-gray-800/50 rounded-xl p-4 text-center border border-gray-600/30">
                    <div className="text-2xl font-bold text-green-400 mb-1">
                      {statistics.clean_files}
                    </div>
                    <div className="text-gray-300 text-sm">Clean files</div>
                  </div>

                  <div className="bg-gradient-to-br from-gray-700/50 to-gray-800/50 rounded-xl p-4 text-center border border-gray-600/30">
                    <div className="text-2xl font-bold text-blue-400 mb-1">
                      {statistics.avg_confidence ? Math.round(statistics.avg_confidence) : 0}%
                    </div>
                    <div className="text-gray-300 text-sm">Average confidence</div>
                  </div>

                  <div className="bg-gradient-to-br from-gray-700/50 to-gray-800/50 rounded-xl p-4 text-center border border-gray-600/30">
                    <div className="text-2xl font-bold text-purple-400 mb-1">
                      {statistics.avg_malware_score ? Math.round(statistics.avg_malware_score) : 0}
                    </div>
                    <div className="text-gray-300 text-sm">Average malware score</div>
                  </div>
                </div>
              </div>
            )}

            {/* Section Historique */}
            <div className="bg-gradient-to-br from-purple-700/30 to-purple-800/40 backdrop-blur-sm border border-purple-500/20 rounded-2xl p-8 shadow-xl">
              <div className="flex items-center justify-between mb-8">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-cyan-500 rounded-xl flex items-center justify-center shadow-lg">
                    <Clock className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-white">Analysis History</h2>
                    <p className="text-gray-400 text-sm">View your previous analyses</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  {/* Bouton Export CSV */}
                  {analysisHistory.length > 0 && (
                    <button
                      onClick={handleExportMalwareCSV}
                      disabled={exportingCSV}
                      className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg"
                      title="Export all analyses to CSV"
                    >
                      {exportingCSV ? (
                        <>
                          <RefreshCw className="w-4 h-4 animate-spin" />
                          <span>Export...</span>
                        </>
                      ) : (
                        <>
                          <FileDown className="w-4 h-4" />
                          <span>Export CSV</span>
                        </>
                      )}
                    </button>
                  )}

                  <button
                    onClick={() => {
                      loadAnalysisHistory();
                      loadStatistics();
                    }}
                    disabled={loadingHistory || loadingStats}
                    className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 disabled:opacity-50 text-white rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg"
                  >
                    <RefreshCw
                      className={`w-4 h-4 ${loadingHistory || loadingStats ? 'animate-spin' : ''}`}
                    />
                    <span>Refresh History</span>
                  </button>
                </div>
              </div>

              {loadingHistory ? (
                <div className="text-center py-8">
                  <RefreshCw className="w-8 h-8 text-purple-400 animate-spin mx-auto mb-4" />
                  <p className="text-purple-200">Loading analysis history...</p>
                </div>
              ) : analysisHistory.length === 0 ? (
                <div className="text-center py-8">
                  <FileX className="w-12 h-12 text-purple-400 mx-auto mb-4" />
                  <p className="text-purple-200">No analysis performed</p>
                </div>
              ) : (
                <div className="space-y-6">
                  {analysisHistory.map((analysis) => (
                    <div
                      key={analysis._id}
                      className="bg-gradient-to-br from-purple-600/20 to-purple-700/30 backdrop-blur-sm border border-purple-500/20 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-purple-400/50"
                    >
                      <div className="flex items-center justify-between mb-6">
                        <div className="flex items-center space-x-4">
                          <div className="w-12 h-12 bg-gradient-to-br from-purple-600/40 to-purple-700/60 rounded-xl flex items-center justify-center shadow-lg border border-purple-500/30">
                            {getThreatIcon(analysis.threat_level)}
                          </div>
                          <div>
                            <div className="font-semibold text-white text-lg">
                              {analysis.original_filename}
                            </div>
                            <div className="text-sm text-gray-400 flex items-center space-x-2">
                              <span>{malwareUtils.formatFileSize(analysis.file_size)}</span>
                              <span>•</span>
                              <span className="capitalize">
                                {analysis.analysis_type || 'standard'}
                              </span>
                              <span>•</span>
                              <span>
                                {malwareUtils.formatDate(
                                  analysis.created_at || analysis.analysis_timestamp
                                )}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-4">
                          <div className="text-right">
                            <div
                              className={`text-2xl font-bold ${malwareUtils.getThreatColor(analysis.threat_level)}`}
                            >
                              {analysis.confidence_pct}%
                            </div>
                            <span
                              className={`px-3 py-1 rounded-full text-xs font-medium ${malwareUtils.getThreatBgColor(analysis.threat_level)}`}
                            >
                              {analysis.threat_level}
                            </span>
                          </div>
                          <button
                            onClick={() => openAnalysisDetails(analysis)}
                            className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white text-sm rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg"
                          >
                            <Eye className="w-4 h-4" />
                            <span>View</span>
                          </button>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="bg-gradient-to-br from-purple-500/10 to-purple-600/20 border border-purple-500/30 rounded-xl p-4 text-center">
                          <div className="text-purple-300 text-xs font-medium uppercase tracking-wide mb-1">
                            Score
                          </div>
                          <div className="text-white text-lg font-bold">
                            {analysis.malware_score || analysis.confidence_pct || 0}/
                            {analysis.max_score || 100}
                          </div>
                        </div>
                        <div className="bg-gradient-to-br from-cyan-500/10 to-cyan-600/20 border border-cyan-500/30 rounded-xl p-4 text-center">
                          <div className="text-cyan-300 text-xs font-medium uppercase tracking-wide mb-1">
                            IOCs
                          </div>
                          <div className="text-white text-lg font-bold">
                            {analysis.iocs
                              ? (analysis.iocs.ips?.length || 0) +
                                (analysis.iocs.domains?.length || 0) +
                                (analysis.iocs.urls?.length || 0) +
                                (analysis.iocs.emails?.length || 0)
                              : 0}
                          </div>
                        </div>
                        <div className="bg-gradient-to-br from-orange-500/10 to-orange-600/20 border border-orange-500/30 rounded-xl p-4 text-center">
                          <div className="text-orange-300 text-xs font-medium uppercase tracking-wide mb-1">
                            Detections
                          </div>
                          <div className="text-white text-lg font-bold">
                            {analysis.detection_details?.length || 0}
                          </div>
                        </div>
                        <div className="bg-gradient-to-br from-green-500/10 to-green-600/20 border border-green-500/30 rounded-xl p-4 text-center">
                          <div className="text-green-300 text-xs font-medium uppercase tracking-wide mb-1">
                            Threat Level
                          </div>
                          <div
                            className={`text-sm font-medium ${malwareUtils.getThreatColor(analysis.threat_level)}`}
                          >
                            {analysis.threat_level}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Guide des Scans */}
        {activeTab === 'help' && (
          <div className="space-y-8">
            {/* Titre principal */}
            <div className="bg-gradient-to-br from-purple-700/30 to-purple-800/40 backdrop-blur-sm border border-purple-500/20 rounded-2xl p-8 shadow-xl text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-cyan-500 rounded-xl flex items-center justify-center shadow-lg mx-auto mb-4">
                <BarChart3 className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-3xl font-bold text-white mb-4">
                📊 Malware Analysis Scans Guide
              </h2>
              <p className="text-gray-300 text-lg">Select the right scan type for your needs.</p>
            </div>

            {/* Comparison table */}
            <div className="bg-gradient-to-br from-purple-700/30 to-purple-800/40 backdrop-blur-sm border border-purple-500/20 rounded-2xl p-8 shadow-xl overflow-x-auto">
              <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
                <BarChart3 className="w-6 h-6 mr-2 text-purple-400" />
                Scan Types Comparison
              </h3>

              <div className="overflow-x-auto bg-gradient-to-br from-purple-600/20 to-purple-700/30 rounded-xl border border-purple-500/20">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b border-purple-500/30 bg-gradient-to-r from-purple-600/30 to-purple-700/40">
                      <th className="text-left py-4 px-6 text-purple-200 font-semibold">Feature</th>
                      <th className="text-center py-4 px-6 text-cyan-300 font-semibold">
                        ⚡ Quick
                      </th>
                      <th className="text-center py-4 px-6 text-green-300 font-semibold">
                        📊 Standard
                      </th>
                      <th className="text-center py-4 px-6 text-yellow-300 font-semibold">
                        🎯 Comprehensive
                      </th>
                      <th className="text-center py-4 px-6 text-red-300 font-semibold">
                        🔥 Aggressive
                      </th>
                      <th className="text-center py-4 px-6 text-purple-300 font-semibold">
                        📦 Packing
                      </th>
                    </tr>
                  </thead>
                  <tbody className="text-white">
                    <tr className="border-b border-purple-500/20 hover:bg-purple-600/10 transition-colors">
                      <td className="py-4 px-6 font-medium text-gray-200">⏱️ Duration</td>
                      <td className="py-4 px-6 text-center text-cyan-200">5-10s</td>
                      <td className="py-4 px-6 text-center text-green-200">30s-2min</td>
                      <td className="py-4 px-6 text-center text-yellow-200">2-10min</td>
                      <td className="py-4 px-6 text-center text-red-300 font-semibold">5-15min</td>
                      <td className="py-4 px-6 text-center text-purple-200">30s-2min</td>
                    </tr>
                    <tr className="border-b border-purple-500/20 hover:bg-purple-600/10 transition-colors">
                      <td className="py-4 px-6 font-medium text-gray-200">🔧 Complexity</td>
                      <td className="py-4 px-6 text-center text-cyan-200">Basic</td>
                      <td className="py-4 px-6 text-center text-green-200">Complete</td>
                      <td className="py-4 px-6 text-center text-yellow-200">Maximum</td>
                      <td className="py-4 px-6 text-center text-red-300 font-semibold">
                        Ultra-Maximum
                      </td>
                      <td className="py-4 px-6 text-center text-purple-200">Specialized</td>
                    </tr>
                    <tr className="border-b border-purple-500/20 hover:bg-purple-600/10 transition-colors">
                      <td className="py-4 px-6 font-medium text-gray-200">📊 Score Max</td>
                      <td className="py-4 px-6 text-center text-cyan-200">100%</td>
                      <td className="py-4 px-6 text-center text-green-200">50 pts</td>
                      <td className="py-4 px-6 text-center text-yellow-200">100%</td>
                      <td className="py-4 px-6 text-center text-red-300 font-semibold">
                        50+ enhanced pts
                      </td>
                      <td className="py-4 px-6 text-center text-purple-200">Confidence</td>
                    </tr>
                    <tr className="border-b border-purple-500/20 hover:bg-purple-600/10 transition-colors">
                      <td className="py-4 px-6 font-medium text-gray-200">🔍 IOCs</td>
                      <td className="py-4 px-6 text-center text-cyan-200">✅ Basic</td>
                      <td className="py-4 px-6 text-center text-green-200">✅ Detailed</td>
                      <td className="py-4 px-6 text-center text-yellow-200">✅ Complete</td>
                      <td className="py-4 px-6 text-center text-red-300 font-semibold">
                        ✅ Ultra-Complete
                      </td>
                      <td className="py-4 px-6 text-center text-gray-400">❌</td>
                    </tr>
                    <tr className="border-b border-purple-500/20 hover:bg-purple-600/10 transition-colors">
                      <td className="py-4 px-6 font-medium text-gray-200">🛡️ YARA</td>
                      <td className="py-4 px-6 text-center text-gray-400">❌</td>
                      <td className="py-4 px-6 text-center text-green-200">✅ Basic</td>
                      <td className="py-4 px-6 text-center text-yellow-200">✅ Complete</td>
                      <td className="py-4 px-6 text-center text-red-300 font-semibold">
                        ✅ Ultra-Complete
                      </td>
                      <td className="py-4 px-6 text-center text-gray-400">❌</td>
                    </tr>
                    <tr className="border-b border-purple-500/20 hover:bg-purple-600/10 transition-colors">
                      <td className="py-4 px-6 font-medium text-gray-200">📦 Packer</td>
                      <td className="py-4 px-6 text-center text-gray-400">❌</td>
                      <td className="py-4 px-6 text-center text-green-200">✅ Basic</td>
                      <td className="py-4 px-6 text-center text-yellow-200">✅ Advanced</td>
                      <td className="py-4 px-6 text-center text-red-300 font-semibold">
                        ✅ Ultra-Advanced
                      </td>
                      <td className="py-4 px-6 text-center text-purple-200">✅ Specialized</td>
                    </tr>
                    <tr className="border-b border-gray-600/30 hover:bg-gray-700/20 transition-colors">
                      <td className="py-4 px-6 font-medium text-gray-200">💾 Save</td>
                      <td className="py-4 px-6 text-center text-gray-400">❌</td>
                      <td className="py-4 px-6 text-center text-gray-400">❌</td>
                      <td className="py-4 px-6 text-center text-green-400">✅</td>
                      <td className="py-4 px-6 text-center text-green-400 font-semibold">✅</td>
                      <td className="py-4 px-6 text-center text-gray-400">❌</td>
                    </tr>
                    <tr className="border-b border-gray-600/30 hover:bg-gray-700/20 transition-colors">
                      <td className="py-4 px-6 font-medium text-gray-200">🎯 Precision</td>
                      <td className="py-4 px-6 text-center text-cyan-200">Low</td>
                      <td className="py-4 px-6 text-center text-green-200">Medium</td>
                      <td className="py-4 px-6 text-center text-yellow-200">High</td>
                      <td className="py-4 px-6 text-center text-red-300 font-semibold">Maximum</td>
                      <td className="py-4 px-6 text-center text-purple-200">Specialized</td>
                    </tr>
                    <tr className="hover:bg-gray-700/20 transition-colors">
                      <td className="py-4 px-6 font-medium text-gray-200">🔥 Amplification</td>
                      <td className="py-4 px-6 text-center text-gray-400">❌</td>
                      <td className="py-4 px-6 text-center text-gray-400">❌</td>
                      <td className="py-4 px-6 text-center text-gray-400">❌</td>
                      <td className="py-4 px-6 text-center text-red-300 font-semibold">
                        ✅ Unique
                      </td>
                      <td className="py-4 px-6 text-center text-gray-400">❌</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            {/* Usage recommendations */}
            <div className="bg-gradient-to-br from-purple-700/30 to-purple-800/40 backdrop-blur-sm border border-purple-500/20 rounded-2xl p-8 shadow-xl">
              <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
                <Info className="w-6 h-6 mr-2 text-purple-400" />
                Usage Recommendations
              </h3>

              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                {/* Scan Rapide */}
                <div className="bg-gradient-to-br from-cyan-900/30 to-cyan-800/30 border border-cyan-500/30 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
                  <h4 className="text-cyan-300 font-semibold mb-3 flex items-center">
                    <Zap className="w-5 h-5 mr-2" />
                    🔍 Quick Scan
                  </h4>
                  <ul className="space-y-2 text-cyan-200 text-sm">
                    <li>✅ Mass file sorting</li>
                    <li>✅ First quick assessment</li>
                    <li>✅ Basic IOCs verification</li>
                  </ul>
                </div>

                {/* Scan Standard */}
                <div className="bg-gradient-to-br from-green-900/30 to-green-800/30 border border-green-500/30 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
                  <h4 className="text-green-300 font-semibold mb-3 flex items-center">
                    <Search className="w-5 h-5 mr-2" />
                    📊 Standard Scan
                  </h4>
                  <ul className="space-y-2 text-green-200 text-sm">
                    <li>✅ Daily usage</li>
                    <li>✅ Speed/precision balance</li>
                    <li>✅ Routine analysis</li>
                  </ul>
                </div>

                {/* Scan Approfondi */}
                <div className="bg-gradient-to-br from-yellow-900/30 to-yellow-800/30 border border-yellow-500/30 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
                  <h4 className="text-yellow-300 font-semibold mb-3 flex items-center">
                    <Activity className="w-5 h-5 mr-2" />
                    🎯 Comprehensive Scan
                  </h4>
                  <ul className="space-y-2 text-yellow-200 text-sm">
                    <li>✅ Suspicious files identified</li>
                    <li>✅ Investigation forensique</li>
                    <li>✅ Analyse complète nécessaire</li>
                  </ul>
                </div>

                {/* Scan Aggressif */}
                <div className="bg-gradient-to-br from-red-900/30 to-red-800/30 border border-red-500/30 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 lg:col-span-2 xl:col-span-1">
                  <h4 className="text-red-300 font-semibold mb-3 flex items-center">
                    <AlertTriangle className="w-5 h-5 mr-2" />
                    🚨 Aggressive Scan
                  </h4>
                  <ul className="space-y-2 text-red-200 text-sm">
                    <li>
                      ✅ <strong>Malware sophistiqué</strong>
                    </li>
                    <li>
                      ✅ <strong>Détection maximale requise</strong>
                    </li>
                    <li>
                      ✅ <strong>Highly suspicious files</strong>
                    </li>
                    <li>
                      ✅ <strong>Critical investigation</strong>
                    </li>
                  </ul>
                </div>

                {/* Scan Packing */}
                <div className="bg-gradient-to-br from-purple-900/30 to-purple-800/30 border border-purple-500/30 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
                  <h4 className="text-purple-300 font-semibold mb-3 flex items-center">
                    <Database className="w-5 h-5 mr-2" />
                    📦 Packing Scan
                  </h4>
                  <ul className="space-y-2 text-purple-200 text-sm">
                    <li>✅ Compressed files</li>
                    <li>✅ Suspicious executables</li>
                    <li>✅ Obfuscation detection</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Analysis details popup */}
      {showAnalysisDetails && selectedAnalysisForView && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-gradient-to-br from-gray-800/90 to-gray-900/95 backdrop-blur-md rounded-2xl max-w-6xl w-full max-h-[95vh] overflow-hidden border border-gray-600/50 shadow-2xl">
            {/* Header du popup */}
            <div className="sticky top-0 bg-gradient-to-r from-gray-800/50 to-gray-900/70 backdrop-blur-md border-b border-gray-600/30 p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-gray-700/80 to-gray-800/80 backdrop-blur-sm border border-gray-600/30">
                    {getThreatIcon(selectedAnalysisForView.threat_level)}
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-white mb-1">
                      {selectedAnalysisForView.original_filename}
                    </h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-300">
                      <span className="flex items-center">
                        <FileText className="w-4 h-4 mr-1" />
                        {malwareUtils.formatFileSize(selectedAnalysisForView.file_size)}
                      </span>
                      <span className="flex items-center">
                        <Shield className="w-4 h-4 mr-1" />
                        {selectedAnalysisForView.analysis_type || 'standard'}
                      </span>
                      <span className="flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        {malwareUtils.formatDate(
                          selectedAnalysisForView.created_at ||
                            selectedAnalysisForView.analysis_timestamp
                        )}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  {/* Bouton Export PDF */}
                  <button
                    onClick={() =>
                      handleExportMalwarePDF(
                        selectedAnalysisForView.analysis_id || selectedAnalysisForView._id
                      )
                    }
                    disabled={exportingPDF}
                    className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg"
                    title="Export report to PDF"
                  >
                    {exportingPDF ? (
                      <>
                        <RefreshCw className="w-4 h-4 animate-spin" />
                        <span>Export...</span>
                      </>
                    ) : (
                      <>
                        <Download className="w-4 h-4" />
                        <span>Export PDF</span>
                      </>
                    )}
                  </button>

                  {/* Bouton Fermer */}
                  <button
                    onClick={closeAnalysisDetails}
                    className="p-2 rounded-xl bg-gray-700/80 hover:bg-gray-600/80 text-gray-300 hover:text-white transition-all duration-200 border border-gray-600/30"
                  >
                    <X className="w-6 h-6" />
                  </button>
                </div>
              </div>
            </div>

            {/* Contenu du popup avec scroll */}
            <div className="overflow-y-auto max-h-[calc(95vh-120px)]">
              <div className="p-6 pb-16 space-y-6">
                {/* Résumé principal */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="bg-gradient-to-br from-purple-600/20 to-purple-700/30 backdrop-blur-sm rounded-xl p-4 border border-purple-500/20 shadow-lg">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-purple-200 text-sm font-medium">Threat level</span>
                      {getThreatIcon(selectedAnalysisForView.threat_level)}
                    </div>
                    <div className="space-y-2">
                      <span
                        className={`text-3xl font-bold ${malwareUtils.getThreatColor(selectedAnalysisForView.threat_level)}`}
                      >
                        {selectedAnalysisForView.confidence_pct}%
                      </span>
                      <div
                        className={`px-3 py-1 rounded-full text-xs font-medium ${malwareUtils.getThreatBgColor(selectedAnalysisForView.threat_level)}`}
                      >
                        {selectedAnalysisForView.threat_level}
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-purple-600/20 to-purple-700/30 backdrop-blur-sm rounded-xl p-4 border border-purple-500/20 shadow-lg">
                    <div className="flex items-center mb-3">
                      <Target className="w-5 h-5 text-cyan-400 mr-2" />
                      <span className="text-purple-200 text-sm font-medium">Malware score</span>
                    </div>
                    <div className="space-y-2">
                      <span className="text-3xl font-bold text-white">
                        {selectedAnalysisForView.malware_score} /{' '}
                        {selectedAnalysisForView.max_score}
                      </span>
                      <div className="w-full bg-gray-700/50 rounded-full h-3 border border-gray-600/30">
                        <div
                          className={`h-3 rounded-full transition-all duration-500 ${
                            selectedAnalysisForView.malware_score >= 40
                              ? 'bg-gradient-to-r from-red-500 to-red-600'
                              : selectedAnalysisForView.malware_score >= 30
                                ? 'bg-gradient-to-r from-orange-500 to-orange-600'
                                : selectedAnalysisForView.malware_score >= 20
                                  ? 'bg-gradient-to-r from-yellow-500 to-yellow-600'
                                  : selectedAnalysisForView.malware_score >= 10
                                    ? 'bg-gradient-to-r from-cyan-500 to-cyan-600'
                                    : 'bg-gradient-to-r from-green-500 to-green-600'
                          }`}
                          style={{
                            width: `${(selectedAnalysisForView.malware_score / selectedAnalysisForView.max_score) * 100}%`,
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-purple-600/20 to-purple-700/30 backdrop-blur-sm rounded-xl p-4 border border-purple-500/20 shadow-lg">
                    <div className="flex items-center mb-3">
                      <FileText className="w-5 h-5 text-purple-400 mr-2" />
                      <span className="text-purple-200 text-sm font-medium">File type</span>
                    </div>
                    <div className="space-y-1">
                      <span className="text-lg font-semibold text-white">
                        {selectedAnalysisForView.file_info?.file_type || 'Inconnu'}
                      </span>
                      <div className="text-xs text-purple-300">
                        {selectedAnalysisForView.original_filename
                          ?.split('.')
                          .pop()
                          ?.toUpperCase() || 'N/A'}
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-purple-600/20 to-purple-700/30 backdrop-blur-sm rounded-xl p-4 border border-purple-500/20 shadow-lg">
                    <div className="flex items-center mb-3">
                      <span className="text-purple-200 text-sm font-medium">Verdict</span>
                    </div>
                    <div className="flex items-center">
                      {(() => {
                        const threatLevel = selectedAnalysisForView.threat_level;
                        const score =
                          selectedAnalysisForView.confidence_pct ||
                          selectedAnalysisForView.malware_score ||
                          0;

                        if (threatLevel === 'CRITICAL' || threatLevel === 'HIGH' || score >= 70) {
                          return (
                            <div className="flex items-center">
                              <Ban className="w-6 h-6 text-red-500 mr-2" />
                              <span className="text-red-400 font-semibold text-sm">
                                Malware Detected
                              </span>
                            </div>
                          );
                        } else if (threatLevel === 'MEDIUM' || score >= 30) {
                          return (
                            <div className="flex items-center">
                              <AlertTriangle className="w-6 h-6 text-orange-500 mr-2" />
                              <span className="text-orange-400 font-semibold text-sm">
                                Suspicious
                              </span>
                            </div>
                          );
                        } else if (threatLevel === 'LOW' || score >= 10) {
                          return (
                            <div className="flex items-center">
                              <Info className="w-6 h-6 text-yellow-500 mr-2" />
                              <span className="text-yellow-400 font-semibold text-sm">
                                Low Risk
                              </span>
                            </div>
                          );
                        } else {
                          return (
                            <div className="flex items-center">
                              <CheckCircle className="w-6 h-6 text-green-500 mr-2" />
                              <span className="text-green-400 font-semibold text-sm">
                                File Appears Safe
                              </span>
                            </div>
                          );
                        }
                      })()}
                    </div>
                  </div>
                </div>

                {/* Hashes */}
                {selectedAnalysisForView.file_info?.hashes &&
                  Object.keys(selectedAnalysisForView.file_info.hashes).length > 0 && (
                    <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/70 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 shadow-lg">
                      <h4 className="text-white font-semibold mb-4 flex items-center">
                        <Shield className="w-5 h-5 mr-2 text-purple-400" />
                        Hashes
                      </h4>
                      <div className="space-y-3">
                        {Object.entries(selectedAnalysisForView.file_info.hashes).map(
                          ([type, hash]) =>
                            hash ? (
                              <div
                                key={type}
                                className="bg-gradient-to-br from-gray-700/50 to-gray-800/50 border border-gray-600/30 rounded-lg p-3"
                              >
                                <div className="flex items-center justify-between mb-2">
                                  <span className="text-purple-300 uppercase font-semibold text-sm">
                                    {type}
                                  </span>
                                  <button
                                    onClick={() => navigator.clipboard.writeText(String(hash))}
                                    className="text-purple-400 hover:text-purple-300 text-xs px-2 py-1 bg-purple-800/30 rounded transition-colors"
                                  >
                                    Copy
                                  </button>
                                </div>
                                <span className="text-white font-mono text-xs break-all bg-gray-900/50 p-2 rounded block">
                                  {String(hash)}
                                </span>
                              </div>
                            ) : null
                        )}
                      </div>
                    </div>
                  )}

                {/* IOCs */}
                {selectedAnalysisForView.iocs && (
                  <div className="bg-gradient-to-br from-purple-800/50 to-purple-900/70 backdrop-blur-sm rounded-xl p-6 border border-purple-600/30 shadow-lg">
                    <h4 className="text-white font-semibold mb-4 flex items-center">
                      <AlertTriangle className="w-6 h-6 mr-2 text-orange-400" />
                      Indicators of Compromise (IOCs)
                    </h4>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* IPs */}
                      {selectedAnalysisForView.iocs.ips &&
                        selectedAnalysisForView.iocs.ips.length > 0 && (
                          <div className="bg-gradient-to-br from-red-900/30 to-red-800/30 border border-red-500/30 rounded-lg p-4">
                            <h5 className="text-red-300 font-semibold mb-3 flex items-center">
                              <Globe className="w-4 h-4 mr-2" />
                              Adresses IP ({selectedAnalysisForView.iocs.ips.length})
                            </h5>
                            <div className="space-y-2 max-h-32 overflow-y-auto">
                              {selectedAnalysisForView.iocs.ips.map((ip: string, i: number) => (
                                <div
                                  key={i}
                                  className="p-2 bg-red-800/20 rounded border border-red-600/20"
                                >
                                  <span className="text-red-200 font-mono text-sm">{ip}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                      {/* Domaines */}
                      {selectedAnalysisForView.iocs.domains &&
                        selectedAnalysisForView.iocs.domains.length > 0 && (
                          <div className="bg-gradient-to-br from-orange-900/30 to-orange-800/30 border border-orange-500/30 rounded-lg p-4">
                            <h5 className="text-orange-300 font-semibold mb-3 flex items-center">
                              <Globe className="w-4 h-4 mr-2" />
                              Domaines ({selectedAnalysisForView.iocs.domains.length})
                            </h5>
                            <div className="space-y-2 max-h-32 overflow-y-auto">
                              {selectedAnalysisForView.iocs.domains.map(
                                (domain: string, i: number) => (
                                  <div
                                    key={i}
                                    className="p-2 bg-orange-800/20 rounded border border-orange-600/20"
                                  >
                                    <span className="text-orange-200 font-mono text-sm break-all">
                                      {domain}
                                    </span>
                                  </div>
                                )
                              )}
                            </div>
                          </div>
                        )}

                      {/* URLs */}
                      {selectedAnalysisForView.iocs.urls &&
                        selectedAnalysisForView.iocs.urls.length > 0 && (
                          <div className="bg-gradient-to-br from-yellow-900/30 to-yellow-800/30 border border-yellow-500/30 rounded-lg p-4">
                            <h5 className="text-yellow-300 font-semibold mb-3 flex items-center">
                              <ExternalLink className="w-4 h-4 mr-2" />
                              URLs ({selectedAnalysisForView.iocs.urls.length})
                            </h5>
                            <div className="space-y-2 max-h-32 overflow-y-auto">
                              {selectedAnalysisForView.iocs.urls.map((url: string, i: number) => (
                                <div
                                  key={i}
                                  className="p-2 bg-yellow-800/20 rounded border border-yellow-600/20"
                                >
                                  <span className="text-yellow-200 font-mono text-xs break-all">
                                    {url}
                                  </span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                      {/* Emails */}
                      {selectedAnalysisForView.iocs.emails &&
                        selectedAnalysisForView.iocs.emails.length > 0 && (
                          <div className="bg-gradient-to-br from-blue-900/30 to-blue-800/30 border border-blue-500/30 rounded-lg p-4">
                            <h5 className="text-blue-300 font-semibold mb-3 flex items-center">
                              <Mail className="w-4 h-4 mr-2" />
                              Emails ({selectedAnalysisForView.iocs.emails.length})
                            </h5>
                            <div className="space-y-2 max-h-32 overflow-y-auto">
                              {selectedAnalysisForView.iocs.emails.map(
                                (email: string, i: number) => (
                                  <div
                                    key={i}
                                    className="p-2 bg-blue-800/20 rounded border border-blue-600/20"
                                  >
                                    <span className="text-blue-200 font-mono text-sm">{email}</span>
                                  </div>
                                )
                              )}
                            </div>
                          </div>
                        )}
                    </div>
                  </div>
                )}

                {/* Détections */}
                {selectedAnalysisForView.detection_details &&
                  selectedAnalysisForView.detection_details.length > 0 && (
                    <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/70 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 shadow-lg">
                      <h4 className="text-white font-semibold mb-4 flex items-center">
                        <Search className="w-5 h-5 mr-2 text-orange-400" />
                        Detections ({selectedAnalysisForView.detection_details.length})
                      </h4>
                      <div className="space-y-3 max-h-48 overflow-y-auto">
                        {selectedAnalysisForView.detection_details.map(
                          (detail: string, index: number) => (
                            <div
                              key={index}
                              className="bg-gradient-to-br from-orange-900/30 to-orange-800/30 border border-orange-500/30 rounded-lg p-3"
                            >
                              <div className="flex items-start">
                                <AlertTriangle className="w-4 h-4 text-orange-400 mr-2 mt-0.5 flex-shrink-0" />
                                <span className="text-orange-200 text-sm">{detail}</span>
                              </div>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}

                {/* Chaînes suspectes */}
                {selectedAnalysisForView.analysis_details?.suspicious_strings &&
                  selectedAnalysisForView.analysis_details.suspicious_strings.length > 0 && (
                    <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/70 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 shadow-lg">
                      <h4 className="text-white font-semibold mb-3 flex items-center">
                        <AlertTriangle className="w-5 h-5 mr-2 text-red-400" />
                        Suspicious Strings (
                        {selectedAnalysisForView.analysis_details.suspicious_strings.length})
                      </h4>
                      <div className="space-y-2 max-h-48 overflow-y-auto">
                        {selectedAnalysisForView.analysis_details.suspicious_strings
                          .slice(0, 10)
                          .map((item: any, index: number) => (
                            <div
                              key={index}
                              className="p-3 bg-gradient-to-br from-red-900/30 to-red-800/30 border border-red-500/30 rounded-lg"
                            >
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <span className="text-red-200 text-sm font-mono break-all">
                                    {item.string}
                                  </span>
                                </div>
                                <span className="ml-2 px-2 py-1 bg-red-500/20 text-red-300 text-xs rounded-full">
                                  {item.type}
                                </span>
                              </div>
                            </div>
                          ))}
                        {selectedAnalysisForView.analysis_details.suspicious_strings.length >
                          10 && (
                          <div className="text-center text-gray-400 text-sm">
                            ... et{' '}
                            {selectedAnalysisForView.analysis_details.suspicious_strings.length -
                              10}{' '}
                            others
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                {/* Recommandations */}
                {selectedAnalysisForView.recommendations &&
                  selectedAnalysisForView.recommendations.length > 0 && (
                    <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/70 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 shadow-lg">
                      <h4 className="text-white font-semibold mb-3 flex items-center">
                        <Info className="w-5 h-5 mr-2 text-blue-400" />
                        Recommandations ({selectedAnalysisForView.recommendations.length})
                      </h4>
                      <div className="space-y-2">
                        {selectedAnalysisForView.recommendations.map(
                          (recommendation: string, index: number) => (
                            <div
                              key={index}
                              className="p-3 bg-gradient-to-br from-blue-900/30 to-blue-800/30 border border-blue-500/30 rounded-lg"
                            >
                              <span className="text-blue-200 text-sm">{recommendation}</span>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}
              </div>
            </div>

            {/* Footer du popup */}
            <div className="sticky bottom-0 bg-gradient-to-r from-gray-800/95 to-gray-900/95 backdrop-blur-md border-t border-gray-600/50 p-4 flex justify-end">
              <button
                onClick={closeAnalysisDetails}
                className="px-6 py-2 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white rounded-lg transition-all duration-200 shadow-lg"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
