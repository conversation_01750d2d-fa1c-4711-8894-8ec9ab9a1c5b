import {
  Shield,
  AlertTriangle,
  Activity,
  Lock,
  Globe,
  Database,
  TrendingUp,
  TrendingDown,
  Eye,
  Zap,
  CheckCircle,
  XCircle,
  Clock,
} from 'lucide-react';

export default function SecurityOverview() {
  // Données simulées pour la page de sécurité
  const threatData = [
    { id: 1, type: 'Malware', severity: 'high', count: 12, trend: 'up' },
    { id: 2, type: 'Phishing', severity: 'medium', count: 8, trend: 'down' },
    { id: 3, type: 'Brute Force', severity: 'high', count: 15, trend: 'up' },
    { id: 4, type: 'DDoS', severity: 'low', count: 3, trend: 'down' },
  ];

  const securityEvents = [
    { id: 1, event: 'Tentative de connexion suspecte détectée', time: '2 min', severity: 'high' },
    { id: 2, event: 'Scan de port détecté sur le serveur web', time: '5 min', severity: 'medium' },
    { id: 3, event: 'Mise à jour de sécurité appliquée', time: '15 min', severity: 'info' },
    { id: 4, event: 'Analyse antivirus terminée', time: '30 min', severity: 'info' },
    { id: 5, event: 'Firewall: règle mise à jour', time: '1h', severity: 'info' },
  ];

  const networkStatus = [
    { name: 'Firewall Principal', status: 'active', connections: 1247 },
    { name: 'IDS/IPS', status: 'active', alerts: 23 },
    { name: 'VPN Gateway', status: 'active', users: 45 },
    { name: 'Proxy Web', status: 'maintenance', requests: 0 },
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'text-red-400 bg-red-500/20 border-red-500/30';
      case 'medium':
        return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30';
      case 'low':
        return 'text-green-400 bg-green-500/20 border-green-500/30';
      default:
        return 'text-blue-400 bg-blue-500/20 border-blue-500/30';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'high':
        return <AlertTriangle className="w-4 h-4" />;
      case 'medium':
        return <Eye className="w-4 h-4" />;
      case 'low':
        return <CheckCircle className="w-4 h-4" />;
      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-purple-900/40 backdrop-blur-xl border border-purple-500/20 rounded-2xl p-6 shadow-2xl">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2 flex items-center space-x-3">
              <Shield className="w-8 h-8 text-purple-400" />
              <span>Vue d'ensemble de la sécurité</span>
            </h1>
            <p className="text-purple-200">
              Surveillance en temps réel des menaces et de l'état de sécurité
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <button className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
              Actualiser
            </button>
            <button className="px-4 py-2 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white rounded-lg transition-all">
              Lancer un scan
            </button>
          </div>
        </div>
      </div>

      {/* Threat Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {threatData.map((threat) => (
          <div
            key={threat.id}
            className="bg-purple-900/40 backdrop-blur-xl border border-purple-500/20 rounded-2xl p-6 shadow-2xl"
          >
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-xl ${getSeverityColor(threat.severity)}`}>
                {getSeverityIcon(threat.severity)}
              </div>
              <div
                className={`flex items-center space-x-1 text-sm ${
                  threat.trend === 'up' ? 'text-red-400' : 'text-green-400'
                }`}
              >
                {threat.trend === 'up' ? (
                  <TrendingUp className="w-4 h-4" />
                ) : (
                  <TrendingDown className="w-4 h-4" />
                )}
              </div>
            </div>
            <div>
              <p className="text-2xl font-bold text-white mb-1">{threat.count}</p>
              <p className="text-purple-200 text-sm">{threat.type}</p>
              <p
                className={`text-xs mt-1 capitalize ${getSeverityColor(threat.severity).split(' ')[0]}`}
              >
                {threat.severity === 'high'
                  ? 'Critique'
                  : threat.severity === 'medium'
                    ? 'Moyen'
                    : 'Faible'}
              </p>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Security Events */}
        <div className="bg-purple-900/40 backdrop-blur-xl border border-purple-500/20 rounded-2xl p-6 shadow-2xl">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-white flex items-center space-x-2">
              <Activity className="w-5 h-5 text-purple-400" />
              <span>Événements de sécurité</span>
            </h3>
            <Clock className="w-5 h-5 text-purple-300" />
          </div>
          <div className="space-y-3 max-h-80 overflow-y-auto">
            {securityEvents.map((event) => (
              <div
                key={event.id}
                className="flex items-start space-x-4 p-3 bg-purple-800/30 rounded-xl hover:bg-purple-800/50 transition-colors"
              >
                <div className={`p-2 rounded-lg ${getSeverityColor(event.severity)}`}>
                  {getSeverityIcon(event.severity)}
                </div>
                <div className="flex-1">
                  <p className="text-white text-sm">{event.event}</p>
                  <p className="text-purple-300 text-xs">Il y a {event.time}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Network Status */}
        <div className="bg-purple-900/40 backdrop-blur-xl border border-purple-500/20 rounded-2xl p-6 shadow-2xl">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-white flex items-center space-x-2">
              <Globe className="w-5 h-5 text-purple-400" />
              <span>État du réseau</span>
            </h3>
            <Zap className="w-5 h-5 text-purple-300" />
          </div>
          <div className="space-y-4">
            {networkStatus.map((item, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-4 bg-purple-800/30 rounded-xl"
              >
                <div className="flex items-center space-x-3">
                  <div
                    className={`w-3 h-3 rounded-full ${
                      item.status === 'active'
                        ? 'bg-green-400 animate-pulse'
                        : item.status === 'maintenance'
                          ? 'bg-yellow-400'
                          : 'bg-red-400'
                    }`}
                  ></div>
                  <div>
                    <p className="text-white font-medium">{item.name}</p>
                    <p className="text-purple-300 text-xs">
                      {item.connections !== undefined
                        ? `${item.connections} connexions`
                        : item.alerts !== undefined
                          ? `${item.alerts} alertes`
                          : item.users !== undefined
                            ? `${item.users} utilisateurs`
                            : item.requests !== undefined
                              ? `${item.requests} requêtes`
                              : ''}
                    </p>
                  </div>
                </div>
                <div
                  className={`px-3 py-1 rounded-full text-xs font-medium ${
                    item.status === 'active'
                      ? 'bg-green-500/20 text-green-400'
                      : item.status === 'maintenance'
                        ? 'bg-yellow-500/20 text-yellow-400'
                        : 'bg-red-500/20 text-red-400'
                  }`}
                >
                  {item.status === 'active'
                    ? 'Actif'
                    : item.status === 'maintenance'
                      ? 'Maintenance'
                      : 'Hors ligne'}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Security Score and Recommendations */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 bg-purple-900/40 backdrop-blur-xl border border-purple-500/20 rounded-2xl p-6 shadow-2xl">
          <h3 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
            <Database className="w-5 h-5 text-purple-400" />
            <span>Recommandations de sécurité</span>
          </h3>
          <div className="space-y-4">
            <div className="flex items-start space-x-4 p-4 bg-red-500/10 border border-red-500/20 rounded-xl">
              <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5" />
              <div>
                <p className="text-white font-medium">Mise à jour critique disponible</p>
                <p className="text-red-300 text-sm">
                  Appliquer la mise à jour de sécurité pour le serveur web
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-4 p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-xl">
              <Eye className="w-5 h-5 text-yellow-400 mt-0.5" />
              <div>
                <p className="text-white font-medium">Configuration firewall</p>
                <p className="text-yellow-300 text-sm">
                  Réviser les règles de pare-feu pour le port 8080
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-4 p-4 bg-blue-500/10 border border-blue-500/20 rounded-xl">
              <Lock className="w-5 h-5 text-blue-400 mt-0.5" />
              <div>
                <p className="text-white font-medium">Authentification renforcée</p>
                <p className="text-blue-300 text-sm">
                  Activer l'authentification à deux facteurs pour tous les utilisateurs
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-purple-900/40 backdrop-blur-xl border border-purple-500/20 rounded-2xl p-6 shadow-2xl">
          <h3 className="text-xl font-bold text-white mb-6">Score global</h3>
          <div className="text-center">
            <div className="relative w-32 h-32 mx-auto mb-4">
              <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="rgba(139, 92, 246, 0.2)"
                  strokeWidth="3"
                />
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="rgb(34, 197, 94)"
                  strokeWidth="3"
                  strokeDasharray="94, 100"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-3xl font-bold text-white">94</span>
              </div>
            </div>
            <p className="text-green-400 font-medium">Excellent</p>
            <p className="text-purple-300 text-sm mt-2">Votre infrastructure est bien sécurisée</p>
          </div>
        </div>
      </div>
    </div>
  );
}
