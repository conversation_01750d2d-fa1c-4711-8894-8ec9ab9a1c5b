import React, { useState } from 'react';
import { Mail, ArrowLeft, Shield, Send, CheckCircle, AlertCircle } from 'lucide-react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import Logo from '../components/Logo';

export default function ForgotPassword() {
  const [email, setEmail] = useState('');
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setStatus('loading');
    setMessage('');

    try {
      const response = await axios.post('http://localhost:5000/auth/forgot-password', { email });
      setStatus('success');
      setMessage(response.data.msg || 'Reset instructions sent by email');
    } catch (error: any) {
      setStatus('error');
      setMessage(error?.response?.data?.msg || 'Error sending instructions');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0">
        {/* Animated background waves */}
        <div className="absolute inset-0 opacity-20">
          <svg className="w-full h-full" viewBox="0 0 1200 800" preserveAspectRatio="none">
            <defs>
              <linearGradient id="wave-gradient-forgot" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#8B5CF6" stopOpacity="0.3" />
                <stop offset="50%" stopColor="#A855F7" stopOpacity="0.2" />
                <stop offset="100%" stopColor="#7C3AED" stopOpacity="0.1" />
              </linearGradient>
            </defs>
            <path
              d="M0,400 C300,200 600,600 1200,300 L1200,800 L0,800 Z"
              fill="url(#wave-gradient-forgot)"
            >
              <animate
                attributeName="d"
                values="M0,400 C300,200 600,600 1200,300 L1200,800 L0,800 Z;
                        M0,300 C300,500 600,100 1200,400 L1200,800 L0,800 Z;
                        M0,400 C300,200 600,600 1200,300 L1200,800 L0,800 Z"
                dur="8s"
                repeatCount="indefinite"
              />
            </path>
          </svg>
        </div>

        {/* Floating particles */}
        <div className="absolute top-20 left-20 opacity-30">
          <div className="w-2 h-2 bg-purple-300 rounded-full animate-pulse"></div>
        </div>
        <div className="absolute top-40 right-32 opacity-25">
          <div
            className="w-3 h-3 bg-purple-400 rounded-full animate-pulse"
            style={{ animationDelay: '1s' }}
          ></div>
        </div>
        <div className="absolute bottom-32 left-16 opacity-20">
          <div
            className="w-4 h-4 bg-purple-200 rounded-full animate-pulse"
            style={{ animationDelay: '2s' }}
          ></div>
        </div>
        <div className="absolute bottom-20 right-20 opacity-35">
          <div
            className="w-2 h-2 bg-purple-300 rounded-full animate-pulse"
            style={{ animationDelay: '0.5s' }}
          ></div>
        </div>

        {/* Gradient orbs */}
        <div className="absolute top-1/3 left-1/3 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div
          className="absolute bottom-1/3 right-1/3 w-80 h-80 bg-indigo-500/15 rounded-full blur-3xl animate-pulse"
          style={{ animationDelay: '2s' }}
        ></div>
      </div>

      <div className="relative z-10 min-h-screen flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          {/* Forgot Password card */}
          <div className="bg-purple-900/40 backdrop-blur-xl border border-purple-500/20 rounded-2xl p-8 shadow-2xl">
            {/* Back button */}
            <div className="mb-6">
              <Link
                to="/login"
                className="inline-flex items-center text-purple-300 hover:text-white transition-colors"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to login
              </Link>
            </div>

            {status === 'idle' || status === 'loading' || status === 'error' ? (
              <>
                {/* Header */}
                <div className="text-center mb-8">
                  <div className="flex justify-center mb-4">
                    <Logo size="md" showText={false} />
                  </div>
                  <h2 className="text-2xl font-bold text-white mb-2">Forgot Password</h2>
                  <p className="text-purple-200">Enter your email to receive reset instructions</p>
                </div>

                {/* Error message */}
                {status === 'error' && (
                  <div className="mb-6 p-4 bg-red-500/10 border border-red-400/30 rounded-xl flex items-center space-x-3 text-red-300">
                    <AlertCircle className="w-5 h-5 flex-shrink-0" />
                    <span className="text-sm">{message}</span>
                  </div>
                )}

                {/* Form */}
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Email field */}
                  <div>
                    <label className="block text-sm font-medium text-purple-200 mb-2">
                      Email Address
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <Mail className="w-5 h-5 text-purple-400" />
                      </div>
                      <input
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="w-full pl-12 pr-4 py-3 bg-purple-800/30 border border-purple-500/30 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                        placeholder="<EMAIL>"
                        required
                        disabled={status === 'loading'}
                      />
                    </div>
                  </div>

                  {/* Submit button */}
                  <button
                    type="submit"
                    disabled={status === 'loading'}
                    className="w-full py-3 px-4 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-purple-900"
                  >
                    {status === 'loading' ? (
                      <div className="flex items-center justify-center">
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                        Sending...
                      </div>
                    ) : (
                      <div className="flex items-center justify-center">
                        <Send className="w-5 h-5 mr-2" />
                        Send instructions
                      </div>
                    )}
                  </button>
                </form>
              </>
            ) : (
              <>
                {/* Success state */}
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-full mb-6 shadow-lg">
                    <CheckCircle className="w-10 h-10 text-white" />
                  </div>
                  <h1 className="text-3xl font-bold text-white mb-4">Email sent!</h1>

                  <div className="bg-green-500/10 border border-green-400/30 rounded-xl p-4 mb-6">
                    <p className="text-sm text-green-300">{message}</p>
                  </div>

                  <div className="text-purple-200 mb-6">
                    <p className="mb-2">Check your email and click on the reset link.</p>
                    <p className="text-sm">The link expires in 10 minutes.</p>
                  </div>

                  {/* Illustration */}
                  <div className="relative mb-6">
                    <div className="flex justify-center items-center space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center shadow-lg animate-float">
                        <Mail className="w-6 h-6 text-white" />
                      </div>
                      <div className="w-8 h-0.5 bg-gradient-to-r from-purple-400 to-green-400"></div>
                      <div
                        className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-lg animate-float"
                        style={{ animationDelay: '1s' }}
                      >
                        <Shield className="w-6 h-6 text-white" />
                      </div>
                    </div>
                  </div>

                  <button
                    onClick={() => {
                      setStatus('idle');
                      setEmail('');
                      setMessage('');
                    }}
                    className="w-full py-3 px-4 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-purple-900"
                  >
                    Resend email
                  </button>
                </div>
              </>
            )}

            {/* Footer */}
            <div className="mt-8 text-center">
              <p className="text-xs text-purple-400">© PICA - Automated Cybersecurity Platform</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
