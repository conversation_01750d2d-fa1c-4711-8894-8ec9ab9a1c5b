import { useEffect, useState, useRef } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { CheckCircle, XCircle, Shield } from 'lucide-react';
import Logo from '../components/Logo';

export default function EmailConfirmed() {
  const [searchParams] = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error' | 'already_used' | null>(
    'loading'
  );
  const [message, setMessage] = useState('');
  const hasRunRef = useRef(false);
  const navigate = useNavigate();

  useEffect(() => {
    if (hasRunRef.current) return; // Éviter les appels multiples
    hasRunRef.current = true;

    const token = searchParams.get('token');
    console.log('Token from URL:', token);

    if (!token) {
      console.log('No token found');
      setStatus('error');
      setMessage('Invalid or missing link.');
      return;
    }

    console.log('🚀 Starting fetch request...');
    fetch(`http://localhost:5000/auth/confirm-email?token=${token}`)
      .then((res) => {
        console.log('📡 Response received - Status:', res.status, 'OK:', res.ok);
        if (res.ok) {
          return res.json().then((data) => {
            console.log('✅ Success data:', data);
            // Success: first use of the link
            setStatus('success');
            setMessage(data.msg);
            console.log('✅ Status updated to SUCCESS');
            setTimeout(() => navigate('/auth/login'), 5000);
          });
        } else {
          return res.json().then((data) => {
            console.log('❌ Error data:', data);
            // Error: link already used, expired or invalid
            if (data.already_used) {
              setStatus('already_used');
              setMessage(data.msg);
              console.log('⚠️ Status updated to ALREADY_USED');
              setTimeout(() => navigate('/auth/login'), 3000);
            } else {
              setStatus('error');
              setMessage(data.msg || 'Error during confirmation.');
              console.log('❌ Status updated to ERROR');
            }
          });
        }
      })
      .catch((error) => {
        console.error('Fetch error:', error);
        setStatus('error');
        setMessage('Server connection error.');
      });
  }, [searchParams, navigate]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0">
        {/* Floating shields */}
        <div className="absolute top-20 left-20 opacity-20">
          <Shield className="w-16 h-16 text-purple-300 animate-pulse" />
        </div>
        <div className="absolute top-40 right-32 opacity-15">
          <Shield
            className="w-12 h-12 text-purple-400 animate-pulse"
            style={{ animationDelay: '1s' }}
          />
        </div>
        <div className="absolute bottom-32 left-16 opacity-10">
          <Shield
            className="w-20 h-20 text-purple-200 animate-pulse"
            style={{ animationDelay: '2s' }}
          />
        </div>
        <div className="absolute bottom-20 right-20 opacity-25">
          <Shield
            className="w-14 h-14 text-purple-300 animate-pulse"
            style={{ animationDelay: '0.5s' }}
          />
        </div>

        {/* Animated background waves */}
        <div className="absolute inset-0 opacity-20">
          <svg className="w-full h-full" viewBox="0 0 1200 800" preserveAspectRatio="none">
            <defs>
              <linearGradient id="wave-gradient-confirm" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#8B5CF6" stopOpacity="0.3" />
                <stop offset="50%" stopColor="#A855F7" stopOpacity="0.2" />
                <stop offset="100%" stopColor="#7C3AED" stopOpacity="0.1" />
              </linearGradient>
            </defs>
            <path
              d="M0,400 C300,200 600,600 1200,300 L1200,800 L0,800 Z"
              fill="url(#wave-gradient-confirm)"
            >
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,0;50,0;0,0"
                dur="20s"
                repeatCount="indefinite"
              />
            </path>
          </svg>
        </div>
      </div>

      <div className="relative z-10 min-h-screen flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          {/* Confirmation card */}
          <div className="bg-purple-900/40 backdrop-blur-xl border border-purple-500/20 rounded-2xl p-8 shadow-2xl">
            {status === 'loading' && (
              <>
                {/* Header */}
                <div className="text-center mb-8">
                  <div className="flex justify-center mb-4">
                    <Logo size="md" showText={false} />
                  </div>
                  <h2 className="text-2xl font-bold text-white mb-2">Verification in progress</h2>
                  <p className="text-purple-200">Confirming your email address...</p>
                </div>

                {/* Loading animation */}
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full mb-6 shadow-lg">
                    <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-white"></div>
                  </div>
                  <p className="text-purple-200">Please wait...</p>
                </div>
              </>
            )}

            {status === 'success' && (
              <>
                {/* Header */}
                <div className="text-center mb-8">
                  <div className="flex justify-center mb-4">
                    <Logo size="md" showText={false} />
                  </div>
                </div>

                {/* Success state */}
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-full mb-6 shadow-lg">
                    <CheckCircle className="w-10 h-10 text-white" />
                  </div>
                  <h1 className="text-3xl font-bold text-white mb-4">Email confirmed!</h1>

                  <div className="bg-green-500/10 border border-green-400/30 rounded-xl p-4 mb-6">
                    <p className="text-sm text-green-300">{message}</p>
                  </div>

                  <div className="text-purple-200 mb-6">
                    <p className="mb-2">Your account is now activated.</p>
                    <p className="text-sm">Automatic redirect to login in 5 seconds...</p>
                  </div>

                  <button
                    onClick={() => navigate('/auth/login')}
                    className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
                  >
                    Login now
                  </button>
                </div>
              </>
            )}

            {status === 'already_used' && (
              <>
                {/* Header */}
                <div className="text-center mb-8">
                  <div className="flex justify-center mb-4">
                    <Logo size="md" showText={false} />
                  </div>
                </div>

                {/* Already used state */}
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full mb-6 shadow-lg">
                    <XCircle className="w-10 h-10 text-white" />
                  </div>
                  <h1 className="text-3xl font-bold text-white mb-4">Link already used</h1>

                  <div className="bg-orange-500/10 border border-orange-400/30 rounded-xl p-4 mb-6">
                    <p className="text-sm text-orange-300">
                      {message || 'This confirmation link has already been used.'}
                    </p>
                  </div>

                  <div className="text-purple-200 mb-6">
                    <p className="mb-2">Your account is already confirmed.</p>
                    <p className="text-sm">Redirecting to login in 3 seconds...</p>
                  </div>

                  <button
                    onClick={() => navigate('/auth/login')}
                    className="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
                  >
                    Go to login
                  </button>
                </div>
              </>
            )}

            {status === 'error' && (
              <>
                {/* Header */}
                <div className="text-center mb-8">
                  <div className="flex justify-center mb-4">
                    <Logo size="md" showText={false} />
                  </div>
                </div>

                {/* Error state */}
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-red-500 to-red-600 rounded-full mb-6 shadow-lg">
                    <XCircle className="w-10 h-10 text-white" />
                  </div>
                  <h1 className="text-3xl font-bold text-white mb-4">Confirmation error</h1>

                  <div className="bg-red-500/10 border border-red-400/30 rounded-xl p-4 mb-6">
                    <p className="text-sm text-red-300">
                      {message || 'An error occurred during confirmation.'}
                    </p>
                  </div>

                  <div className="text-purple-200 mb-6">
                    <p className="mb-2">The link may be expired or invalid.</p>
                    <p className="text-sm">Contact support if the problem persists.</p>
                  </div>

                  <button
                    onClick={() => navigate('/auth/login')}
                    className="w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
                  >
                    Back to login
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
