import React from 'react';
import { useReduxAlerts } from '../hooks/useAlerts';
import { useAppSelector, useAppDispatch } from '../store/hooks';
import { setCurrentUser, clearAuth, updateCurrentUser } from '../store/slices/userSlice';
import ReduxAlertDisplay from '../components/alerts/ReduxAlertDisplay';

const ReduxDemo: React.FC = () => {
  const { showSuccess, showError, showWarning, showInfo, clearAll } = useReduxAlerts();
  const dispatch = useAppDispatch();

  // Sélecteurs pour l'état utilisateur
  const currentUser = useAppSelector((state) => state.user.currentUser);
  const isAuthenticated = useAppSelector((state) => state.user.auth.isAuthenticated);
  const alerts = useAppSelector((state) => state.alerts.alerts);

  const handleTestAlerts = () => {
    showSuccess('This is a success message!');
    setTimeout(() => showError('This is an error message!'), 1000);
    setTimeout(() => showWarning('This is a warning!'), 2000);
    setTimeout(() => showInfo('This is an information!'), 3000);
  };

  const handleSetUser = () => {
    dispatch(
      setCurrentUser({
        id: '1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        username: 'johndoe',
        dateOfBirth: '1990-01-01',
        gender: 'Male',
        role: 'user',
        isEmailVerified: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
    );
    showSuccess('User set successfully!');
  };

  const handleUpdateUser = () => {
    if (currentUser) {
      dispatch(
        updateCurrentUser({
          firstName: 'Jane',
          lastName: 'Smith',
        })
      );
      showSuccess('Utilisateur mis à jour !');
    } else {
      showError('Aucun utilisateur à mettre à jour');
    }
  };

  const handleClearUser = () => {
    dispatch(clearAuth());
    showInfo('Utilisateur déconnecté');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900 p-8">
      <ReduxAlertDisplay />

      <div className="max-w-4xl mx-auto">
        <div className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl p-8">
          <h1 className="text-3xl font-bold text-white mb-8 text-center">
            🧪 Démonstration Redux Slices
          </h1>

          {/* Section Alertes */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-white mb-4">📢 Test des Alertes</h2>
            <div className="space-x-4 mb-4">
              <button
                onClick={handleTestAlerts}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Test all alerts
              </button>
              <button
                onClick={() => showSuccess('Simple success')}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Success
              </button>
              <button
                onClick={() => showError('Simple error')}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Error
              </button>
              <button
                onClick={clearAll}
                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Effacer toutes
              </button>
            </div>
            <div className="text-purple-200 text-sm">Alertes actives: {alerts.length}</div>
          </div>

          {/* Section Utilisateur */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-white mb-4">👤 Test du Slice Utilisateur</h2>
            <div className="space-x-4 mb-4">
              <button
                onClick={handleSetUser}
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Définir utilisateur
              </button>
              <button
                onClick={handleUpdateUser}
                className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors"
                disabled={!currentUser}
              >
                Mettre à jour
              </button>
              <button
                onClick={handleClearUser}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Déconnecter
              </button>
            </div>

            {/* Affichage de l'état utilisateur */}
            <div className="bg-black/20 rounded-lg p-4">
              <h3 className="text-white font-medium mb-2">État actuel:</h3>
              <div className="text-purple-200 text-sm space-y-1">
                <div>Authentifié: {isAuthenticated ? '✅ Oui' : '❌ Non'}</div>
                {currentUser ? (
                  <div>
                    <div>
                      Nom: {currentUser.firstName} {currentUser.lastName}
                    </div>
                    <div>Email: {currentUser.email}</div>
                    <div>Rôle: {currentUser.role}</div>
                  </div>
                ) : (
                  <div>Aucun utilisateur connecté</div>
                )}
              </div>
            </div>
          </div>

          {/* État Redux complet */}
          <div>
            <h2 className="text-xl font-semibold text-white mb-4">🔍 État Redux (Debug)</h2>
            <div className="bg-black/30 rounded-lg p-4 overflow-auto max-h-64">
              <pre className="text-green-300 text-xs">
                {JSON.stringify(
                  {
                    alerts: alerts.map((a) => ({ id: a.id, type: a.type, message: a.message })),
                    user: {
                      isAuthenticated,
                      currentUser: currentUser
                        ? {
                            id: currentUser.id,
                            name: `${currentUser.firstName} ${currentUser.lastName}`,
                            email: currentUser.email,
                            role: currentUser.role,
                          }
                        : null,
                    },
                  },
                  null,
                  2
                )}
              </pre>
            </div>
          </div>

          {/* Retour */}
          <div className="mt-8 text-center">
            <button
              onClick={() => (window.location.href = '/auth/login')}
              className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg transition-colors"
            >
              ← Retour à la connexion
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReduxDemo;
