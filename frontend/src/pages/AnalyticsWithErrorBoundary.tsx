import React from 'react';
import ErrorBoundary from '../components/ErrorBoundary';
import Analytics from './Analytics';

/**
 * Analytics page wrapped with Error Boundary for comprehensive error handling
 * This ensures that any unhandled errors in the Analytics component are caught
 * and displayed gracefully instead of crashing the entire application
 */
const AnalyticsWithErrorBoundary: React.FC = () => {
  return (
    <ErrorBoundary>
      <Analytics />
    </ErrorBoundary>
  );
};

export default AnalyticsWithErrorBoundary;
