import { api } from './api';

export interface ActivityLog {
  _id: string;
  username: string;
  activity_type: string;
  activity_description: string;
  category: string;
  category_description: string;
  details: any;
  ip_address: string;
  user_agent: string;
  success: boolean;
  timestamp: string;
  target_user_id?: string;
  resource_id?: string;
  resource_type?: string;
}

export interface ActivityResponse {
  activities: ActivityLog[];
  total_count: number;
  limit: number;
  offset: number;
  has_more: boolean;
}

export interface ActivityFilters {
  category?: string;
  activity_type?: string;
  success_only?: boolean;
  start_date?: string;
  end_date?: string;
  limit?: number;
  offset?: number;
}

export interface ActivityStatistics {
  statistics: Array<{
    _id: string;
    total: number;
    successful: number;
    failed: number;
  }>;
  categories: { [key: string]: string };
  activity_types: { [key: string]: string };
}

export interface ActivitySearchRequest {
  query?: string;
  user_id?: string;
  category?: string;
  activity_type?: string;
  start_date?: string;
  end_date?: string;
  success_only?: boolean;
  limit?: number;
  offset?: number;
}

export interface ActivityExportRequest {
  user_id?: string;
  category?: string;
  start_date?: string;
  end_date?: string;
  format?: 'json' | 'csv';
}

export interface ActivityExportResponse {
  data: ActivityLog[];
  metadata: {
    export_date: string;
    total_records: number;
    filters: any;
  };
}

class ActivityService {
  /**
   * Get activity logs for a specific user
   */
  async getUserActivities(
    userId: string,
    filters: ActivityFilters = {}
  ): Promise<ActivityResponse> {
    const params = new URLSearchParams();

    if (filters.limit) params.append('limit', filters.limit.toString());
    if (filters.offset) params.append('offset', filters.offset.toString());
    if (filters.category) params.append('category', filters.category);
    if (filters.activity_type) params.append('activity_type', filters.activity_type);
    if (filters.success_only !== undefined)
      params.append('success_only', filters.success_only.toString());
    if (filters.start_date) params.append('start_date', filters.start_date);
    if (filters.end_date) params.append('end_date', filters.end_date);

    const response = await api.get(`/admin/activity/users/${userId}/activities?${params}`);
    return response.data;
  }

  /**
   * Get all activity logs across all users (admin only)
   */
  async getAllActivities(
    filters: ActivityFilters & { user_id?: string } = {}
  ): Promise<ActivityResponse> {
    const params = new URLSearchParams();

    if (filters.limit) params.append('limit', filters.limit.toString());
    if (filters.offset) params.append('offset', filters.offset.toString());
    if (filters.category) params.append('category', filters.category);
    if (filters.activity_type) params.append('activity_type', filters.activity_type);
    if (filters.user_id) params.append('user_id', filters.user_id);
    if (filters.start_date) params.append('start_date', filters.start_date);
    if (filters.end_date) params.append('end_date', filters.end_date);

    const response = await api.get(`/admin/activity/activities?${params}`);
    return response.data;
  }

  /**
   * Get activity statistics
   */
  async getActivityStatistics(userId?: string): Promise<ActivityStatistics> {
    const params = new URLSearchParams();
    if (userId) params.append('user_id', userId);

    const response = await api.get(`/admin/activity/statistics?${params}`);
    return response.data;
  }

  /**
   * Get available activity categories and types
   */
  async getActivityCategories(): Promise<{
    categories: { [key: string]: string };
    activity_types: { [key: string]: string };
  }> {
    const response = await api.get('/admin/activity/categories');
    return response.data;
  }

  /**
   * Get recent activities (last 24 hours)
   */
  async getRecentActivities(limit: number = 20, category?: string): Promise<ActivityResponse> {
    const params = new URLSearchParams();
    params.append('limit', limit.toString());
    if (category) params.append('category', category);

    const response = await api.get(`/admin/activity/recent?${params}`);
    return response.data;
  }

  /**
   * Search activities with advanced filters
   */
  async searchActivities(searchRequest: ActivitySearchRequest): Promise<ActivityResponse> {
    const response = await api.post('/admin/activity/search', searchRequest);
    return response.data;
  }

  /**
   * Export activity logs
   */
  async exportActivities(exportRequest: ActivityExportRequest): Promise<ActivityExportResponse> {
    const response = await api.post('/admin/activity/export', exportRequest);
    return response.data;
  }

  /**
   * Download activity logs as a file
   */
  async downloadActivityReport(
    exportRequest: ActivityExportRequest,
    filename?: string
  ): Promise<void> {
    try {
      const data = await this.exportActivities(exportRequest);

      // Create file content based on format
      let content: string;
      let mimeType: string;
      let fileExtension: string;

      if (exportRequest.format === 'csv') {
        // Convert to CSV
        const headers = [
          'Timestamp',
          'Username',
          'Activity',
          'Category',
          'Success',
          'IP Address',
          'Details',
        ];

        const csvRows = [
          headers.join(','),
          ...data.data.map((activity) =>
            [
              `"${activity.timestamp}"`,
              `"${activity.username}"`,
              `"${activity.activity_description}"`,
              `"${activity.category_description}"`,
              activity.success ? 'Success' : 'Failed',
              `"${activity.ip_address || ''}"`,
              `"${JSON.stringify(activity.details).replace(/"/g, '""')}"`,
            ].join(',')
          ),
        ];

        content = csvRows.join('\n');
        mimeType = 'text/csv';
        fileExtension = 'csv';
      } else {
        // JSON format
        content = JSON.stringify(data, null, 2);
        mimeType = 'application/json';
        fileExtension = 'json';
      }

      // Create and download file
      const blob = new Blob([content], { type: mimeType });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download =
        filename || `activity_log_${new Date().toISOString().split('T')[0]}.${fileExtension}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading activity report:', error);
      throw error;
    }
  }

  /**
   * Get activity summary for dashboard
   */
  async getActivitySummary(): Promise<{
    total_activities: number;
    successful_activities: number;
    failed_activities: number;
    categories_summary: { [key: string]: number };
    recent_activities: ActivityLog[];
  }> {
    try {
      const [stats, recent] = await Promise.all([
        this.getActivityStatistics(),
        this.getRecentActivities(10),
      ]);

      const totalActivities = stats.statistics.reduce((sum, stat) => sum + stat.total, 0);
      const successfulActivities = stats.statistics.reduce((sum, stat) => sum + stat.successful, 0);
      const failedActivities = stats.statistics.reduce((sum, stat) => sum + stat.failed, 0);

      const categoriesSummary: { [key: string]: number } = {};
      stats.statistics.forEach((stat) => {
        categoriesSummary[stat._id] = stat.total;
      });

      return {
        total_activities: totalActivities,
        successful_activities: successfulActivities,
        failed_activities: failedActivities,
        categories_summary: categoriesSummary,
        recent_activities: recent.activities,
      };
    } catch (error) {
      console.error('Error getting activity summary:', error);
      throw error;
    }
  }

  /**
   * Format activity for display
   */
  formatActivityForDisplay(activity: ActivityLog): {
    title: string;
    description: string;
    icon: string;
    color: string;
    timestamp: string;
  } {
    const categoryColors: { [key: string]: string } = {
      AUTH: 'blue',
      SCAN: 'purple',
      EXPORT: 'green',
      USER_MGMT: 'orange',
      INCIDENT: 'red',
      CONFIG: 'yellow',
      SECURITY: 'red',
      SYSTEM: 'gray',
    };

    const categoryIcons: { [key: string]: string } = {
      AUTH: 'shield',
      SCAN: 'scan',
      EXPORT: 'download',
      USER_MGMT: 'user',
      INCIDENT: 'alert-triangle',
      CONFIG: 'settings',
      SECURITY: 'shield',
      SYSTEM: 'activity',
    };

    return {
      title: activity.activity_description,
      description: `${activity.username} - ${activity.category_description}`,
      icon: categoryIcons[activity.category] || 'activity',
      color: categoryColors[activity.category] || 'gray',
      timestamp: new Date(activity.timestamp).toLocaleString(),
    };
  }
}

export const activityService = new ActivityService();
