import { getToken } from '../utils/auth';

const API_BASE_URL = 'http://localhost:5000/api/incident';

// Types for incident management
export interface InvestigationTicket {
  id: string;
  ticket_number: string;
  short_description: string;
  description: string;
  user_id: string;
  user_email?: string; // Email de l'utilisateur qui a créé le ticket
  category: string;
  subcategory?: string;
  impact: 'High' | 'Medium' | 'Low';
  urgency: 'High' | 'Medium' | 'Low';
  priority: number;
  assignment_group?: string;
  assigned_to?: string;
  configuration_item?: string;
  contact_type: string;
  location?: string;
  status: 'New' | 'In Progress' | 'On Hold' | 'Resolved' | 'Closed' | 'converted_to_incident';
  created_at: string;
  updated_at: string;
  converted_incident_id?: string;
  timeline?: TimelineEntry[];
  attachments?: Attachment[];
  // Backward compatibility
  title?: string;
  severity?: 'critical' | 'high' | 'medium' | 'low';
}

export interface TimelineEntry {
  timestamp: string;
  action: string;
  user_id: string;
  details?: any;
}

export interface Attachment {
  id: string;
  original_name: string;
  stored_name: string;
  file_path: string;
  file_size: number;
  upload_date: string;
  file_type: string;
}

export interface Incident {
  id: string;
  incident_id: string;
  title: string;
  description: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  created_at: string;
  updated_at: string;
  resolved_at?: string;
  closed_at?: string;
  evidence: any[];
  timeline: any[];
  recommendations: string[];
  tags: string[];
  false_positive: boolean;
  escalated: boolean;
  assigned_to?: string;
  // Creator information
  user_id?: string;
  user_email?: string;
  // Conversion information
  original_ticket_number?: string;
  original_ticket_id?: string;
  original_creator_id?: string;
  original_creator_email?: string;
  attachments?: Attachment[];
  // Extended incident information
  impact_assessment?: string;
  business_impact?: 'critical' | 'high' | 'medium' | 'low' | 'none';
  root_cause?: string;
  mitigation_steps?: string[];
  next_steps?: string[];
  linked_assets?: string[];
  assigned_team?: string;
  references?: {
    type: 'cve' | 'threat_id' | 'external_report' | 'other';
    value: string;
    description?: string;
  }[];
}

interface NotificationSettings {
  user_id: string;
  email_enabled: boolean;
  telegram_enabled: boolean;
  email_address?: string;
  telegram_chat_id?: string;
}

export interface CreateTicketRequest {
  short_description: string;
  description: string;
  user_id: string;
  category: string;
  subcategory?: string;
  impact: 'High' | 'Medium' | 'Low';
  urgency: 'High' | 'Medium' | 'Low';
  assignment_group?: string;
  assigned_to?: string;
  configuration_item?: string;
  contact_type: string;
  location?: string;
  // Backward compatibility
  title?: string;
  severity?: 'critical' | 'high' | 'medium' | 'low';
}

export interface UpdateTicketRequest {
  title?: string;
  description?: string;
  severity?: 'critical' | 'high' | 'medium' | 'low';
  status?: 'open' | 'in_progress' | 'converted_to_incident' | 'closed';
}

export interface AssignTicketRequest {
  assigned_to: string;
}

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = getToken();
  if (!token) {
    throw new Error('No authentication token found');
  }
  return {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`,
  };
};

// Ticket management functions
export const createTicket = async (
  ticketData: CreateTicketRequest
): Promise<InvestigationTicket> => {
  const response = await fetch(`${API_BASE_URL}/tickets`, {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify(ticketData),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to create ticket');
  }

  return response.json();
};

export const getTickets = async (): Promise<InvestigationTicket[]> => {
  const response = await fetch(`${API_BASE_URL}/tickets`, {
    method: 'GET',
    headers: getAuthHeaders(),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to fetch tickets');
  }

  return response.json();
};

export const getTicket = async (ticketNumber: string): Promise<InvestigationTicket> => {
  const response = await fetch(`${API_BASE_URL}/tickets/${ticketNumber}`, {
    method: 'GET',
    headers: getAuthHeaders(),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to fetch ticket');
  }

  return response.json();
};

export const updateTicket = async (
  ticketNumber: string,
  updateData: UpdateTicketRequest
): Promise<InvestigationTicket> => {
  const response = await fetch(`${API_BASE_URL}/tickets/${ticketNumber}`, {
    method: 'PUT',
    headers: getAuthHeaders(),
    body: JSON.stringify(updateData),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to update ticket');
  }

  return response.json();
};

export const assignTicket = async (
  ticketNumber: string,
  assignData: AssignTicketRequest
): Promise<InvestigationTicket> => {
  const response = await fetch(`${API_BASE_URL}/tickets/${ticketNumber}/assign`, {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify(assignData),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to assign ticket');
  }

  return response.json();
};

export const convertTicketToIncident = async (ticketNumber: string): Promise<Incident> => {
  const response = await fetch(`${API_BASE_URL}/tickets/${ticketNumber}/convert`, {
    method: 'POST',
    headers: getAuthHeaders(),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to convert ticket to incident');
  }

  return response.json();
};

export const closeTicket = async (ticketNumber: string): Promise<InvestigationTicket> => {
  const response = await fetch(`${API_BASE_URL}/tickets/${ticketNumber}/close`, {
    method: 'POST',
    headers: getAuthHeaders(),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to close ticket');
  }

  return response.json();
};

export const reopenTicket = async (ticketNumber: string): Promise<InvestigationTicket> => {
  const response = await fetch(`${API_BASE_URL}/tickets/${ticketNumber}/reopen`, {
    method: 'POST',
    headers: getAuthHeaders(),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to reopen ticket');
  }

  return response.json();
};

export const deleteTicket = async (ticketNumber: string): Promise<void> => {
  const response = await fetch(`${API_BASE_URL}/tickets/${ticketNumber}`, {
    method: 'DELETE',
    headers: getAuthHeaders(),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to delete ticket');
  }
};

export const closeIncident = async (incidentId: string): Promise<Incident> => {
  const response = await fetch(`${API_BASE_URL}/incidents/${incidentId}/close`, {
    method: 'POST',
    headers: getAuthHeaders(),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to close incident');
  }

  return response.json();
};

export const reopenIncident = async (incidentId: string): Promise<Incident> => {
  const response = await fetch(`${API_BASE_URL}/incidents/${incidentId}/reopen`, {
    method: 'POST',
    headers: getAuthHeaders(),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to reopen incident');
  }

  return response.json();
};

export const deleteIncident = async (incidentId: string): Promise<void> => {
  const response = await fetch(`${API_BASE_URL}/incidents/${incidentId}`, {
    method: 'DELETE',
    headers: getAuthHeaders(),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to delete incident');
  }
};

// Incident management functions
export const getIncidents = async (): Promise<Incident[]> => {
  const response = await fetch(`${API_BASE_URL}/incidents`, {
    method: 'GET',
    headers: getAuthHeaders(),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to fetch incidents');
  }

  return response.json();
};

export const getIncident = async (incidentId: string): Promise<Incident> => {
  const response = await fetch(`${API_BASE_URL}/incidents/${incidentId}`, {
    method: 'GET',
    headers: getAuthHeaders(),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to fetch incident');
  }

  return response.json();
};

export const updateIncident = async (incidentId: string, updateData: any): Promise<Incident> => {
  const response = await fetch(`${API_BASE_URL}/incidents/${incidentId}`, {
    method: 'PUT',
    headers: getAuthHeaders(),
    body: JSON.stringify(updateData),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to update incident');
  }

  return response.json();
};

// Notification settings functions
export const getNotificationSettings = async (
  userId: string
): Promise<NotificationSettings | null> => {
  const response = await fetch(`${API_BASE_URL}/notification-settings?user_id=${userId}`, {
    method: 'GET',
    headers: getAuthHeaders(),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to fetch notification settings');
  }

  const data = await response.json();
  return Object.keys(data).length > 0 ? data : null;
};

export const updateNotificationSettings = async (
  userId: string,
  settings: Partial<NotificationSettings>
): Promise<NotificationSettings> => {
  const response = await fetch(`${API_BASE_URL}/notification-settings`, {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify({ user_id: userId, ...settings }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to update notification settings');
  }

  return response.json();
};

// Utility functions for enhanced dark theme styling
export const getSeverityColor = (severity: string): string => {
  switch (severity?.toLowerCase()) {
    case 'critical':
      // Rouge foncé pour critical
      return 'text-red-100 bg-gradient-to-r from-red-700 to-red-800 border border-red-600/50 shadow-lg shadow-red-600/25';
    case 'high':
      // Orange foncé pour high
      return 'text-orange-100 bg-gradient-to-r from-orange-600 to-orange-700 border border-orange-500/50 shadow-md shadow-orange-500/20';
    case 'medium':
      // Violet pour medium (différent du statut)
      return 'text-purple-100 bg-gradient-to-r from-purple-600 to-purple-700 border border-purple-500/50 shadow-md shadow-purple-500/20';
    case 'low':
      // Vert foncé pour low
      return 'text-green-100 bg-gradient-to-r from-green-600 to-green-700 border border-green-500/50 shadow-md shadow-green-500/20';
    default:
      return 'text-gray-300 bg-gradient-to-r from-gray-600 to-gray-700 border border-gray-500/50';
  }
};

export const getPriorityColor = (priority: number): string => {
  switch (priority) {
    case 1:
      // Rose/Pink pour P1 (différent de critical severity)
      return 'text-pink-100 bg-gradient-to-r from-pink-600 to-pink-700 border border-pink-500/50 shadow-lg shadow-pink-500/25';
    case 2:
      // Indigo pour P2
      return 'text-indigo-100 bg-gradient-to-r from-indigo-600 to-indigo-700 border border-indigo-500/50 shadow-md shadow-indigo-500/20';
    case 3:
      // Cyan pour P3
      return 'text-cyan-100 bg-gradient-to-r from-cyan-600 to-cyan-700 border border-cyan-500/50 shadow-md shadow-cyan-500/20';
    case 4:
      // Teal pour P4
      return 'text-teal-100 bg-gradient-to-r from-teal-600 to-teal-700 border border-teal-500/50 shadow-md shadow-teal-500/20';
    case 5:
      // Slate pour P5
      return 'text-slate-100 bg-gradient-to-r from-slate-600 to-slate-700 border border-slate-500/50 shadow-md shadow-slate-500/20';
    default:
      return 'text-gray-300 bg-gradient-to-r from-gray-600 to-gray-700 border border-gray-500/50';
  }
};

export const getPriorityLabel = (priority: number): string => {
  switch (priority) {
    case 1:
      return 'P1 - Critical';
    case 2:
      return 'P2 - High';
    case 3:
      return 'P3 - Medium';
    case 4:
      return 'P4 - Low';
    case 5:
      return 'P5 - Planning';
    default:
      return `P${priority}`;
  }
};

export const getStatusColor = (status: string): string => {
  switch (status?.toLowerCase()) {
    case 'new':
      // Bleu clair pour new
      return 'text-sky-100 bg-gradient-to-r from-sky-500 to-sky-600 border border-sky-400/50 shadow-md shadow-sky-400/20';
    case 'open':
      // Bleu pour open
      return 'text-blue-100 bg-gradient-to-r from-blue-500 to-blue-600 border border-blue-400/50 shadow-md shadow-blue-400/20';
    case 'in progress':
    case 'in_progress':
      // Jaune pour in progress
      return 'text-yellow-100 bg-gradient-to-r from-yellow-500 to-yellow-600 border border-yellow-400/50 shadow-md shadow-yellow-400/20';
    case 'on hold':
    case 'on_hold':
      // Amber pour on hold
      return 'text-amber-100 bg-gradient-to-r from-amber-500 to-amber-600 border border-amber-400/50 shadow-md shadow-amber-400/20';
    case 'resolved':
      // Emerald pour resolved
      return 'text-emerald-100 bg-gradient-to-r from-emerald-500 to-emerald-600 border border-emerald-400/50 shadow-md shadow-emerald-400/20';
    case 'closed':
      // Gris pour closed
      return 'text-gray-100 bg-gradient-to-r from-gray-500 to-gray-600 border border-gray-400/50 shadow-md shadow-gray-400/20';
    case 'converted_to_incident':
      // Violet clair pour converted
      return 'text-violet-100 bg-gradient-to-r from-violet-500 to-violet-600 border border-violet-400/50 shadow-md shadow-violet-400/20';
    default:
      return 'text-gray-300 bg-gradient-to-r from-gray-600 to-gray-700 border border-gray-500/50';
  }
};

export const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleString();
};

// Enhanced utility functions for interactive badges
export const getSeverityIcon = (severity: string): string => {
  switch (severity?.toLowerCase()) {
    case 'critical':
      return '🔥';
    case 'high':
      return '⚠️';
    case 'medium':
      return '📋';
    case 'low':
      return '📝';
    default:
      return '❓';
  }
};

export const getStatusIcon = (status: string): string => {
  switch (status?.toLowerCase()) {
    case 'new':
      return '🆕';
    case 'open':
      return '🔓';
    case 'in progress':
    case 'in_progress':
      return '⚡';
    case 'on hold':
    case 'on_hold':
      return '⏸️';
    case 'resolved':
      return '✅';
    case 'closed':
      return '🔒';
    case 'converted_to_incident':
      return '🔄';
    default:
      return '❓';
  }
};

export const getPriorityIcon = (priority: number): string => {
  switch (priority) {
    case 1:
      return '🔴';
    case 2:
      return '🟠';
    case 3:
      return '🟡';
    case 4:
      return '🔵';
    case 5:
      return '🟢';
    default:
      return '⚪';
  }
};

// Function to get hover effects for badges
export const getBadgeHoverEffect = (): string => {
  return 'hover:scale-105 hover:shadow-xl transition-all duration-300 cursor-pointer';
};

// Function to get time-based urgency indicator
export const getTimeUrgencyColor = (createdAt: string): string => {
  const now = new Date();
  const created = new Date(createdAt);
  const hoursDiff = (now.getTime() - created.getTime()) / (1000 * 60 * 60);
  const daysDiff = Math.floor(hoursDiff / 24);

  // Only use red blinking for extremely overdue items (10+ days)
  if (daysDiff > 10) {
    return 'border-l-4 border-red-500 bg-red-500/15 animate-pulse';
  } else if (daysDiff > 7) {
    return 'border-l-4 border-orange-500 bg-orange-500/10';
  } else if (daysDiff > 3) {
    return 'border-l-4 border-yellow-500 bg-yellow-500/10';
  } else if (hoursDiff > 24) {
    return 'border-l-4 border-blue-500 bg-blue-500/10';
  } else {
    return 'border-l-4 border-green-500 bg-green-500/10';
  }
};
