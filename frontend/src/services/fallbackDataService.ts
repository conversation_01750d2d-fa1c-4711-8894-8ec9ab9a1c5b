import { DashboardData, DashboardStats, SystemStatus, AttackSource, RecentIncident, VulnerabilityStats, ThreatStats, ScanActivity } from './dashboardService';

/**
 * Fallback Data Service
 * Provides consistent fallback data when real APIs are unavailable
 */
export class FallbackDataService {
  
  /**
   * Get fallback dashboard statistics
   */
  static getFallbackStats(): DashboardStats {
    return {
      totalScans: 0,
      activeScans: 0,
      criticalThreats: 0,
      securityAlerts: 0,
      securityScore: 85, // Conservative fallback score
      scanTrends: {
        totalChange: 0,
        activeChange: 0,
        threatsChange: 0,
        alertsChange: 0
      }
    };
  }

  /**
   * Get fallback system status (all services in warning state)
   */
  static getFallbackSystemStatus(): SystemStatus {
    return {
      pentesting: 'warning',
      phishing: 'warning',
      malware: 'warning',
      analytics: 'warning',
      incidents: 'warning'
    };
  }

  /**
   * Get fallback attack sources (empty array)
   */
  static getFallbackAttackSources(): AttackSource[] {
    return [];
  }

  /**
   * Get fallback recent incidents (empty array)
   */
  static getFallbackRecentIncidents(): RecentIncident[] {
    return [];
  }

  /**
   * Get fallback vulnerability statistics
   */
  static getFallbackVulnerabilityStats(): VulnerabilityStats {
    return {
      total: 0,
      critical: 0,
      high: 0,
      medium: 0,
      low: 0,
      byTool: {
        openvas: 0,
        nmap: 0,
        nikto: 0,
        sqlmap: 0
      }
    };
  }

  /**
   * Get fallback threat statistics
   */
  static getFallbackThreatStats(): ThreatStats {
    return {
      phishing: {
        total: 0,
        blocked: 0,
        risk_score_avg: 0
      },
      malware: {
        total: 0,
        quarantined: 0,
        threat_score_avg: 0
      }
    };
  }

  /**
   * Get fallback scan activity
   */
  static getFallbackScanActivity(): ScanActivity {
    return {
      today: 0,
      week: 0,
      month: 0,
      byType: {
        network: 0,
        web: 0,
        vulnerability: 0,
        deep: 0
      }
    };
  }

  /**
   * Get complete fallback dashboard data
   */
  static getFallbackDashboardData(): DashboardData {
    return {
      stats: this.getFallbackStats(),
      systemStatus: this.getFallbackSystemStatus(),
      attackSources: this.getFallbackAttackSources(),
      recentIncidents: this.getFallbackRecentIncidents(),
      vulnerabilityStats: this.getFallbackVulnerabilityStats(),
      threatStats: this.getFallbackThreatStats(),
      scanActivity: this.getFallbackScanActivity()
    };
  }

  /**
   * Get minimal emergency dashboard data for critical failures
   */
  static getEmergencyDashboardData(): DashboardData {
    return {
      stats: {
        totalScans: 0,
        activeScans: 0,
        criticalThreats: 0,
        securityAlerts: 0,
        securityScore: 50, // Lower score to indicate system issues
        scanTrends: {
          totalChange: 0,
          activeChange: 0,
          threatsChange: 0,
          alertsChange: 0
        }
      },
      systemStatus: {
        pentesting: 'offline',
        phishing: 'offline',
        malware: 'offline',
        analytics: 'offline',
        incidents: 'offline'
      },
      attackSources: [],
      recentIncidents: [],
      vulnerabilityStats: this.getFallbackVulnerabilityStats(),
      threatStats: this.getFallbackThreatStats(),
      scanActivity: this.getFallbackScanActivity()
    };
  }

  /**
   * Check if dashboard data is fallback data
   */
  static isFallbackData(data: DashboardData): boolean {
    return (
      data.stats.totalScans === 0 &&
      data.stats.activeScans === 0 &&
      data.attackSources.length === 0 &&
      data.recentIncidents.length === 0
    );
  }

  /**
   * Get data quality indicator
   */
  static getDataQuality(data: DashboardData): 'real' | 'partial' | 'fallback' | 'emergency' {
    if (this.isFallbackData(data)) {
      return data.stats.securityScore === 50 ? 'emergency' : 'fallback';
    }
    
    // Check if we have partial data
    const hasVulnStats = data.vulnerabilityStats && data.vulnerabilityStats.total > 0;
    const hasThreatStats = data.threatStats && (data.threatStats.phishing.total > 0 || data.threatStats.malware.total > 0);
    const hasActivityStats = data.scanActivity && data.scanActivity.today > 0;
    
    if (hasVulnStats && hasThreatStats && hasActivityStats) {
      return 'real';
    }
    
    return 'partial';
  }

  /**
   * Merge real data with fallback data for missing fields
   */
  static mergeWithFallback(realData: Partial<DashboardData>): DashboardData {
    const fallbackData = this.getFallbackDashboardData();
    
    return {
      stats: realData.stats || fallbackData.stats,
      systemStatus: realData.systemStatus || fallbackData.systemStatus,
      attackSources: realData.attackSources || fallbackData.attackSources,
      recentIncidents: realData.recentIncidents || fallbackData.recentIncidents,
      vulnerabilityStats: realData.vulnerabilityStats || fallbackData.vulnerabilityStats,
      threatStats: realData.threatStats || fallbackData.threatStats,
      scanActivity: realData.scanActivity || fallbackData.scanActivity
    };
  }
}
