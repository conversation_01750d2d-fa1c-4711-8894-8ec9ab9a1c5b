import { api } from './api';

export interface DashboardStats {
  totalScans: number;
  activeScans: number;
  criticalThreats: number;
  securityAlerts: number;
  securityScore: number;
  scanTrends: {
    totalChange: number;
    activeChange: number;
    threatsChange: number;
    alertsChange: number;
  };
}

export interface SystemStatus {
  pentesting: 'online' | 'warning' | 'offline';
  phishing: 'online' | 'warning' | 'offline';
  malware: 'online' | 'warning' | 'offline';
  analytics: 'online' | 'warning' | 'offline';
  incidents: 'online' | 'warning' | 'offline';
}

export interface AttackSource {
  ip: string;
  country: string;
  attacks: number;
}

export interface RecentIncident {
  id: number;
  title: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  status: string;
  created: string;
  assigned: string;
}

export interface DashboardData {
  stats: DashboardStats;
  systemStatus: SystemStatus;
  attackSources: AttackSource[];
  recentIncidents: RecentIncident[];
}

export const dashboardService = {
  /**
   * Get comprehensive dashboard statistics
   */
  async getDashboardStats(): Promise<DashboardStats> {
    try {
      // Try to get real data from multiple endpoints
      const [scanStats, threatStats, alertStats] = await Promise.allSettled([
        api.get('/scan/pentesting/statistics'),
        api.get('/api/vulnerabilities/statistics'),
        api.get('/admin/activity/statistics')
      ]);

      // Process scan statistics
      let totalScans = 0;
      let activeScans = 0;
      if (scanStats.status === 'fulfilled') {
        totalScans = scanStats.value.data.total_scans || 0;
        activeScans = scanStats.value.data.active_scans || 0;
      }

      // Process threat statistics
      let criticalThreats = 0;
      if (threatStats.status === 'fulfilled') {
        const vulnStats = threatStats.value.data.statistics;
        criticalThreats = vulnStats?.critical_vulnerabilities || 0;
      }

      // Process alert statistics
      let securityAlerts = 0;
      if (alertStats.status === 'fulfilled') {
        securityAlerts = alertStats.value.data.total_activities || 0;
      }

      // Calculate security score based on available data
      const securityScore = Math.max(50, 100 - (criticalThreats * 5) - (securityAlerts * 2));

      return {
        totalScans,
        activeScans,
        criticalThreats,
        securityAlerts,
        securityScore: Math.min(100, securityScore),
        scanTrends: {
          totalChange: Math.floor(Math.random() * 20) - 10, // Mock trend data
          activeChange: Math.floor(Math.random() * 20) - 10,
          threatsChange: Math.floor(Math.random() * 10) - 5,
          alertsChange: Math.floor(Math.random() * 15) - 7
        }
      };
    } catch (error) {
      console.warn('Failed to fetch dashboard stats, using mock data:', error);
      // Return mock data as fallback
      return {
        totalScans: 127,
        activeScans: 42,
        criticalThreats: 8,
        securityAlerts: 18,
        securityScore: 94,
        scanTrends: {
          totalChange: 8,
          activeChange: 5,
          threatsChange: -2,
          alertsChange: 3
        }
      };
    }
  },

  /**
   * Get system status for all services
   */
  async getSystemStatus(): Promise<SystemStatus> {
    try {
      const statusChecks = await Promise.allSettled([
        api.get('/scan/pentesting/'),
        api.get('/api/phishing/'),
        api.get('/api/malware/'),
        api.get('/api/analysis/statistics'),
        api.get('/admin/activity/statistics')
      ]);

      return {
        pentesting: statusChecks[0].status === 'fulfilled' ? 'online' : 'offline',
        phishing: statusChecks[1].status === 'fulfilled' ? 'online' : 'offline',
        malware: statusChecks[2].status === 'fulfilled' ? 'online' : 'offline',
        analytics: statusChecks[3].status === 'fulfilled' ? 'online' : 'warning',
        incidents: statusChecks[4].status === 'fulfilled' ? 'online' : 'warning'
      };
    } catch (error) {
      console.warn('Failed to fetch system status:', error);
      return {
        pentesting: 'online',
        phishing: 'online',
        malware: 'online',
        analytics: 'online',
        incidents: 'warning'
      };
    }
  },

  /**
   * Get recent incidents
   */
  async getRecentIncidents(): Promise<RecentIncident[]> {
    try {
      const response = await api.get('/admin/activity/activities?limit=5&category=security');
      
      // Transform activity data to incident format
      const activities = response.data.activities || [];
      return activities.map((activity: any, index: number) => ({
        id: activity.id || index,
        title: activity.description || 'Security Event',
        severity: activity.success ? 'low' : 'high',
        status: activity.success ? 'resolved' : 'investigating',
        created: new Date(activity.timestamp).toLocaleString(),
        assigned: 'Security Team'
      }));
    } catch (error) {
      console.warn('Failed to fetch recent incidents:', error);
      return [
        { id: 1, title: 'Suspicious Login Attempt', severity: 'high', status: 'investigating', created: '2 hours ago', assigned: 'Security Team' },
        { id: 2, title: 'Malware Detection on Endpoint', severity: 'critical', status: 'resolved', created: '4 hours ago', assigned: 'IT Team' },
        { id: 3, title: 'Phishing Email Detected', severity: 'medium', status: 'monitoring', created: '6 hours ago', assigned: 'SOC Team' }
      ];
    }
  },

  /**
   * Get top attack sources (mock data for now)
   */
  async getAttackSources(): Promise<AttackSource[]> {
    // This would typically come from firewall logs, IDS/IPS systems, etc.
    // For now, return mock data
    return [
      { ip: '*************', country: 'United States', attacks: 127 },
      { ip: '**************', country: 'Russia', attacks: 98 },
      { ip: '************', country: 'China', attacks: 76 },
      { ip: '*************', country: 'Brazil', attacks: 54 },
      { ip: '*************', country: 'India', attacks: 32 }
    ];
  },

  /**
   * Get all dashboard data in one call
   */
  async getDashboardData(): Promise<DashboardData> {
    try {
      const [stats, systemStatus, attackSources, recentIncidents] = await Promise.all([
        this.getDashboardStats(),
        this.getSystemStatus(),
        this.getAttackSources(),
        this.getRecentIncidents()
      ]);

      return {
        stats,
        systemStatus,
        attackSources,
        recentIncidents
      };
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      throw error;
    }
  }
};
