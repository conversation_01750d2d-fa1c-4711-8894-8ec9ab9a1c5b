/**
 * Service API pour la détection de phishing
 *
 * Ce module fournit toutes les fonctions nécessaires pour interagir
 * avec l'API de détection de phishing de PICA.
 */

import { getToken, isTokenExpired, clearAuthData, refreshAccessToken } from '../utils/auth';

const API_BASE_URL = 'http://localhost:5000/api/phishing';

// Types TypeScript
export interface PhishingCheck {
  name: string;
  result: 'Passed' | 'Failed' | 'Warning';
  description: string;
  weight?: number;
  high_risk?: boolean;
}

export interface PhishingResult {
  url: string;
  domain: string;
  risk_score: number;
  likelihood: string;
  is_phishing: boolean;
  checks: PhishingCheck[];
}

export interface PhishingAnalysis {
  _id: string;
  analysis_id: string;
  url: string;
  status: 'running' | 'completed' | 'error';
  created_at: string;
  completed_at?: string;
  result?: PhishingResult;
}

export interface SuspiciousLink {
  _id: string;
  url: string;
  domain: string;
  risk_score: number;
  likelihood: string;
  status: 'active' | 'verified' | 'false_positive';
  detection_count: number;
  first_detected: string;
  last_detected: string;
  failed_checks_count: number;
  warning_checks_count: number;
  notes?: string;
}

export interface PhishingServiceStatus {
  status: string;
  service: string;
  version: string;
  statistics: {
    total_analyses: number;
    user_analyses: number;
    active_analyses: number;
  };
  recent_analyses: PhishingAnalysis[];
  capabilities: string[];
}

// Fonction utilitaire pour obtenir les headers d'authentification
const getAuthHeaders = async () => {
  let token = getToken();

  // Vérifier si le token est expiré
  if (token && isTokenExpired(token)) {
    console.log('🔄 Token expired in phishing service, attempting refresh...');
    const refreshResult = await refreshAccessToken();

    if (refreshResult.success && refreshResult.token) {
      token = refreshResult.token;
      console.log('✅ Token refreshed successfully in phishing service');
    } else {
      console.log('🔒 Token refresh failed in phishing service, clearing auth data');
      clearAuthData();
      window.location.href = '/login';
      throw new Error('Token expired and refresh failed');
    }
  }

  return {
    Authorization: `Bearer ${token}`,
    'Content-Type': 'application/json',
  };
};

// Fonction utilitaire pour gérer les erreurs API
const handleApiError = async (response: Response) => {
  // Vérifier les erreurs d'authentification
  if (response.status === 401 || response.status === 422) {
    console.log('🔒 Authentication error in phishing API, clearing auth data');
    clearAuthData();

    // Éviter les redirections multiples
    if (
      !window.location.pathname.includes('/login') &&
      !window.location.pathname.includes('/auth')
    ) {
      window.location.href = '/login';
    }
    throw new Error('Authentication failed');
  }

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
  }
  return response.json();
};

/**
 * Obtient le statut du service de détection de phishing
 */
export const getPhishingServiceStatus = async (): Promise<PhishingServiceStatus> => {
  const response = await fetch(`${API_BASE_URL}/`, {
    method: 'GET',
    headers: await getAuthHeaders(),
  });

  return handleApiError(response);
};

/**
 * Effectue une vérification rapide d'une URL (synchrone)
 */
export const quickPhishingCheck = async (
  url: string
): Promise<{
  analysis_id: string;
  url: string;
  result: PhishingResult;
}> => {
  const response = await fetch(`${API_BASE_URL}/quick-check`, {
    method: 'POST',
    headers: await getAuthHeaders(),
    body: JSON.stringify({ url }),
  });

  return handleApiError(response);
};

/**
 * Lance une analyse complète d'une URL (asynchrone)
 */
export const startPhishingAnalysis = async (
  url: string
): Promise<{
  analysis_id: string;
  status: string;
  url: string;
}> => {
  const response = await fetch(`${API_BASE_URL}/analyze`, {
    method: 'POST',
    headers: await getAuthHeaders(),
    body: JSON.stringify({ url }),
  });

  return handleApiError(response);
};

/**
 * Obtient le statut d'une analyse en cours
 */
export const getAnalysisStatus = async (
  analysisId: string
): Promise<{
  analysis_id: string;
  status: string;
  progress: number;
  url: string;
  result?: PhishingResult;
  error?: string;
}> => {
  const response = await fetch(`${API_BASE_URL}/status/${analysisId}`, {
    method: 'GET',
    headers: await getAuthHeaders(),
  });

  return handleApiError(response);
};

/**
 * Obtient l'historique des analyses de l'utilisateur
 */
export const getAnalysisHistory = async (
  page: number = 1,
  limit: number = 20
): Promise<{
  analyses: PhishingAnalysis[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}> => {
  const response = await fetch(`${API_BASE_URL}/history?page=${page}&limit=${limit}`, {
    method: 'GET',
    headers: await getAuthHeaders(),
  });

  return handleApiError(response);
};

/**
 * Obtient les analyses actives de l'utilisateur
 */
export const getActiveAnalyses = async (): Promise<{
  active_analyses: Array<{
    analysis_id: string;
    url: string;
    status: string;
    progress: number;
    started_at: string;
  }>;
  count: number;
}> => {
  const response = await fetch(`${API_BASE_URL}/active-analyses`, {
    method: 'GET',
    headers: await getAuthHeaders(),
  });

  return handleApiError(response);
};

/**
 * Obtient la liste des liens suspects
 */
export const getSuspiciousLinks = async (
  page: number = 1,
  limit: number = 20,
  status?: string,
  minScore: number = 30
): Promise<{
  suspicious_links: SuspiciousLink[];
  statistics: {
    total_suspicious: number;
    active: number;
    verified: number;
    false_positive: number;
    high_risk: number;
    medium_risk: number;
  };
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    min_score: minScore.toString(),
  });

  if (status) {
    params.append('status', status);
  }

  const response = await fetch(`${API_BASE_URL}/suspicious-links?${params}`, {
    method: 'GET',
    headers: await getAuthHeaders(),
  });

  return handleApiError(response);
};

/**
 * Obtient les détails d'un lien suspect
 */
export const getSuspiciousLinkDetails = async (
  linkId: string
): Promise<{
  suspicious_link: SuspiciousLink;
  related_analyses: PhishingAnalysis[];
}> => {
  const response = await fetch(`${API_BASE_URL}/suspicious-links/${linkId}`, {
    method: 'GET',
    headers: await getAuthHeaders(),
  });

  return handleApiError(response);
};

/**
 * Vérifie un lien suspect (admin seulement)
 */
export const verifySuspiciousLink = async (
  linkId: string,
  status: 'verified' | 'false_positive',
  notes: string = ''
): Promise<{
  message: string;
  link_id: string;
  status: string;
  verified_by: string;
}> => {
  const response = await fetch(`${API_BASE_URL}/suspicious-links/${linkId}/verify`, {
    method: 'POST',
    headers: await getAuthHeaders(),
    body: JSON.stringify({ status, notes }),
  });

  return handleApiError(response);
};

/**
 * Supprime un lien suspect (admin seulement)
 */
export const deleteSuspiciousLink = async (
  linkId: string
): Promise<{
  message: string;
  link_id: string;
}> => {
  const response = await fetch(`${API_BASE_URL}/suspicious-links/${linkId}`, {
    method: 'DELETE',
    headers: await getAuthHeaders(),
  });

  return handleApiError(response);
};

/**
 * Obtient les statistiques détaillées des liens suspects (admin seulement)
 */
export const getSuspiciousLinksStats = async (): Promise<{
  overview: {
    total_suspicious: number;
    active_links: number;
    verified_phishing: number;
    false_positives: number;
    verification_rate: number;
  };
  risk_distribution: {
    high_risk: number;
    medium_risk: number;
    low_risk: number;
  };
  top_domains: Array<{
    _id: string;
    count: number;
    avg_score: number;
  }>;
  most_detected_links: Array<{
    _id: string;
    url: string;
    detection_count: number;
    risk_score: number;
    status: string;
  }>;
}> => {
  const response = await fetch(`${API_BASE_URL}/suspicious-links/stats`, {
    method: 'GET',
    headers: await getAuthHeaders(),
  });

  return handleApiError(response);
};

/**
 * Exporte les liens suspects (admin seulement)
 */
export const exportSuspiciousLinks = async (
  status?: string,
  minScore: number = 30
): Promise<{
  export_data: Array<any>;
  metadata: {
    export_date: string;
    total_links: number;
    filters: {
      status?: string;
      min_score: number;
    };
  };
}> => {
  const params = new URLSearchParams({
    min_score: minScore.toString(),
  });

  if (status) {
    params.append('status', status);
  }

  const response = await fetch(`${API_BASE_URL}/suspicious-links/export?${params}`, {
    method: 'GET',
    headers: await getAuthHeaders(),
  });

  return handleApiError(response);
};
