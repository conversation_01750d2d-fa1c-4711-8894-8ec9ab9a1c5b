import { api } from './api';

export interface CreateUserData {
  first_name: string;
  last_name: string;
  date_of_birth: string;
  username: string;
  gender: string;
  email: string;
  password: string;
  role?: 'user' | 'admin';
}

export interface UpdateUserData {
  first_name?: string;
  last_name?: string;
  date_of_birth?: string;
  username?: string;
  gender?: string;
  email?: string;
  password?: string;
  role?: 'user' | 'admin';
}

interface GetUsersParams {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
  status?: string;
}

interface PaginatedUsersResponse {
  users: any[];
  total: number;
  totalPages: number;
  currentPage: number;
  limit: number;
}

interface UserStatsResponse {
  total: number;
  active: number;
  banned: number;
  unverified: number;
  admins: number;
  regularUsers: number;
}

export const userService = {
  // Get all users with optional pagination and filtering
  getAll: (params?: GetUsersParams): Promise<{ data: PaginatedUsersResponse }> => {
    if (params) {
      const searchParams = new URLSearchParams();
      if (params.page) searchParams.append('page', params.page.toString());
      if (params.limit) searchParams.append('limit', params.limit.toString());
      if (params.search) searchParams.append('search', params.search);
      if (params.role && params.role !== 'all') searchParams.append('role', params.role);
      if (params.status && params.status !== 'all') searchParams.append('status', params.status);

      return api.get(`/admin/users?${searchParams.toString()}`);
    }
    return api.get('/admin/users');
  },

  // Get user statistics
  getStats: (): Promise<{ data: UserStatsResponse }> => {
    return api.get('/admin/users/stats');
  },

  // Get user by ID
  getById: (id: string) => api.get(`/admin/users/${id}`),

  // Create new user
  create: (userData: CreateUserData) => api.post('/admin/users', userData),

  // Update user
  update: (id: string, userData: UpdateUserData) => api.put(`/admin/users/${id}`, userData),

  // Delete user
  delete: (id: string) => api.delete(`/admin/users/${id}`),

  // Ban user
  ban: (id: string) => api.put(`/admin/users/${id}/ban`),

  // Unban user
  unban: (id: string) => api.put(`/admin/users/${id}/unban`),

  // Legacy method for role change (keeping for compatibility)
  changeRole: (id: string, role: string) => api.put(`/admin/users/${id}`, { role }),

  // Session management
  getUserSessions: (id: string, limit?: number, activeOnly?: boolean) => {
    const params = new URLSearchParams();
    if (limit) params.append('limit', limit.toString());
    if (activeOnly) params.append('active_only', 'true');
    return api.get(`/admin/users/${id}/sessions?${params.toString()}`);
  },

  getUserSessionStats: (id: string) => api.get(`/admin/users/${id}/sessions/stats`),

  endAllUserSessions: (id: string) => api.post(`/admin/users/${id}/sessions/end-all`),

  endSpecificSession: (sessionId: string) => api.post(`/admin/sessions/${sessionId}/end`),
};
