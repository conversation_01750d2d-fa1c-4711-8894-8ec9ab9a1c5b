import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { activeConfig } from '../config/api';

/**
 * Service API dédié pour les appels directs au serveur d'analytics externe
 * Utilise directement l'URL du serveur IA sans passer par le backend
 */
class ExternalAnalyticsApi {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: activeConfig.ANALYTICS_BASE_URL,
      timeout: 10000, // Timeout plus court pour éviter les attentes longues
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Intercepteur pour les requêtes
    this.api.interceptors.request.use(
      (config) => {
        console.log(`🚀 Appel API Analytics: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('❌ Erreur dans la requête Analytics:', error);
        return Promise.reject(error);
      }
    );

    // Intercepteur pour les réponses
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log(`✅ Réponse API Analytics: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error('❌ Erreur dans la réponse Analytics:', error);

        // Gestion spécifique des erreurs CORS
        if (error.code === 'ERR_NETWORK') {
          console.error(
            '🚫 Erreur réseau - Vérifiez que le serveur Analytics est accessible et que CORS est configuré'
          );
        }

        return Promise.reject(error);
      }
    );
  }

  /**
   * Appel POST avec FormData (pour les uploads de fichiers)
   */
  async postFormData(
    endpoint: string,
    formData: FormData,
    showProgress: boolean = false
  ): Promise<any> {
    const config: any = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: activeConfig.TIMEOUTS.ANALYSIS, // Timeout long pour les analyses
    };

    // Ajouter un callback de progression si demandé
    if (showProgress) {
      config.onUploadProgress = (progressEvent: any) => {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        console.log(`📤 Upload progress: ${percentCompleted}%`);
      };
    }

    const response = await this.api.post(endpoint, formData, config);
    return response.data;
  }

  /**
   * Appel GET standard
   */
  async get(endpoint: string): Promise<any> {
    const response = await this.api.get(endpoint);
    return response.data;
  }

  /**
   * Appel POST standard
   */
  async post(endpoint: string, data?: any): Promise<any> {
    const response = await this.api.post(endpoint, data);
    return response.data;
  }

  /**
   * Appel DELETE
   */
  async delete(endpoint: string): Promise<any> {
    const response = await this.api.delete(endpoint);
    return response.data;
  }

  /**
   * Test de connectivité avec le serveur Analytics
   */
  async testConnection(
    skipCorsTest: boolean = false
  ): Promise<{ success: boolean; message: string; latency?: number }> {
    // Si on veut éviter les erreurs CORS, on retourne juste la configuration
    if (skipCorsTest) {
      return {
        success: true,
        message: `Configuré pour ${this.api.defaults.baseURL} (test CORS ignoré)`,
        latency: 0,
      };
    }
    try {
      const startTime = Date.now();

      // Essayer avec un timeout plus court pour éviter les attentes longues
      await this.api.get('/analyses', { timeout: 3000 });

      const latency = Date.now() - startTime;

      return {
        success: true,
        message: 'Connexion au serveur Analytics réussie',
        latency,
      };
    } catch (error: any) {
      let message = 'Impossible de se connecter au serveur Analytics';

      if (error.code === 'ERR_NETWORK') {
        message =
          'Erreur CORS - Le serveur Analytics doit autoriser les requêtes depuis localhost:5173';
      } else if (error.code === 'ECONNABORTED') {
        message = 'Timeout - Le serveur Analytics ne répond pas dans les temps';
      } else if (error.response?.status === 404) {
        message = 'Endpoint /analyses non trouvé sur le serveur Analytics';
      } else if (error.response?.status === 401) {
        message = 'Non autorisé - Authentification requise';
      } else if (error.response?.status) {
        message = `Erreur serveur Analytics: ${error.response.status}`;
      }

      return {
        success: false,
        message,
      };
    }
  }

  /**
   * Obtenir l'URL de base configurée
   */
  getBaseUrl(): string {
    return activeConfig.ANALYTICS_BASE_URL;
  }

  /**
   * Obtenir les informations de configuration
   */
  getConfig() {
    return {
      baseURL: this.api.defaults.baseURL,
      timeout: this.api.defaults.timeout,
      headers: this.api.defaults.headers,
    };
  }
}

// Export d'une instance singleton
export const externalAnalyticsApi = new ExternalAnalyticsApi();

// Export de la classe pour les tests
export { ExternalAnalyticsApi };
