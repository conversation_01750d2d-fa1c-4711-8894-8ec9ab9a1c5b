import React, { createContext, useContext, ReactNode } from 'react';
import { useModal } from '../hooks/useModal';
import AlertModal from '../components/modals/AlertModal';
import ConfirmModal from '../components/modals/ConfirmModal';

interface AlertOptions {
  title?: string;
  message: string;
  type?: 'error' | 'success' | 'info' | 'warning';
  buttonText?: string;
}

interface ConfirmOptions {
  title?: string;
  message: string;
  type?: 'warning' | 'danger' | 'info' | 'success';
  confirmText?: string;
  cancelText?: string;
  confirmButtonClass?: string;
}

interface ModalContextType {
  showAlert: (options: AlertOptions) => void;
  showConfirm: (options: ConfirmOptions) => Promise<boolean>;
}

const ModalContext = createContext<ModalContextType | undefined>(undefined);

interface ModalProviderProps {
  children: ReactNode;
}

export function ModalProvider({ children }: ModalProviderProps) {
  const { alertModal, confirmModal, showAlert, showConfirm } = useModal();

  // Set up global functions for convenience
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).__platformModal = {
        showAlert,
        showConfirm,
      };
    }

    return () => {
      if (typeof window !== 'undefined') {
        delete (window as any).__platformModal;
      }
    };
  }, [showAlert, showConfirm]);

  const contextValue: ModalContextType = {
    showAlert,
    showConfirm,
  };

  return (
    <ModalContext.Provider value={contextValue}>
      {children}

      {/* Alert Modal */}
      <AlertModal
        isOpen={alertModal.isOpen}
        onClose={alertModal.onClose}
        title={alertModal.title}
        message={alertModal.message}
        type={alertModal.type}
        buttonText={alertModal.buttonText}
      />

      {/* Confirm Modal */}
      <ConfirmModal
        isOpen={confirmModal.isOpen}
        onClose={confirmModal.onClose}
        onConfirm={confirmModal.onConfirm}
        title={confirmModal.title}
        message={confirmModal.message}
        type={confirmModal.type}
        confirmText={confirmModal.confirmText}
        cancelText={confirmModal.cancelText}
        confirmButtonClass={confirmModal.confirmButtonClass}
      />
    </ModalContext.Provider>
  );
}

export function useModalContext(): ModalContextType {
  const context = useContext(ModalContext);
  if (context === undefined) {
    throw new Error('useModalContext must be used within a ModalProvider');
  }
  return context;
}

// Convenience functions that can be used anywhere in the app
export const platformAlert = (message: string, options?: Omit<AlertOptions, 'message'>) => {
  // This will be set up by the ModalProvider
  if (typeof window !== 'undefined' && (window as any).__platformModal) {
    (window as any).__platformModal.showAlert({ message, ...options });
  } else {
    // Fallback to browser alert if modal system not available
    alert(message);
  }
};

export const platformConfirm = (
  message: string,
  options?: Omit<ConfirmOptions, 'message'>
): Promise<boolean> => {
  // This will be set up by the ModalProvider
  if (typeof window !== 'undefined' && (window as any).__platformModal) {
    return (window as any).__platformModal.showConfirm({ message, ...options });
  } else {
    // Fallback to browser confirm if modal system not available
    return Promise.resolve(confirm(message));
  }
};
