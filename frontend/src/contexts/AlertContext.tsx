import { createContext, useState, ReactNode, useEffect } from 'react';

type AlertType = 'info' | 'success' | 'warning' | 'error';

interface AlertMessage {
  id: string;
  type: AlertType;
  message: string;
  autoClose?: boolean;
}

interface AlertContextType {
  alerts: AlertMessage[];
  addAlert: (type: AlertType, message: string, autoClose?: boolean) => void;
  removeAlert: (id: string) => void;
}

export const AlertContext = createContext<AlertContextType>({
  alerts: [],
  addAlert: () => {},
  removeAlert: () => {},
});

interface AlertProviderProps {
  children: ReactNode;
}

export const AlertProvider = ({ children }: AlertProviderProps) => {
  const [alerts, setAlerts] = useState<AlertMessage[]>([]);

  const addAlert = (type: AlertType, message: string, autoClose = true) => {
    const id = Date.now().toString();
    setAlerts((prev) => [...prev, { id, type, message, autoClose }]);
  };

  const removeAlert = (id: string) => {
    setAlerts((prev) => prev.filter((alert) => alert.id !== id));
  };

  useEffect(() => {
    const timers: number[] = [];
    alerts.forEach((alert) => {
      if (alert.autoClose) {
        const timer = setTimeout(() => removeAlert(alert.id), 5000);
        timers.push(timer);
      }
    });
    return () => timers.forEach(clearTimeout);
  }, [alerts]);

  return (
    <AlertContext.Provider value={{ alerts, addAlert, removeAlert }}>
      {children}
    </AlertContext.Provider>
  );
};
