import React, { createContext, useContext, useState, ReactNode, useCallback } from 'react';
import { Check<PERSON><PERSON>cle, AlertTriangle, AlertCircle, Info, X } from 'lucide-react';

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

interface NotificationContextType {
  notifications: Notification[];
  showNotification: (notification: Omit<Notification, 'id'>) => string;
  hideNotification: (id: string) => void;
  clearAllNotifications: () => void;
  // Convenience methods
  showSuccess: (message: string, title?: string, duration?: number) => string;
  showError: (message: string, title?: string, duration?: number) => string;
  showWarning: (message: string, title?: string, duration?: number) => string;
  showInfo: (message: string, title?: string, duration?: number) => string;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  const showNotification = useCallback((notification: Omit<Notification, 'id'>): string => {
    const id = Math.random().toString(36).substr(2, 9);
    const newNotification: Notification = {
      ...notification,
      id,
      duration: notification.duration ?? 5000,
    };

    setNotifications(prev => [...prev, newNotification]);

    // Auto-dismiss after duration
    if (newNotification.duration > 0) {
      setTimeout(() => {
        hideNotification(id);
      }, newNotification.duration);
    }

    return id;
  }, []);

  const hideNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  }, []);

  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  // Convenience methods
  const showSuccess = useCallback((message: string, title?: string, duration?: number) => {
    return showNotification({
      type: 'success',
      title: title || 'Success',
      message,
      duration: duration || 4000 // Shorter duration for success
    });
  }, [showNotification]);

  const showError = useCallback((message: string, title?: string, duration?: number) => {
    return showNotification({
      type: 'error',
      title: title || 'Error',
      message,
      duration: duration || 6000 // Errors stay a bit longer
    });
  }, [showNotification]);

  const showWarning = useCallback((message: string, title?: string, duration?: number) => {
    return showNotification({
      type: 'warning',
      title: title || 'Warning',
      message,
      duration: duration || 5000
    });
  }, [showNotification]);

  const showInfo = useCallback((message: string, title?: string, duration?: number) => {
    return showNotification({
      type: 'info',
      title: title || 'Information',
      message,
      duration: duration || 4000 // Shorter duration for info
    });
  }, [showNotification]);

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-400" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-400" />;
      case 'info':
      default:
        return <Info className="w-4 h-4 text-blue-400" />;
    }
  };

  const getNotificationStyles = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return 'bg-green-900/90 border-green-500/50 text-green-100';
      case 'error':
        return 'bg-red-900/90 border-red-500/50 text-red-100';
      case 'warning':
        return 'bg-yellow-900/90 border-yellow-500/50 text-yellow-100';
      case 'info':
      default:
        return 'bg-blue-900/90 border-blue-500/50 text-blue-100';
    }
  };

  const contextValue: NotificationContextType = {
    notifications,
    showNotification,
    hideNotification,
    clearAllNotifications,
    showSuccess,
    showError,
    showWarning,
    showInfo,
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
      
      {/* Notification Container */}
      <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
        {notifications.map((notification) => (
          <div
            key={notification.id}
            className={`p-3 rounded-lg shadow-lg backdrop-blur-lg border transition-all duration-300 transform animate-in slide-in-from-right ${getNotificationStyles(notification.type)}`}
          >
            <div className="flex items-start space-x-2">
              {/* Icon */}
              <div className="flex-shrink-0 mt-0.5">
                {getNotificationIcon(notification.type)}
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                {notification.title && (
                  <h4 className="font-medium text-xs mb-1">
                    {notification.title}
                  </h4>
                )}
                <p className="text-xs leading-relaxed">
                  {notification.message}
                </p>
                
                {/* Action button if provided */}
                {notification.action && (
                  <button
                    onClick={notification.action.onClick}
                    className="mt-2 text-xs font-semibold underline hover:no-underline transition-all duration-200"
                  >
                    {notification.action.label}
                  </button>
                )}
              </div>
              
              {/* Close button */}
              <button
                onClick={() => hideNotification(notification.id)}
                className="flex-shrink-0 text-gray-300 hover:text-white hover:bg-white/20 transition-all duration-200 p-1.5 rounded-lg ml-2"
                title="Close notification"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
            
            {/* Progress bar for timed notifications */}
            {notification.duration && notification.duration > 0 && (
              <div className="mt-3 h-1 bg-white/20 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-white/60 animate-pulse"
                  style={{
                    animation: `shrink ${notification.duration}ms linear forwards`
                  }}
                />
              </div>
            )}
          </div>
        ))}
      </div>
      
      <style jsx>{`
        @keyframes shrink {
          from { width: 100%; }
          to { width: 0%; }
        }
        
        @keyframes slide-in-from-right {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
        
        .animate-in {
          animation: slide-in-from-right 0.3s ease-out;
        }
      `}</style>
    </NotificationContext.Provider>
  );
};
