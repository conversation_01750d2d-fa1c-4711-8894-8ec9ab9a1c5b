// Test simple pour vérifier que la page Analytics se charge
console.log('🧪 Test Analytics - Vérification des imports...');

try {
  // Test des imports
  console.log('✅ Import de la configuration API...');
  import('./config/api.js')
    .then((apiConfig) => {
      console.log('✅ Configuration API chargée:', apiConfig.activeConfig);
    })
    .catch((err) => {
      console.error('❌ Erreur lors du chargement de la configuration API:', err);
    });

  console.log('✅ Import du service Analytics...');
  import('./services/analyticsService.js')
    .then((analyticsService) => {
      console.log('✅ Service Analytics chargé:', Object.keys(analyticsService));
    })
    .catch((err) => {
      console.error('❌ Erreur lors du chargement du service Analytics:', err);
    });

  console.log('✅ Import de la page Analytics...');
  import('./pages/Analytics.js')
    .then((analytics) => {
      console.log('✅ Page Analytics chargée');
    })
    .catch((err) => {
      console.error('❌ Erreur lors du chargement de la page Analytics:', err);
    });

  console.log('✅ Import des composants Analytics...');
  import('./components/analytics/VulnerabilityCard.js')
    .then((card) => {
      console.log('✅ VulnerabilityCard chargé');
    })
    .catch((err) => {
      console.error('❌ Erreur lors du chargement de VulnerabilityCard:', err);
    });

  import('./components/analytics/VulnerabilityStats.js')
    .then((stats) => {
      console.log('✅ VulnerabilityStats chargé');
    })
    .catch((err) => {
      console.error('❌ Erreur lors du chargement de VulnerabilityStats:', err);
    });
} catch (error) {
  console.error('❌ Erreur générale lors du test:', error);
}

console.log('🧪 Test Analytics terminé');
