import { useState, ReactNode } from 'react';
import { Shield } from 'lucide-react';
import Header from '../components/navigation/Header';
import Sidebar from '../components/navigation/Sidebar';
import ReduxAlertDisplay from '../components/alerts/ReduxAlertDisplay';
import { NavigationProvider } from '../contexts/NavigationContext';

interface MainLayoutProps {
  children: ReactNode;
}

export default function MainLayout({ children }: MainLayoutProps) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const closeSidebar = () => {
    setIsSidebarOpen(false);
  };

  return (
    <NavigationProvider>
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900 relative overflow-hidden">
        {/* Alertes Redux globales */}
        <ReduxAlertDisplay />
        {/* Background decorative elements - same as login/register pages */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 opacity-20">
            <Shield className="w-16 h-16 text-purple-300" />
          </div>
          <div className="absolute top-40 right-32 opacity-15">
            <Shield className="w-12 h-12 text-purple-400" />
          </div>
          <div className="absolute bottom-32 left-16 opacity-10">
            <Shield className="w-20 h-20 text-purple-200" />
          </div>
          <div className="absolute bottom-20 right-20 opacity-25">
            <Shield className="w-14 h-14 text-purple-300" />
          </div>
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-purple-500/20 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-indigo-500/15 rounded-full blur-3xl"></div>
        </div>

        <div className="relative z-10 flex h-screen">
          {/* Sidebar */}
          <Sidebar isOpen={isSidebarOpen} onClose={closeSidebar} />

          {/* Main content area */}
          <div className="flex-1 flex flex-col lg:ml-0">
            {/* Header */}
            <Header onMenuToggle={toggleSidebar} isSidebarOpen={isSidebarOpen} />

            {/* Main content */}
            <main className="flex-1 overflow-y-auto p-6">{children}</main>
          </div>
        </div>
      </div>
    </NavigationProvider>
  );
}
