interface User {
  _id: string;
  first_name: string;
  last_name: string;
  username: string;
  email: string;
  role: 'admin' | 'user';
  active: boolean;
  banned: boolean;
  email_verified: boolean;
  created_at: string;
  date_of_birth?: string;
  gender?: string;
  _optimisticOperation?: number;
}

/**
 * Utility functions for user status and role management
 * Centralizes user status logic for consistency and reusability
 */
export const userStatusUtils = {
  /**
   * Get the appropriate color class for a user role
   */
  getRoleColor: (role: string): string => {
    const colors: Record<string, string> = {
      admin: 'bg-purple-600 text-white',
      user: 'bg-blue-600 text-white',
      moderator: 'bg-green-600 text-white',
      default: 'bg-gray-600 text-white'
    };
    return colors[role] || colors.default;
  },

  /**
   * Get human-readable status text for a user
   */
  getStatusText: (user: User): string => {
    if (user.banned) return 'Banned';
    if (!user.email_verified) return 'Unverified';
    if (user.active) return 'Active';
    return 'Inactive';
  },

  /**
   * Get the appropriate color class for user status
   */
  getStatusColor: (user: User): string => {
    if (user.banned) return 'text-red-400';
    if (!user.email_verified) return 'text-yellow-400';
    if (user.active) return 'text-green-400';
    return 'text-gray-400';
  },

  /**
   * Check if a user is considered active (active, not banned, and email verified)
   */
  isActiveUser: (user: User): boolean => {
    return user.active && !user.banned && user.email_verified;
  },

  /**
   * Get user creation date in a readable format
   */
  getUserCreationDate: (user: User): string => {
    try {
      return new Date(user.created_at).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return 'Unknown';
    }
  },

  /**
   * Filter users based on search term
   */
  matchesSearch: (user: User, searchTerm: string): boolean => {
    if (!searchTerm) return true;
    
    const term = searchTerm.toLowerCase();
    return (
      user.first_name.toLowerCase().includes(term) ||
      user.last_name.toLowerCase().includes(term) ||
      user.username.toLowerCase().includes(term) ||
      user.email.toLowerCase().includes(term)
    );
  },

  /**
   * Filter users based on role
   */
  matchesRole: (user: User, roleFilter: string): boolean => {
    return roleFilter === 'all' || user.role === roleFilter;
  },

  /**
   * Filter users based on status
   */
  matchesStatus: (user: User, statusFilter: string): boolean => {
    switch (statusFilter) {
      case 'all':
        return true;
      case 'active':
        return userStatusUtils.isActiveUser(user);
      case 'banned':
        return user.banned;
      case 'unverified':
        return !user.email_verified;
      case 'inactive':
        return !user.active;
      default:
        return true;
    }
  },

  /**
   * Get comprehensive user statistics
   */
  getUserStats: (users: User[]) => {
    return {
      total: users.length,
      active: users.filter(userStatusUtils.isActiveUser).length,
      banned: users.filter(u => u.banned).length,
      unverified: users.filter(u => !u.email_verified).length,
      admins: users.filter(u => u.role === 'admin').length,
      regularUsers: users.filter(u => u.role === 'user').length
    };
  }
};
