import { AnalysisReport, VulnerabilityInfo } from '../services/analyticsService';

/**
 * Utility functions for exporting analysis reports to Markdown format
 */

/**
 * Converts a vulnerability severity to an emoji and color indicator
 */
const getSeverityIndicator = (severity: string): string => {
  switch (severity.toUpperCase()) {
    case 'CRITICAL':
      return '🔴 **CRITICAL**';
    case 'HIGH':
      return '🟠 **HIGH**';
    case 'MEDIUM':
      return '🟡 **MEDIUM**';
    case 'LOW':
      return '🟢 **LOW**';
    default:
      return '⚪ **UNKNOWN**';
  }
};

/**
 * Formats a vulnerability as a Markdown section
 */
const formatVulnerabilityMarkdown = (vuln: VulnerabilityInfo, index: number): string => {
  const basicInfo = vuln.basic_info || {};
  const aiAnalysis = vuln.ai_analysis?.vulnerability_analysis || {};
  const impact = aiAnalysis.impact_assessment || {};
  const exploitation = aiAnalysis.exploitation_details || {};
  const remediation = aiAnalysis.remediation || {};
  const responsibility = aiAnalysis.responsibility_assignment || {};
  const prevention = aiAnalysis.prevention_strategy || {};

  return `
## ${index + 1}. ${basicInfo.name || 'Unknown Vulnerability'}

### 📋 Basic Information
- **Target:** ${basicInfo.target || 'N/A'}
- **Severity:** ${getSeverityIndicator(basicInfo.severity || 'unknown')}
- **CVE:** ${basicInfo.cve || 'N/A'}
- **CVSS Score:** ${basicInfo.cvss_score || 'N/A'}
- **Port:** ${basicInfo.port || 'N/A'}
- **Protocol:** ${basicInfo.protocol || 'N/A'}
- **Service:** ${basicInfo.service || 'N/A'}
- **Version:** ${basicInfo.version || 'N/A'}

### 📝 Description
${basicInfo.description || 'No description available.'}

### 🤖 AI Analysis Summary
${aiAnalysis.summary || 'No AI analysis available.'}

### 🔍 Technical Description
${aiAnalysis.technical_description || 'No technical description available.'}

### 💥 Impact Assessment
- **Confidentiality:** ${impact.confidentiality || 'N/A'}
- **Integrity:** ${impact.integrity || 'N/A'}
- **Availability:** ${impact.availability || 'N/A'}
- **Business Impact:** ${impact.business_impact || 'N/A'}

### ⚔️ Exploitation Details
- **Attack Vector:** ${exploitation.attack_vector || 'N/A'}
- **Prerequisites:** ${exploitation.prerequisites || 'N/A'}
- **Difficulty:** ${exploitation.difficulty || 'N/A'}
- **Likelihood:** ${exploitation.likelihood || 'N/A'}

### 🛠️ Remediation
${remediation.immediate_actions?.length ? `
**Immediate Actions:**
${remediation.immediate_actions.map(action => `- ${action}`).join('\n')}
` : ''}

${remediation.long_term_solutions?.length ? `
**Long-term Solutions:**
${remediation.long_term_solutions.map(solution => `- ${solution}`).join('\n')}
` : ''}

${remediation.workarounds?.length ? `
**Workarounds:**
${remediation.workarounds.map(workaround => `- ${workaround}`).join('\n')}
` : ''}

- **Priority:** ${remediation.priority || 'N/A'}
- **Estimated Effort:** ${remediation.estimated_effort || 'N/A'}

### 👥 Responsibility Assignment
- **Primary Responsible:** ${responsibility.primary_responsible || 'N/A'}
- **Secondary Responsible:** ${responsibility.secondary_responsible || 'N/A'}
- **Escalation Contact:** ${responsibility.escalation_contact || 'N/A'}

${responsibility.required_skills?.length ? `
**Required Skills:**
${responsibility.required_skills.map(skill => `- ${skill}`).join('\n')}
` : ''}

### 🛡️ Prevention Strategy
- **Root Cause:** ${prevention.root_cause || 'N/A'}

${prevention.prevention_measures?.length ? `
**Prevention Measures:**
${prevention.prevention_measures.map(measure => `- ${measure}`).join('\n')}
` : ''}

${prevention.monitoring_requirements?.length ? `
**Monitoring Requirements:**
${prevention.monitoring_requirements.map(req => `- ${req}`).join('\n')}
` : ''}

### 📚 References
${aiAnalysis.references?.length ? 
  aiAnalysis.references.map(ref => `- ${ref}`).join('\n') : 
  'No references available.'
}

### ⚖️ Compliance Impact
${aiAnalysis.compliance_impact || 'No compliance impact information available.'}

---
`;
};

/**
 * Exports an analysis report to Markdown format
 */
export const exportReportToMarkdown = (report: AnalysisReport, vulnerabilities: VulnerabilityInfo[]): string => {
  const metadata = report.report?.vulnerability_assessment_report?.metadata;
  const currentDate = new Date().toLocaleString();
  
  // Calculate vulnerability statistics
  const stats = {
    total: vulnerabilities.length,
    critical: vulnerabilities.filter(v => v.basic_info?.severity?.toUpperCase() === 'CRITICAL').length,
    high: vulnerabilities.filter(v => v.basic_info?.severity?.toUpperCase() === 'HIGH').length,
    medium: vulnerabilities.filter(v => v.basic_info?.severity?.toUpperCase() === 'MEDIUM').length,
    low: vulnerabilities.filter(v => v.basic_info?.severity?.toUpperCase() === 'LOW').length,
  };

  const markdownContent = `# 🛡️ Vulnerability Analysis Report

## 📊 Report Information
- **Analysis ID:** ${report.analysis_id}
- **Status:** ${report.status}
- **Generated At:** ${report.generated_at || currentDate}
- **Export Date:** ${currentDate}
- **Report Type:** ${report.full_report ? 'Full Analysis' : 'Basic Analysis'}

${report.file_info ? `
## 📁 File Information
- **Filename:** ${report.file_info.filename}
- **File Size:** ${(report.file_info.size / 1024).toFixed(2)} KB
` : ''}

${report.scan_ids?.length ? `
## 🔍 Scan IDs
${report.scan_ids.map(id => `- ${id}`).join('\n')}
` : ''}

## 📈 Vulnerability Statistics
- **Total Vulnerabilities:** ${stats.total}
- **🔴 Critical:** ${stats.critical}
- **🟠 High:** ${stats.high}
- **🟡 Medium:** ${stats.medium}
- **🟢 Low:** ${stats.low}

${metadata ? `
## 🗂️ Metadata
- **Total Scans:** ${metadata.total_scans || 'N/A'}
- **Total Vulnerabilities:** ${metadata.total_vulnerabilities || 'N/A'}
- **Report Generated:** ${metadata.report_generated || 'N/A'}
` : ''}

## 🚨 Vulnerability Details

${vulnerabilities.length > 0 ? 
  vulnerabilities.map((vuln, index) => formatVulnerabilityMarkdown(vuln, index)).join('\n') :
  'No vulnerabilities found in this analysis.'
}

## 📋 Summary

This report contains ${stats.total} vulnerabilities across different severity levels. 
${stats.critical > 0 ? `⚠️ **${stats.critical} critical vulnerabilities require immediate attention.**` : ''}
${stats.high > 0 ? `🔸 **${stats.high} high-severity vulnerabilities should be addressed promptly.**` : ''}

---
*Report generated by PICA Analytics Platform*
*Export Date: ${currentDate}*
`;

  return markdownContent;
};

/**
 * Downloads a markdown report as a .md file
 */
export const downloadMarkdownReport = (report: AnalysisReport, vulnerabilities: VulnerabilityInfo[]): void => {
  const markdownContent = exportReportToMarkdown(report, vulnerabilities);
  const blob = new Blob([markdownContent], { type: 'text/markdown;charset=utf-8' });
  const url = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.href = url;
  link.download = `vulnerability_report_${report.analysis_id}_${new Date().toISOString().split('T')[0]}.md`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  URL.revokeObjectURL(url);
};

/**
 * Exports a single vulnerability to Markdown format
 */
export const exportSingleVulnerabilityToMarkdown = (vulnerability: VulnerabilityInfo): string => {
  const currentDate = new Date().toLocaleString();
  const basicInfo = vulnerability.basic_info || {};

  const markdownContent = `# 🛡️ Vulnerability Report

## 📊 Export Information
- **Export Date:** ${currentDate}
- **Vulnerability ID:** ${basicInfo.scan_id || 'N/A'}
- **Target:** ${basicInfo.target || 'N/A'}

${formatVulnerabilityMarkdown(vulnerability, 0)}

---
*Report generated by PICA Analytics Platform*
*Export Date: ${currentDate}*
`;

  return markdownContent;
};

/**
 * Downloads a single vulnerability as a .md file
 */
export const downloadSingleVulnerabilityMarkdown = (vulnerability: VulnerabilityInfo): void => {
  const markdownContent = exportSingleVulnerabilityToMarkdown(vulnerability);
  const blob = new Blob([markdownContent], { type: 'text/markdown;charset=utf-8' });
  const url = URL.createObjectURL(blob);

  const basicInfo = vulnerability.basic_info || {};
  const fileName = `vulnerability_${basicInfo.scan_id || 'unknown'}_${basicInfo.name?.replace(/[^a-zA-Z0-9]/g, '_') || 'vulnerability'}_${new Date().toISOString().split('T')[0]}.md`;

  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  URL.revokeObjectURL(url);
};

export default {
  exportReportToMarkdown,
  downloadMarkdownReport,
  exportSingleVulnerabilityToMarkdown,
  downloadSingleVulnerabilityMarkdown,
  getSeverityIndicator,
  formatVulnerabilityMarkdown
};
