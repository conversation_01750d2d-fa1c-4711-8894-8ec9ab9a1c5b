# Configuration des APIs pour Vite
# URL de l'API principale (backend Flask)
VITE_API_URL=http://localhost:5000

# URL de l'API Analytics avec IA Ollama
# Remplacez cette URL par l'adresse de votre serveur Ollama
VITE_ANALYTICS_API_URL=http://localhost:5000

# Autres configurations
VITE_ENV=development

# Configuration pour la production
# VITE_API_URL=https://your-production-api.com
# VITE_ANALYTICS_API_URL=https://your-ollama-server.com
