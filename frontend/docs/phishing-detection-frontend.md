# Frontend - Détection de Phishing PICA

## Vue d'ensemble

Le frontend de détection de phishing fournit une interface utilisateur moderne et intuitive pour analyser les URLs suspectes et gérer les liens de phishing détectés.

## Architecture

### Structure des fichiers

```
frontend/src/
├── pages/
│   └── PhishingDetection.tsx     # Page principale
├── services/
│   └── phishingService.ts        # Service API
└── router/
    └── router.tsx                # Configuration des routes
```

### Technologies utilisées

- **React 18** avec TypeScript
- **Tailwind CSS** pour le styling
- **Lucide React** pour les icônes
- **React Router** pour la navigation
- **js-cookie** pour la gestion des tokens

## Fonctionnalités

### 1. Analyse d'URLs

#### Interface d'analyse
- **Champ de saisie** pour l'URL à analyser
- **Bouton d'analyse** avec état de chargement
- **Validation** de l'URL en temps réel

#### Résultats d'analyse
- **Score de risque** avec indicateur visuel (0-100%)
- **Niveau de risque** (Low/Medium/High Risk)
- **Verdict** (Sûre/Phishing détecté)
- **Détails des vérifications** avec statuts (Passed/Failed/Warning)

### 2. Historique des analyses

#### Liste des analyses
- **Pagination** des résultats
- **Tri** par date (plus récent en premier)
- **Filtrage** par statut et score de risque
- **Actualisation** en temps réel

#### Informations affichées
- URL analysée avec lien externe
- Score et niveau de risque
- Date et heure d'analyse
- Nombre de vérifications échouées

### 3. Liens suspects

#### Gestion des liens
- **Liste** des liens avec score > 30%
- **Statuts** : Actif, Vérifié, Faux positif
- **Statistiques** de détection
- **Historique** des détections multiples

#### Informations détaillées
- Domaine et URL complète
- Nombre de détections
- Première et dernière détection
- Nombre d'échecs et d'avertissements

## Interface utilisateur

### Design System

#### Couleurs
- **Arrière-plan** : Dégradé violet/bleu/indigo
- **Conteneurs** : Glassmorphism (transparence + flou)
- **Texte** : Blanc avec variations d'opacité
- **Accents** : Violet pour les éléments interactifs

#### Indicateurs de risque
- **Vert** : Risque faible (< 40%)
- **Orange** : Risque moyen (40-69%)
- **Rouge** : Risque élevé (≥ 70%)

#### Icônes
- **Shield** : Protection/Sécurité
- **Search** : Analyse/Recherche
- **AlertTriangle** : Avertissement/Danger
- **CheckCircle** : Validation/Succès
- **Clock** : Historique/Temps

### Navigation

#### Onglets principaux
1. **Analyser** : Interface d'analyse d'URLs
2. **Historique** : Liste des analyses passées
3. **Liens Suspects** : Gestion des liens détectés

#### États interactifs
- **Hover** : Changement de couleur et opacité
- **Active** : Surbrillance et ombre
- **Disabled** : Grisé et curseur interdit
- **Loading** : Animation de rotation

## Service API (phishingService.ts)

### Fonctions principales

```typescript
// Analyse rapide d'URL
quickPhishingCheck(url: string): Promise<PhishingResult>

// Historique des analyses
getAnalysisHistory(page: number, limit: number): Promise<AnalysisHistory>

// Liens suspects
getSuspiciousLinks(page: number, limit: number): Promise<SuspiciousLinks>

// Statut du service
getPhishingServiceStatus(): Promise<ServiceStatus>
```

### Gestion des erreurs

```typescript
const handleApiError = async (response: Response) => {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}`);
  }
  return response.json();
};
```

### Authentification

```typescript
const getAuthHeaders = () => {
  const token = Cookies.get('access_token');
  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };
};
```

## Types TypeScript

### Interfaces principales

```typescript
interface PhishingResult {
  url: string;
  domain: string;
  risk_score: number;
  likelihood: string;
  is_phishing: boolean;
  checks: PhishingCheck[];
}

interface PhishingCheck {
  name: string;
  result: 'Passed' | 'Failed' | 'Warning';
  description: string;
  weight?: number;
  high_risk?: boolean;
}

interface SuspiciousLink {
  _id: string;
  url: string;
  domain: string;
  risk_score: number;
  status: 'active' | 'verified' | 'false_positive';
  detection_count: number;
  // ... autres propriétés
}
```

## États et gestion

### États React

```typescript
const [url, setUrl] = useState('');
const [isAnalyzing, setIsAnalyzing] = useState(false);
const [currentAnalysis, setCurrentAnalysis] = useState<PhishingAnalysis | null>(null);
const [analysisHistory, setAnalysisHistory] = useState<PhishingAnalysis[]>([]);
const [suspiciousLinks, setSuspiciousLinks] = useState<SuspiciousLink[]>([]);
const [activeTab, setActiveTab] = useState<'analyze' | 'history' | 'suspicious'>('analyze');
```

### Cycle de vie

```typescript
// Chargement initial
useEffect(() => {
  loadAnalysisHistory();
  loadSuspiciousLinks();
}, []);

// Actualisation après analyse
const analyzeUrl = async () => {
  // ... analyse
  loadAnalysisHistory();
  loadSuspiciousLinks();
};
```

## Responsive Design

### Breakpoints
- **Mobile** : < 768px
- **Tablet** : 768px - 1024px
- **Desktop** : > 1024px

### Adaptations
- **Grid** : 1 colonne sur mobile, 2 sur desktop
- **Navigation** : Onglets empilés sur mobile
- **Texte** : Tailles adaptatives
- **Espacement** : Marges réduites sur mobile

## Performance

### Optimisations
- **Lazy loading** des composants
- **Memoization** des calculs coûteux
- **Debouncing** des requêtes API
- **Pagination** des listes longues

### Gestion de cache
- **Cookies** pour l'authentification
- **State local** pour les données temporaires
- **Actualisation** manuelle des données

## Sécurité

### Authentification
- **Token JWT** dans les cookies
- **Vérification** automatique d'expiration
- **Redirection** vers login si non authentifié

### Validation
- **URLs** : Format et protocole
- **Inputs** : Sanitisation des entrées
- **Outputs** : Échappement des données affichées

## Tests

### Tests unitaires
```bash
npm test
```

### Tests d'intégration
```bash
npm run test:integration
```

### Tests E2E
```bash
npm run test:e2e
```

## Déploiement

### Build de production
```bash
npm run build
```

### Variables d'environnement
```env
VITE_API_BASE_URL=http://localhost:5000
VITE_APP_NAME=PICA
```

### Serveur de développement
```bash
npm run dev
```

## Maintenance

### Logs
- **Console** : Erreurs et avertissements
- **Network** : Requêtes API échouées
- **Performance** : Temps de chargement

### Monitoring
- **Erreurs** : Capture automatique
- **Performance** : Métriques de rendu
- **Usage** : Statistiques d'utilisation

## Évolutions futures

### Fonctionnalités prévues
- **Notifications** en temps réel
- **Rapports** PDF/Excel
- **Graphiques** de tendances
- **API** de partage

### Améliorations techniques
- **PWA** : Application progressive
- **Offline** : Mode hors ligne
- **WebSockets** : Temps réel
- **Caching** : Stratégie avancée
