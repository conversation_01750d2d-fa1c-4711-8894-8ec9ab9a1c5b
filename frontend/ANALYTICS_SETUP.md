# Configuration Analytics avec IA Ollama

## Configuration de l'URL du serveur Ollama

### 1. Fichier de configuration

Créez un fichier `.env` dans le dossier `frontend/` en copiant `.env.example` :

```bash
cp .env.example .env
```

### 2. Modifier l'URL du serveur Ollama

Éditez le fichier `.env` et modifiez la ligne suivante avec l'URL de votre serveur Ollama :

```env
# URL de votre serveur IA Analytics externe
VITE_ANALYTICS_API_URL=http://*************:4001
```

**Configuration actuelle :**
- Serveur IA externe : `http://*************:4001`
- <PERSON><PERSON><PERSON> directs depuis le frontend (pas de proxy backend)

**Autres exemples :**
- Serveur local : `http://localhost:11434`
- Serveur distant : `http://*************:11434`
- Serveur avec domaine : `https://ollama.votre-domaine.com`

### 3. Endpoints Analytics

Les endpoints suivants appellent directement votre serveur IA externe :

- **POST** `http://*************:4001/analyze` - Lance une analyse (complète ou basique)
- **GET** `http://*************:4001/status/{analysis_id}` - Vérifie le statut d'une analyse
- **GET** `http://*************:4001/report/{analysis_id}` - Récupère le rapport final
- **GET** `http://*************:4001/analyses` - Liste toutes les analyses

**Note importante :** Les appels sont faits directement depuis le frontend vers le serveur externe, sans passer par le backend Flask.

### 4. Format des données

Le système attend des données au format du fichier `vulnerability_assessment_report.json` avec :

```json
{
  "vulnerability_assessment_report": {
    "metadata": {
      "total_scans": 3,
      "total_vulnerabilities": 5,
      "report_generated": "2025-01-09"
    },
    "scan_reports": {
      "scan_id": {
        "scan_metadata": { ... },
        "vulnerabilities": [
          {
            "basic_info": { ... },
            "ai_analysis": { ... }
          }
        ]
      }
    }
  }
}
```

### 5. Navigation

La page Analytics est accessible via :
- **Sidebar** : Cliquez sur "Analytics" dans la barre latérale
- **Dashboard** : Bouton "📊 Analytics" dans les actions rapides
- **URL directe** : `/analytics`

### 6. Fonctionnalités

#### Interface de lancement d'analyses
- Upload de fichiers CSV
- Choix entre rapport complet (avec IA) et rapport basique
- Configuration des scan IDs pour analyses basiques

#### Affichage des résultats
- **Cards visuelles** pour chaque vulnérabilité
- **Statistiques détaillées** avec graphiques
- **Filtres** par sévérité et recherche textuelle
- **Modes d'affichage** : grille ou liste

#### Actions disponibles
- Voir les détails complets d'une vulnérabilité
- Exporter les rapports (JSON)
- Relancer les analyses échouées
- Supprimer les analyses

### 7. Dépannage

#### Problème de navigation
Si le clic sur "Analytics" dans la sidebar ne fonctionne pas :

1. Vérifiez que le serveur de développement est démarré
2. Ouvrez la console du navigateur pour voir les erreurs
3. Essayez d'accéder directement à `/analytics`

#### Problème d'API
Si les appels API échouent :

1. Vérifiez l'URL dans le fichier `.env`
2. Assurez-vous que votre serveur Ollama est démarré
3. Vérifiez les CORS si le serveur est sur un autre domaine
4. Consultez la console réseau du navigateur

#### Configuration CORS
Si vous avez des erreurs CORS, ajoutez ces headers à votre serveur Ollama :

```
Access-Control-Allow-Origin: http://localhost:3000
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization
```

### 8. Variables d'environnement complètes

```env
# API principale
VITE_API_URL=http://localhost:5000

# API Analytics (Ollama)
VITE_ANALYTICS_API_URL=http://localhost:11434

# Environnement
VITE_ENV=development
```

### 9. Redémarrage

Après modification du fichier `.env`, redémarrez le serveur de développement :

```bash
npm start
# ou
yarn start
```

La page Analytics devrait maintenant être accessible et fonctionnelle avec votre serveur Ollama !
