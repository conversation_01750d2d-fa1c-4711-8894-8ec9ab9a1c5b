# PICA Security Dashboard

## Overview

The PICA Security Dashboard provides a comprehensive real-time view of your cybersecurity platform's status, including security metrics, system health, recent incidents, and quick access to all security tools.

## Features

### 🔍 Real-time Security Monitoring
- **Security Score**: Overall security posture based on threat detection and system health
- **Total Scans**: Comprehensive count of all security scans performed
- **Active Scans**: Currently running security assessments
- **Critical Threats**: High-priority security issues requiring immediate attention
- **Security Alerts**: Unread notifications and warnings

### 📊 Visual Analytics
- **Incident Trends**: 24-hour trend analysis of security events
- **Attack Type Distribution**: Pie chart showing malware, phishing, network, and other attack types
- **Global Threat Map**: Geographic visualization of security threats
- **Trend Indicators**: Up/down arrows showing percentage changes in key metrics

### 🖥️ System Status Monitoring
Real-time status indicators for all PICA services:
- **Pentesting**: Network, web, vulnerability, and deep scan services
- **Phishing Detection**: URL analysis and brand impersonation detection
- **Malware Detection**: File analysis and threat identification
- **Analytics**: AI-powered security analysis
- **Incidents**: Security event management

### 🚀 Quick Actions
One-click access to all major PICA features:
- **Pentesting**: Launch automated security scans
- **Phishing Detection**: Analyze suspicious URLs
- **Malware Detection**: Upload and analyze files
- **Analytics**: View AI-powered security reports
- **Incidents**: Manage security events
- **Settings**: Configure platform preferences

### 🌐 Attack Intelligence
- **Top Attack Sources**: List of most active attacking IP addresses with country information
- **Attack Statistics**: Number of attacks per source
- **Geographic Distribution**: Country-based attack analysis

### 📋 Recent Incidents
- **Incident List**: Latest security events and their status
- **Severity Levels**: Critical, high, medium, and low priority classification
- **Assignment Tracking**: Which team is handling each incident
- **Status Updates**: Investigation, monitoring, resolved states

## Technical Implementation

### Data Sources
The dashboard aggregates data from multiple PICA services:

1. **Scan Statistics**: `/scan/pentesting/statistics`
2. **Vulnerability Data**: `/api/vulnerabilities/statistics`
3. **Activity Logs**: `/admin/activity/statistics`
4. **System Health**: Service-specific health check endpoints

### Real-time Updates
- **Auto-refresh**: Dashboard data refreshes every 5 minutes
- **Manual Refresh**: Click the refresh button for immediate updates
- **Live Timestamps**: Shows last update time
- **Error Handling**: Graceful fallback to cached data if services are unavailable

### Responsive Design
- **Mobile-friendly**: Optimized for tablets and mobile devices
- **Grid Layout**: Adaptive grid system that adjusts to screen size
- **Touch-friendly**: Large buttons and touch targets for mobile users

## User Interface

### Design Principles
- **AI-themed Aesthetics**: Sophisticated gradients and glow effects
- **Consistent Styling**: Matches the overall PICA platform design
- **Visual Hierarchy**: Clear information organization with proper spacing
- **Color Coding**: Intuitive color schemes for different severity levels

### Color Scheme
- **Green**: Positive metrics, online status, low severity
- **Yellow**: Warnings, medium severity, pending states
- **Orange**: Alerts, high severity, attention required
- **Red**: Critical issues, offline status, immediate action needed
- **Purple**: Platform branding, navigation elements
- **Gray**: Background, secondary information

### Interactive Elements
- **Hover Effects**: Cards lift and glow on hover
- **Loading States**: Animated spinners during data fetching
- **Error States**: Clear error messages with retry options
- **Search Functionality**: Global search across dashboard elements

## Configuration

### Environment Variables
The dashboard respects the following environment variables:
- `VITE_API_URL`: Backend API URL (default: http://localhost:5000)
- `VITE_ANALYTICS_API_URL`: Analytics API URL (default: http://*************:4001)

### Customization
- **Refresh Intervals**: Modify in `Dashboard.tsx` useEffect hooks
- **Mock Data**: Fallback data defined in `dashboardService.ts`
- **Color Themes**: CSS classes in component styling
- **Layout**: Grid configurations in component JSX

## API Integration

### Dashboard Service
The `dashboardService.ts` provides:
- **getDashboardStats()**: Aggregated security metrics
- **getSystemStatus()**: Service health checks
- **getRecentIncidents()**: Latest security events
- **getAttackSources()**: Geographic attack data
- **getDashboardData()**: Combined data fetch

### Error Handling
- **Graceful Degradation**: Shows cached data if APIs fail
- **Retry Logic**: Automatic retry for failed requests
- **User Feedback**: Clear error messages and retry buttons
- **Fallback Data**: Mock data ensures dashboard always displays content

## Performance

### Optimization Features
- **Lazy Loading**: Components load as needed
- **Data Caching**: Reduces API calls with intelligent caching
- **Efficient Updates**: Only re-renders when data changes
- **Minimal Bundle**: Optimized imports and tree shaking

### Loading States
- **Initial Load**: Full-screen loading spinner
- **Refresh**: Button spinner during manual refresh
- **Error Recovery**: Retry mechanisms for failed requests

## Security

### Authentication
- **JWT Integration**: Uses platform authentication system
- **Role-based Access**: Respects user permissions
- **Session Management**: Handles token expiration gracefully
- **Secure API Calls**: All requests include proper authentication headers

### Data Privacy
- **User Data**: Only displays data accessible to current user
- **Admin Features**: Additional data for admin users
- **Audit Trail**: All dashboard actions are logged

## Troubleshooting

### Common Issues
1. **Loading Errors**: Check network connectivity and API endpoints
2. **Missing Data**: Verify user permissions and service availability
3. **Slow Performance**: Check API response times and network latency
4. **Display Issues**: Verify browser compatibility and clear cache

### Debug Mode
Enable debug logging by opening browser console to see:
- API request/response details
- Service status checks
- Error messages and stack traces
- Performance metrics

## Future Enhancements

### Planned Features
- **Real-time WebSocket Updates**: Live data streaming
- **Custom Dashboards**: User-configurable layouts
- **Advanced Filtering**: Search and filter capabilities
- **Export Functionality**: PDF and CSV export options
- **Mobile App**: Native mobile application
- **Dark/Light Themes**: User preference themes

### Integration Opportunities
- **SIEM Integration**: Connect with external security tools
- **Notification Systems**: Email and SMS alerts
- **Reporting**: Automated security reports
- **API Extensions**: Additional data sources and metrics
