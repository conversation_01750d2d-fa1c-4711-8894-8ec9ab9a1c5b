#!/usr/bin/env python3
"""
Test script to verify vulnerability scanning tools are available
"""

import subprocess
import sys
import os

def test_tool_availability(tool_name, command, expected_output=None):
    """Test if a tool is available and working"""
    print(f"\n🔍 Testing {tool_name}...")
    
    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0 or (expected_output and expected_output in result.stdout):
            print(f"✅ {tool_name} is available")
            if result.stdout:
                print(f"   Output: {result.stdout.strip()[:100]}...")
            return True
        else:
            print(f"❌ {tool_name} failed with return code {result.returncode}")
            if result.stderr:
                print(f"   Error: {result.stderr.strip()[:100]}...")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {tool_name} command timed out")
        return False
    except FileNotFoundError:
        print(f"❌ {tool_name} not found in PATH")
        return False
    except Exception as e:
        print(f"❌ {tool_name} test failed: {e}")
        return False

def test_openvas_docker():
    """Test OpenVAS Docker container"""
    print(f"\n🐳 Testing OpenVAS Docker container...")
    
    try:
        # Check if Docker is available
        result = subprocess.run(['docker', '--version'], capture_output=True, text=True, timeout=5)
        if result.returncode != 0:
            print("❌ Docker not available")
            return False
        
        # Check for Greenbone containers
        result = subprocess.run(
            ['docker', 'ps', '--filter', 'name=greenbone', '--format', '{{.Names}}'],
            capture_output=True, text=True, timeout=10
        )
        
        if 'greenbone' in result.stdout:
            print("✅ Greenbone containers are running")
            print(f"   Containers: {result.stdout.strip()}")
            return True
        else:
            print("❌ No Greenbone containers found")
            print("💡 Start with: docker-compose up -d (in greenbone-community-edition directory)")
            return False
            
    except Exception as e:
        print(f"❌ Docker test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing Vulnerability Scanning Tools")
    print("=" * 50)
    
    tools_status = {}
    
    # Test basic tools
    tools_to_test = [
        ("Nmap", ["nmap", "--version"]),
        ("Nuclei", ["nuclei", "-version"]),
        ("Metasploit", ["msfconsole", "--version"]),
        ("Python requests", [sys.executable, "-c", "import requests; print('requests available')"]),
    ]
    
    for tool_name, command in tools_to_test:
        tools_status[tool_name] = test_tool_availability(tool_name, command)
    
    # Test OpenVAS Docker
    tools_status["OpenVAS Docker"] = test_openvas_docker()
    
    # Test network connectivity
    print(f"\n🌐 Testing network connectivity...")
    try:
        import requests
        response = requests.get("https://httpbin.org/ip", timeout=5)
        if response.status_code == 200:
            print("✅ Internet connectivity available")
            tools_status["Internet"] = True
        else:
            print("❌ Internet connectivity issues")
            tools_status["Internet"] = False
    except Exception as e:
        print(f"❌ Network test failed: {e}")
        tools_status["Internet"] = False
    
    # Summary
    print(f"\n📊 Summary:")
    print("=" * 30)
    
    available_count = sum(1 for status in tools_status.values() if status)
    total_count = len(tools_status)
    
    for tool, status in tools_status.items():
        status_icon = "✅" if status else "❌"
        print(f"{status_icon} {tool}")
    
    print(f"\n🎯 {available_count}/{total_count} tools available")
    
    if available_count >= total_count - 1:  # Allow one tool to be missing
        print("🟢 Vulnerability scanning should work!")
        return 0
    else:
        print("🟡 Some tools are missing - vulnerability scanning may be limited")
        return 1

if __name__ == "__main__":
    sys.exit(main())
