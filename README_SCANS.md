# PICA - Automated Cybersecurity Platform
## Complete Pentesting Scans Guide

### 🚀 Overview

PICA offers 4 main categories of automated pentesting scans, each using specialized tools for different types of security assessments.

---

## 🟦 1. NETWORK SCAN - Network Analysis

**Tools used:** Nmap + OpenVAS + Metasploit (in parallel)

### Available scan types:

#### 🔹 Basic
- **Description:** Common ports + known vulnerabilities
- **Nmap:** `-sS --script=vuln --script=smb-vuln*`
- **OpenVAS:** "Full and fast" profile
- **Metasploit:** `auxiliary/scanner/portscan/tcp`
- **Intensity:** Low
- **Estimated duration:** 5-10 minutes

#### 🔹 Aggressive
- **Description:** Complete fingerprinting + OS detection + CVE
- **Nmap:** `-A --script=vuln,safe`
- **OpenVAS:** "Full and very deep" profile
- **Metasploit:** SMB, SSH, HTTP modules
- **Intensity:** High
- **Estimated duration:** 15-30 minutes

#### 🔹 Stealth
- **Description:** Slow and discreet scan (no OS detection)
- **Nmap:** `-T1 -sS`
- **OpenVAS:** "Discovery" profile
- **Metasploit:** Basic TCP scan
- **Intensity:** Minimal
- **Estimated duration:** 20-45 minutes

#### 🔹 Comprehensive
- **Description:** Ports + vulnerabilities + extended scans
- **Nmap:** `-p- -A --script=vuln,safe,discovery`
- **OpenVAS:** "Full and very deep ultimate" profile
- **Metasploit:** All scan modules
- **Intensity:** Maximum
- **Estimated duration:** 45-90 minutes

---

## 🟩 2. WEB SCAN - Web Analysis

**Tools used:** Nikto + SQLMap + Dirb + GoBuster + ZAP (in parallel)

### Available scan types:

#### 🔹 Basic
- **Description:** Known vulnerabilities check + simple directories
- **Nikto:** Standard web vulnerability scan
- **SQLMap:** `--batch --level=1` (basic SQL injections)
- **Dirb:** Common wordlist
- **GoBuster:** Fast directory enumeration
- **ZAP:** Basic spider
- **Intensity:** Low

#### 🔹 Aggressive
- **Description:** Deep testing + advanced injections
- **Nikto:** `-Tuning 9` (all tests)
- **SQLMap:** `--level=5 --risk=3` (thorough testing)
- **Dirb:** Large wordlist
- **GoBuster:** Extended scan with extensions
- **ZAP:** Complete active scan
- **Intensity:** High

#### 🔹 Stealth
- **Description:** Slow, discreet, low-intrusive scan
- **Nikto:** `-T 1` (limited tests)
- **SQLMap:** `--level=1 --risk=1` (minimal)
- **Dirb:** Small wordlist with delays
- **GoBuster:** Reduced threads
- **ZAP:** Passive scan only
- **Intensity:** Minimal

#### 🔹 Comprehensive
- **Description:** All tools + active ZAP scan
- **Nikto:** `-Tuning x` (all available tests)
- **SQLMap:** `--level=3 --risk=2` (balanced)
- **Dirb:** Multiple wordlists
- **GoBuster:** Complete scan with all extensions
- **ZAP:** Spider + active scan + manual tests
- **Intensity:** Maximum

---

## 🟪 3. VULNERABILITY SCAN - Vulnerability Analysis

**Tools used:** OpenVAS + Metasploit (in parallel)

### Available scan types:

#### 🔹 Basic
- **Description :** Vulnérabilités courantes, configuration par défaut
- **OpenVAS :** Profil "Full and fast"
- **Metasploit :** `auxiliary/scanner/smb/smb_version`
- **Focus :** CVE connus, configurations par défaut
- **Intensité :** Faible

#### 🔹 Aggressive
- **Description :** Vulnérabilités critiques + exploits détectés
- **OpenVAS :** Profil "Full and very deep"
- **Metasploit :** Modules SMB, SSH, HTTP
- **Focus :** Exploits disponibles, vulnérabilités critiques
- **Intensité :** Élevée

#### 🔹 Stealth
- **Description :** Vulnérabilités minimales sans perturbation
- **OpenVAS :** Profil "Discovery"
- **Metasploit :** Scan TCP uniquement
- **Focus :** Détection passive
- **Intensité :** Minimale

#### 🔹 Comprehensive
- **Description :** Tout détecté + prévalidation exploit
- **OpenVAS :** Profil "Full and very deep ultimate"
- **Metasploit :** Tous les modules de scan
- **Focus :** Analyse complète + validation d'exploits
- **Intensité :** Maximum

---

## 🟥 4. DEEP SCAN - Analyse Complète

**Outils utilisés :** TOUS les outils (séquentiel avec progression)

### Séquence d'exécution :

1. **Phase Network :** Nmap → OpenVAS → Metasploit
2. **Phase Web :** Nikto → SQLMap → Dirb → GoBuster → ZAP
3. **Phase Vulnerability :** OpenVAS approfondi → Metasploit étendu
4. **Phase Analysis :** Corrélation des résultats

### Types de scans disponibles :

#### 🔹 Basic
- **Description :** Aperçu rapide et peu intrusif de tous les outils
- **Durée estimée :** 30-60 minutes
- **Outils :** Tous en mode basique

#### 🔹 Aggressive
- **Description :** Tests poussés sur TOUS les outils disponibles
- **Durée estimée :** 2-4 heures
- **Outils :** Tous en mode agressif

#### 🔹 Stealth
- **Description :** Séquence discrète avec tous les outils
- **Durée estimée :** 4-8 heures
- **Outils :** Tous en mode furtif

#### 🔹 Comprehensive
- **Description :** Audit total avec progression affichée
- **Durée estimée :** 6-12 heures
- **Outils :** Tous en mode maximum

---

## 📊 Résultats et Rapports

### Types de résultats générés :

- **Ports ouverts** avec services identifiés
- **Vulnérabilités** classées par sévérité (Critical, High, Medium, Low)
- **Répertoires/fichiers** découverts
- **Injections SQL** détectées
- **Configurations** non sécurisées
- **Exploits** disponibles

### Format des rapports :

- **JSON** pour intégration API
- **Interface web** pour visualisation
- **Logs détaillés** pour analyse technique
- **Résumé exécutif** pour management

---

## 🛠️ Outils Intégrés

| Outil | Version | Fonction | Statut |
|-------|---------|----------|--------|
| **Nmap** | 7.94+ | Scan de ports et services | ✅ Intégré |
| **OpenVAS** | 23.x | Scanner de vulnérabilités | ✅ Intégré |
| **Metasploit** | 6.x | Framework d'exploitation | 🔄 En cours |
| **Nikto** | 2.5+ | Scanner web | ✅ Intégré |
| **SQLMap** | 1.7+ | Détection injection SQL | ✅ Intégré |
| **Dirb** | 2.22+ | Énumération répertoires | ✅ Intégré |
| **GoBuster** | 3.6+ | Énumération rapide | ✅ Intégré |
| **OWASP ZAP** | 2.14+ | Proxy de sécurité | ✅ Intégré |

---

## 🚦 Utilisation

### Via Interface Web :
1. Sélectionner la catégorie de scan
2. Choisir le type d'intensité
3. Spécifier la cible (IP/URL)
4. Configurer les ports (optionnel)
5. Lancer le scan
6. Suivre la progression en temps réel
7. Consulter les résultats

### Via API :
```bash
# Exemple scan web basique
curl -X POST http://localhost:5000/scan/pentesting/scan/web \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "http://example.com",
    "scan_type": "basic"
  }'
```

---

## ⚠️ Avertissements Légaux

- **Autorisation requise :** N'utilisez ces outils que sur des systèmes dont vous êtes propriétaire ou pour lesquels vous avez une autorisation écrite explicite.
- **Responsabilité :** L'utilisateur est seul responsable de l'utilisation de ces outils.
- **Conformité :** Respectez les lois locales et internationales sur la cybersécurité.

---

## 📞 Support

Pour toute question ou problème :
- **Documentation :** Consultez la documentation technique
- **Logs :** Vérifiez les logs d'application
- **Contact :** Équipe de développement PICA
