# Tests Postman - PICA Auth

## 🚀 Utilisation

1. **Importez** le fichier `PICA_Auth_Tests.postman_collection.json` dans Postman
2. **Modifiez** les variables selon vos besoins dans l'onglet "Variables" de la collection
3. **Exécutez** les tests dans l'ordre

## 📝 Variables à modifier

| Variable | Description | Exemple |
|----------|-------------|---------|
| `BASE_URL` | URL du serveur | `http://localhost:5000` |
| `TEST_EMAIL` | Email de test | `<EMAIL>` |
| `TEST_PASSWORD` | Mot de passe | `VotreMotDePasse123!` |
| `TEST_FIRSTNAME` | Prénom | `John` |
| `TEST_LASTNAME` | Nom | `Doe` |
| `TEST_USERNAME` | Nom d'utilisateur | `johndoe` |
| `TEST_BIRTHDATE` | Date de naissance | `15/01/1990` |
| `TEST_GENDER` | Genre | `Male` ou `Female` |
| `EMAIL_TOKEN` | Token d'email | Copiez depuis l'email reçu |
| `RESET_TOKEN` | Token de reset | Copiez depuis l'email reçu |
| `ACCESS_TOKEN` | Token d'accès | Copiez depuis la réponse login |
| `OTP_CODE` | Code OTP | `123456` |

## 🔄 Ordre d'exécution

1. **Register User** → Créer un compte
2. **Confirm Email** → Confirmer l'email (copiez le token depuis l'email)
3. **Login User** → Se connecter (copiez l'access_token)
4. **Forgot Password** → Demander reset
5. **Reset Password** → Réinitialiser (copiez le reset_token depuis l'email)
6. **Toggle 2FA** → Activer la 2FA
7. **Verify OTP** → Vérifier le code OTP (copiez depuis l'email)

## 📧 Récupération des tokens

- **EMAIL_TOKEN** : Dans l'URL du lien de confirmation d'email
- **RESET_TOKEN** : Dans l'URL du lien de reset de mot de passe  
- **ACCESS_TOKEN** : Dans la réponse JSON du login
- **OTP_CODE** : Code à 6 chiffres dans l'email de 2FA

## ✅ Tests disponibles

- ✅ Inscription utilisateur
- ✅ Confirmation d'email
- ✅ Connexion
- ✅ Mot de passe oublié
- ✅ Réinitialisation mot de passe
- ✅ Activation 2FA
- ✅ Vérification OTP

**Simple et efficace !** 🎯
