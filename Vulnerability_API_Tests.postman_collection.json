{"info": {"_postman_id": "vulnerability-api-tests", "name": "Vulnerability API Tests", "description": "Collection complète de tests pour l'API d'analyse de vulnérabilités", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. API Info", "event": [{"listen": "test", "script": {"exec": ["// Test de statut", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "// Test de structure de réponse", "pm.test(\"Response has correct structure\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('version');", "    pm.expect(jsonData).to.have.property('workflow');", "    pm.expect(jsonData).to.have.property('endpoints');", "});", "", "// Test de version", "pm.test(\"API version is correct\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.version).to.eql(\"1.0.0\");", "});", "", "// Test de temps de réponse", "pm.test(\"Response time is less than 2000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "console.log(\"API Info:\", pm.response.json());"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/", "host": ["{{base_url}}"], "path": [""]}}, "response": []}, {"name": "2. Start Analysis - Full Report", "event": [{"listen": "test", "script": {"exec": ["// Test de statut", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "// Test de structure de réponse", "pm.test(\"Response has correct structure\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status');", "    pm.expect(jsonData).to.have.property('analysis_id');", "    pm.expect(jsonData).to.have.property('filename');", "    pm.expect(jsonData).to.have.property('next_steps');", "});", "", "// Sauvegarder l'analysis_id pour les tests suivants", "pm.test(\"Save analysis_id\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set(\"analysis_id\", jsonData.analysis_id);", "    console.log(\"Analysis ID saved:\", jsonData.analysis_id);", "});", "", "// Test de statut success", "pm.test(\"Analysis started successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql(\"success\");", "});", "", "// Test des options", "pm.test(\"Options are correct\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.options).to.have.property('generate_full_report');", "    pm.expect(jsonData.options.generate_full_report).to.be.true;", "});", "", "console.log(\"Analysis started:\", pm.response.json());"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}, {"key": "generate_full_report", "value": "true", "type": "text"}]}, "url": {"raw": "{{base_url}}/analyze", "host": ["{{base_url}}"], "path": ["analyze"]}}, "response": []}, {"name": "3. Start Analysis - Basic Report with Scan IDs", "event": [{"listen": "test", "script": {"exec": ["// Test de statut", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "// Test de structure de réponse", "pm.test(\"Response has correct structure\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status');", "    pm.expect(jsonData).to.have.property('analysis_id');", "    pm.expect(jsonData).to.have.property('filename');", "});", "", "// Sauvegarder l'analysis_id pour les tests suivants", "pm.test(\"Save analysis_id_basic\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set(\"analysis_id_basic\", jsonData.analysis_id);", "    console.log(\"Basic Analysis ID saved:\", jsonData.analysis_id);", "});", "", "// Test des scan_ids", "pm.test(\"Scan IDs are processed\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.options && jsonData.options.scan_ids) {", "        pm.expect(jsonData.options.scan_ids).to.be.an('array');", "    }", "});", "", "console.log(\"Basic Analysis started:\", pm.response.json());"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}, {"key": "scan_ids", "value": "scan1,scan2,scan3", "type": "text"}, {"key": "generate_full_report", "value": "false", "type": "text"}]}, "url": {"raw": "{{base_url}}/analyze", "host": ["{{base_url}}"], "path": ["analyze"]}}, "response": []}, {"name": "4. Check Analysis Status", "event": [{"listen": "test", "script": {"exec": ["// Test de statut", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "// Test de structure de réponse", "pm.test(\"Response has correct structure\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status');", "    pm.expect(jsonData).to.have.property('analysis');", "    pm.expect(jsonData.analysis).to.have.property('analysis_id');", "    pm.expect(jsonData.analysis).to.have.property('status');", "    pm.expect(jsonData.analysis).to.have.property('progress');", "});", "", "// Test des valeurs de statut valides", "pm.test(\"Analysis status is valid\", function () {", "    var jsonData = pm.response.json();", "    var validStatuses = [\"queued\", \"processing\", \"completed\", \"failed\"];", "    pm.expect(validStatuses).to.include(jsonData.analysis.status);", "});", "", "// Test de progression", "pm.test(\"Progress is valid\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.analysis.progress).to.be.at.least(0);", "    pm.expect(jsonData.analysis.progress).to.be.at.most(100);", "});", "", "// Test de l'ID d'analyse", "pm.test(\"Analysis ID matches\", function () {", "    var jsonData = pm.response.json();", "    var expectedId = pm.environment.get(\"analysis_id\");", "    if (expectedId) {", "        pm.expect(jsonData.analysis.analysis_id).to.eql(expectedId);", "    }", "});", "", "// Affiche<PERSON> le statut", "var jsonData = pm.response.json();", "console.log(\"Analysis Status:\", jsonData.analysis.status);", "console.log(\"Progress:\", jsonData.analysis.progress + \"%\");", "console.log(\"Message:\", jsonData.analysis.message);", "", "// Si l'analyse est terminée, programmer la récupération du rapport", "if (jsonData.analysis.status === \"completed\") {", "    console.log(\"Analysis completed! Ready to fetch report.\");", "} else if (jsonData.analysis.status === \"processing\") {", "    console.log(\"Analysis in progress. Check again in a few seconds.\");", "} else if (jsonData.analysis.status === \"failed\") {", "    console.log(\"Analysis failed:\", jsonData.analysis.message);", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/status/{{analysis_id}}", "host": ["{{base_url}}"], "path": ["status", "{{analysis_id}}"]}}, "response": []}, {"name": "5. Get Analysis Report", "event": [{"listen": "test", "script": {"exec": ["// Test de statut (peut être 200, 202, ou 500)", "pm.test(\"Status code is valid\", function () {", "    var validCodes = [200, 202, 400, 404, 500];", "    pm.expect(validCodes).to.include(pm.response.code);", "});", "", "// Test pour analyse terminée (200)", "if (pm.response.code === 200) {", "    pm.test(\"Report retrieved successfully\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('status');", "        pm.expect(jsonData).to.have.property('report');", "        pm.expect(jsonData.status).to.eql(\"success\");", "    });", "    ", "    pm.test(\"Report has correct structure\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.report).to.have.property('vulnerability_assessment_report');", "        pm.expect(jsonData.report.vulnerability_assessment_report).to.have.property('metadata');", "        pm.expect(jsonData.report.vulnerability_assessment_report).to.have.property('scan_reports');", "    });", "    ", "    pm.test(\"<PERSON><PERSON><PERSON> is complete\", function () {", "        var jsonData = pm.response.json();", "        var metadata = jsonData.report.vulnerability_assessment_report.metadata;", "        pm.expect(metadata).to.have.property('analysis_id');", "        pm.expect(metadata).to.have.property('total_scans');", "        pm.expect(metadata).to.have.property('total_vulnerabilities');", "        pm.expect(metadata).to.have.property('report_generated');", "    });", "    ", "    console.log(\"Report retrieved successfully!\");", "    var jsonData = pm.response.json();", "    console.log(\"Total scans:\", jsonData.report.vulnerability_assessment_report.metadata.total_scans);", "    console.log(\"Total vulnerabilities:\", jsonData.report.vulnerability_assessment_report.metadata.total_vulnerabilities);", "}", "", "// Test pour analyse en cours (202)", "if (pm.response.code === 202) {", "    pm.test(\"Analysis still in progress\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.detail).to.have.property('message');", "        pm.expect(jsonData.detail).to.have.property('progress');", "    });", "    ", "    console.log(\"Analysis still in progress...\");", "    var jsonData = pm.response.json();", "    console.log(\"Progress:\", jsonData.detail.progress + \"%\");", "    console.log(\"Message:\", jsonData.detail.current_message);", "}", "", "// Test pour analyse échouée (500)", "if (pm.response.code === 500) {", "    pm.test(\"Analysis failed with error\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.detail).to.have.property('message');", "        pm.expect(jsonData.detail).to.have.property('error');", "    });", "    ", "    console.log(\"Analysis failed!\");", "    var jsonData = pm.response.json();", "    console.log(\"Error:\", jsonData.detail.error);", "}", "", "// Test pour ID non trouvé (404)", "if (pm.response.code === 404) {", "    pm.test(\"Analysis ID not found\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.detail).to.include(\"non trouvé\");", "    });", "    ", "    console.log(\"Analysis ID not found!\");", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/report/{{analysis_id}}", "host": ["{{base_url}}"], "path": ["report", "{{analysis_id}}"]}}, "response": []}, {"name": "6. List All Analyses", "event": [{"listen": "test", "script": {"exec": ["// Test de statut", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "// Test de structure de réponse", "pm.test(\"Response has correct structure\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status');", "    pm.expect(jsonData).to.have.property('total_analyses');", "    pm.expect(jsonData).to.have.property('analyses');", "    pm.expect(jsonData.analyses).to.be.an('array');", "});", "", "// Test de statut success", "pm.test(\"Request successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql(\"success\");", "});", "", "// Test de cohérence du nombre d'analyses", "pm.test(\"Total analyses count matches array length\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.total_analyses).to.eql(jsonData.analyses.length);", "});", "", "// Test de structure des éléments d'analyse", "pm.test(\"Analysis items have correct structure\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.analyses.length > 0) {", "        var analysis = jsonData.analyses[0];", "        pm.expect(analysis).to.have.property('analysis_id');", "        pm.expect(analysis).to.have.property('status');", "        pm.expect(analysis).to.have.property('progress');", "        pm.expect(analysis).to.have.property('created_at');", "    }", "});", "", "// A<PERSON><PERSON><PERSON> les <PERSON>", "var jsonData = pm.response.json();", "console.log(\"Total analyses:\", jsonData.total_analyses);", "", "if (jsonData.analyses.length > 0) {", "    console.log(\"Recent analyses:\");", "    jsonData.analyses.slice(0, 3).forEach(function(analysis, index) {", "        console.log(`${index + 1}. ID: ${analysis.analysis_id.substring(0, 8)}... Status: ${analysis.status} Progress: ${analysis.progress}%`);", "    });", "} else {", "    console.log(\"No analyses found.\");", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analyses", "host": ["{{base_url}}"], "path": ["analyses"]}}, "response": []}, {"name": "7. <PERSON> Error - Invalid File Type", "event": [{"listen": "test", "script": {"exec": ["// Test de statut d'erreur", "pm.test(\"Status code is 400 for invalid file\", function () {", "    pm.response.to.have.status(400);", "});", "", "// Test du message d'erreur", "pm.test(\"Error message mentions CSV\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.detail).to.include(\"CSV\");", "});", "", "console.log(\"Error test passed - API correctly rejects non-CSV files\");"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/analyze", "host": ["{{base_url}}"], "path": ["analyze"]}}, "response": []}, {"name": "8. Test Error - Invalid Analysis ID", "event": [{"listen": "test", "script": {"exec": ["// Test de statut d'erreur", "pm.test(\"Status code is 404 for invalid ID\", function () {", "    pm.response.to.have.status(404);", "});", "", "// Test du message d'erreur", "pm.test(\"Error message mentions not found\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.detail).to.include(\"non trouvé\");", "});", "", "console.log(\"Error test passed - API correctly handles invalid analysis IDs\");"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/status/invalid-analysis-id-12345", "host": ["{{base_url}}"], "path": ["status", "invalid-analysis-id-12345"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Script exécuté avant chaque requête", "console.log(\"Executing request to:\", pm.request.url.toString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Script exécuté après chaque requête", "console.log(\"Response received with status:\", pm.response.code);"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "analysis_id", "value": "", "type": "string"}, {"key": "analysis_id_basic", "value": "", "type": "string"}]}