{"id": "87654321-4321-4321-4321-123456789def", "name": "PICA Development Environment v2.0", "values": [{"key": "baseUrl", "value": "http://localhost:5000", "type": "default", "enabled": true, "description": "Base URL for PICA API"}, {"key": "token", "value": "", "type": "secret", "enabled": true, "description": "JWT token for authentication (auto-filled by login)"}, {"key": "last_scan_id", "value": "", "type": "default", "enabled": true, "description": "Last scan ID for testing specific scan results"}, {"key": "adminEmail", "value": "<EMAIL>", "type": "default", "enabled": true, "description": "Admin email for login"}, {"key": "adminPassword", "value": "admin123", "type": "secret", "enabled": true, "description": "Admin password for login"}, {"key": "testTargetNetwork", "value": "scanme.nmap.org", "type": "default", "enabled": true, "description": "Test target for network scans (official Nmap test target)"}, {"key": "testTargetWeb", "value": "http://testphp.vulnweb.com", "type": "default", "enabled": true, "description": "Test target for web scans (official vulnerable web app)"}, {"key": "testTargetVuln", "value": "***********", "type": "default", "enabled": true, "description": "Test target for vulnerability scans (local network)"}, {"key": "testPorts", "value": "22,80,443", "type": "default", "enabled": true, "description": "Common ports for testing"}, {"key": "testPortsExtended", "value": "1-1000", "type": "default", "enabled": true, "description": "Extended port range for aggressive scans"}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-01-23T10:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}