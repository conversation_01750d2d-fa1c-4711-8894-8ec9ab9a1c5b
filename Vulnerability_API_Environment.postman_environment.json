{"id": "vulnerability-api-env", "name": "Vulnerability API Environment", "values": [{"key": "base_url", "value": "http://localhost:8000", "type": "default", "enabled": true}, {"key": "analysis_id", "value": "", "type": "default", "enabled": true}, {"key": "analysis_id_basic", "value": "", "type": "default", "enabled": true}, {"key": "api_version", "value": "1.0.0", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-01-15T10:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}