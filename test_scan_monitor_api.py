#!/usr/bin/env python3
"""
Script de test pour les nouvelles API de monitoring des scans
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:5000"
USERNAME = "admin"  # Remplacez par vos identifiants
PASSWORD = "admin123"

def get_jwt_token():
    """Obtenir un token JWT"""
    login_data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        return response.json().get('access_token')
    else:
        print(f"❌ Login failed: {response.status_code} - {response.text}")
        return None

def test_running_scans_api(token):
    """Tester l'API des scans en cours"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print("🔍 Testing /scan/pentesting/scans/running")
    response = requests.get(f"{BASE_URL}/scan/pentesting/scans/running", headers=headers)
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Found {data['total_running']} running scans")
        
        for scan in data['running_scans']:
            print(f"  📊 {scan['category']} scan: {scan['scan_id'][:8]}...")
            print(f"     Target: {scan['target']}")
            print(f"     Current tool: {scan['current_tool']}")
            if 'overall_progress' in scan:
                print(f"     Progress: {scan['overall_progress']}%")
            print(f"     Duration: {scan.get('current_duration_formatted', 'N/A')}")
            print()
        
        return data['running_scans']
    else:
        print(f"❌ Error: {response.text}")
        return []

def test_scan_details_api(token, scan_id):
    """Tester l'API des détails de scan"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print(f"🔍 Testing /scan/pentesting/scan/details/{scan_id}")
    response = requests.get(f"{BASE_URL}/scan/pentesting/scan/details/{scan_id}", headers=headers)
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Scan details for {scan_id[:8]}...")
        print(f"   Category: {data['category']}")
        print(f"   Type: {data['scan_type']}")
        print(f"   Target: {data['target']}")
        print(f"   Status: {data['status']}")
        print(f"   Current tool: {data['current_tool']}")
        
        if 'tools_status' in data:
            print("   Tools status:")
            for tool, status in data['tools_status'].items():
                print(f"     {tool}: {status['status']} ({status['progress']}%)")
                if status.get('error'):
                    print(f"       Error: {status['error']}")
        
        return data
    else:
        print(f"❌ Error: {response.text}")
        return None

def start_test_scan(token):
    """Démarrer un scan de test pour avoir des données"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    scan_data = {
        "target_url": "http://testphp.vulnweb.com",
        "scan_type": "basic"
    }
    
    print("🚀 Starting test web scan...")
    response = requests.post(f"{BASE_URL}/scan/pentesting/scan/web", 
                           headers=headers, json=scan_data)
    
    if response.status_code == 200:
        scan_id = response.json().get('scan_id')
        print(f"✅ Test scan started: {scan_id}")
        return scan_id
    else:
        print(f"❌ Failed to start test scan: {response.text}")
        return None

def main():
    print("🧪 Testing Scan Monitor APIs")
    print("=" * 50)
    
    # 1. Obtenir le token
    print("1. Getting JWT token...")
    token = get_jwt_token()
    if not token:
        print("❌ Cannot proceed without token")
        return
    print("✅ Token obtained")
    print()
    
    # 2. Tester l'API des scans en cours
    print("2. Testing running scans API...")
    running_scans = test_running_scans_api(token)
    print()
    
    # 3. Si pas de scans en cours, en démarrer un
    if not running_scans:
        print("3. No running scans found, starting a test scan...")
        test_scan_id = start_test_scan(token)
        if test_scan_id:
            time.sleep(2)  # Attendre un peu
            running_scans = test_running_scans_api(token)
        print()
    
    # 4. Tester l'API des détails si on a des scans
    if running_scans:
        print("4. Testing scan details API...")
        scan_id = running_scans[0]['scan_id']
        scan_details = test_scan_details_api(token, scan_id)
        print()
        
        # 5. Monitoring en temps réel (quelques itérations)
        print("5. Real-time monitoring (5 iterations)...")
        for i in range(5):
            print(f"   Iteration {i+1}/5...")
            running_scans = test_running_scans_api(token)
            if running_scans:
                scan = running_scans[0]
                print(f"   Current tool: {scan['current_tool']}")
                if 'overall_progress' in scan:
                    print(f"   Progress: {scan['overall_progress']}%")
            time.sleep(3)
            print()
    
    print("🏁 Test completed!")

if __name__ == "__main__":
    main()
