#!/usr/bin/env python3
"""
Script pour tester Nikto directement
"""

import subprocess
import sys
import time

def test_nikto_availability():
    """Tester si Nikto est disponible"""
    print("🔍 Testing Nikto availability...")
    
    possible_paths = [
        '/usr/bin/nikto',
        '/usr/local/bin/nikto',
        '/opt/nikto/program/nikto.pl',
        'nikto'
    ]
    
    for path in possible_paths:
        try:
            print(f"   Trying: {path}")
            result = subprocess.run([path, '-Version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"   ✅ Found Nikto at: {path}")
                print(f"   Version info: {result.stdout.strip()}")
                return path
            else:
                print(f"   ❌ Failed with return code: {result.returncode}")
        except subprocess.TimeoutExpired:
            print(f"   ⏰ Timeout")
        except FileNotFoundError:
            print(f"   ❌ Not found")
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("❌ Nikto not found in any standard location")
    return None

def test_nikto_scan(nikto_path, target):
    """Tester un scan Nikto simple"""
    print(f"\n🚀 Testing Nikto scan on: {target}")
    
    cmd = [
        nikto_path,
        '-h', target,
        '-ask', 'no',
        '-maxtime', '60'  # 1 minute max pour le test
    ]
    
    print(f"🔧 Command: {' '.join(cmd)}")
    
    try:
        start_time = time.time()
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        print("⏳ Nikto is running...")
        
        # Attendre avec timeout
        try:
            stdout, stderr = process.communicate(timeout=120)  # 2 minutes max
            end_time = time.time()
            
            print(f"✅ Nikto completed in {end_time - start_time:.1f} seconds")
            print(f"📊 Return code: {process.returncode}")
            print(f"📊 Stdout length: {len(stdout)} chars")
            print(f"📊 Stderr length: {len(stderr)} chars")
            
            if stdout:
                print("\n📝 First 500 chars of output:")
                print(stdout[:500])
                if len(stdout) > 500:
                    print("...")
            
            if stderr:
                print("\n❌ Stderr:")
                print(stderr[:500])
            
            return True
            
        except subprocess.TimeoutExpired:
            print("⏰ Nikto timeout, terminating...")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            return False
            
    except Exception as e:
        print(f"❌ Error running Nikto: {e}")
        return False

def test_nikto_with_xml_output(nikto_path, target):
    """Tester Nikto avec sortie XML"""
    print(f"\n🔬 Testing Nikto with XML output on: {target}")
    
    import tempfile
    import os
    
    output_file = os.path.join(tempfile.gettempdir(), "test_nikto_output.xml")
    
    cmd = [
        nikto_path,
        '-h', target,
        '-Format', 'xml',
        '-output', output_file,
        '-ask', 'no',
        '-maxtime', '60'
    ]
    
    print(f"🔧 Command: {' '.join(cmd)}")
    print(f"📁 Output file: {output_file}")
    
    try:
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        end_time = time.time()
        
        print(f"✅ Nikto XML test completed in {end_time - start_time:.1f} seconds")
        print(f"📊 Return code: {result.returncode}")
        
        # Vérifier si le fichier XML a été créé
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"📁 XML file created: {file_size} bytes")
            
            # Lire le début du fichier XML
            with open(output_file, 'r') as f:
                content = f.read(500)
                print(f"📝 XML content preview:")
                print(content)
            
            # Nettoyer
            os.remove(output_file)
            return True
        else:
            print("❌ XML file not created")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Nikto XML test timeout")
        return False
    except Exception as e:
        print(f"❌ Error in XML test: {e}")
        return False

def main():
    print("🧪 Nikto Testing Tool")
    print("=" * 50)
    
    # Test 1: Availability
    nikto_path = test_nikto_availability()
    if not nikto_path:
        print("\n❌ Cannot proceed without Nikto")
        return
    
    # Test 2: Simple scan
    target = "http://testphp.vulnweb.com"  # Site de test sécurisé
    if len(sys.argv) > 1:
        target = sys.argv[1]
    
    print(f"\n🎯 Target: {target}")
    
    success1 = test_nikto_scan(nikto_path, target)
    success2 = test_nikto_with_xml_output(nikto_path, target)
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   Nikto availability: ✅")
    print(f"   Simple scan: {'✅' if success1 else '❌'}")
    print(f"   XML output: {'✅' if success2 else '❌'}")
    
    if success1 and success2:
        print("\n🎉 All tests passed! Nikto is working correctly.")
    else:
        print("\n⚠️ Some tests failed. Check Nikto installation.")

if __name__ == "__main__":
    main()
