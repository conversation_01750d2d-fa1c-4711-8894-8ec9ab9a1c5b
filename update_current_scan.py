#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to manually update the current running scan to test the frontend
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.extensions import mongo
from app import create_app
from datetime import datetime
import time

def update_running_scan():
    """Update the current running scan with phase information"""
    app = create_app()
    
    with app.app_context():
        print("🔍 Finding running scans...")
        
        # Find running scans
        running_scans = list(mongo.db.scans.find({'status': 'running'}))
        
        if not running_scans:
            print("❌ No running scans found")
            return
        
        scan = running_scans[0]  # Take the first running scan
        scan_id = scan['scan_id']
        
        print(f"📋 Found running scan: {scan_id}")
        print(f"   Category: {scan.get('category', 'N/A')}")
        print(f"   Current Tool: {scan.get('current_tool', 'N/A')}")
        
        # Simulate deep scan phases
        phases = [
            ("Phase 1: Network Scanning", 10),
            ("Phase 1: NMAP", 15),
            ("Phase 1: NMAP Complete", 20),
            ("Phase 1: OPENVAS", 25),
            ("Phase 1: OPENVAS Complete", 30),
            ("Phase 1: METASPLOIT", 35),
            ("Phase 2: Web Scanning", 40),
            ("Phase 2: NIKTO", 45),
            ("Phase 2: NIKTO Complete", 50),
            ("Phase 2: SQLMAP", 55),
            ("Phase 2: SQLMAP Complete", 60),
            ("Phase 2: DIRB", 65),
            ("Phase 3: Analysis", 70),
            ("Phase 3: Consolidating Ports", 75),
            ("Phase 3: Consolidating Vulnerabilities", 80),
            ("Phase 3: Calculating Summary", 85),
            ("Phase 3: Finalizing", 90),
            ("Completing Deep Scan", 95)
        ]
        
        print(f"\n🚀 Starting phase simulation for scan {scan_id[:8]}...")
        
        for i, (phase, progress) in enumerate(phases):
            print(f"   [{i+1:2d}/18] {phase} ({progress}%)")
            
            # Update the scan in database
            mongo.db.scans.update_one(
                {'scan_id': scan_id},
                {
                    '$set': {
                        'current_tool': phase,
                        'progress': progress,
                        'last_update': datetime.utcnow().isoformat()
                    }
                }
            )
            
            # Wait a bit so you can see the changes in the frontend
            time.sleep(3)
        
        print(f"\n✅ Phase simulation completed for scan {scan_id[:8]}")
        print("   You should now see the phases updating in the frontend monitor!")
        print("   The scan will continue running with the last phase shown.")

if __name__ == '__main__':
    update_running_scan()
