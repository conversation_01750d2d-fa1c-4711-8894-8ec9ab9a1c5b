#!/usr/bin/env python3
"""
Debug script to check scan details and identify the 500 error
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from pymongo import MongoClient
import json
from datetime import datetime

def debug_scan_details():
    """Debug scan details to find the 500 error cause"""
    try:
        # Connect to MongoDB
        client = MongoClient('mongodb://localhost:27017/')
        db = client['pica_db']
        scans_collection = db['scans']
        
        print("🔍 Debugging scan details...")
        
        # Get all scans (not just running ones)
        all_scans = list(scans_collection.find().sort('start_time', -1).limit(10))
        running_scans = [scan for scan in all_scans if scan.get('status') == 'running']

        print(f"Found {len(all_scans)} total scans (last 10)")
        print(f"Found {len(running_scans)} running scans:")

        if len(all_scans) == 0:
            print("❌ No scans found in database!")
            return

        # Show all recent scans first
        print("\n📋 Recent scans:")
        for i, scan in enumerate(all_scans[:5]):
            print(f"   {i+1}. {scan.get('scan_id', 'unknown')[:8]}... - {scan.get('status', 'unknown')} - {scan.get('category', 'unknown')}")

        if len(running_scans) == 0:
            print("\n⚠️ No running scans found. Checking the most recent scan for issues...")
            running_scans = [all_scans[0]] if all_scans else []
        
        for i, scan in enumerate(running_scans):
            print(f"\n📋 Scan {i+1}: {scan.get('scan_id', 'unknown')[:8]}...")
            print(f"   Category: {scan.get('category', 'unknown')}")
            print(f"   Type: {scan.get('scan_type', 'unknown')}")
            print(f"   Target: {scan.get('target', 'unknown')}")
            print(f"   Current Tool: {scan.get('current_tool', 'unknown')}")
            print(f"   Status: {scan.get('status', 'unknown')}")
            
            # Check for problematic fields
            problematic_fields = []
            
            # Check progress field structure
            if 'progress' in scan:
                progress = scan['progress']
                print(f"   Progress field type: {type(progress)}")
                if isinstance(progress, dict):
                    print(f"   Progress keys: {list(progress.keys())}")
                else:
                    print(f"   Progress value: {progress}")
                    problematic_fields.append("progress field is not a dict")
            
            # Check tools_status
            if 'tools_status' in scan:
                tools_status = scan['tools_status']
                print(f"   Tools status: {len(tools_status)} tools")
                for tool, status in tools_status.items():
                    if not isinstance(status, dict):
                        problematic_fields.append(f"tools_status.{tool} is not a dict")
                    else:
                        # Check for datetime parsing issues
                        for time_field in ['start_time', 'end_time']:
                            if time_field in status and status[time_field]:
                                try:
                                    datetime.fromisoformat(status[time_field].replace('Z', '+00:00'))
                                except Exception as e:
                                    problematic_fields.append(f"tools_status.{tool}.{time_field} parsing error: {e}")
            
            # Check main datetime fields
            for time_field in ['start_time', 'end_time']:
                if time_field in scan and scan[time_field]:
                    try:
                        datetime.fromisoformat(scan[time_field].replace('Z', '+00:00'))
                    except Exception as e:
                        problematic_fields.append(f"{time_field} parsing error: {e}")
            
            if problematic_fields:
                print(f"   ⚠️ Potential issues:")
                for issue in problematic_fields:
                    print(f"      - {issue}")
            else:
                print(f"   ✅ No obvious issues detected")
            
            # Try to simulate the get_scan_details logic
            print(f"   🧪 Testing scan details logic...")
            try:
                # Simulate the problematic part
                scan_details = {
                    'scan_id': scan.get('scan_id'),
                    'status': scan.get('status', 'unknown'),
                    'category': scan.get('category', ''),
                    'scan_type': scan.get('scan_type', ''),
                    'target': scan.get('target', ''),
                    'current_tool': scan.get('current_tool', 'unknown')
                }
                
                # Test tools_status processing
                if 'tools_status' in scan:
                    tools_status = scan['tools_status']
                    scan_details['tools_status'] = {}
                    
                    for tool, status in tools_status.items():
                        scan_details['tools_status'][tool] = {
                            'status': status.get('status', 'pending'),
                            'start_time': status.get('start_time'),
                            'end_time': status.get('end_time'),
                            'progress': status.get('progress', 0),
                            'error': status.get('error'),
                            'duration': None
                        }
                        
                        # Test duration calculation
                        if status.get('start_time') and status.get('end_time'):
                            start = datetime.fromisoformat(status['start_time'].replace('Z', '+00:00'))
                            end = datetime.fromisoformat(status['end_time'].replace('Z', '+00:00'))
                            duration = (end - start).total_seconds()
                            scan_details['tools_status'][tool]['duration'] = f"{duration:.1f}s"
                
                # Test deep scan progress
                if scan.get('category') == 'deep' and 'progress' in scan:
                    progress = scan['progress']
                    scan_details['deep_progress'] = {
                        'current_tool': progress.get('current_tool', ''),
                        'progress_percent': progress.get('progress_percent', 0),
                        'completed_tools': progress.get('completed_tools', []),
                        'total_tools': progress.get('total_tools', 0)
                    }
                
                # Test duration calculation
                if scan.get('start_time'):
                    start = datetime.fromisoformat(scan['start_time'].replace('Z', '+00:00'))
                    if scan.get('end_time'):
                        end = datetime.fromisoformat(scan['end_time'].replace('Z', '+00:00'))
                        total_duration = (end - start).total_seconds()
                        scan_details['total_duration'] = f"{total_duration:.1f}s"
                    else:
                        now = datetime.utcnow()
                        current_duration = (now - start).total_seconds()
                        scan_details['current_duration'] = f"{current_duration:.1f}s"
                
                print(f"      ✅ Scan details logic completed successfully")
                
            except Exception as e:
                print(f"      ❌ Error in scan details logic: {e}")
                print(f"         This is likely the cause of the 500 error!")
                
                # Print the full scan document for debugging
                print(f"      📄 Full scan document:")
                scan_copy = dict(scan)
                # Remove _id for JSON serialization
                if '_id' in scan_copy:
                    del scan_copy['_id']
                print(json.dumps(scan_copy, indent=2, default=str))
        
        client.close()
        
    except Exception as e:
        print(f"❌ Error debugging scan details: {e}")

if __name__ == '__main__':
    debug_scan_details()
