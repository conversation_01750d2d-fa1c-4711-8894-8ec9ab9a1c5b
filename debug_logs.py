#!/usr/bin/env python3
"""
Script pour déboguer les logs dans MongoDB
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from pymongo import MongoClient
from datetime import datetime
import json

# Configuration MongoDB
MONGO_URI = "mongodb://localhost:27017/"
DB_NAME = "pica_db"

def connect_to_mongo():
    """Se connecter à MongoDB"""
    try:
        client = MongoClient(MONGO_URI)
        db = client[DB_NAME]
        # Test de connexion
        db.admin.command('ping')
        print("✅ Connected to MongoDB")
        return db
    except Exception as e:
        print(f"❌ Failed to connect to MongoDB: {e}")
        return None

def check_scan_logs_collection(db):
    """Vérifier la collection scan_logs"""
    try:
        collection = db.scan_logs
        count = collection.count_documents({})
        print(f"📋 Total logs in scan_logs collection: {count}")
        
        if count > 0:
            # Afficher les derniers logs
            latest_logs = list(collection.find().sort('timestamp', -1).limit(10))
            print("\n🔍 Latest 10 logs:")
            for i, log in enumerate(latest_logs, 1):
                timestamp = log.get('timestamp', '')[:19]
                scan_id = log.get('scan_id', '')[:8]
                level = log.get('level', '').upper()
                message = log.get('message', '')[:60]
                print(f"   {i:2d}. {timestamp} [{level}] {scan_id}... {message}")
        
        return count
    except Exception as e:
        print(f"❌ Error checking scan_logs: {e}")
        return 0

def check_scans_collection(db):
    """Vérifier la collection scans"""
    try:
        collection = db.scans
        count = collection.count_documents({})
        print(f"\n📊 Total scans in scans collection: {count}")
        
        # Vérifier les scans avec des logs récents
        scans_with_logs = list(collection.find(
            {'recent_logs': {'$exists': True, '$ne': []}},
            {'scan_id': 1, 'status': 1, 'category': 1, 'recent_logs': 1}
        ))
        
        print(f"🔍 Scans with recent_logs: {len(scans_with_logs)}")
        for scan in scans_with_logs:
            scan_id = scan.get('scan_id', '')[:8]
            status = scan.get('status', '')
            category = scan.get('category', '')
            logs_count = len(scan.get('recent_logs', []))
            print(f"   - {scan_id}... ({category}/{status}) - {logs_count} logs")
        
        return scans_with_logs
    except Exception as e:
        print(f"❌ Error checking scans: {e}")
        return []

def get_logs_for_scan(db, scan_id):
    """Récupérer les logs pour un scan spécifique"""
    try:
        # Logs de la collection scan_logs
        logs_collection = db.scan_logs
        logs = list(logs_collection.find(
            {'scan_id': scan_id}
        ).sort('timestamp', -1))
        
        print(f"\n🔍 Logs for scan {scan_id[:8]}... from scan_logs collection:")
        print(f"   Found {len(logs)} logs")
        
        for i, log in enumerate(logs[:5], 1):  # Afficher les 5 premiers
            timestamp = log.get('timestamp', '')[:19]
            level = log.get('level', '').upper()
            event_type = log.get('event_type', '')
            message = log.get('message', '')[:80]
            print(f"   {i}. {timestamp} [{level}] {event_type}: {message}")
        
        # Logs du document scan
        scan_doc = db.scans.find_one({'scan_id': scan_id})
        if scan_doc:
            recent_logs = scan_doc.get('recent_logs', [])
            last_log = scan_doc.get('last_log')
            
            print(f"\n🔍 Logs from scan document:")
            print(f"   Recent logs: {len(recent_logs)}")
            print(f"   Last log: {'Yes' if last_log else 'No'}")
            
            if last_log:
                print(f"   Last log message: {last_log.get('message', '')[:80]}")
        
        return logs
    except Exception as e:
        print(f"❌ Error getting logs for scan: {e}")
        return []

def test_log_creation(db):
    """Tester la création d'un log"""
    try:
        from backend.app.services.scan_logger import ScanLogger
        
        # Créer un logger de test
        test_scan_id = "test-scan-12345"
        logger = ScanLogger(test_scan_id)
        
        print(f"\n🧪 Testing log creation for scan: {test_scan_id}")
        
        # Créer quelques logs de test
        logger.log_scan_start('web', 'basic', 'http://test.com', ['nikto', 'sqlmap'])
        logger.log_tool_start('nikto', 'nikto -h http://test.com')
        logger.log_tool_progress('nikto', 50, 'Scanning in progress...')
        logger.log_tool_result('nikto', 'completed', {'vulnerabilities': ['XSS', 'SQLi']})
        
        print("✅ Test logs created")
        
        # Vérifier que les logs ont été créés
        logs = get_logs_for_scan(db, test_scan_id)
        
        # Nettoyer les logs de test
        db.scan_logs.delete_many({'scan_id': test_scan_id})
        db.scans.delete_one({'scan_id': test_scan_id})
        print("🧹 Test logs cleaned up")
        
        return len(logs) > 0
    except Exception as e:
        print(f"❌ Error testing log creation: {e}")
        return False

def main():
    print("🔍 MongoDB Logs Debug Tool")
    print("=" * 50)
    
    # Se connecter à MongoDB
    db = connect_to_mongo()
    if not db:
        return
    
    # Vérifier les collections
    logs_count = check_scan_logs_collection(db)
    scans_with_logs = check_scans_collection(db)
    
    # Si on a des arguments, afficher les logs pour un scan spécifique
    if len(sys.argv) > 1:
        scan_id = sys.argv[1]
        get_logs_for_scan(db, scan_id)
    elif scans_with_logs:
        # Afficher les logs du premier scan avec des logs
        first_scan = scans_with_logs[0]
        scan_id = first_scan['scan_id']
        get_logs_for_scan(db, scan_id)
    
    # Tester la création de logs
    print("\n" + "=" * 50)
    test_success = test_log_creation(db)
    
    print(f"\n📊 Summary:")
    print(f"   Total logs in DB: {logs_count}")
    print(f"   Scans with logs: {len(scans_with_logs)}")
    print(f"   Log creation test: {'✅ PASS' if test_success else '❌ FAIL'}")
    
    print("\n💡 Usage:")
    print(f"   python3 {sys.argv[0]} <scan_id>  # Show logs for specific scan")

if __name__ == "__main__":
    main()
