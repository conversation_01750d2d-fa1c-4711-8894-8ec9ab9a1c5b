#!/usr/bin/env python3
"""
Test script to verify the analysis store endpoint is working
"""

import requests
import json

# Test data
test_data = {
    "analysis_id": "d9a3f9b9-255e-4d06-aac7-59eea091b3e0",
    "file_info": {
        "filename": "PICA_All_Scans_Export_20250709T133832 (Copy).csv",
        "size": 653,
        "content_type": "text/csv"
    },
    "analysis_type": "full",
    "scan_ids": []
}

# Test the endpoint
url = "http://localhost:5000/api/analysis/store"
headers = {
    "Content-Type": "application/json",
    "Origin": "http://localhost:5173"
}

print("Testing analysis store endpoint...")
print(f"URL: {url}")
print(f"Data: {json.dumps(test_data, indent=2)}")

try:
    response = requests.post(url, json=test_data, headers=headers)
    print(f"\nResponse Status: {response.status_code}")
    print(f"Response Headers: {dict(response.headers)}")
    print(f"Response Body: {response.text}")
    
    if response.status_code == 401:
        print("\n❌ Authentication required - this is expected without a valid token")
    elif response.status_code == 200:
        print("\n✅ Success!")
    else:
        print(f"\n⚠️ Unexpected status code: {response.status_code}")
        
except Exception as e:
    print(f"\n❌ Error: {e}")
