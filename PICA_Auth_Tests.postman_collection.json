{"info": {"name": "PICA - Tests Auth", "description": "Tests pour l'authentification PICA - Modifiez les variables selon vos besoins", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "BASE_URL", "value": "http://localhost:5000"}, {"key": "TEST_EMAIL", "value": "<EMAIL>"}, {"key": "TEST_PASSWORD", "value": "SecurePass123!"}, {"key": "TEST_FIRSTNAME", "value": "<PERSON>"}, {"key": "TEST_LASTNAME", "value": "<PERSON><PERSON>"}, {"key": "TEST_USERNAME", "value": "johndoe"}, {"key": "TEST_BIRTHDATE", "value": "15/01/1990"}, {"key": "TEST_GENDER", "value": "Male"}, {"key": "EMAIL_TOKEN", "value": "REMPLACEZ_PAR_TOKEN_EMAIL"}, {"key": "RESET_TOKEN", "value": "REMPLACEZ_PAR_TOKEN_RESET"}, {"key": "ACCESS_TOKEN", "value": "REMPLACEZ_PAR_ACCESS_TOKEN"}, {"key": "OTP_CODE", "value": "123456"}], "item": [{"name": "1. Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"first_name\": \"{{TEST_FIRSTNAME}}\",\n    \"last_name\": \"{{TEST_LASTNAME}}\",\n    \"date_of_birth\": \"{{TEST_BIRTHDATE}}\",\n    \"username\": \"{{TEST_USERNAME}}\",\n    \"gender\": \"{{TEST_GENDER}}\",\n    \"email\": \"{{TEST_EMAIL}}\",\n    \"password\": \"{{TEST_PASSWORD}}\"\n}"}, "url": {"raw": "{{BASE_URL}}/auth/register", "host": ["{{BASE_URL}}"], "path": ["auth", "register"]}}}, {"name": "2. <PERSON><PERSON><PERSON>", "request": {"method": "GET", "url": {"raw": "{{BASE_URL}}/auth/confirm-email?token={{EMAIL_TOKEN}}", "host": ["{{BASE_URL}}"], "path": ["auth", "confirm-email"], "query": [{"key": "token", "value": "{{EMAIL_TOKEN}}"}]}}}, {"name": "3. <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{TEST_EMAIL}}\",\n    \"password\": \"{{TEST_PASSWORD}}\"\n}"}, "url": {"raw": "{{BASE_URL}}/auth/login", "host": ["{{BASE_URL}}"], "path": ["auth", "login"]}}}, {"name": "4. Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{TEST_EMAIL}}\"\n}"}, "url": {"raw": "{{BASE_URL}}/auth/forgot-password", "host": ["{{BASE_URL}}"], "path": ["auth", "forgot-password"]}}}, {"name": "5. <PERSON>set Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{RESET_TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"new_password\": \"NewSecurePass123!\"\n}"}, "url": {"raw": "{{BASE_URL}}/auth/reset-password", "host": ["{{BASE_URL}}"], "path": ["auth", "reset-password"]}}}, {"name": "6. <PERSON><PERSON> 2FA", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{ACCESS_TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"enable_2fa\": true\n}"}, "url": {"raw": "{{BASE_URL}}/auth/2fa-toggle", "host": ["{{BASE_URL}}"], "path": ["auth", "2fa-toggle"]}}}, {"name": "7. <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{TEST_EMAIL}}\",\n    \"otp\": \"{{OTP_CODE}}\"\n}"}, "url": {"raw": "{{BASE_URL}}/auth/login/verify", "host": ["{{BASE_URL}}"], "path": ["auth", "login", "verify"]}}}]}