# PICA - Documentation Technique des Scans

## 🏗️ Architecture des Services

### Structure des Services

```
backend/app/services/
├── openvas_service.py      # Intégration OpenVAS/GVM
├── nmap_service.py         # Scanner de ports Nmap
├── nikto_service.py        # Scanner web Nikto
├── sqlmap_service.py       # Détection injection SQL
├── dirb_service.py         # Énumération répertoires
├── gobuster_service.py     # Énumération rapide
└── zap_service.py          # OWASP ZAP Proxy
```

### Contrôleur Principal

```
backend/app/controllers/scan_controller.py
```

**Routes disponibles :**
- `POST /scan/pentesting/scan/network` - <PERSON>an r<PERSON>eau
- `POST /scan/pentesting/scan/web` - Scan web
- `POST /scan/pentesting/scan/vulnerability` - Scan vulnérabilités
- `POST /scan/pentesting/scan/deep` - Scan complet
- `GET /scan/pentesting/scans` - Historique des scans
- `GET /scan/pentesting/scan/<scan_id>` - Détails d'un scan

---

## 🔧 Configuration des Services

### OpenVAS/GVM
```python
# Configuration dans openvas_service.py
OPENVAS_HOST = "127.0.0.1"
OPENVAS_PORT = 9392
SOCKET_PATH = "/var/lib/docker/volumes/greenbone-community-edition_gvmd_socket_vol/_data/gvmd.sock"
```

### Nmap
```python
# Options par type de scan
SCAN_CONFIGS = {
    'basic': ['-sS', '--script=vuln'],
    'aggressive': ['-A', '--script=vuln,safe'],
    'stealth': ['-T1', '-sS'],
    'comprehensive': ['-p-', '-A', '--script=vuln,safe,discovery']
}
```

### SQLMap
```python
# Configuration par intensité
SQLMAP_OPTIONS = {
    'basic': ['--batch', '--level=1'],
    'aggressive': ['--batch', '--level=5', '--risk=3'],
    'stealth': ['--batch', '--level=1', '--risk=1'],
    'comprehensive': ['--batch', '--level=3', '--risk=2']
}
```

---

## 📊 Structure des Données

### Document Scan MongoDB
```json
{
  "_id": "ObjectId",
  "scan_id": "uuid4",
  "category": "network|web|vulnerability|deep",
  "scan_type": "basic|aggressive|stealth|comprehensive",
  "target": "IP ou URL",
  "ports": "ports spécifiques (optionnel)",
  "status": "running|completed|failed",
  "start_time": "ISO datetime",
  "end_time": "ISO datetime",
  "user_id": "username",
  "tools": ["nmap", "openvas", "metasploit"],
  "config": {
    "description": "Description du scan",
    "intensity": "low|high|minimal|maximum",
    "nmap_options": ["-sS", "--script=vuln"],
    "openvas_profile": "Full and fast"
  },
  "results": {
    "nmap": {
      "status": "completed|failed|unavailable",
      "ports": [
        {
          "port": 80,
          "protocol": "tcp",
          "state": "open",
          "service": "http",
          "version": "Apache 2.4.41"
        }
      ],
      "vulnerabilities": [
        {
          "id": "CVE-2021-44228",
          "name": "Log4Shell",
          "severity": "critical",
          "description": "Remote code execution",
          "tool": "nmap"
        }
      ]
    },
    "openvas": {
      "status": "completed",
      "scan_id": "openvas_scan_id",
      "task_id": "openvas_task_id",
      "target_id": "openvas_target_id"
    },
    "summary": {
      "total_ports": 5,
      "open_ports": 3,
      "total_vulnerabilities": 2,
      "critical": 1,
      "high": 1,
      "medium": 0,
      "low": 0
    }
  }
}
```

---

## 🔄 Flux d'Exécution

### 1. Réception de la Requête
```python
@pentesting_bp.route('/scan/web', methods=['POST'])
@jwt_required()
def web_scan():
    # Validation des paramètres
    # Création de l'entrée MongoDB
    # Lancement du thread de scan
```

### 2. Exécution en Arrière-Plan
```python
def run_web_scan():
    # Initialisation des services
    # Exécution parallèle des outils
    # Mise à jour des résultats en temps réel
    # Finalisation et stockage
```

### 3. Gestion des Erreurs
```python
try:
    # Exécution du service
except subprocess.TimeoutExpired:
    # Gestion timeout
except Exception as e:
    # Gestion erreurs générales
```

---

## 🛠️ Services Détaillés

### NmapService
```python
class NmapService:
    def scan_target(self, target, options, ports):
        # Construction commande
        # Exécution subprocess
        # Parsing résultats XML
        # Retour données structurées
```

**Méthodes disponibles :**
- `is_available()` - Vérifier disponibilité
- `scan_target()` - Scan principal
- `quick_scan()` - Scan rapide
- `stealth_scan()` - Scan furtif
- `aggressive_scan()` - Scan agressif

### OpenVASService
```python
class OpenVASService:
    def scan_target(self, target_hosts, scan_name):
        # Création target OpenVAS
        # Création task
        # Lancement scan
        # Monitoring progression
```

**Méthodes disponibles :**
- `is_available()` - Test connexion GMP
- `create_target()` - Créer cible
- `create_task()` - Créer tâche
- `start_task()` - Démarrer scan
- `get_task_status()` - Statut progression

### SQLMapService
```python
class SQLMapService:
    def scan_target(self, target_url, options, data):
        # Configuration SQLMap
        # Exécution tests injection
        # Parsing résultats
        # Classification vulnérabilités
```

---

## 🔍 Monitoring et Logs

### Logs d'Application
```python
print(f"🟦 Starting {scan_type} network scan on {target}")
print(f"✅ Nmap found {len(ports_found)} ports")
print(f"❌ OpenVAS scan failed: {error}")
```

### Progression Deep Scan
```python
# Mise à jour progression en temps réel
mongo.db.scans.update_one(
    {'scan_id': scan_id},
    {
        '$set': {
            'progress.current_tool': tool,
            'progress.progress_percent': progress
        }
    }
)
```

---

## 🚀 Déploiement et Installation

### Prérequis Système
```bash
# Outils de pentesting
sudo apt install nmap nikto sqlmap dirb gobuster

# OpenVAS/GVM (via Docker)
docker-compose up -d greenbone-community-edition

# OWASP ZAP
sudo apt install zaproxy
```

### Configuration Python
```bash
# Dépendances
pip install -r requirements.txt

# Services spécifiques
pip install python-gvm python-nmap
```

### Variables d'Environnement
```bash
export OPENVAS_HOST="127.0.0.1"
export OPENVAS_PORT="9392"
export MONGODB_URI="mongodb://localhost:27017/pica"
export JWT_SECRET_KEY="your-secret-key"
```

---

## 🧪 Tests et Validation

### Test des Services
```python
# Script de test
python3 backend/test_services.py
```

### Test API
```bash
# Test scan web
curl -X POST http://localhost:5000/scan/pentesting/scan/web \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"target_url": "http://testphp.vulnweb.com", "scan_type": "basic"}'
```

---

## 🔒 Sécurité

### Authentification JWT
- Tous les endpoints protégés par `@jwt_required()`
- Rôles utilisateur (admin/user) pour accès différencié
- Tokens avec expiration configurable

### Isolation des Scans
- Chaque scan dans un thread séparé
- Timeouts configurés pour éviter blocages
- Nettoyage automatique des fichiers temporaires

### Validation des Entrées
- Validation des cibles (IP/URL)
- Sanitisation des paramètres
- Limitation des types de scan par rôle

---

## 📈 Performance

### Optimisations
- Exécution parallèle des outils (Network, Web, Vulnerability)
- Exécution séquentielle avec progression (Deep Scan)
- Mise en cache des résultats OpenVAS
- Nettoyage automatique des anciens scans

### Limitations
- Maximum 5 scans simultanés par utilisateur
- Timeout de 30 minutes par outil
- Taille maximale des résultats : 10MB

---

## 🐛 Dépannage

### Problèmes Courants

1. **Service non disponible**
   ```bash
   # Vérifier installation
   which nmap nikto sqlmap
   ```

2. **OpenVAS non accessible**
   ```bash
   # Vérifier Docker
   docker ps | grep greenbone
   ```

3. **Erreur contexte Flask**
   ```python
   # Solution : récupérer user_id avant thread
   current_user_id = get_jwt_identity()
   ```

### Logs de Debug
```bash
# Logs Flask
tail -f backend/logs/app.log

# Logs MongoDB
tail -f /var/log/mongodb/mongod.log
```
