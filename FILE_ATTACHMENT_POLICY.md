# File Attachment Policy - PICA ITSM System

## 📎 **File Attachment Scope**

File attachments are **ONLY** available for **Investigation Tickets**, not for Incidents.

### ✅ **Where File Attachments ARE Available:**

#### **1. Ticket Creation (CreateTicketForm)**
- Users can attach files when creating new investigation tickets
- Supports multiple file types: documents, images, logs, archives
- Maximum 5 files per ticket, 16MB each
- Files are uploaded during ticket creation process

#### **2. Ticket Updates (TicketDetailsModal)**
- <PERSON>mins can add additional files to existing tickets
- <PERSON><PERSON> can remove files from tickets
- All file operations are logged in ticket timeline
- File management available through AttachmentViewer component

### ❌ **Where File Attachments ARE NOT Available:**

#### **1. Incident Management (IncidentDetailsModal)**
- Incidents do not support file attachments
- Incidents are higher-level security events derived from tickets
- Incident editing focuses on status, assignment, and classification
- Evidence and documentation remain with the original tickets

## 🔄 **Workflow Logic**

```
Investigation Ticket (with files) → Convert to → Incident (no files)
                ↓                                      ↓
        File attachments                        Status management
        Timeline tracking                       Assignment tracking
        Evidence collection                     Escalation handling
```

## 📁 **Supported File Types**

- **Documents**: .pdf, .doc, .docx, .xls, .xlsx
- **Images**: .png, .jpg, .jpeg, .gif
- **Logs/Text**: .txt, .log, .csv
- **Archives**: .zip

## 🔒 **Security & Storage**

- Files stored in `/backend/uploads/tickets/{ticket_number}/`
- Unique filenames prevent conflicts
- File type validation on upload
- Size limits enforced (16MB max)
- Admin-only file deletion capability

## 🎯 **User Experience**

### **For Ticket Creation:**
1. Fill out ticket form
2. Drag & drop files or click to browse
3. Preview selected files
4. Submit ticket with attachments

### **For Ticket Management:**
1. Open ticket details modal
2. View existing attachments
3. Add new files (admin only)
4. Download or remove files (admin only)

### **For Incident Management:**
1. View incident details
2. Edit incident properties (status, assignment, etc.)
3. No file attachment options
4. Reference original ticket for evidence

## 📋 **Implementation Status**

✅ **Completed:**
- File upload in ticket creation
- File management in ticket updates
- AttachmentViewer component
- Backend file handling
- Timeline integration
- Security validation

✅ **Correctly Excluded:**
- File attachments in incident management
- File upload in incident creation/editing
- Attachment options in IncidentDetailsModal

This policy ensures that file attachments are properly scoped to investigation tickets where evidence collection is most relevant, while keeping incident management focused on high-level status and assignment tracking.
