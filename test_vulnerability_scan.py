#!/usr/bin/env python3
"""
Test script for the new vulnerability scan implementation
"""

import requests
import json
import time
import sys

BASE_URL = "http://localhost:5000/api/scan/pentesting"

def get_auth_token():
    """Get authentication token"""
    try:
        login_data = {
            "email": "<EMAIL>",
            "password": "admin123"
        }
        
        response = requests.post(
            "http://localhost:5000/api/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            return data.get('access_token')
        else:
            print(f"❌ Login failed: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def start_vulnerability_scan(token, target="*************", scan_type="basic"):
    """Start a vulnerability scan"""
    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        scan_data = {
            "target": target,
            "scan_type": scan_type,
            "ports": ""
        }
        
        print(f"🟪 Starting {scan_type} vulnerability scan on {target}...")
        response = requests.post(
            f"{BASE_URL}/scan/vulnerability",
            json=scan_data,
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            scan_id = data.get('scan_id')
            print(f"✅ Vulnerability scan started successfully!")
            print(f"   Scan ID: {scan_id}")
            print(f"   Tools: {data.get('tools', [])}")
            print(f"   Status: {data.get('status')}")
            return scan_id
        else:
            print(f"❌ Scan failed: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Scan error: {e}")
        return None

def check_scan_status(token, scan_id):
    """Check scan status"""
    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(
            f"{BASE_URL}/scan/{scan_id}",
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            status = data.get('status')
            progress = data.get('progress', 0)
            current_tool = data.get('current_tool', 'unknown')
            
            print(f"📊 Scan Status: {status}")
            print(f"   Progress: {progress}%")
            print(f"   Current Tool: {current_tool}")
            
            if status == 'completed':
                results = data.get('results', {})
                summary = results.get('summary', {})
                print(f"   Results Summary:")
                print(f"     Total Vulnerabilities: {summary.get('total_vulnerabilities', 0)}")
                print(f"     Critical: {summary.get('critical_severity', 0)}")
                print(f"     High: {summary.get('high_severity', 0)}")
                print(f"     Medium: {summary.get('medium_severity', 0)}")
                print(f"     Low: {summary.get('low_severity', 0)}")
                
            return status
        else:
            print(f"❌ Status check failed: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Status check error: {e}")
        return None

def get_scan_logs(token, scan_id):
    """Get scan logs"""
    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(
            f"{BASE_URL}/scan/logs/{scan_id}/live",
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            logs = data.get('latest_logs', [])
            
            if logs:
                print(f"📋 Recent Logs ({len(logs)} entries):")
                for log in logs[-5:]:  # Show last 5 logs
                    timestamp = log.get('timestamp', '')[:19]
                    level = log.get('level', '').upper()
                    message = log.get('message', '')[:80]
                    print(f"   {timestamp} [{level}] {message}")
            else:
                print("📋 No logs available yet")
                
        else:
            print(f"❌ Logs check failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Logs error: {e}")

def main():
    """Main test function"""
    print("🧪 Testing New Vulnerability Scanner")
    print("=" * 50)
    
    # Get authentication token
    print("1. Getting authentication token...")
    token = get_auth_token()
    if not token:
        print("❌ Cannot proceed without token")
        return 1
    
    print("✅ Authentication successful")
    
    # Start vulnerability scan
    print("\n2. Starting vulnerability scan...")
    target = "*************"  # Test target
    scan_id = start_vulnerability_scan(token, target, "basic")
    
    if not scan_id:
        print("❌ Cannot proceed without scan ID")
        return 1
    
    # Monitor scan progress
    print(f"\n3. Monitoring scan progress...")
    max_checks = 20  # Maximum 20 checks (10 minutes)
    check_count = 0
    
    while check_count < max_checks:
        print(f"\n--- Check {check_count + 1}/{max_checks} ---")
        
        # Check status
        status = check_scan_status(token, scan_id)
        
        if status in ['completed', 'failed']:
            print(f"\n🏁 Scan finished with status: {status}")
            break
        elif status == 'running':
            print("🔄 Scan still running...")
            
            # Get logs
            get_scan_logs(token, scan_id)
        
        check_count += 1
        if check_count < max_checks:
            print("⏳ Waiting 30 seconds before next check...")
            time.sleep(30)
    
    if check_count >= max_checks:
        print("⏰ Maximum checks reached - scan may still be running")
    
    print(f"\n📊 Final scan status check...")
    check_scan_status(token, scan_id)
    
    print(f"\n✅ Test completed!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
