#!/usr/bin/env python3
"""
Script de migration intelligent pour convertir les user_id de username vers ObjectId
avec mapping automatique des utilisateurs orphelins.
"""

from app.extensions import mongo
from app import create_app
from bson import ObjectId

def get_user_mapping():
    """Crée un mapping intelligent des anciens usernames vers les ObjectId actuels"""
    app = create_app()
    with app.app_context():
        # Récupérer tous les utilisateurs
        users = list(mongo.db.users.find({}, {'_id': 1, 'username': 1, 'email': 1, 'role': 1}))
        
        # Trouver le premier admin
        admin_user = next((u for u in users if u['role'] == 'admin'), None)
        
        # C<PERSON>er le mapping
        mapping = {}
        
        if admin_user:
            # Mapper les anciens usernames vers l'admin principal
            mapping['admin'] = admin_user['_id']
            mapping['zech'] = admin_user['_id']  # Vous êtes probablement l'admin
            print(f"📋 Mapping créé:")
            print(f"  - 'admin' -> {admin_user['username']} ({admin_user['_id']})")
            print(f"  - 'zech' -> {admin_user['username']} ({admin_user['_id']})")
        
        # Mapper les usernames existants vers leurs ObjectId
        for user in users:
            mapping[user['username']] = user['_id']
            
        return mapping

def migrate_with_mapping():
    """Migre les user_id avec le mapping intelligent"""
    print("🚀 Migration intelligente des user_id...")
    
    app = create_app()
    with app.app_context():
        # Obtenir le mapping
        mapping = get_user_mapping()
        
        # Migrer analyses_scan
        print("\n🔄 Migration analyses_scan...")
        analyses = list(mongo.db.analyses_scan.find({}))
        migrated_analyses = 0
        
        for analysis in analyses:
            user_id = analysis.get('user_id')
            
            # Si c'est déjà un ObjectId, passer
            if isinstance(user_id, ObjectId):
                continue
                
            # Si c'est un string, utiliser le mapping
            if isinstance(user_id, str) and user_id in mapping:
                result = mongo.db.analyses_scan.update_one(
                    {'_id': analysis['_id']},
                    {'$set': {'user_id': mapping[user_id]}}
                )
                if result.modified_count > 0:
                    migrated_analyses += 1
                    print(f"  ✅ Analyse {analysis['analysis_id']}: '{user_id}' -> {mapping[user_id]}")
        
        # Migrer scans
        print("\n🔄 Migration scans...")
        scans = list(mongo.db.scans.find({}))
        migrated_scans = 0
        
        for scan in scans:
            user_id = scan.get('user_id')
            
            # Si user_id est None, ignorer
            if user_id is None:
                continue
                
            # Si c'est déjà un ObjectId, passer
            if isinstance(user_id, ObjectId):
                continue
                
            # Si c'est un string, utiliser le mapping
            if isinstance(user_id, str) and user_id in mapping:
                result = mongo.db.scans.update_one(
                    {'_id': scan['_id']},
                    {'$set': {'user_id': mapping[user_id]}}
                )
                if result.modified_count > 0:
                    migrated_scans += 1
                    if migrated_scans <= 5:  # Afficher seulement les 5 premiers
                        print(f"  ✅ Scan {scan.get('scan_id', 'unknown')}: '{user_id}' -> {mapping[user_id]}")
                    elif migrated_scans == 6:
                        print(f"  ... (migration en cours)")
        
        print(f"\n📊 Résultats:")
        print(f"  - Analyses migrées: {migrated_analyses}")
        print(f"  - Scans migrés: {migrated_scans}")
        
        # Vérification finale
        print("\n🔍 Vérification...")
        analyses_remaining = mongo.db.analyses_scan.count_documents({'user_id': {'$type': 'string'}})
        scans_remaining = mongo.db.scans.count_documents({'user_id': {'$type': 'string'}})
        
        print(f"  - Analyses avec user_id string restants: {analyses_remaining}")
        print(f"  - Scans avec user_id string restants: {scans_remaining}")
        
        if analyses_remaining == 0 and scans_remaining == 0:
            print("  ✅ Migration réussie ! Tous les user_id sont maintenant des ObjectId.")
        else:
            print("  ⚠️ Il reste des user_id en string à migrer.")

def test_analytics_after_migration():
    """Teste que les analytics fonctionnent après migration"""
    print("\n🧪 Test des analytics après migration...")
    
    app = create_app()
    with app.app_context():
        # Importer le modèle d'analyse
        from app.models.analysis import AnalysisManager
        
        # Tester avec le premier admin
        admin_user = mongo.db.users.find_one({'role': 'admin'})
        if admin_user:
            user_id = str(admin_user['_id'])
            
            # Tester get_user_analyses
            try:
                result = AnalysisManager.get_user_analyses(user_id)
                print(f"  ✅ get_user_analyses: {result['total']} analyses trouvées")
            except Exception as e:
                print(f"  ❌ get_user_analyses: {e}")
            
            # Tester get_analysis_statistics
            try:
                stats = AnalysisManager.get_analysis_statistics(user_id)
                print(f"  ✅ get_analysis_statistics: {stats['total_analyses']} analyses")
            except Exception as e:
                print(f"  ❌ get_analysis_statistics: {e}")

if __name__ == "__main__":
    migrate_with_mapping()
    test_analytics_after_migration()
    print("\n✅ Migration intelligente terminée !")
