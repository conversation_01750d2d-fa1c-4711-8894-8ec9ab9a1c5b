# Tests Postman - API de Détection de Phishing PICA

## Vue d'ensemble

Cette collection Postman contient tous les tests nécessaires pour valider l'API de détection de phishing de PICA. Elle inclut des tests pour l'analyse d'URLs, la gestion des liens suspects, et les fonctions d'administration.

## Fichiers inclus

- `PICA_Phishing_Detection.postman_collection.json` - Collection principale des tests
- `PICA_Local_Environment.postman_environment.json` - Environnement de test local
- `README.md` - Ce guide d'utilisation

## Installation

### 1. Importer dans Postman

1. Ouvrez Postman
2. Cliquez sur "Import" dans le coin supérieur gauche
3. Sélectionnez les fichiers JSON ou glissez-déposez les dans Postman
4. Importez d'abord l'environnement, puis la collection

### 2. Configuration de l'environnement

1. Sélectionnez l'environnement "PICA Local Environment" dans le menu déroulant
2. Vérifiez que les variables suivantes sont configurées :
   - `base_url` : http://localhost:5000
   - `admin_email` : <EMAIL>
   - `admin_password` : admin123

## Structure de la collection

### 1. Authentication
- **Login** : Authentification et récupération du token JWT

### 2. Phishing Service Status
- **Get Service Status** : Vérification du statut du service

### 3. URL Analysis
- **Quick Check - Safe URL** : Test avec une URL sûre
- **Quick Check - Suspicious URL** : Test avec une URL suspecte
- **Full Analysis - Async** : Analyse complète asynchrone
- **Get Analysis Status** : Vérification du statut d'une analyse

### 4. Analysis History
- **Get User Analysis History** : Historique des analyses de l'utilisateur
- **Get Active Analyses** : Analyses en cours

### 5. Suspicious Links
- **Get Suspicious Links** : Liste des liens suspects
- **Get Suspicious Link Details** : Détails d'un lien suspect
- **Verify Suspicious Link (Admin)** : Vérification d'un lien (admin)

### 6. Admin Functions
- **Get All Analyses (Admin)** : Toutes les analyses (admin)
- **Get Phishing Statistics (Admin)** : Statistiques générales (admin)
- **Get Suspicious Links Stats (Admin)** : Statistiques des liens suspects (admin)
- **Export Suspicious Links (Admin)** : Export des données (admin)

### 7. Error Handling Tests
- **Quick Check - Invalid URL** : Test avec URL invalide
- **Get Non-existent Analysis** : Test avec ID inexistant

## Utilisation

### Exécution manuelle

1. **Commencez toujours par l'authentification** :
   - Exécutez "Authentication > Login"
   - Le token sera automatiquement sauvegardé

2. **Testez le service** :
   - Exécutez "Phishing Service Status > Get Service Status"

3. **Testez l'analyse d'URLs** :
   - Commencez par "URL Analysis > Quick Check - Safe URL"
   - Puis "URL Analysis > Quick Check - Suspicious URL"

4. **Testez les fonctions avancées** :
   - Historique, liens suspects, fonctions admin

### Exécution automatique (Runner)

1. Cliquez sur la collection "PICA - Phishing Detection API"
2. Cliquez sur "Run collection"
3. Sélectionnez l'environnement "PICA Local Environment"
4. Configurez les options :
   - Iterations : 1
   - Delay : 1000ms (pour éviter la surcharge)
5. Cliquez sur "Run PICA - Phishing Detection API"

## Variables automatiques

La collection utilise des variables qui sont automatiquement mises à jour :

- `access_token` : Token JWT (mis à jour lors du login)
- `analysis_id` : ID de la dernière analyse (mis à jour lors des analyses)
- `suspicious_link_id` : ID du premier lien suspect (mis à jour lors de la récupération)

## Tests automatisés

Chaque requête inclut des tests automatisés qui vérifient :

- **Codes de statut HTTP** appropriés
- **Structure des réponses** JSON
- **Présence des champs** requis
- **Logique métier** (ex: score de risque, statuts)

### Exemples de tests

```javascript
// Vérification du code de statut
pm.test('Status code is 200', function () {
    pm.response.to.have.status(200);
});

// Vérification de la structure de réponse
pm.test('Response has required fields', function () {
    const response = pm.response.json();
    pm.expect(response.result).to.not.be.undefined;
    pm.expect(response.result.risk_score).to.be.a('number');
});

// Vérification de la logique métier
pm.test('URL is suspicious', function () {
    const response = pm.response.json();
    pm.expect(response.result.risk_score).to.be.above(30);
});
```

## Prérequis

### Serveur backend

Assurez-vous que le serveur PICA est démarré :

```bash
cd backend
python app.py
```

### Base de données

Vérifiez que MongoDB est en cours d'exécution et que la collection `suspicious_links` est initialisée :

```bash
cd backend
python3 init_suspicious_links.py
```

### Données de test

Pour des tests optimaux, assurez-vous d'avoir :

1. **Un utilisateur admin** avec les identifiants configurés
2. **Quelques liens suspects** dans la base de données
3. **Le service de phishing** opérationnel

## Dépannage

### Erreurs d'authentification

- Vérifiez que les identifiants admin sont corrects
- Assurez-vous que le serveur backend est démarré
- Vérifiez que l'URL de base est correcte

### Erreurs de connexion

- Vérifiez que MongoDB est en cours d'exécution
- Assurez-vous que le port 5000 est disponible
- Vérifiez les logs du serveur backend

### Tests échoués

- Exécutez d'abord le test de login
- Vérifiez que l'environnement est sélectionné
- Consultez les logs de la console Postman

## Personnalisation

### Ajouter de nouveaux tests

1. Dupliquez une requête existante
2. Modifiez l'URL et les paramètres
3. Adaptez les tests automatisés
4. Ajoutez la documentation appropriée

### Modifier les URLs de test

Modifiez les variables d'environnement :
- `test_safe_url`
- `test_suspicious_url`
- `test_phishing_url`

### Ajouter des assertions

Ajoutez des tests dans l'onglet "Tests" de chaque requête :

```javascript
pm.test('Custom test', function () {
    const response = pm.response.json();
    // Votre logique de test ici
});
```

## Rapports

### Génération de rapports

1. Exécutez la collection avec le Runner
2. Cliquez sur "Export Results" après l'exécution
3. Choisissez le format (JSON, HTML)

### Intégration CI/CD

Utilisez Newman pour l'intégration continue :

```bash
npm install -g newman
newman run PICA_Phishing_Detection.postman_collection.json \
  -e PICA_Local_Environment.postman_environment.json \
  --reporters html,json
```

## Support

Pour toute question ou problème :

1. Vérifiez les logs du serveur backend
2. Consultez la documentation de l'API
3. Vérifiez les tests automatisés dans Postman
4. Contactez l'équipe de développement PICA
