{
	"info": {
		"_postman_id": "phishing-detection-api",
		"name": "PICA - Phishing Detection API",
		"description": "Collection de tests pour l'API de détection de phishing de PICA",
		"schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
	},
	"auth": {
		"type": "bearer",
		"bearer": [
			{
				"key": "token",
				"value": "{{access_token}}",
				"type": "string"
			}
		]
	},
	"variable": [
		{
			"key": "base_url",
			"value": "http://localhost:5000",
			"type": "string"
		},
		{
			"key": "access_token",
			"value": "",
			"type": "string"
		},
		{
			"key": "analysis_id",
			"value": "",
			"type": "string"
		},
		{
			"key": "suspicious_link_id",
			"value": "",
			"type": "string"
		}
	],
	"item": [
		{
			"name": "Authentication",
			"item": [
				{
					"name": "Login",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"if (pm.response.code === 200) {",
									"    const response = pm.response.json();",
									"    pm.collectionVariables.set('access_token', response.access_token);",
									"    pm.test('Login successful', function () {",
									"        pm.expect(response.access_token).to.not.be.undefined;",
									"    });",
									"} else {",
									"    pm.test('Login failed', function () {",
									"        pm.expect(pm.response.code).to.equal(200);",
									"    });",
									"}"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"admin123\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/auth/login",
							"host": ["{{base_url}}"],
							"path": ["auth", "login"]
						}
					}
				}
			]
		},
		{
			"name": "Phishing Service Status",
			"item": [
				{
					"name": "Get Service Status",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"pm.test('Status code is 200', function () {",
									"    pm.response.to.have.status(200);",
									"});",
									"",
									"pm.test('Response has service info', function () {",
									"    const response = pm.response.json();",
									"    pm.expect(response.service).to.equal('Phishing Detection Service');",
									"    pm.expect(response.version).to.not.be.undefined;",
									"    pm.expect(response.statistics).to.not.be.undefined;",
									"});"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{base_url}}/api/phishing/",
							"host": ["{{base_url}}"],
							"path": ["api", "phishing", ""]
						}
					}
				}
			]
		},
		{
			"name": "URL Analysis",
			"item": [
				{
					"name": "Quick Check - Safe URL",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"pm.test('Status code is 200', function () {",
									"    pm.response.to.have.status(200);",
									"});",
									"",
									"pm.test('Analysis completed', function () {",
									"    const response = pm.response.json();",
									"    pm.expect(response.result).to.not.be.undefined;",
									"    pm.expect(response.result.risk_score).to.be.a('number');",
									"    pm.expect(response.result.likelihood).to.not.be.undefined;",
									"});",
									"",
									"pm.test('URL is safe', function () {",
									"    const response = pm.response.json();",
									"    pm.expect(response.result.risk_score).to.be.below(40);",
									"});"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n    \"url\": \"https://google.com\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/api/phishing/quick-check",
							"host": ["{{base_url}}"],
							"path": ["api", "phishing", "quick-check"]
						}
					}
				},
				{
					"name": "Quick Check - Suspicious URL",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"pm.test('Status code is 200', function () {",
									"    pm.response.to.have.status(200);",
									"});",
									"",
									"pm.test('Analysis completed', function () {",
									"    const response = pm.response.json();",
									"    pm.expect(response.result).to.not.be.undefined;",
									"    pm.expect(response.result.risk_score).to.be.a('number');",
									"});",
									"",
									"pm.test('URL is suspicious', function () {",
									"    const response = pm.response.json();",
									"    pm.expect(response.result.risk_score).to.be.above(30);",
									"});"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n    \"url\": \"http://fake-paypal.tk/login\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/api/phishing/quick-check",
							"host": ["{{base_url}}"],
							"path": ["api", "phishing", "quick-check"]
						}
					}
				},
				{
					"name": "Full Analysis - Async",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"pm.test('Status code is 202', function () {",
									"    pm.response.to.have.status(202);",
									"});",
									"",
									"pm.test('Analysis started', function () {",
									"    const response = pm.response.json();",
									"    pm.expect(response.analysis_id).to.not.be.undefined;",
									"    pm.expect(response.status).to.equal('running');",
									"    pm.collectionVariables.set('analysis_id', response.analysis_id);",
									"});"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n    \"url\": \"https://example.com\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/api/phishing/analyze",
							"host": ["{{base_url}}"],
							"path": ["api", "phishing", "analyze"]
						}
					}
				},
				{
					"name": "Get Analysis Status",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"pm.test('Status code is 200', function () {",
									"    pm.response.to.have.status(200);",
									"});",
									"",
									"pm.test('Analysis status available', function () {",
									"    const response = pm.response.json();",
									"    pm.expect(response.analysis_id).to.not.be.undefined;",
									"    pm.expect(response.status).to.not.be.undefined;",
									"    pm.expect(response.progress).to.be.a('number');",
									"});"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{base_url}}/api/phishing/status/{{analysis_id}}",
							"host": ["{{base_url}}"],
							"path": ["api", "phishing", "status", "{{analysis_id}}"]
						}
					}
				}
			]
		},
		{
			"name": "Suspicious Links",
			"item": [
				{
					"name": "Get Suspicious Links",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"pm.test('Status code is 200', function () {",
									"    pm.response.to.have.status(200);",
									"});",
									"",
									"pm.test('Suspicious links data available', function () {",
									"    const response = pm.response.json();",
									"    pm.expect(response.suspicious_links).to.be.an('array');",
									"    pm.expect(response.statistics).to.not.be.undefined;",
									"    pm.expect(response.pagination).to.not.be.undefined;",
									"});",
									"",
									"// Save first suspicious link ID for other tests",
									"const response = pm.response.json();",
									"if (response.suspicious_links.length > 0) {",
									"    pm.collectionVariables.set('suspicious_link_id', response.suspicious_links[0]._id);",
									"}"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{base_url}}/api/phishing/suspicious-links?page=1&limit=20&min_score=30",
							"host": ["{{base_url}}"],
							"path": ["api", "phishing", "suspicious-links"],
							"query": [
								{
									"key": "page",
									"value": "1"
								},
								{
									"key": "limit",
									"value": "20"
								},
								{
									"key": "min_score",
									"value": "30"
								}
							]
						}
					}
				},
				{
					"name": "Get Suspicious Link Details",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"pm.test('Status code is 200', function () {",
									"    pm.response.to.have.status(200);",
									"});",
									"",
									"pm.test('Link details available', function () {",
									"    const response = pm.response.json();",
									"    pm.expect(response.suspicious_link).to.not.be.undefined;",
									"    pm.expect(response.related_analyses).to.be.an('array');",
									"});"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{base_url}}/api/phishing/suspicious-links/{{suspicious_link_id}}",
							"host": ["{{base_url}}"],
							"path": ["api", "phishing", "suspicious-links", "{{suspicious_link_id}}"]
						}
					}
				},
				{
					"name": "Verify Suspicious Link (Admin)",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"pm.test('Status code is 200', function () {",
									"    pm.response.to.have.status(200);",
									"});",
									"",
									"pm.test('Link verified successfully', function () {",
									"    const response = pm.response.json();",
									"    pm.expect(response.message).to.include('verified successfully');",
									"    pm.expect(response.status).to.equal('verified');",
									"});"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n    \"status\": \"verified\",\n    \"notes\": \"Confirmed phishing site through manual verification\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/api/phishing/suspicious-links/{{suspicious_link_id}}/verify",
							"host": ["{{base_url}}"],
							"path": ["api", "phishing", "suspicious-links", "{{suspicious_link_id}}", "verify"]
						}
					}
				}
			]
		},
		{
			"name": "Admin Functions",
			"item": [
				{
					"name": "Get All Analyses (Admin)",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"pm.test('Status code is 200', function () {",
									"    pm.response.to.have.status(200);",
									"});",
									"",
									"pm.test('Admin data available', function () {",
									"    const response = pm.response.json();",
									"    pm.expect(response.analyses).to.be.an('array');",
									"    pm.expect(response.statistics).to.not.be.undefined;",
									"    pm.expect(response.pagination).to.not.be.undefined;",
									"});"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{base_url}}/api/phishing/admin/all-analyses?page=1&limit=50",
							"host": ["{{base_url}}"],
							"path": ["api", "phishing", "admin", "all-analyses"],
							"query": [
								{
									"key": "page",
									"value": "1"
								},
								{
									"key": "limit",
									"value": "50"
								}
							]
						}
					}
				},
				{
					"name": "Get Phishing Statistics (Admin)",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"pm.test('Status code is 200', function () {",
									"    pm.response.to.have.status(200);",
									"});",
									"",
									"pm.test('Statistics available', function () {",
									"    const response = pm.response.json();",
									"    pm.expect(response.overview).to.not.be.undefined;",
									"    pm.expect(response.analysis_types).to.not.be.undefined;",
									"    pm.expect(response.risk_distribution).to.not.be.undefined;",
									"});"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{base_url}}/api/phishing/admin/stats",
							"host": ["{{base_url}}"],
							"path": ["api", "phishing", "admin", "stats"]
						}
					}
				},
				{
					"name": "Get Suspicious Links Stats (Admin)",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"pm.test('Status code is 200', function () {",
									"    pm.response.to.have.status(200);",
									"});",
									"",
									"pm.test('Suspicious links statistics available', function () {",
									"    const response = pm.response.json();",
									"    pm.expect(response.overview).to.not.be.undefined;",
									"    pm.expect(response.risk_distribution).to.not.be.undefined;",
									"    pm.expect(response.top_domains).to.be.an('array');",
									"});"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{base_url}}/api/phishing/suspicious-links/stats",
							"host": ["{{base_url}}"],
							"path": ["api", "phishing", "suspicious-links", "stats"]
						}
					}
				},
				{
					"name": "Export Suspicious Links (Admin)",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"pm.test('Status code is 200', function () {",
									"    pm.response.to.have.status(200);",
									"});",
									"",
									"pm.test('Export data available', function () {",
									"    const response = pm.response.json();",
									"    pm.expect(response.export_data).to.be.an('array');",
									"    pm.expect(response.metadata).to.not.be.undefined;",
									"});"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{base_url}}/api/phishing/suspicious-links/export?status=active&min_score=50",
							"host": ["{{base_url}}"],
							"path": ["api", "phishing", "suspicious-links", "export"],
							"query": [
								{
									"key": "status",
									"value": "active"
								},
								{
									"key": "min_score",
									"value": "50"
								}
							]
						}
					}
				}
			]
		},
		{
			"name": "Error Handling Tests",
			"item": [
				{
					"name": "Quick Check - Invalid URL",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"pm.test('Status code is 400', function () {",
									"    pm.response.to.have.status(400);",
									"});",
									"",
									"pm.test('Error message present', function () {",
									"    const response = pm.response.json();",
									"    pm.expect(response.error).to.not.be.undefined;",
									"});"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n    \"url\": \"\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/api/phishing/quick-check",
							"host": ["{{base_url}}"],
							"path": ["api", "phishing", "quick-check"]
						}
					}
				},
				{
					"name": "Get Non-existent Analysis",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"pm.test('Status code is 404', function () {",
									"    pm.response.to.have.status(404);",
									"});",
									"",
									"pm.test('Error message present', function () {",
									"    const response = pm.response.json();",
									"    pm.expect(response.error).to.include('not found');",
									"});"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{base_url}}/api/phishing/status/non-existent-id",
							"host": ["{{base_url}}"],
							"path": ["api", "phishing", "status", "non-existent-id"]
						}
					}
				}
			]
		}
	]
},
		{
			"name": "Analysis History",
			"item": [
				{
					"name": "Get User Analysis History",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"pm.test('Status code is 200', function () {",
									"    pm.response.to.have.status(200);",
									"});",
									"",
									"pm.test('History data available', function () {",
									"    const response = pm.response.json();",
									"    pm.expect(response.analyses).to.be.an('array');",
									"    pm.expect(response.pagination).to.not.be.undefined;",
									"});"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{base_url}}/api/phishing/history?page=1&limit=10",
							"host": ["{{base_url}}"],
							"path": ["api", "phishing", "history"],
							"query": [
								{
									"key": "page",
									"value": "1"
								},
								{
									"key": "limit",
									"value": "10"
								}
							]
						}
					}
				},
				{
					"name": "Get Active Analyses",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"pm.test('Status code is 200', function () {",
									"    pm.response.to.have.status(200);",
									"});",
									"",
									"pm.test('Active analyses data available', function () {",
									"    const response = pm.response.json();",
									"    pm.expect(response.active_analyses).to.be.an('array');",
									"    pm.expect(response.count).to.be.a('number');",
									"});"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{base_url}}/api/phishing/active-analyses",
							"host": ["{{base_url}}"],
							"path": ["api", "phishing", "active-analyses"]
						}
					}
				}
			]
		}
