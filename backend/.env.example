# PICA Configuration Example
# Copy this file to .env and fill in your actual values

# Security Keys
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here
SECURITY_PASSWORD_SALT=your-password-salt-here

# Database
MONGO_URI=mongodb://localhost:27017/pica

# Email Configuration (for notifications)
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Telegram Bot Configuration (for notifications)
# Create a bot with @<PERSON><PERSON><PERSON><PERSON> on Telegram to get this token
TELEGRAM_BOT_TOKEN=your-telegram-bot-token-here

# OpenVAS Configuration
OPENVAS_USERNAME=admin
OPENVAS_PASSWORD=admin
OPENVAS_SOCKET_PATH=/var/run/gvmd/gvmd.sock

# Nessus Configuration
NESSUS_URL=https://localhost:8834
NESSUS_ACCESS_KEY=your-nessus-access-key
NESSUS_SECRET_KEY=your-nessus-secret-key
NESSUS_VERIFY_SSL=False
