"""
Contrôleur pour la gestion des analyses de malware dans PICA.
Fournit les endpoints API pour l'analyse de fichiers et la détection de malware.
"""

import os
import uuid
import tempfile
from datetime import datetime, timezone
from flask import Blueprint, request, jsonify, current_app
from werkzeug.utils import secure_filename
from typing import Dict, Any, List

from ..services.malware_service import MalwareService
from ..services.malwareDetector import MalwareDetector
from ..models.malware_model import (
    save_malware_analysis_to_db,
    get_malware_analyses_from_db,
    get_malware_analysis_by_id,
    get_malware_stats_from_db
)
from ..utils.response_utils import success_response, error_response
from ..utils.auth_utils import require_auth, get_current_user

# Créer le blueprint pour les routes malware
malware_bp = Blueprint('malware', __name__, url_prefix='/api/malware')

# Initialiser le service malware
malware_service = None

def get_malware_service():
    """Récupère l'instance du service malware (singleton)."""
    global malware_service
    if malware_service is None:
        malware_service = MalwareService()
    return malware_service

def get_packing_analysis_json(packing_analysis: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert packing analysis to JSON format.

    Args:
        packing_analysis: Dictionary containing packing analysis results

    Returns:
        Dictionary with packing analysis in JSON format
    """
    result = {
        "status": "WHITELISTED" if packing_analysis.get('is_whitelisted', False) else
                 ("DETECTED" if packing_analysis.get('is_packed', False) else "NOT DETECTED"),
        "is_whitelisted": packing_analysis.get('is_whitelisted', False),
        "is_packed": packing_analysis.get('is_packed', False),
        "confidence": packing_analysis.get('confidence', 'unknown'),
        "packer_name": packing_analysis.get('packer_name'),
        "indicators": packing_analysis.get('indicators', []),
        "sections": [],
        "suspicious_imports": [],
        "unpacking_sequences": []
    }

    # Process sections
    if 'sections' in packing_analysis and packing_analysis['sections']:
        for sec in packing_analysis['sections']:
            section_data = {
                "name": sec.get('name', 'N/A'),
                "size": sec.get('raw_size', 0),
                "entropy": round(sec.get('entropy', 0), 2),
                "executable": bool(sec.get('characteristics', 0) & 0x20000000),
                "suspicious": sec.get('is_suspicious', False)
            }
            result["sections"].append(section_data)

    # Process suspicious imports
    if 'suspicious_imports' in packing_analysis and packing_analysis['suspicious_imports']:
        for imp in packing_analysis['suspicious_imports']:
            import_data = {
                "api": imp.get('api'),
                "score": imp.get('score', 0)
            }
            result["suspicious_imports"].append(import_data)

    # Process unpacking sequences
    if 'unpacking_sequences_found' in packing_analysis and packing_analysis['unpacking_sequences_found']:
        result["unpacking_sequences"] = [list(seq) for seq in packing_analysis['unpacking_sequences_found']]

    return result

def get_malware_analysis_json(results: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert malware analysis results to JSON format.

    Args:
        results: Dictionary containing malware analysis results

    Returns:
        Dictionary with malware analysis in JSON format
    """
    # Initialize scoring and detection details
    malware_score = 0
    max_score = 50
    detection_details = []

    # Define scoring weights
    SCORE_WEIGHTS = {
        'suspicious_imports': 3,
        'suspicious_exports': 2,
        'suspicious_sections': 4,
        'high_entropy': 3,
        'suspicious_strings': 2,
        'anti_debugging': 3,
        'packed': 5,
        'iocs': 2,
        'yara_matches': 3,
        'packing_indicators': 5,
        'office_macros': 10,
        'document_threats': 5
    }

    # Initialize result structure
    result = {
        "file_info": {
            "filename": results.get('filename', 'Unknown'),
            "file_type": results.get('file_type', {}).get('mime_type', 'Unknown'),
            "hashes": results.get('hashes', {})
        },
        "analysis_details": {
            "sections": [],
            "imports": [],
            "exports": [],
            "entropy_analysis": [],
            "indicators": [],
            "suspicious_strings": [],
            "macros": {},
            "packing_analysis": {}
        },
        "suspicious_apis": [],
        "yara_matches": [],
        "iocs": {
            "ips": results.get('iocs', {}).get('ip_addresses', []),
            "domains": results.get('iocs', {}).get('domains', []),
            "urls": results.get('iocs', {}).get('urls', []),
            "emails": results.get('iocs', {}).get('email_addresses', [])
        },
        "embedded_strings": [],
        "detection_details": [],
        "warnings": [],
        "error": None
    }

    # Process errors
    if results.get('status') == 'error':
        result["error"] = results.get('message', 'Unknown error')
        return result

    # Process suspicious strings
    suspicious_strings = results.get('suspicious_strings', {})
    if suspicious_strings.get('suspicious_count', 0) > 0:
        result["analysis_details"]["suspicious_strings"] = suspicious_strings.get('suspicious_strings', [])
        malware_score += min(SCORE_WEIGHTS['suspicious_strings'], suspicious_strings['suspicious_count'] * 0.5)
        detection_details.append(f"Found {suspicious_strings['suspicious_count']} suspicious strings")

    # Process IOCs
    iocs = results.get('iocs', {})
    ioc_count = (len(iocs.get('ip_addresses', [])) +
                len(iocs.get('domains', [])) +
                len(iocs.get('urls', [])) +
                len(iocs.get('email_addresses', [])))

    if ioc_count > 0:
        malware_score += min(SCORE_WEIGHTS['iocs'], ioc_count * 0.5)
        detection_details.append(f"Found {ioc_count} IOCs (IPs, domains, URLs, emails)")

    # Process PE analysis if available
    pe_analysis = results.get('pe_analysis')
    if pe_analysis and not pe_analysis.get('error'):

        # Process sections
        if 'sections' in pe_analysis:
            for section in pe_analysis['sections']:
                section_data = {
                    "name": section.get('name', ''),
                    "virtual_address": section.get('virtual_address', ''),
                    "virtual_size": section.get('virtual_size', ''),
                    "raw_size": section.get('raw_size', ''),
                    "entropy": round(section.get('entropy', 0), 2),
                    "suspicious": section.get('entropy', 0) > 7.0,
                    "reasons": []
                }

                if section_data["entropy"] > 7.0:
                    section_data["reasons"].append("High entropy")
                    malware_score += SCORE_WEIGHTS['high_entropy'] * 0.5
                    detection_details.append(f"High entropy section '{section['name']}': {section['entropy']:.2f}")

                result["analysis_details"]["sections"].append(section_data)

        # Process suspicious imports
        if 'suspicious_imports' in pe_analysis and pe_analysis['suspicious_imports']:
            result["suspicious_apis"] = pe_analysis['suspicious_imports']
            malware_score += min(SCORE_WEIGHTS['suspicious_imports'], len(pe_analysis['suspicious_imports']) * 0.5)
            detection_details.append(f"Found {len(pe_analysis['suspicious_imports'])} suspicious imports")

        # Process packer detection
        if 'packer_detected' in pe_analysis and pe_analysis['packer_detected']:
            malware_score += SCORE_WEIGHTS['packed']
            detection_details.append(f"Packer detected: {pe_analysis['packer_detected']}")
            result["analysis_details"]["packing_analysis"] = {
                "status": "DETECTED",
                "packer_name": pe_analysis['packer_detected'],
                "is_packed": True
            }

    # Process YARA matches
    yara_matches = results.get('yara_matches', [])
    if yara_matches:
        result["yara_matches"] = yara_matches
        malware_score += SCORE_WEIGHTS['yara_matches'] * len(yara_matches)
        detection_details.append(f"Found {len(yara_matches)} YARA rule matches")

    # Process advanced analysis
    advanced_analysis = results.get('advanced_analysis', {})
    if advanced_analysis:
        # Behavioral indicators
        behavioral_indicators = advanced_analysis.get('behavioral_indicators', [])
        if behavioral_indicators:
            malware_score += len(behavioral_indicators) * 2
            for indicator in behavioral_indicators:
                detection_details.append(f"Behavioral: {indicator.get('description', indicator.get('pattern', 'Unknown'))}")

        # Network indicators
        network_indicators = advanced_analysis.get('network_indicators', [])
        if network_indicators:
            malware_score += len(network_indicators) * 2
            for indicator in network_indicators:
                detection_details.append(f"Network: {indicator.get('description', indicator.get('pattern', 'Unknown'))}")

    # Calculate confidence and threat level
    normalized_score = min(malware_score / max_score, 1.0) if max_score > 0 else 0
    confidence_pct = min(100, int((normalized_score ** 0.7) * 100))

    # Determine threat level
    if confidence_pct >= 80:
        threat_level = "CRITICAL"
    elif confidence_pct >= 60:
        threat_level = "HIGH"
    elif confidence_pct >= 30:
        threat_level = "MEDIUM"
    elif confidence_pct >= 10:
        threat_level = "LOW"
    else:
        threat_level = "CLEAN"

    # Generate recommendations
    recommendations = []
    if threat_level == "CRITICAL":
        recommendations = [
            "CRITICAL THREAT DETECTED! This file is highly malicious!",
            "IMMEDIATELY ISOLATE this file and DO NOT execute it!",
            "Report this to your security team immediately.",
            "Delete this file if it's not needed for investigation."
        ]
    elif threat_level == "HIGH":
        recommendations = [
            "This file is highly likely to be malicious. Do not execute it!",
            "Isolate the file and report it to your security team.",
            "Consider deleting this file if it's not needed."
        ]
    elif threat_level == "MEDIUM":
        recommendations = [
            "This file shows suspicious behavior. Exercise caution!",
            "Analyze further in a sandboxed environment.",
            "Restrict execution if not required."
        ]
    elif threat_level == "LOW":
        recommendations = [
            "This file shows some low-risk indicators.",
            "Review the detection details before execution."
        ]
    else:
        recommendations = [
            "No significant malicious indicators detected.",
            "Still, always verify files from untrusted sources."
        ]

    # Set final result values
    result["threat_level"] = threat_level
    result["confidence_pct"] = confidence_pct
    result["detection_details"] = detection_details
    result["recommendations"] = recommendations
    result["malware_score"] = malware_score
    result["max_score"] = max_score

    return result

@malware_bp.route('/status', methods=['GET'])
@require_auth
def get_service_status():
    """
    Récupère le statut du service de détection de malware.
    
    Returns:
        JSON: Statut du service et ses capacités
    """
    try:
        service = get_malware_service()
        status = service.get_service_status()
        
        return success_response(
            data=status,
            message="Service status retrieved successfully"
        )
        
    except Exception as e:
        current_app.logger.error(f"Error getting malware service status: {e}")
        return error_response(
            message="Failed to get service status",
            error=str(e)
        ), 500

@malware_bp.route('/analyze', methods=['POST', 'OPTIONS'])
@require_auth
def analyze_file():
    """
    Analyse un fichier pour détecter les indicateurs de malware.
    
    Expected form data:
        file: Fichier à analyser
        
    Returns:
        JSON: Résultats de l'analyse de malware
    """
    # Handle CORS preflight requests
    if request.method == 'OPTIONS':
        return jsonify({}), 200

    try:
        # Vérifier qu'un fichier a été fourni
        if 'file' not in request.files:
            return error_response(
                message="No file provided",
                error="File is required for analysis"
            ), 400
        
        file = request.files['file']
        if file.filename == '':
            return error_response(
                message="No file selected",
                error="Please select a file to analyze"
            ), 400
        
        # Sécuriser le nom de fichier
        filename = secure_filename(file.filename)
        if not filename:
            filename = f"uploaded_file_{uuid.uuid4().hex[:8]}"
        
        # Sauvegarder temporairement le fichier
        temp_dir = tempfile.gettempdir()
        temp_file_path = os.path.join(temp_dir, f"malware_analysis_{uuid.uuid4().hex}_{filename}")
        
        try:
            file.save(temp_file_path)
            
            # Analyser le fichier avec le service
            service = get_malware_service()
            analysis_result = service.analyze_file(temp_file_path, filename)

            # Convertir en format JSON pour le frontend
            analysis_json = get_malware_analysis_json(analysis_result)

            # Ajouter des métadonnées utilisateur
            current_user = get_current_user()
            analysis_json['user_id'] = current_user.get('id') if current_user else None
            analysis_json['original_filename'] = file.filename
            analysis_json['file_size'] = os.path.getsize(temp_file_path)
            analysis_json['analysis_timestamp'] = datetime.now(timezone.utc).isoformat()

            # Sauvegarder les résultats en base de données MongoDB
            try:
                mongo_id = save_malware_analysis_to_db(analysis_json)
                if mongo_id:
                    analysis_json['mongo_id'] = mongo_id
                    print(f"✅ Analysis saved to MongoDB with ID: {mongo_id}")
                else:
                    print("⚠️ Failed to save analysis to MongoDB, but analysis completed")
            except Exception as db_error:
                print(f"⚠️ Database save error: {db_error}")
                # Continue même si la sauvegarde échoue

            return success_response(
                data=analysis_json,
                message="File analysis completed successfully"
            )
            
        finally:
            # Nettoyer le fichier temporaire
            if os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                except Exception as cleanup_error:
                    current_app.logger.warning(f"Failed to cleanup temp file: {cleanup_error}")
        
    except Exception as e:
        current_app.logger.error(f"Error analyzing file: {e}")
        return error_response(
            message="File analysis failed",
            error=str(e)
        ), 500

@malware_bp.route('/analyze/directory', methods=['POST'])
@require_auth
def analyze_directory():
    """
    Analyse tous les fichiers d'un répertoire.
    
    Expected JSON:
        {
            "directory_path": "/path/to/directory",
            "recursive": true
        }
        
    Returns:
        JSON: Résultats de l'analyse du répertoire
    """
    try:
        data = request.get_json()
        if not data or 'directory_path' not in data:
            return error_response(
                message="Directory path is required",
                error="Please provide a directory_path in the request body"
            ), 400
        
        directory_path = data['directory_path']
        recursive = data.get('recursive', True)
        
        # Vérifier que le répertoire existe
        if not os.path.exists(directory_path) or not os.path.isdir(directory_path):
            return error_response(
                message="Directory not found",
                error=f"Directory {directory_path} does not exist or is not accessible"
            ), 400
        
        # Analyser le répertoire avec le service
        service = get_malware_service()
        scan_result = service.scan_directory(directory_path, recursive)
        
        # Ajouter des métadonnées utilisateur
        current_user = get_current_user()
        scan_result['user_id'] = current_user.get('id') if current_user else None
        
        # TODO: Sauvegarder les résultats en base de données
        
        return success_response(
            data=scan_result,
            message="Directory analysis completed successfully"
        )
        
    except Exception as e:
        current_app.logger.error(f"Error analyzing directory: {e}")
        return error_response(
            message="Directory analysis failed",
            error=str(e)
        ), 500

@malware_bp.route('/history', methods=['GET'])
@require_auth
def get_analysis_history():
    """
    Récupère l'historique des analyses de malware.
    
    Query parameters:
        limit: Nombre maximum de résultats (défaut: 10)
        
    Returns:
        JSON: Historique des analyses
    """
    try:
        limit = request.args.get('limit', 10, type=int)
        
        # Limiter le nombre de résultats
        if limit > 100:
            limit = 100

        # Récupérer l'utilisateur actuel pour filtrer les résultats
        current_user = get_current_user()
        user_id = None

        # Si l'utilisateur n'est pas admin, filtrer par son ID
        if current_user and not current_user.get('is_admin', False):
            user_id = current_user.get('id')

        # Récupérer l'historique depuis MongoDB
        try:
            history = get_malware_analyses_from_db(limit=limit, user_id=user_id)

            return success_response(
                data={
                    'analyses': history,
                    'count': len(history),
                    'limit': limit,
                    'filtered_by_user': user_id is not None
                },
                message="Analysis history retrieved successfully"
            )
        except Exception as e:
            current_app.logger.error(f"Error retrieving analysis history: {e}")
            return error_response(
                message="Failed to retrieve analysis history",
                error=str(e)
            ), 500
        
    except Exception as e:
        current_app.logger.error(f"Error getting analysis history: {e}")
        return error_response(
            message="Failed to get analysis history",
            error=str(e)
        ), 500

@malware_bp.route('/analyze/url', methods=['POST'])
@require_auth
def analyze_url():
    """
    Analyse une URL pour détecter les indicateurs de malware.
    
    Expected JSON:
        {
            "url": "http://example.com/file.exe"
        }
        
    Returns:
        JSON: Résultats de l'analyse de l'URL
    """
    try:
        data = request.get_json()
        if not data or 'url' not in data:
            return error_response(
                message="URL is required",
                error="Please provide a URL in the request body"
            ), 400
        
        url = data['url']
        
        # TODO: Implémenter le téléchargement et l'analyse d'URL
        # Pour l'instant, retourner une réponse de placeholder
        
        return success_response(
            data={
                'url': url,
                'status': 'not_implemented',
                'message': 'URL analysis feature coming soon'
            },
            message="URL analysis endpoint reached"
        )
        
    except Exception as e:
        current_app.logger.error(f"Error analyzing URL: {e}")
        return error_response(
            message="URL analysis failed",
            error=str(e)
        ), 500

@malware_bp.route('/scan/quick', methods=['POST', 'OPTIONS'])
@require_auth
def quick_scan():
    """
    Effectue un scan rapide d'un fichier (analyse basique).
    
    Expected form data:
        file: Fichier à analyser rapidement
        
    Returns:
        JSON: Résultats du scan rapide
    """
    # Handle CORS preflight requests
    if request.method == 'OPTIONS':
        return jsonify({}), 200

    try:
        # Vérifier qu'un fichier a été fourni
        if 'file' not in request.files:
            return error_response(
                message="No file provided",
                error="File is required for quick scan"
            ), 400
        
        file = request.files['file']
        if file.filename == '':
            return error_response(
                message="No file selected",
                error="Please select a file for quick scan"
            ), 400
        
        # Sécuriser le nom de fichier
        filename = secure_filename(file.filename)
        if not filename:
            filename = f"quick_scan_{uuid.uuid4().hex[:8]}"
        
        # Sauvegarder temporairement le fichier
        temp_dir = tempfile.gettempdir()
        temp_file_path = os.path.join(temp_dir, f"quick_scan_{uuid.uuid4().hex}_{filename}")
        
        try:
            file.save(temp_file_path)
            
            # Effectuer un scan rapide (analyse basique)
            service = get_malware_service()
            detector = service.detector
            
            # Analyse rapide : hashes + IOCs + type de fichier
            quick_result = {
                'scan_id': str(uuid.uuid4()),
                'filename': filename,
                'scan_type': 'quick',
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'file_size': os.path.getsize(temp_file_path),
                'hashes': detector.calculate_hashes(temp_file_path),
                'file_type': detector.get_file_type(temp_file_path),
                'iocs': detector.extract_iocs(temp_file_path)
            }
            
            # Score de menace basique
            ioc_count = (len(quick_result['iocs']['urls']) + 
                        len(quick_result['iocs']['ip_addresses']) + 
                        len(quick_result['iocs']['domains']))
            quick_result['threat_score'] = min(ioc_count * 10, 100)
            quick_result['is_suspicious'] = quick_result['threat_score'] > 30
            
            return success_response(
                data=quick_result,
                message="Quick scan completed successfully"
            )
            
        finally:
            # Nettoyer le fichier temporaire
            if os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                except Exception as cleanup_error:
                    current_app.logger.warning(f"Failed to cleanup temp file: {cleanup_error}")
        
    except Exception as e:
        current_app.logger.error(f"Error in quick scan: {e}")
        return error_response(
            message="Quick scan failed",
            error=str(e)
        ), 500

@malware_bp.route('/analyze/packing', methods=['POST', 'OPTIONS'])
@require_auth
def analyze_packing():
    """
    Analyse un fichier pour détecter les indicateurs de packing.

    Expected form data:
        file: Fichier à analyser pour le packing

    Returns:
        JSON: Résultats de l'analyse de packing
    """
    # Handle CORS preflight requests
    if request.method == 'OPTIONS':
        return jsonify({}), 200

    try:
        # Vérifier qu'un fichier a été fourni
        if 'file' not in request.files:
            return error_response(
                message="No file provided",
                error="File is required for packing analysis"
            ), 400

        file = request.files['file']
        if file.filename == '':
            return error_response(
                message="No file selected",
                error="Please select a file for packing analysis"
            ), 400

        # Sécuriser le nom de fichier
        filename = secure_filename(file.filename)
        if not filename:
            filename = f"packing_analysis_{uuid.uuid4().hex[:8]}"

        # Sauvegarder temporairement le fichier
        temp_dir = tempfile.gettempdir()
        temp_file_path = os.path.join(temp_dir, f"packing_{uuid.uuid4().hex}_{filename}")

        try:
            file.save(temp_file_path)

            # Analyser le fichier avec le service
            service = get_malware_service()
            current_app.logger.info(f"Starting packing analysis for file: {filename}")

            try:
                analysis_result = service.analyze_file(temp_file_path, filename)
                current_app.logger.info(f"Analysis result keys: {list(analysis_result.keys()) if analysis_result else 'None'}")
            except Exception as analysis_error:
                current_app.logger.error(f"Analysis failed: {analysis_error}")
                return error_response(
                    message="File analysis failed",
                    error=f"Analysis error: {str(analysis_error)}"
                ), 500

            # Extraire l'analyse de packing
            pe_analysis = analysis_result.get('pe_analysis') if analysis_result else None
            current_app.logger.info(f"PE analysis available: {pe_analysis is not None}")

            if pe_analysis and not pe_analysis.get('error'):
                current_app.logger.info(f"PE analysis successful, packer_detected: {pe_analysis.get('packer_detected')}")

                packing_analysis = {
                    'is_packed': pe_analysis.get('packer_detected') is not None,
                    'packer_name': pe_analysis.get('packer_detected'),
                    'confidence': 'high' if pe_analysis.get('packer_detected') else 'low',
                    'indicators': [],
                    'sections': pe_analysis.get('sections', []),
                    'suspicious_imports': pe_analysis.get('suspicious_imports', [])
                }

                # Ajouter des indicateurs basés sur l'entropie
                for section in packing_analysis['sections']:
                    if section.get('entropy', 0) > 7.0:
                        packing_analysis['indicators'].append(f"High entropy section: {section['name']}")

                packing_json = get_packing_analysis_json(packing_analysis)
                current_app.logger.info(f"Packing analysis completed successfully")

                return success_response(
                    data={
                        'file_path': filename,
                        'packing_analysis': packing_json
                    },
                    message="Packing analysis completed successfully"
                )
            else:
                error_msg = pe_analysis.get('error') if pe_analysis else "No PE analysis available"
                current_app.logger.warning(f"Packing analysis failed: {error_msg}")

                # Return empty analysis rather than an error
                empty_packing_analysis = {
                    'is_packed': False,
                    'packer_name': None,
                    'confidence': 'low',
                    'indicators': [],
                    'sections': [],
                    'suspicious_imports': []
                }

                packing_json = get_packing_analysis_json(empty_packing_analysis)

                return success_response(
                    data={
                        'file_path': filename,
                        'packing_analysis': packing_json
                    },
                    message="Packing analysis completed (no PE file or analysis failed)"
                )

        finally:
            # Nettoyer le fichier temporaire
            if os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                except Exception as cleanup_error:
                    current_app.logger.warning(f"Failed to cleanup temp file: {cleanup_error}")

    except Exception as e:
        current_app.logger.error(f"Error in packing analysis: {e}")
        return error_response(
            message="Packing analysis failed",
            error=str(e)
        ), 500

@malware_bp.route('/analyze/aggressive', methods=['POST', 'OPTIONS'])
@require_auth
def analyze_aggressive():
    """
    Effectue une analyse agressive de malware avec tous les outils disponibles.

    Expected form data:
        file: Fichier à analyser agressivement

    Returns:
        JSON: Résultats complets de l'analyse agressive
    """
    # Handle CORS preflight requests
    if request.method == 'OPTIONS':
        return jsonify({}), 200

    try:
        # Vérifier qu'un fichier a été fourni
        if 'file' not in request.files:
            return error_response(
                message="No file provided",
                error="File is required for aggressive analysis"
            ), 400

        file = request.files['file']
        if file.filename == '':
            return error_response(
                message="No file selected",
                error="Please select a file for aggressive analysis"
            ), 400

        # Sécuriser le nom de fichier
        filename = secure_filename(file.filename)
        if not filename:
            filename = f"aggressive_{uuid.uuid4().hex[:8]}"

        # Sauvegarder temporairement le fichier
        temp_dir = tempfile.gettempdir()
        temp_file_path = os.path.join(temp_dir, f"aggressive_{uuid.uuid4().hex}_{filename}")

        try:
            file.save(temp_file_path)
            current_app.logger.info(f"Starting aggressive analysis for file: {filename}")

            # Analyser le fichier avec le service (analyse complète)
            service = get_malware_service()
            try:
                analysis_result = service.analyze_file(temp_file_path, filename)
                current_app.logger.info(f"Base analysis completed for {filename}")
            except Exception as analysis_error:
                current_app.logger.error(f"Base analysis failed: {analysis_error}")
                return error_response(
                    message="Base file analysis failed",
                    error=str(analysis_error)
                ), 500

            # Effectuer une analyse avancée supplémentaire
            try:
                advanced_analysis = service._perform_advanced_analysis(temp_file_path, analysis_result.get('file_type', {}))
                current_app.logger.info(f"Advanced analysis completed for {filename}")
            except Exception as advanced_error:
                current_app.logger.warning(f"Advanced analysis failed: {advanced_error}")
                # Continuer sans l'analyse avancée
                advanced_analysis = {
                    'packer_detected': False,
                    'suspicious_imports': [],
                    'behavioral_indicators': [],
                    'network_indicators': [],
                    'entropy_analysis': {}
                }

            # Combiner tous les résultats
            aggressive_analysis = {
                **analysis_result,
                'advanced_analysis': advanced_analysis,
                'analysis_type': 'aggressive',
                'scan_id': str(uuid.uuid4()),
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'original_filename': filename
            }

            # Calculer un score de menace amélioré pour l'analyse agressive
            enhanced_score = calculate_enhanced_threat_score(aggressive_analysis)
            aggressive_analysis.update(enhanced_score)

            # Convertir en format JSON pour le frontend
            analysis_json = get_malware_analysis_json(aggressive_analysis)

            # Sauvegarder en base de données
            current_user = get_current_user()
            if current_user:
                analysis_json['user_id'] = current_user.get('id')
                save_malware_analysis_to_db(analysis_json)

            current_app.logger.info(f"Aggressive analysis completed successfully for {filename}")

            return success_response(
                data={'analysis': analysis_json},
                message="Aggressive analysis completed successfully"
            )

        finally:
            # Nettoyer le fichier temporaire
            if os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                except Exception as cleanup_error:
                    current_app.logger.warning(f"Failed to cleanup temp file: {cleanup_error}")

    except Exception as e:
        current_app.logger.error(f"Error in aggressive analysis: {e}")
        return error_response(
            message="Aggressive analysis failed",
            error=str(e)
        ), 500

def calculate_enhanced_threat_score(analysis_result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Calcule un score de menace amélioré pour l'analyse agressive.
    """
    base_score = analysis_result.get('malware_score', 0)
    max_score = analysis_result.get('max_score', 50)

    # Facteurs d'amplification pour l'analyse agressive
    amplification_factors = {
        'advanced_packer': 15,
        'multiple_suspicious_apis': 10,
        'high_entropy_sections': 8,
        'suspicious_network_indicators': 12,
        'behavioral_indicators': 10,
        'yara_matches': 8
    }

    enhanced_score = base_score
    threat_indicators = []

    # Vérifier les indicateurs avancés
    advanced_analysis = analysis_result.get('advanced_analysis', {})

    if advanced_analysis.get('packer_detected'):
        enhanced_score += amplification_factors['advanced_packer']
        threat_indicators.append("Advanced packer detected")

    if advanced_analysis.get('behavioral_indicators'):
        enhanced_score += amplification_factors['behavioral_indicators']
        threat_indicators.append("Suspicious behavioral patterns")

    if advanced_analysis.get('network_indicators'):
        enhanced_score += amplification_factors['suspicious_network_indicators']
        threat_indicators.append("Network communication indicators")

    # Limiter le score maximum
    enhanced_score = min(enhanced_score, max_score)

    # Déterminer le niveau de menace
    if enhanced_score >= 40:
        threat_level = 'CRITICAL'
    elif enhanced_score >= 30:
        threat_level = 'HIGH'
    elif enhanced_score >= 20:
        threat_level = 'MEDIUM'
    elif enhanced_score >= 10:
        threat_level = 'LOW'
    else:
        threat_level = 'CLEAN'

    return {
        'malware_score': enhanced_score,
        'threat_level': threat_level,
        'confidence_pct': min(int((enhanced_score / max_score) * 100), 100),
        'is_malware': enhanced_score >= 25,
        'threat_indicators': threat_indicators
    }

@malware_bp.route('/analyze/comprehensive', methods=['POST', 'OPTIONS'])
@require_auth
def analyze_comprehensive():
    """
    Effectue une analyse complète de malware sur un fichier.

    Expected form data:
        file: Fichier à analyser complètement

    Returns:
        JSON: Résultats complets de l'analyse de malware
    """
    # Handle CORS preflight requests
    if request.method == 'OPTIONS':
        return jsonify({}), 200

    try:
        # Vérifier qu'un fichier a été fourni
        if 'file' not in request.files:
            return error_response(
                message="No file provided",
                error="File is required for comprehensive analysis"
            ), 400

        file = request.files['file']
        if file.filename == '':
            return error_response(
                message="No file selected",
                error="Please select a file for comprehensive analysis"
            ), 400

        # Sécuriser le nom de fichier
        filename = secure_filename(file.filename)
        if not filename:
            filename = f"comprehensive_{uuid.uuid4().hex[:8]}"

        # Sauvegarder temporairement le fichier
        temp_dir = tempfile.gettempdir()
        temp_file_path = os.path.join(temp_dir, f"comprehensive_{uuid.uuid4().hex}_{filename}")

        try:
            file.save(temp_file_path)

            # Analyser le fichier avec le service
            service = get_malware_service()
            analysis_result = service.analyze_file(temp_file_path, filename)

            # Convertir en format JSON
            malware_analysis_json = get_malware_analysis_json(analysis_result)

            # Ajouter des métadonnées utilisateur
            current_user = get_current_user()
            malware_analysis_json['user_id'] = current_user.get('id') if current_user else None
            malware_analysis_json['original_filename'] = file.filename
            malware_analysis_json['file_size'] = os.path.getsize(temp_file_path)
            malware_analysis_json['analysis_timestamp'] = datetime.now(timezone.utc).isoformat()
            malware_analysis_json['analysis_type'] = 'comprehensive'

            # Sauvegarder l'analyse complète en base de données
            try:
                mongo_id = save_malware_analysis_to_db(malware_analysis_json)
                if mongo_id:
                    malware_analysis_json['mongo_id'] = mongo_id
                    print(f"✅ Comprehensive analysis saved to MongoDB with ID: {mongo_id}")
                else:
                    print("⚠️ Failed to save comprehensive analysis to MongoDB")
            except Exception as db_error:
                print(f"⚠️ Database save error for comprehensive analysis: {db_error}")

            return success_response(
                data={
                    'file_path': filename,
                    'analysis': malware_analysis_json
                },
                message="Comprehensive malware analysis completed successfully"
            )

        finally:
            # Nettoyer le fichier temporaire
            if os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                except Exception as cleanup_error:
                    current_app.logger.warning(f"Failed to cleanup temp file: {cleanup_error}")

    except Exception as e:
        current_app.logger.error(f"Error in comprehensive analysis: {e}")
        return error_response(
            message="Comprehensive analysis failed",
            error=str(e)
        ), 500

@malware_bp.route('/analysis/<analysis_id>', methods=['GET'])
@require_auth
def get_analysis_by_id(analysis_id):
    """
    Récupère une analyse de malware spécifique par son ID.

    Args:
        analysis_id: ID de l'analyse à récupérer

    Returns:
        JSON: Analyse de malware détaillée
    """
    try:
        # Récupérer l'utilisateur actuel pour filtrer les résultats
        current_user = get_current_user()
        user_id = None

        # Si l'utilisateur n'est pas admin, filtrer par son ID
        if current_user and not current_user.get('is_admin', False):
            user_id = current_user.get('id')

        # Récupérer l'analyse depuis MongoDB
        analysis = get_malware_analysis_by_id(analysis_id, user_id=user_id)

        if analysis:
            return success_response(
                data=analysis,
                message="Analysis retrieved successfully"
            )
        else:
            return error_response(
                message="Analysis not found",
                error=f"No analysis found with ID: {analysis_id}"
            ), 404

    except Exception as e:
        current_app.logger.error(f"Error retrieving analysis {analysis_id}: {e}")
        return error_response(
            message="Failed to retrieve analysis",
            error=str(e)
        ), 500

@malware_bp.route('/stats', methods=['GET'])
@require_auth
def get_malware_statistics():
    """
    Récupère les statistiques des analyses de malware.

    Returns:
        JSON: Statistiques des analyses
    """
    try:
        # Récupérer l'utilisateur actuel pour filtrer les résultats
        current_user = get_current_user()
        user_id = None

        # Si l'utilisateur n'est pas admin, filtrer par son ID
        if current_user and not current_user.get('is_admin', False):
            user_id = current_user.get('id')

        # Récupérer les statistiques depuis MongoDB
        stats = get_malware_stats_from_db(user_id=user_id)

        return success_response(
            data={
                'statistics': stats,
                'filtered_by_user': user_id is not None,
                'user_id': user_id
            },
            message="Malware statistics retrieved successfully"
        )

    except Exception as e:
        current_app.logger.error(f"Error retrieving malware statistics: {e}")
        return error_response(
            message="Failed to retrieve malware statistics",
            error=str(e)
        ), 500
