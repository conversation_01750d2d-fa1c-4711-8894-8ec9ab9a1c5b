"""
Module de tests d'intrusion automatisés - Structure organisée selon spécifications
🟦 Network Scan: Nmap + OpenVAS + Metasploit (parallèle)
🟩 Web Scan: Nikto + SQLMap + Dirb + GoBuster + Zap (parallèle)
🟪 Vulnerability Scan: OpenVAS + Metasploit (parallèle)
🟥 Deep Scan: TOUS les outils (séquentiel avec progression)
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from datetime import datetime, timedelta
import uuid
import subprocess
import os
import re
import sys
import platform
import json
import time
import threading
from functools import wraps
from pymongo import MongoClient
from bson import ObjectId
from app.extensions import mongo
from app.models.scan_model import (
    save_scan_to_db,
    get_scan_by_id,
    get_scans_from_db
)
from app.utils.decorators import handle_options, admin_required

# Import des services réels
# from app.services.openvas_service import OpenVASService  # Moved to function level to avoid circular import
from app.services.nikto_service import NiktoService
from app.services.zap_service import ZAPService
from app.services.nmap_service import NmapService
from app.services.sqlmap_service import SQLMapService
from app.services.dirb_service import DirbService
from app.services.gobuster_service import GoBusterService
from app.services.scan_logger import ScanLogger
from app.services.scan_manager import ScanManager
from app.services.vulnerability_service import VulnerabilityService

pentesting_bp = Blueprint('pentesting', __name__)

# ============================================================================
# ROUTES PRINCIPALES
# ============================================================================

@pentesting_bp.route('/', methods=['GET'])
@jwt_required()
def get_pentesting_status():
    """Obtenir le statut du module de pentesting"""
    return jsonify({
        "status": "active",
        "module": "pentesting",
        "description": "Tests d'intrusion automatisés - 4 catégories organisées",
        "version": "2.0.0",
        "categories": {
            "network": "Nmap + OpenVAS + Metasploit (parallèle)",
            "web": "Nikto + SQLMap + Dirb + GoBuster + Zap (parallèle)",
            "vulnerability": "OpenVAS + Metasploit (parallèle)",
            "deep": "TOUS les outils (séquentiel avec progression)"
        },
        "scan_types": ["basic", "aggressive", "stealth", "comprehensive"]
    }), 200
# ============================================================================
# 🟦 1. NETWORK SCAN - Nmap + OpenVAS + Metasploit (parallèle)
# Types: basic, aggressive, stealth, comprehensive
# ============================================================================

@pentesting_bp.route('/scan/network', methods=['POST', 'OPTIONS'])
@handle_options
@jwt_required()
def network_scan():
    """
    🟦 NETWORK SCAN - Nmap + OpenVAS + Metasploit (parallèle)
    Types: basic, aggressive, stealth, comprehensive
    """
    try:
        data = request.get_json()
        target = data.get('target')
        scan_type = data.get('scan_type', 'basic')
        ports = data.get('ports', '')

        if not target:
            return jsonify({'error': 'Target is required'}), 400

        # Validation du type de scan
        valid_types = ['basic', 'aggressive', 'stealth', 'comprehensive']
        if scan_type not in valid_types:
            return jsonify({'error': f'Invalid scan_type. Must be one of: {valid_types}'}), 400

        # Configuration selon le type
        scan_configs = {
            'basic': {
                'description': 'Ports communs + vulnérabilités connues',
                'nmap_options': ['-sS', '--script=vuln', '--script=smb-vuln*'],
                'openvas_profile': 'Full and fast',
                'metasploit_modules': ['auxiliary/scanner/portscan/tcp'],
                'intensity': 'low'
            },
            'aggressive': {
                'description': 'Fingerprinting complet + détection OS + CVE',
                'nmap_options': ['-A', '--script=vuln,safe'],
                'openvas_profile': 'Full and very deep',
                'metasploit_modules': [
                    'auxiliary/scanner/portscan/tcp',
                    'auxiliary/scanner/smb/smb_version',
                    'auxiliary/scanner/ssh/ssh_version'
                ],
                'intensity': 'high'
            },
            'stealth': {
                'description': 'Scan lent et discret (pas d\'OS detect)',
                'nmap_options': ['-T1', '-sS'],
                'openvas_profile': 'Discovery',
                'metasploit_modules': ['auxiliary/scanner/portscan/tcp'],
                'intensity': 'minimal'
            },
            'comprehensive': {
                'description': 'Ports + vulnérabilités + scans étendus',
                'nmap_options': ['-p-', '-A', '--script=vuln,safe,discovery'],
                'openvas_profile': 'Full and very deep ultimate',
                'metasploit_modules': [
                    'auxiliary/scanner/portscan/tcp',
                    'auxiliary/scanner/smb/smb_version',
                    'auxiliary/scanner/ssh/ssh_version',
                    'auxiliary/scanner/http/http_version',
                    'auxiliary/scanner/ftp/ftp_version'
                ],
                'intensity': 'maximum'
            }
        }

        config = scan_configs[scan_type]

        # Get user ID
        current_user_id = get_jwt_identity()

        # Use robust scan manager for network scans
        from app.services.scan_manager import ScanManager

        # Prepare scan configuration
        scan_config = {
            'category': 'network',
            'scan_type': scan_type,
            'target': target,
            'tools': ['nmap', 'openvas', 'metasploit'],
            'user_id': current_user_id,
            'ports': ports,
            'config': config
        }

        # Start robust network scan
        try:
            scan_id = ScanManager.start_scan(scan_config)
            if not scan_id:
                return jsonify({'error': 'Failed to start robust network scan'}), 500
        except Exception as e:
            print(f"❌ Error starting robust network scan: {e}")
            return jsonify({'error': f'Failed to start robust network scan: {str(e)}'}), 500

        return jsonify({
            'message': f'Network scan started successfully',
            'scan_id': scan_id,
            'scan_type': scan_type,
            'target': target,
            'config': config
        }), 200

    except Exception as e:
        print(f"❌ Error in network_scan: {e}")
        return jsonify({'error': str(e)}), 500
# ============================================================================
# 🟩 2. WEB SCAN - Nikto + SQLMap + Dirb + GoBuster + Zap (parallèle)
# Types: basic, aggressive, stealth, comprehensive
# ============================================================================

@pentesting_bp.route('/scan/web-robust', methods=['POST', 'OPTIONS'])
@handle_options
@jwt_required()
def web_scan_robust():
    """
    🟩 ROBUST WEB SCAN - Uses ScanManager for background execution
    Scans survive page refreshes and API calls
    """
    try:
        data = request.get_json()
        target_url = data.get('target_url') or data.get('target')
        scan_type = data.get('scan_type', 'basic')
        user_id = get_jwt_identity()

        if not target_url:
            return jsonify({'error': 'Target URL is required'}), 400

        # Validation du type de scan
        valid_types = ['basic', 'aggressive', 'stealth', 'comprehensive']
        if scan_type not in valid_types:
            return jsonify({'error': f'Invalid scan_type. Must be one of: {valid_types}'}), 400

        # S'assurer que l'URL a un protocole
        if not target_url.startswith(('http://', 'https://')):
            target_url = f"http://{target_url}"

        # Prepare scan configuration for ScanManager
        scan_config = {
            'category': 'web',
            'scan_type': scan_type,
            'target': target_url,
            'user_id': user_id,
            'tools': ['nikto', 'sqlmap', 'dirb', 'gobuster', 'zap']
        }

        # Start scan using robust ScanManager
        scan_id = ScanManager.start_scan(scan_config)

        return jsonify({
            'message': f'🟩 {scan_type.title()} web scan started successfully',
            'scan_id': scan_id,
            'target': target_url,
            'scan_type': scan_type,
            'tools': scan_config['tools'],
            'status': 'running',
            'note': 'This scan runs in background and survives page refreshes'
        }), 200

    except Exception as e:
        print(f"❌ Error starting robust web scan: {e}")
        return jsonify({'error': str(e)}), 500

# ============================================================================
# ROBUST SCAN MANAGEMENT ENDPOINTS
# ============================================================================

@pentesting_bp.route('/scan/robust-status/<scan_id>', methods=['GET'])
@jwt_required()
def get_robust_scan_status(scan_id):
    """Get status of a robust scan"""
    try:
        status = ScanManager.get_scan_status(scan_id)
        if not status:
            return jsonify({'error': 'Scan not found'}), 404

        return jsonify(status), 200

    except Exception as e:
        print(f"❌ Error getting robust scan status: {e}")
        return jsonify({'error': str(e)}), 500

@pentesting_bp.route('/scan/robust-stop/<scan_id>', methods=['POST'])
@jwt_required()
def stop_robust_scan(scan_id):
    """Stop a running robust scan"""
    try:
        success = ScanManager.stop_scan(scan_id)
        if success:
            return jsonify({
                'message': 'Scan stopped successfully',
                'scan_id': scan_id
            }), 200
        else:
            return jsonify({'error': 'Failed to stop scan'}), 500

    except Exception as e:
        print(f"❌ Error stopping robust scan: {e}")
        return jsonify({'error': str(e)}), 500

@pentesting_bp.route('/scan/robust-active', methods=['GET'])
@jwt_required()
def get_active_robust_scans():
    """Get list of all active robust scans"""
    try:
        active_scans = ScanManager.get_active_scans()
        return jsonify({
            'active_scans': active_scans,
            'count': len(active_scans)
        }), 200

    except Exception as e:
        print(f"❌ Error getting active robust scans: {e}")
        return jsonify({'error': str(e)}), 500

@pentesting_bp.route('/scan/robust-logs/<scan_id>', methods=['GET'])
@jwt_required()
def get_robust_scan_logs(scan_id):
    """Get logs for a robust scan"""
    try:
        limit = int(request.args.get('limit', 100))
        level = request.args.get('level')
        tool = request.args.get('tool')

        if tool:
            logs = ScanLogger.get_tool_logs(scan_id, tool, limit)
        else:
            logs = ScanLogger.get_scan_logs(scan_id, limit, level)

        return jsonify({
            'scan_id': scan_id,
            'logs': logs,
            'total_logs': len(logs),
            'filters': {
                'limit': limit,
                'level': level,
                'tool': tool
            }
        }), 200

    except Exception as e:
        print(f"❌ Error getting robust scan logs: {e}")
        return jsonify({'error': str(e)}), 500

@pentesting_bp.route('/scan/web', methods=['POST', 'OPTIONS'])
@handle_options
@jwt_required()
def web_scan():
    """
    🟩 WEB SCAN - Nikto + SQLMap + Dirb + GoBuster + Zap (parallèle)
    Types: basic, aggressive, stealth, comprehensive
    """
    try:
        data = request.get_json()
        target_url = data.get('target_url') or data.get('target')
        scan_type = data.get('scan_type', 'basic')

        if not target_url:
            return jsonify({'error': 'Target URL is required'}), 400

        # Validation du type de scan
        valid_types = ['basic', 'aggressive', 'stealth', 'comprehensive']
        if scan_type not in valid_types:
            return jsonify({'error': f'Invalid scan_type. Must be one of: {valid_types}'}), 400

        # S'assurer que l'URL a un protocole
        if not target_url.startswith(('http://', 'https://')):
            target_url = f"http://{target_url}"

        # Configuration selon le type
        scan_configs = {
            'basic': {
                'description': 'Vérif failles connues + répertoires simples',
                'nikto_options': ['-h', target_url],
                'sqlmap_options': ['--batch', '--level=1'],
                'dirb_wordlist': '/usr/share/dirb/wordlists/common.txt',
                'intensity': 'low'
            },
            'aggressive': {
                'description': 'Tests profonds + injections poussées',
                'nikto_options': ['-h', target_url, '-Tuning', '9'],
                'sqlmap_options': ['--batch', '--level=5', '--risk=3'],
                'dirb_wordlist': '/usr/share/dirb/wordlists/big.txt',
                'intensity': 'high'
            },
            'stealth': {
                'description': 'Scan lent, discret, peu intrusif',
                'nikto_options': ['-h', target_url, '-T', '1'],
                'sqlmap_options': ['--batch', '--level=1', '--risk=1'],
                'dirb_wordlist': '/usr/share/dirb/wordlists/small.txt',
                'intensity': 'minimal'
            },
            'comprehensive': {
                'description': 'Tous les outils + scan actif Zap',
                'nikto_options': ['-h', target_url, '-Tuning', 'x'],
                'sqlmap_options': ['--batch', '--level=3', '--risk=2'],
                'dirb_wordlist': '/usr/share/dirb/wordlists/big.txt',
                'intensity': 'maximum'
            }
        }

        config = scan_configs[scan_type]
        scan_id = str(uuid.uuid4())

        # Créer l'entrée dans MongoDB
        scan_entry = {
            'scan_id': scan_id,
            'category': 'web',
            'scan_type': scan_type,
            'target': target_url,
            'status': 'running',
            'start_time': datetime.utcnow().isoformat(),
            'tools': ['nikto', 'sqlmap', 'dirb', 'gobuster', 'zap'],
            'config': config,
            'current_tool': 'initializing',
            'tools_status': {
                'nikto': {'status': 'pending', 'start_time': None, 'end_time': None, 'progress': 0},
                'sqlmap': {'status': 'pending', 'start_time': None, 'end_time': None, 'progress': 0},
                'dirb': {'status': 'pending', 'start_time': None, 'end_time': None, 'progress': 0},
                'gobuster': {'status': 'pending', 'start_time': None, 'end_time': None, 'progress': 0},
                'zap': {'status': 'pending', 'start_time': None, 'end_time': None, 'progress': 0}
            },
            'results': {
                'nikto': {},
                'sqlmap': {},
                'dirb': {},
                'gobuster': {},
                'zap': {},
                'summary': {}
            }
        }

        mongo.db.scans.insert_one(scan_entry)

        # Récupérer l'ID utilisateur avant de lancer le thread
        current_user_id = get_jwt_identity()

        # Lancer le scan en arrière-plan
        def run_web_scan():
            # Initialiser le logger pour ce scan
            logger = ScanLogger(scan_id)

            try:
                print(f"🟩 Starting {scan_type} web scan on {target_url}")

                # Logger le début du scan
                tools_list = ['nikto', 'sqlmap', 'dirb', 'gobuster', 'zap']
                logger.log_scan_start('web', scan_type, target_url, tools_list)

                # Initialiser les services
                nikto_service = NiktoService()
                zap_service = ZAPService()
                sqlmap_service = SQLMapService()
                dirb_service = DirbService()
                gobuster_service = GoBusterService()

                # Stocker l'ID utilisateur pour le scan
                mongo.db.scans.update_one(
                    {'scan_id': scan_id},
                    {'$set': {'user_id': current_user_id}}
                )

                all_results = {
                    'nikto': {},
                    'sqlmap': {},
                    'dirb': {},
                    'gobuster': {},
                    'zap': {},
                    'summary': {}
                }
                all_vulnerabilities = []
                all_directories = []

                # 1. NIKTO SCAN
                print(f"🔍 Running Nikto scan...")
                logger.log_tool_start('nikto', f"nikto -h {target_url}")

                # Mettre à jour le statut : Nikto en cours
                mongo.db.scans.update_one(
                    {'scan_id': scan_id},
                    {
                        '$set': {
                            'current_tool': 'nikto',
                            'tools_status.nikto.status': 'running',
                            'tools_status.nikto.start_time': datetime.utcnow().isoformat(),
                            'tools_status.nikto.progress': 10
                        }
                    }
                )

                try:
                    if nikto_service.is_available():
                        logger.log_tool_progress('nikto', 10, 'Nikto service available, starting scan')

                        nikto_options = {
                            'ssl': target_url.startswith('https'),
                            'timeout': 300
                        }

                        logger.log_tool_progress('nikto', 25, f'Configured options: SSL={nikto_options["ssl"]}, Timeout={nikto_options["timeout"]}s')

                        # Progression intermédiaire
                        mongo.db.scans.update_one(
                            {'scan_id': scan_id},
                            {'$set': {'tools_status.nikto.progress': 50}}
                        )
                        logger.log_tool_progress('nikto', 50, 'Executing Nikto scan...')

                        # Progression pendant l'exécution
                        mongo.db.scans.update_one(
                            {'scan_id': scan_id},
                            {'$set': {'tools_status.nikto.progress': 75}}
                        )
                        logger.log_tool_progress('nikto', 75, 'Nikto scan in progress, please wait...')

                        nikto_result = nikto_service.scan_target(target_url, nikto_options, logger)

                        logger.log_tool_progress('nikto', 90, f'Nikto scan completed with status: {nikto_result.get("status")}')

                        # Logger la sortie de Nikto
                        if nikto_result.get('raw_output'):
                            logger.log_tool_output('nikto', nikto_result['raw_output'])

                        # Log errors if present
                        if nikto_result.get('errors'):
                            logger.log_tool_output('nikto', nikto_result['errors'], 'stderr')

                        if nikto_result.get('status') == 'completed':
                            nikto_vulns = nikto_result.get('vulnerabilities', [])
                            all_results['nikto'] = {
                                'status': 'completed',
                                'vulnerabilities': nikto_vulns,
                                'scan_time': nikto_result.get('scan_time')
                            }
                            all_vulnerabilities.extend(nikto_vulns)

                            # Logger les résultats
                            logger.log_tool_result('nikto', 'completed', {
                                'vulnerabilities': nikto_vulns,
                                'scan_time': nikto_result.get('scan_time')
                            })

                            # Mettre à jour le statut : Nikto terminé
                            mongo.db.scans.update_one(
                                {'scan_id': scan_id},
                                {
                                    '$set': {
                                        'tools_status.nikto.status': 'completed',
                                        'tools_status.nikto.end_time': datetime.utcnow().isoformat(),
                                        'tools_status.nikto.progress': 100
                                    }
                                }
                            )
                            logger.log_tool_progress('nikto', 100, f'Nikto completed successfully - found {len(nikto_vulns)} vulnerabilities')
                            print(f"✅ Nikto found {len(nikto_vulns)} vulnerabilities")
                        else:
                            error_msg = nikto_result.get('error', 'Unknown error')
                            all_results['nikto'] = {
                                'status': 'failed',
                                'error': error_msg
                            }

                            # Log the error
                            logger.log_tool_error('nikto', error_msg, 'scan_failed')

                            # Mettre à jour le statut : Nikto échoué
                            mongo.db.scans.update_one(
                                {'scan_id': scan_id},
                                {
                                    '$set': {
                                        'tools_status.nikto.status': 'failed',
                                        'tools_status.nikto.end_time': datetime.utcnow().isoformat(),
                                        'tools_status.nikto.error': error_msg
                                    }
                                }
                            )
                            print(f"❌ Nikto scan failed: {error_msg}")
                    else:
                        error_msg = 'Nikto not available'
                        all_results['nikto'] = {
                            'status': 'unavailable',
                            'error': error_msg
                        }

                        # Logger l'indisponibilité
                        logger.log_tool_error('nikto', error_msg, 'service_unavailable')

                        # Mettre à jour le statut : Nikto non disponible
                        mongo.db.scans.update_one(
                            {'scan_id': scan_id},
                            {
                                '$set': {
                                    'tools_status.nikto.status': 'unavailable',
                                    'tools_status.nikto.end_time': datetime.utcnow().isoformat(),
                                    'tools_status.nikto.error': error_msg
                                }
                            }
                        )
                        print("⚠️ Nikto not available")

                except Exception as e:
                    error_msg = str(e)
                    all_results['nikto'] = {'status': 'error', 'error': error_msg}

                    # Logger l'exception
                    logger.log_tool_error('nikto', error_msg, 'exception')

                    # Update status: Nikto error
                    mongo.db.scans.update_one(
                        {'scan_id': scan_id},
                        {
                            '$set': {
                                'tools_status.nikto.status': 'error',
                                'tools_status.nikto.end_time': datetime.utcnow().isoformat(),
                                'tools_status.nikto.error': error_msg
                            }
                        }
                    )
                    print(f"❌ Nikto scan error: {e}")

                # 2. SQLMAP SCAN
                print(f"💉 Running SQLMap scan...")
                logger.log_tool_start('sqlmap', f"sqlmap -u {target_url} --batch")

                # Mettre à jour le statut : SQLMap en cours
                mongo.db.scans.update_one(
                    {'scan_id': scan_id},
                    {
                        '$set': {
                            'current_tool': 'sqlmap',
                            'tools_status.sqlmap.status': 'running',
                            'tools_status.sqlmap.start_time': datetime.utcnow().isoformat(),
                            'tools_status.sqlmap.progress': 10
                        }
                    }
                )

                try:
                    if sqlmap_service.is_available():
                        logger.log_tool_progress('sqlmap', 10, 'SQLMap service available, starting scan')

                        sqlmap_options = config.get('sqlmap_options', [])
                        logger.log_tool_progress('sqlmap', 25, f'Configured options: {sqlmap_options}')

                        # Progression intermédiaire
                        mongo.db.scans.update_one(
                            {'scan_id': scan_id},
                            {'$set': {'tools_status.sqlmap.progress': 50}}
                        )
                        logger.log_tool_progress('sqlmap', 50, 'Executing SQLMap scan...')

                        sqlmap_result = sqlmap_service.scan_target(
                            target_url=target_url,
                            options=sqlmap_options
                        )

                        # Logger la sortie de SQLMap
                        if sqlmap_result.get('raw_output'):
                            logger.log_tool_output('sqlmap', sqlmap_result['raw_output'])

                        if sqlmap_result.get('status') == 'completed':
                            sqlmap_vulns = sqlmap_result.get('vulnerabilities', [])
                            all_results['sqlmap'] = {
                                'status': 'completed',
                                'scan_id': sqlmap_result.get('scan_id'),
                                'vulnerabilities': sqlmap_vulns,
                                'scan_time': sqlmap_result.get('scan_time'),
                                'command': sqlmap_result.get('command')
                            }
                            all_vulnerabilities.extend(sqlmap_vulns)
                            print(f"✅ SQLMap found {len(sqlmap_vulns)} vulnerabilities")
                        else:
                            all_results['sqlmap'] = {
                                'status': sqlmap_result.get('status', 'failed'),
                                'error': sqlmap_result.get('error', 'Unknown error')
                            }
                            print(f"❌ SQLMap scan failed: {sqlmap_result.get('error')}")
                    else:
                        all_results['sqlmap'] = {
                            'status': 'unavailable',
                            'error': 'SQLMap not available'
                        }
                        print("⚠️ SQLMap not available")

                except Exception as e:
                    all_results['sqlmap'] = {'status': 'error', 'error': str(e)}
                    print(f"❌ SQLMap scan error: {e}")

                # 3. DIRB SCAN
                print(f"📁 Running Dirb scan...")
                try:
                    if dirb_service.is_available():
                        wordlist = config.get('dirb_wordlist', 'common')
                        dirb_result = dirb_service.scan_target(
                            target_url=target_url,
                            wordlist=wordlist
                        )

                        if dirb_result.get('status') == 'completed':
                            dirb_dirs = dirb_result.get('directories', [])
                            all_results['dirb'] = {
                                'status': 'completed',
                                'scan_id': dirb_result.get('scan_id'),
                                'directories': dirb_dirs,
                                'wordlist': dirb_result.get('wordlist'),
                                'scan_time': dirb_result.get('scan_time')
                            }
                            all_directories.extend(dirb_dirs)
                            print(f"✅ Dirb found {len(dirb_dirs)} directories/files")
                        else:
                            all_results['dirb'] = {
                                'status': dirb_result.get('status', 'failed'),
                                'error': dirb_result.get('error', 'Unknown error')
                            }
                            print(f"❌ Dirb scan failed: {dirb_result.get('error')}")
                    else:
                        all_results['dirb'] = {
                            'status': 'unavailable',
                            'error': 'Dirb not available'
                        }
                        print("⚠️ Dirb not available")

                except Exception as e:
                    all_results['dirb'] = {'status': 'error', 'error': str(e)}
                    print(f"❌ Dirb scan error: {e}")

                # 4. GOBUSTER SCAN
                print(f"🔍 Running GoBuster scan...")
                try:
                    if gobuster_service.is_available():
                        gobuster_result = gobuster_service.scan_directories(
                            target_url=target_url,
                            wordlist='common'
                        )

                        if gobuster_result.get('status') == 'completed':
                            gobuster_dirs = gobuster_result.get('directories', [])
                            all_results['gobuster'] = {
                                'status': 'completed',
                                'scan_id': gobuster_result.get('scan_id'),
                                'directories': gobuster_dirs,
                                'wordlist': gobuster_result.get('wordlist'),
                                'scan_time': gobuster_result.get('scan_time')
                            }
                            all_directories.extend(gobuster_dirs)
                            print(f"✅ GoBuster found {len(gobuster_dirs)} directories/files")
                        else:
                            all_results['gobuster'] = {
                                'status': gobuster_result.get('status', 'failed'),
                                'error': gobuster_result.get('error', 'Unknown error')
                            }
                            print(f"❌ GoBuster scan failed: {gobuster_result.get('error')}")
                    else:
                        all_results['gobuster'] = {
                            'status': 'unavailable',
                            'error': 'GoBuster not available'
                        }
                        print("⚠️ GoBuster not available")

                except Exception as e:
                    all_results['gobuster'] = {'status': 'error', 'error': str(e)}
                    print(f"❌ GoBuster scan error: {e}")

                # 5. ZAP SCAN
                print(f"🕷️ Running OWASP ZAP scan...")
                try:
                    if zap_service.is_available():
                        zap_result = zap_service.spider_scan(target_url)

                        if zap_result.get('status') == 'completed':
                            zap_vulns = zap_result.get('vulnerabilities', [])
                            all_results['zap'] = {
                                'status': 'completed',
                                'vulnerabilities': zap_vulns,
                                'urls_found': zap_result.get('urls_found', [])
                            }
                            all_vulnerabilities.extend(zap_vulns)
                            print(f"✅ ZAP found {len(zap_vulns)} vulnerabilities")
                        else:
                            all_results['zap'] = {
                                'status': 'failed',
                                'error': zap_result.get('message', 'Unknown error')
                            }
                            print(f"❌ ZAP scan failed: {zap_result.get('message')}")
                    else:
                        all_results['zap'] = {
                            'status': 'unavailable',
                            'error': 'ZAP service not available'
                        }
                        print("⚠️ ZAP service not available")

                except Exception as e:
                    all_results['zap'] = {'status': 'error', 'error': str(e)}
                    print(f"❌ ZAP scan error: {e}")

                # Sauvegarder les vulnérabilités dans la collection vulnerabilités
                try:
                    vulnerability_ids = VulnerabilityService.process_scan_vulnerabilities(
                        scan_id=scan_id,
                        scan_results=all_results,
                        target=target_url,
                        user_id=current_user_id
                    )
                    print(f"✅ Saved {len(vulnerability_ids)} vulnerabilities to vulnerabilities collection")
                except Exception as vuln_error:
                    print(f"⚠️ Error saving vulnerabilities: {vuln_error}")

                # Mettre à jour les résultats
                mongo.db.scans.update_one(
                    {'scan_id': scan_id},
                    {
                        '$set': {
                            'status': 'completed',
                            'end_time': datetime.utcnow().isoformat(),
                            'results': all_results,
                            'results.vulnerabilities': all_vulnerabilities,
                            'results.directories': all_directories,
                            'results.summary': {
                                'total_vulnerabilities': len(all_vulnerabilities),
                                'total_directories': len(all_directories),
                                'critical': len([v for v in all_vulnerabilities if v.get('severity') == 'critical']),
                                'high': len([v for v in all_vulnerabilities if v.get('severity') == 'high']),
                                'medium': len([v for v in all_vulnerabilities if v.get('severity') == 'medium']),
                                'low': len([v for v in all_vulnerabilities if v.get('severity') == 'low'])
                            }
                        }
                    }
                )

                print(f"✅ Web scan {scan_id} completed")

            except Exception as e:
                print(f"❌ Web scan {scan_id} failed: {e}")
                mongo.db.scans.update_one(
                    {'scan_id': scan_id},
                    {
                        '$set': {
                            'status': 'failed',
                            'end_time': datetime.utcnow().isoformat(),
                            'error': str(e)
                        }
                    }
                )

        # Lancer en thread
        thread = threading.Thread(target=run_web_scan)
        thread.daemon = True
        thread.start()

        return jsonify({
            'message': f'Web scan started successfully',
            'scan_id': scan_id,
            'scan_type': scan_type,
            'target': target_url,
            'config': config
        }), 200

    except Exception as e:
        print(f"❌ Error in web_scan: {e}")
        return jsonify({'error': str(e)}), 500

# ============================================================================
# 🟪 3. VULNERABILITY SCAN - OpenVAS + Metasploit (parallèle)
# Types: basic, aggressive, stealth, comprehensive
# ============================================================================

@pentesting_bp.route('/scan/vulnerability', methods=['POST', 'OPTIONS'])
@handle_options
@jwt_required()
def vulnerability_scan():
    """
    🟪 VULNERABILITY SCAN - Enhanced with real vulnerability scanning tools
    Uses ScanManager for robust background execution
    Types: basic, aggressive, stealth, comprehensive
    """
    try:
        data = request.get_json()
        target = data.get('target')
        scan_type = data.get('scan_type', 'basic')
        ports = data.get('ports', '')

        if not target:
            return jsonify({'error': 'Target is required'}), 400

        # Validation du type de scan
        valid_types = ['basic', 'aggressive', 'stealth', 'comprehensive']
        if scan_type not in valid_types:
            return jsonify({'error': f'Invalid scan_type. Must be one of: {valid_types}'}), 400

        # Get user ID
        user_id = get_jwt_identity()

        # Prepare scan configuration for ScanManager
        scan_config = {
            'category': 'vulnerability',
            'scan_type': scan_type,
            'target': target,
            'user_id': user_id,
            'tools': ['openvas', 'metasploit'],
            'ports': ports
        }

        # Start scan using robust ScanManager
        scan_id = ScanManager.start_scan(scan_config)

        return jsonify({
            'message': f'🟪 {scan_type.title()} vulnerability scan started successfully',
            'scan_id': scan_id,
            'target': target,
            'scan_type': scan_type,
            'tools': scan_config['tools'],
            'status': 'running',
            'note': 'This scan runs in background and survives page refreshes'
        }), 200

    except Exception as e:
        print(f"❌ Error in vulnerability_scan: {e}")
        return jsonify({'error': str(e)}), 500

# ============================================================================
# 🟥 4. DEEP SCAN - TOUS les outils (séquentiel avec progression)
# Types: basic, aggressive, stealth, comprehensive
# ============================================================================

# ============================================================================
# 🟥 4. DEEP SCAN - TOUS les outils (séquentiel avec progression)
# Types: basic, aggressive, stealth, comprehensive
# ============================================================================

@pentesting_bp.route('/scan/deep', methods=['POST', 'OPTIONS'])
@handle_options
@jwt_required()
def deep_scan():
    """
    🟥 DEEP SCAN - TOUS les outils (séquentiel avec progression)
    Types: basic, aggressive, stealth, comprehensive
    """
    try:
        data = request.get_json()
        target = data.get('target')
        scan_type = data.get('scan_type', 'basic')
        ports = data.get('ports', '')

        if not target:
            return jsonify({'error': 'Target is required'}), 400

        # Validation du type de scan
        valid_types = ['basic', 'aggressive', 'stealth', 'comprehensive']
        if scan_type not in valid_types:
            return jsonify({'error': f'Invalid scan_type. Must be one of: {valid_types}'}), 400

        # Configuration selon le type - TOUS les outils de toutes les catégories
        scan_configs = {
            'basic': {
                'description': 'Aperçu rapide et peu intrusif de tous les outils',
                'tools': ['nmap', 'openvas', 'metasploit', 'nikto', 'sqlmap', 'dirb', 'gobuster', 'zap'],
                'sequence': 'Basic: Network + Web + Vulnerability tools',
                'intensity': 'low'
            },
            'aggressive': {
                'description': 'Tests poussés sur TOUS les outils disponibles',
                'tools': ['nmap', 'openvas', 'metasploit', 'nikto', 'sqlmap', 'dirb', 'gobuster', 'zap'],
                'sequence': 'Aggressive: All tools with high intensity',
                'intensity': 'high'
            },
            'stealth': {
                'description': 'Séquence discrète avec tous les outils',
                'tools': ['nmap', 'openvas', 'metasploit', 'nikto', 'sqlmap', 'dirb', 'gobuster', 'zap'],
                'sequence': 'Stealth: All tools with minimal footprint',
                'intensity': 'minimal'
            },
            'comprehensive': {
                'description': 'Audit total avec TOUS les outils et progression affichée',
                'tools': ['nmap', 'openvas', 'metasploit', 'nikto', 'sqlmap', 'dirb', 'gobuster', 'zap'],
                'sequence': 'Comprehensive: Complete audit with all available tools',
                'intensity': 'maximum'
            }
        }

        config = scan_configs[scan_type]
        tools = config['tools']

        # Get user ID
        current_user_id = get_jwt_identity()

        # Use ScanManager for consistent behavior and logging (it will create the scan entry)
        from app.services.scan_manager import ScanManager

        scan_config = {
            'category': 'deep',
            'scan_type': scan_type,
            'target': target,
            'user_id': current_user_id,
            'tools': tools,
            'ports': ports,
            'config': config
        }

        # Start scan using ScanManager (this will handle all the logging and phase tracking)
        scan_id = ScanManager.start_scan(scan_config)

        return jsonify({
            'message': f'Deep scan started successfully',
            'scan_id': scan_id,
            'scan_type': scan_type,
            'target': target,
            'config': config,
            'progress_tracking': True
        }), 200

    except Exception as e:
        print(f"❌ Error in deep_scan: {e}")
        return jsonify({'error': str(e)}), 500

# Deep scan now uses ScanManager for consistent behavior and logging
# ============================================================================
# ROUTES UTILITAIRES - Historique et résultats
# ============================================================================

@pentesting_bp.route('/scan/progress/<scan_id>', methods=['GET', 'OPTIONS'])
@handle_options
@jwt_required()
def get_scan_progress(scan_id):
    """
    📊 Récupérer la progression d'un scan en temps réel
    """
    try:
        scan = mongo.db.scans.find_one({'scan_id': scan_id})

        if not scan:
            return jsonify({'error': 'Scan not found'}), 404

        # Extraire les informations de progression
        progress_info = {
            'scan_id': scan_id,
            'status': scan.get('status', 'unknown'),
            'category': scan.get('category', ''),
            'scan_type': scan.get('scan_type', ''),
            'target': scan.get('target', ''),
            'start_time': scan.get('start_time', ''),
            'end_time': scan.get('end_time', ''),
        }

        # Ajouter les informations de progression pour les deep scans
        if scan.get('category') == 'deep' and 'progress' in scan:
            progress = scan['progress']
            progress_info.update({
                'progress': {
                    'current_tool': progress.get('current_tool', ''),
                    'progress_percent': progress.get('progress_percent', 0),
                    'completed_tools': progress.get('completed_tools', []),
                    'total_tools': progress.get('total_tools', 0)
                }
            })

        # Ajouter un résumé des résultats si disponible
        if 'results' in scan and 'summary' in scan['results']:
            progress_info['summary'] = scan['results']['summary']

        return jsonify(progress_info), 200

    except Exception as e:
        print(f"❌ Error getting scan progress: {e}")
        return jsonify({'error': str(e)}), 500

@pentesting_bp.route('/scan/details/<scan_id>', methods=['GET', 'OPTIONS'])
@handle_options
@jwt_required()
def get_scan_details(scan_id):
    """
    🔍 Récupérer les détails complets d'un scan en cours avec statut des outils
    """
    try:
        scan = mongo.db.scans.find_one({'scan_id': scan_id})

        if not scan:
            return jsonify({'error': 'Scan not found'}), 404

        # Vérifier les permissions utilisateur
        from flask_jwt_extended import get_jwt
        claims = get_jwt()
        current_user_id = get_jwt_identity()
        user_role = claims.get('role', 'user')

        # Si pas admin, vérifier que c'est le scan de l'utilisateur
        if user_role != 'admin' and scan.get('user_id') != current_user_id:
            return jsonify({'error': 'Access denied'}), 403

        # Informations de base
        scan_details = {
            'scan_id': scan_id,
            'status': scan.get('status', 'unknown'),
            'category': scan.get('category', ''),
            'scan_type': scan.get('scan_type', ''),
            'target': scan.get('target', ''),
            'ports': scan.get('ports', ''),
            'start_time': scan.get('start_time', ''),
            'end_time': scan.get('end_time', ''),
            'user_id': scan.get('user_id', ''),
            'tools': scan.get('tools', []),
            'config': scan.get('config', {}),
            'current_tool': scan.get('current_tool', 'unknown')
        }

        # Ajouter le statut détaillé des outils si disponible
        if 'tools_status' in scan:
            tools_status = scan['tools_status']
            scan_details['tools_status'] = {}

            for tool, status in tools_status.items():
                scan_details['tools_status'][tool] = {
                    'status': status.get('status', 'pending'),
                    'start_time': status.get('start_time'),
                    'end_time': status.get('end_time'),
                    'progress': status.get('progress', 0),
                    'error': status.get('error'),
                    'duration': None
                }

                # Calculer la durée si start_time et end_time sont disponibles
                if status.get('start_time') and status.get('end_time'):
                    try:
                        from datetime import datetime
                        start = datetime.fromisoformat(status['start_time'].replace('Z', '+00:00'))
                        end = datetime.fromisoformat(status['end_time'].replace('Z', '+00:00'))
                        duration = (end - start).total_seconds()
                        scan_details['tools_status'][tool]['duration'] = f"{duration:.1f}s"
                    except:
                        pass

        # Ajouter les informations de progression pour les deep scans
        if scan.get('category') == 'deep' and 'progress' in scan:
            progress = scan['progress']

            # Handle both old format (dict) and new format (integer)
            if isinstance(progress, dict):
                # Old format - progress is a dictionary
                scan_details['deep_progress'] = {
                    'current_tool': progress.get('current_tool', ''),
                    'progress_percent': progress.get('progress_percent', 0),
                    'completed_tools': progress.get('completed_tools', []),
                    'total_tools': progress.get('total_tools', 0)
                }
            else:
                # New format - progress is an integer, use current_tool from main scan
                scan_details['deep_progress'] = {
                    'current_tool': scan.get('current_tool', ''),
                    'progress_percent': progress if isinstance(progress, (int, float)) else 0,
                    'completed_tools': [],
                    'total_tools': len(scan.get('tools', []))
                }

        # Ajouter les résultats partiels si disponibles
        if 'results' in scan:
            scan_details['results'] = scan['results']

        # Calculer la durée totale du scan
        if scan.get('start_time'):
            try:
                from datetime import datetime
                start = datetime.fromisoformat(scan['start_time'].replace('Z', '+00:00'))
                if scan.get('end_time'):
                    end = datetime.fromisoformat(scan['end_time'].replace('Z', '+00:00'))
                    total_duration = (end - start).total_seconds()
                    scan_details['total_duration'] = f"{total_duration:.1f}s"
                else:
                    # Scan en cours
                    now = datetime.utcnow()
                    current_duration = (now - start).total_seconds()
                    scan_details['current_duration'] = f"{current_duration:.1f}s"
            except:
                pass

        return jsonify(scan_details), 200

    except Exception as e:
        print(f"❌ Error getting scan details: {e}")
        return jsonify({'error': str(e)}), 500

@pentesting_bp.route('/scans/running', methods=['GET', 'OPTIONS'])
@handle_options
@jwt_required()
def get_running_scans():
    """
    🏃 Récupérer tous les scans en cours avec leurs détails
    """
    try:
        from flask_jwt_extended import get_jwt
        claims = get_jwt()
        current_user_id = get_jwt_identity()
        user_role = claims.get('role', 'user')

        # Construire la requête selon le rôle
        if user_role == 'admin':
            # Admin voit tous les scans en cours
            query = {'status': 'running'}
        else:
            # Utilisateur normal voit seulement ses scans
            query = {'status': 'running', 'user_id': current_user_id}

        # Récupérer les scans en cours
        running_scans = list(mongo.db.scans.find(query).sort('start_time', -1))

        scans_details = []
        for scan in running_scans:
            scan_info = {
                'scan_id': scan.get('scan_id'),
                'category': scan.get('category'),
                'scan_type': scan.get('scan_type'),
                'target': scan.get('target'),
                'ports': scan.get('ports', ''),
                'start_time': scan.get('start_time'),
                'user_id': scan.get('user_id'),
                'current_tool': scan.get('current_tool', 'unknown'),
                'tools': scan.get('tools', [])
            }

            # Ajouter le statut des outils si disponible
            if 'tools_status' in scan:
                tools_status = scan['tools_status']
                scan_info['tools_status'] = {}

                # Compter les outils par statut
                status_counts = {
                    'pending': 0,
                    'running': 0,
                    'completed': 0,
                    'failed': 0,
                    'error': 0,
                    'unavailable': 0
                }

                for tool, status in tools_status.items():
                    tool_status = status.get('status', 'pending')
                    status_counts[tool_status] = status_counts.get(tool_status, 0) + 1

                    scan_info['tools_status'][tool] = {
                        'status': tool_status,
                        'progress': status.get('progress', 0),
                        'start_time': status.get('start_time'),
                        'error': status.get('error')
                    }

                scan_info['status_summary'] = status_counts

                # Calculer le pourcentage global de progression
                total_tools = len(tools_status)
                completed_tools = status_counts['completed']
                running_tools = status_counts['running']

                if total_tools > 0:
                    # Progression = (outils terminés + 0.5 * outils en cours) / total
                    progress_percent = ((completed_tools + 0.5 * running_tools) / total_tools) * 100
                    scan_info['overall_progress'] = round(progress_percent, 1)
                else:
                    scan_info['overall_progress'] = 0

            # Calculer la durée actuelle
            if scan.get('start_time'):
                try:
                    from datetime import datetime
                    start = datetime.fromisoformat(scan['start_time'].replace('Z', '+00:00'))
                    now = datetime.utcnow()
                    current_duration = (now - start).total_seconds()
                    scan_info['current_duration'] = f"{current_duration:.0f}s"

                    # Formatage plus lisible pour les longues durées
                    if current_duration > 3600:  # Plus d'1 heure
                        hours = int(current_duration // 3600)
                        minutes = int((current_duration % 3600) // 60)
                        scan_info['current_duration_formatted'] = f"{hours}h {minutes}m"
                    elif current_duration > 60:  # Plus d'1 minute
                        minutes = int(current_duration // 60)
                        seconds = int(current_duration % 60)
                        scan_info['current_duration_formatted'] = f"{minutes}m {seconds}s"
                    else:
                        scan_info['current_duration_formatted'] = f"{current_duration:.0f}s"
                except:
                    pass

            scans_details.append(scan_info)

        return jsonify({
            'running_scans': scans_details,
            'total_running': len(scans_details),
            'user_role': user_role
        }), 200

    except Exception as e:
        print(f"❌ Error getting running scans: {e}")
        return jsonify({'error': str(e)}), 500

@pentesting_bp.route('/scan/stop/<scan_id>', methods=['POST', 'OPTIONS'])
@handle_options
@jwt_required()
def stop_scan(scan_id):
    """
    🛑 Arrêter un scan en cours
    """
    try:
        from flask_jwt_extended import get_jwt
        claims = get_jwt()
        current_user_id = get_jwt_identity()
        user_role = claims.get('role', 'user')

        # Récupérer le scan
        scan = mongo.db.scans.find_one({'scan_id': scan_id})
        if not scan:
            return jsonify({'error': 'Scan not found'}), 404

        # Vérifier les permissions
        if user_role != 'admin' and scan.get('user_id') != current_user_id:
            return jsonify({'error': 'Access denied'}), 403

        # Vérifier que le scan est en cours
        if scan.get('status') != 'running':
            return jsonify({'error': f'Scan is not running (status: {scan.get("status")})'}), 400

        # Marquer le scan comme arrêté
        mongo.db.scans.update_one(
            {'scan_id': scan_id},
            {
                '$set': {
                    'status': 'stopped',
                    'end_time': datetime.utcnow().isoformat(),
                    'stopped_by': current_user_id,
                    'stop_reason': 'Manual stop by user'
                }
            }
        )

        # Mettre à jour le statut des outils en cours
        if 'tools_status' in scan:
            tools_updates = {}
            for tool, status in scan['tools_status'].items():
                if status.get('status') == 'running':
                    tools_updates[f'tools_status.{tool}.status'] = 'stopped'
                    tools_updates[f'tools_status.{tool}.end_time'] = datetime.utcnow().isoformat()
                    tools_updates[f'tools_status.{tool}.stop_reason'] = 'Manual stop'

            if tools_updates:
                mongo.db.scans.update_one({'scan_id': scan_id}, {'$set': tools_updates})

        print(f"🛑 Scan {scan_id} stopped by user {current_user_id}")

        return jsonify({
            'message': 'Scan stopped successfully',
            'scan_id': scan_id,
            'status': 'stopped'
        }), 200

    except Exception as e:
        print(f"❌ Error stopping scan: {e}")
        return jsonify({'error': str(e)}), 500

@pentesting_bp.route('/scan/cancel/<scan_id>', methods=['POST', 'OPTIONS'])
@handle_options
@jwt_required()
def cancel_scan(scan_id):
    """
    ❌ Annuler/supprimer un scan
    """
    try:
        from flask_jwt_extended import get_jwt
        claims = get_jwt()
        current_user_id = get_jwt_identity()
        user_role = claims.get('role', 'user')

        # Récupérer le scan
        scan = mongo.db.scans.find_one({'scan_id': scan_id})
        if not scan:
            return jsonify({'error': 'Scan not found'}), 404

        # Vérifier les permissions
        if user_role != 'admin' and scan.get('user_id') != current_user_id:
            return jsonify({'error': 'Access denied'}), 403

        # Si le scan est en cours, l'arrêter d'abord
        if scan.get('status') == 'running':
            mongo.db.scans.update_one(
                {'scan_id': scan_id},
                {
                    '$set': {
                        'status': 'cancelled',
                        'end_time': datetime.utcnow().isoformat(),
                        'cancelled_by': current_user_id,
                        'cancel_reason': 'Manual cancellation by user'
                    }
                }
            )

            # Mettre à jour le statut des outils en cours
            if 'tools_status' in scan:
                tools_updates = {}
                for tool, status in scan['tools_status'].items():
                    if status.get('status') in ['running', 'pending']:
                        tools_updates[f'tools_status.{tool}.status'] = 'cancelled'
                        tools_updates[f'tools_status.{tool}.end_time'] = datetime.utcnow().isoformat()
                        tools_updates[f'tools_status.{tool}.cancel_reason'] = 'Manual cancellation'

                if tools_updates:
                    mongo.db.scans.update_one({'scan_id': scan_id}, {'$set': tools_updates})

            print(f"❌ Running scan {scan_id} cancelled by user {current_user_id}")
        else:
            # Si le scan n'est pas en cours, le marquer comme annulé
            mongo.db.scans.update_one(
                {'scan_id': scan_id},
                {
                    '$set': {
                        'status': 'cancelled',
                        'cancelled_by': current_user_id,
                        'cancel_reason': 'Manual cancellation by user'
                    }
                }
            )
            print(f"❌ Scan {scan_id} marked as cancelled by user {current_user_id}")

        return jsonify({
            'message': 'Scan cancelled successfully',
            'scan_id': scan_id,
            'status': 'cancelled'
        }), 200

    except Exception as e:
        print(f"❌ Error cancelling scan: {e}")
        return jsonify({'error': str(e)}), 500

@pentesting_bp.route('/scan/restart/<scan_id>', methods=['POST', 'OPTIONS'])
@handle_options
@jwt_required()
def restart_scan(scan_id):
    """
    🔄 Redémarrer un scan avec les mêmes paramètres
    """
    try:
        current_user_id = get_jwt_identity()
        print(f"🔄 Resuming stopped scan {scan_id} for user {current_user_id}")

        # Récupérer les détails du scan original
        original_scan = mongo.db.scans.find_one({'scan_id': scan_id})
        if not original_scan:
            return jsonify({'error': 'Scan not found'}), 404

        # Vérifier les permissions
        if original_scan.get('user_id') != current_user_id:
            # Vérifier si l'utilisateur est admin
            user = mongo.db.users.find_one({'username': current_user_id})
            if not user or user.get('role') != 'admin':
                return jsonify({'error': 'Permission denied'}), 403

        # Vérifier que le scan peut être redémarré (seulement les scans arrêtés)
        if original_scan.get('status') != 'stopped':
            return jsonify({'error': 'Can only restart stopped scans'}), 400

        # ScanManager will create the scan record, so we don't need to create it manually

        # Démarrer le scan selon sa catégorie
        category = original_scan.get('category')
        scan_type = original_scan.get('scan_type')
        target = original_scan.get('target')
        ports = original_scan.get('ports')

        # Import ScanManager
        from app.services.scan_manager import ScanManager

        # Prepare scan configuration based on category
        if category == 'network':
            scan_config = {
                'category': 'network',
                'scan_type': scan_type,
                'target': target,
                'tools': ['nmap', 'openvas', 'metasploit'],
                'user_id': current_user_id,
                'ports': ports,
                'config': {}
            }
        elif category == 'web':
            scan_config = {
                'category': 'web',
                'scan_type': scan_type,
                'target': target,
                'tools': ['nikto', 'sqlmap', 'dirb', 'gobuster', 'zap'],
                'user_id': current_user_id,
                'config': {}
            }
        elif category == 'vulnerability':
            scan_config = {
                'category': 'vulnerability',
                'scan_type': scan_type,
                'target': target,
                'tools': ['openvas', 'metasploit'],
                'user_id': current_user_id,
                'config': {}
            }
        elif category == 'deep':
            scan_config = {
                'category': 'deep',
                'scan_type': scan_type,
                'target': target,
                'tools': original_scan.get('tools', ['nmap', 'nikto', 'openvas', 'metasploit']),
                'user_id': current_user_id,
                'ports': ports,
                'config': {}
            }
        else:
            return jsonify({'error': f'Unknown scan category: {category}'}), 400

        # Start the scan using ScanManager
        try:
            new_scan_id = ScanManager.start_scan(scan_config)
            if not new_scan_id:
                return jsonify({'error': 'Failed to start resumed scan'}), 500

        except Exception as e:
            print(f"❌ Error starting resumed scan: {e}")
            return jsonify({'error': f'Failed to start resumed scan: {str(e)}'}), 500

        # Update the new scan record to indicate it was resumed from another scan
        try:
            mongo.db.scans.update_one(
                {'scan_id': new_scan_id},
                {'$set': {'resumed_from': scan_id}}
            )
        except Exception as e:
            print(f"⚠️ Warning: Could not update resumed_from field: {e}")

        print(f"✅ Scan resumed successfully: {new_scan_id}")
        return jsonify({
            'message': 'Scan resumed successfully',
            'original_scan_id': scan_id,
            'new_scan_id': new_scan_id,
            'status': 'running'
        }), 200

    except Exception as e:
        print(f"❌ Error resuming scan: {e}")
        return jsonify({'error': str(e)}), 500

@pentesting_bp.route('/scan/delete/<scan_id>', methods=['DELETE', 'OPTIONS'])
@handle_options
@jwt_required()
def delete_scan(scan_id):
    """
    🗑️ Supprimer complètement un scan de la base de données
    """
    try:
        from flask_jwt_extended import get_jwt
        claims = get_jwt()
        current_user_id = get_jwt_identity()
        user_role = claims.get('role', 'user')

        # Récupérer le scan
        scan = mongo.db.scans.find_one({'scan_id': scan_id})
        if not scan:
            return jsonify({'error': 'Scan not found'}), 404

        # Vérifier les permissions (seuls les admins ou le propriétaire peuvent supprimer)
        if user_role != 'admin' and scan.get('user_id') != current_user_id:
            return jsonify({'error': 'Access denied'}), 403

        # Ne pas permettre la suppression d'un scan en cours
        if scan.get('status') == 'running':
            return jsonify({'error': 'Cannot delete a running scan. Stop it first.'}), 400

        # Supprimer le scan de la base de données
        result = mongo.db.scans.delete_one({'scan_id': scan_id})

        if result.deleted_count > 0:
            print(f"🗑️ Scan {scan_id} deleted by user {current_user_id}")
            return jsonify({
                'message': 'Scan deleted successfully',
                'scan_id': scan_id
            }), 200
        else:
            return jsonify({'error': 'Failed to delete scan'}), 500

    except Exception as e:
        print(f"❌ Error deleting scan: {e}")
        return jsonify({'error': str(e)}), 500

@pentesting_bp.route('/scan/logs/<scan_id>', methods=['GET', 'OPTIONS'])
@handle_options
@jwt_required()
def get_scan_logs(scan_id):
    """
    📋 Récupérer les logs d'un scan
    """
    try:
        from flask_jwt_extended import get_jwt
        claims = get_jwt()
        current_user_id = get_jwt_identity()
        user_role = claims.get('role', 'user')

        # Récupérer le scan pour vérifier les permissions
        scan = mongo.db.scans.find_one({'scan_id': scan_id})
        if not scan:
            return jsonify({'error': 'Scan not found'}), 404

        # Vérifier les permissions
        if user_role != 'admin' and scan.get('user_id') != current_user_id:
            return jsonify({'error': 'Access denied'}), 403

        # Paramètres de requête
        limit = int(request.args.get('limit', 100))
        level = request.args.get('level')  # info, debug, error, etc.
        tool = request.args.get('tool')    # filtre par outil

        # Récupérer les logs
        if tool:
            logs = ScanLogger.get_tool_logs(scan_id, tool, limit)
        else:
            logs = ScanLogger.get_scan_logs(scan_id, limit, level)

        return jsonify({
            'scan_id': scan_id,
            'logs': logs,
            'total_logs': len(logs),
            'filters': {
                'limit': limit,
                'level': level,
                'tool': tool
            }
        }), 200

    except Exception as e:
        print(f"❌ Error getting scan logs: {e}")
        return jsonify({'error': str(e)}), 500

@pentesting_bp.route('/scan/logs/<scan_id>/live', methods=['GET', 'OPTIONS'])
@handle_options
@jwt_required()
def get_live_scan_logs(scan_id):
    """
    📡 Récupérer les logs en temps réel d'un scan (derniers logs)
    """
    try:
        from flask_jwt_extended import get_jwt
        claims = get_jwt()
        current_user_id = get_jwt_identity()
        user_role = claims.get('role', 'user')

        # Récupérer le scan pour vérifier les permissions
        scan = mongo.db.scans.find_one({'scan_id': scan_id})
        if not scan:
            return jsonify({'error': 'Scan not found'}), 404

        # Vérifier les permissions
        if user_role != 'admin' and scan.get('user_id') != current_user_id:
            return jsonify({'error': 'Access denied'}), 403

        # Récupérer les logs récents depuis le scan document
        recent_logs = scan.get('recent_logs', [])
        last_log = scan.get('last_log')

        # Nettoyer les logs pour éviter les problèmes de sérialisation
        def clean_log(log):
            if not log:
                return None
            return {
                'scan_id': log.get('scan_id'),
                'timestamp': log.get('timestamp'),
                'level': log.get('level'),
                'event_type': log.get('event_type'),
                'message': log.get('message'),
                'data': log.get('data', {})
            }

        cleaned_recent_logs = [clean_log(log) for log in recent_logs if log]
        cleaned_last_log = clean_log(last_log)

        # Aussi récupérer les 20 derniers logs de la collection
        latest_logs = ScanLogger.get_scan_logs(scan_id, 20)

        return jsonify({
            'scan_id': scan_id,
            'status': scan.get('status'),
            'current_tool': scan.get('current_tool'),
            'last_log': cleaned_last_log,
            'recent_logs': cleaned_recent_logs,
            'latest_logs': latest_logs,
            'timestamp': datetime.utcnow().isoformat()
        }), 200

    except Exception as e:
        print(f"❌ Error getting live scan logs: {e}")
        return jsonify({'error': str(e)}), 500

@pentesting_bp.route('/scans', methods=['GET'])
@jwt_required()
def get_scan_history():
    """Obtenir l'historique des scans depuis MongoDB"""
    try:
        print("🔍 Fetching scan history from MongoDB...")

        # Récupérer les informations de l'utilisateur depuis le JWT
        from flask_jwt_extended import get_jwt
        claims = get_jwt()
        current_user_id = get_jwt_identity()  # username
        user_role = claims.get('role', 'user')

        print(f"👤 User: {current_user_id}, Role: {user_role}")

        # Si l'utilisateur est admin, récupérer tous les scans
        # Sinon, filtrer par user_id
        if user_role == 'admin':
            scans = get_scans_from_db(limit=50)  # Tous les scans
            print("🔑 Admin access: retrieving all scans")
        else:
            scans = get_scans_from_db(limit=50, user_id=current_user_id)  # Scans de l'utilisateur uniquement
            print(f"👤 User access: retrieving scans for {current_user_id}")

        if not scans:
            print("📭 No scans found in MongoDB")
            return jsonify({
                "scans": [],
                "total": 0,
                "message": "No scans found. Start your first scan to see results here."
            }), 200

        print(f"✅ Retrieved {len(scans)} scans from MongoDB")

        # Formater les scans pour l'API
        formatted_scans = []
        for scan in scans:
            # Récupérer l'email de l'utilisateur au lieu de l'user_id
            user_id = scan.get('user_id')
            user_email = None
            if user_id:
                try:
                    from app.models.incident_model import get_user_email_by_id
                    user_email = get_user_email_by_id(user_id)
                except Exception as e:
                    print(f"❌ Error getting user email for {user_id}: {e}")
                    user_email = user_id  # Fallback to user_id if email not found

            formatted_scan = {
                "scan_id": scan.get('scan_id'),
                "mongo_id": str(scan.get('_id')),
                "category": scan.get('category'),
                "scan_type": scan.get('scan_type'),
                "target": scan.get('target'),
                "status": scan.get('status'),
                "start_time": scan.get('start_time'),
                "end_time": scan.get('end_time'),
                "tools": scan.get('tools', []),
                "results": scan.get('results', {}),
                "created_at": scan.get('created_at'),
                "user_id": user_email or user_id,  # Utiliser l'email si disponible, sinon user_id
                "user_email": user_email  # Ajouter un champ séparé pour l'email
            }
            formatted_scans.append(formatted_scan)

        return jsonify({
            "scans": formatted_scans,
            "total": len(formatted_scans),
            "source": "MongoDB",
            "message": f"Showing {len(formatted_scans)} scan results",
            "user_role": user_role  # Pour debug côté frontend
        }), 200

    except Exception as e:
        return jsonify({"error": f"Error retrieving history: {str(e)}"}), 500

@pentesting_bp.route('/scan/<scan_id>', methods=['GET'])
@jwt_required()
def get_scan_result(scan_id):
    """Obtenir le résultat d'un scan spécifique depuis MongoDB"""
    try:
        print(f"🔍 Fetching scan result for ID: {scan_id}")

        # Récupérer les informations de l'utilisateur depuis le JWT
        from flask_jwt_extended import get_jwt
        claims = get_jwt()
        current_user_id = get_jwt_identity()  # username
        user_role = claims.get('role', 'user')

        print(f"👤 User: {current_user_id}, Role: {user_role}")

        # Si l'utilisateur est admin, récupérer le scan sans filtre
        # Sinon, filtrer par user_id
        if user_role == 'admin':
            scan_result = get_scan_by_id(scan_id)  # Accès admin
            print("🔑 Admin access: retrieving scan without user filter")
        else:
            scan_result = get_scan_by_id(scan_id, user_id=current_user_id)  # Accès utilisateur
            print(f"👤 User access: retrieving scan for {current_user_id}")

        if not scan_result:
            print(f"❌ Scan {scan_id} not found in MongoDB or access denied")
            return jsonify({"error": f"Scan {scan_id} not found or access denied"}), 404

        print(f"✅ Retrieved scan {scan_id} from MongoDB")

        # Retourner le résultat
        return jsonify(scan_result), 200

    except Exception as e:
        print(f"❌ Error retrieving scan {scan_id}: {str(e)}")
        return jsonify({"error": f"Error retrieving scan: {str(e)}"}), 500



# ============================================================================
# FONCTIONS UTILITAIRES - Parsing des résultats
# ============================================================================

def _parse_nmap_vulnerabilities(nmap_output):
    """Parser les vulnérabilités depuis la sortie nmap"""
    vulnerabilities = []
    lines = nmap_output.split('\n')

    for i, line in enumerate(lines):
        if 'VULNERABLE' in line.upper() or 'CVE-' in line:
            vuln = {
                "name": line.strip(),
                "severity": "medium",  # Par défaut
                "description": line.strip(),
                "cve": None
            }

            # Extraire CVE si présent
            if 'CVE-' in line:
                import re
                cve_match = re.search(r'CVE-\d{4}-\d+', line)
                if cve_match:
                    vuln["cve"] = cve_match.group()

            # Déterminer la sévérité basée sur des mots-clés
            if any(keyword in line.upper() for keyword in ['CRITICAL', 'HIGH', 'SEVERE']):
                vuln["severity"] = "high"
            elif any(keyword in line.upper() for keyword in ['LOW', 'INFO']):
                vuln["severity"] = "low"

            vulnerabilities.append(vuln)

    return vulnerabilities

def _parse_nmap_ports(nmap_output):
    """Parser les ports ouverts depuis la sortie nmap"""
    ports = []
    lines = nmap_output.split('\n')

    for line in lines:
        if '/tcp' in line or '/udp' in line:
            parts = line.split()
            if len(parts) >= 3:
                port_info = parts[0].split('/')
                if len(port_info) >= 2:
                    port = {
                        "port": port_info[0],
                        "protocol": port_info[1],
                        "state": parts[1] if len(parts) > 1 else "unknown",
                        "service": parts[2] if len(parts) > 2 else "unknown"
                    }
                    ports.append(port)

    return ports

def _parse_nikto_vulnerabilities(nikto_output):
    """Parser les vulnérabilités depuis la sortie Nikto"""
    vulnerabilities = []
    lines = nikto_output.split('\n')

    for line in lines:
        if line.startswith('+ ') and any(keyword in line.lower() for keyword in ['vulnerability', 'error', 'disclosure', 'missing', 'outdated']):
            vuln = {
                "name": "Web Vulnerability Found",
                "severity": "medium",  # Par défaut
                "description": line.strip(),
                "tool": "nikto"
            }

            # Déterminer la sévérité basée sur des mots-clés
            if any(keyword in line.lower() for keyword in ['critical', 'high', 'severe', 'exploit']):
                vuln["severity"] = "high"
            elif any(keyword in line.lower() for keyword in ['low', 'info', 'disclosure']):
                vuln["severity"] = "low"

            vulnerabilities.append(vuln)

    return vulnerabilities


# ============================================================================
# ROUTES ADMIN - Gestion OpenVAS
# ============================================================================

@pentesting_bp.route('/admin/openvas/targets', methods=['GET'])
@jwt_required()
@admin_required
def get_openvas_targets():
    """Obtenir la liste des targets OpenVAS (Admin uniquement)"""
    try:
        print("🔍 Admin: Fetching OpenVAS targets...")

        from app.services.openvas_service import OpenVASService
        openvas = OpenVASService()

        if not openvas.is_available():
            return jsonify({"error": "OpenVAS service not available"}), 503

        targets = openvas.get_targets()
        print(f"✅ Retrieved {len(targets)} OpenVAS targets")

        return jsonify({
            "targets": targets,
            "total": len(targets),
            "message": f"Found {len(targets)} OpenVAS targets"
        }), 200

    except Exception as e:
        print(f"❌ Error fetching OpenVAS targets: {str(e)}")
        return jsonify({"error": f"Error fetching targets: {str(e)}"}), 500

@pentesting_bp.route('/admin/openvas/tasks', methods=['GET'])
@jwt_required()
@admin_required
def get_openvas_tasks():
    """Obtenir la liste des tasks OpenVAS (Admin uniquement)"""
    try:
        print("🔍 Admin: Fetching OpenVAS tasks...")

        from app.services.openvas_service import OpenVASService
        openvas = OpenVASService()

        if not openvas.is_available():
            return jsonify({"error": "OpenVAS service not available"}), 503

        tasks = openvas.get_tasks()
        print(f"✅ Retrieved {len(tasks)} OpenVAS tasks")

        return jsonify({
            "tasks": tasks,
            "total": len(tasks),
            "message": f"Found {len(tasks)} OpenVAS tasks"
        }), 200

    except Exception as e:
        print(f"❌ Error fetching OpenVAS tasks: {str(e)}")
        return jsonify({"error": f"Error fetching tasks: {str(e)}"}), 500

@pentesting_bp.route('/admin/openvas/targets/<target_id>', methods=['DELETE'])
@jwt_required()
@admin_required
def delete_openvas_target(target_id):
    """Supprimer une target OpenVAS (Admin uniquement)"""
    try:
        print(f"🗑️ Admin: Deleting OpenVAS target {target_id}...")

        from app.services.openvas_service import OpenVASService
        openvas = OpenVASService()

        if not openvas.is_available():
            return jsonify({"error": "OpenVAS service not available"}), 503

        result = openvas.delete_target(target_id)

        if result.get('success'):
            print(f"✅ Target {target_id} deleted successfully")
            return jsonify({
                "message": f"Target {target_id} deleted successfully",
                "target_id": target_id
            }), 200
        else:
            print(f"❌ Failed to delete target {target_id}: {result.get('message')}")
            return jsonify({"error": result.get('message', 'Failed to delete target')}), 400

    except Exception as e:
        print(f"❌ Error deleting OpenVAS target {target_id}: {str(e)}")
        return jsonify({"error": f"Error deleting target: {str(e)}"}), 500

@pentesting_bp.route('/admin/openvas/tasks/<task_id>', methods=['DELETE'])
@jwt_required()
@admin_required
def delete_openvas_task(task_id):
    """Supprimer une task OpenVAS (Admin uniquement)"""
    try:
        print(f"🗑️ Admin: Deleting OpenVAS task {task_id}...")

        from app.services.openvas_service import OpenVASService
        openvas = OpenVASService()

        if not openvas.is_available():
            return jsonify({"error": "OpenVAS service not available"}), 503

        result = openvas.delete_task(task_id)

        if result.get('success'):
            print(f"✅ Task {task_id} deleted successfully")
            return jsonify({
                "message": f"Task {task_id} deleted successfully",
                "task_id": task_id
            }), 200
        else:
            print(f"❌ Failed to delete task {task_id}: {result.get('message')}")
            return jsonify({"error": result.get('message', 'Failed to delete task')}), 400

    except Exception as e:
        print(f"❌ Error deleting OpenVAS task {task_id}: {str(e)}")
        return jsonify({"error": f"Error deleting task: {str(e)}"}), 500

@pentesting_bp.route('/admin/openvas/cleanup', methods=['POST'])
@jwt_required()
@admin_required
def cleanup_openvas():
    """Nettoyer tous les targets et tasks OpenVAS (Admin uniquement)"""
    try:
        print("🧹 Admin: Starting OpenVAS cleanup...")

        from app.services.openvas_service import OpenVASService
        openvas = OpenVASService()

        if not openvas.is_available():
            return jsonify({"error": "OpenVAS service not available"}), 503

        # Récupérer tous les targets et tasks
        targets = openvas.get_targets()
        tasks = openvas.get_tasks()

        deleted_targets = 0
        deleted_tasks = 0
        errors = []

        # Supprimer toutes les tasks d'abord
        for task in tasks:
            try:
                result = openvas.delete_task(task['id'])
                if result.get('success'):
                    deleted_tasks += 1
                    print(f"✅ Deleted task: {task['name']} ({task['id']})")
                else:
                    errors.append(f"Failed to delete task {task['name']}: {result.get('message')}")
            except Exception as e:
                errors.append(f"Error deleting task {task['name']}: {str(e)}")

        # Supprimer tous les targets ensuite
        for target in targets:
            try:
                result = openvas.delete_target(target['id'])
                if result.get('success'):
                    deleted_targets += 1
                    print(f"✅ Deleted target: {target['name']} ({target['id']})")
                else:
                    errors.append(f"Failed to delete target {target['name']}: {result.get('message')}")
            except Exception as e:
                errors.append(f"Error deleting target {target['name']}: {str(e)}")

        print(f"🧹 Cleanup completed: {deleted_tasks} tasks, {deleted_targets} targets deleted")

        return jsonify({
            "message": "OpenVAS cleanup completed",
            "deleted_tasks": deleted_tasks,
            "deleted_targets": deleted_targets,
            "total_targets": len(targets),
            "total_tasks": len(tasks),
            "errors": errors
        }), 200

    except Exception as e:
        print(f"❌ Error during OpenVAS cleanup: {str(e)}")
        return jsonify({"error": f"Error during cleanup: {str(e)}"}), 500


