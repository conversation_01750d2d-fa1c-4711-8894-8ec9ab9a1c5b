from flask import Blueprint, jsonify, request
from app.services.nessus_scan_service import NessusScanService
nessus_bp = Blueprint("nessus", __name__)

@nessus_bp.route("/scans", methods=["GET"])
def list_scans():
    try:
        service = NessusScanService()
        result = service.list_scans()
        return jsonify(result), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500



@nessus_bp.route("/policies", methods=["GET"])
def list_policies():
    try:
        service = NessusScanService()
        response = service.list_policies()
        return jsonify(response), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@nessus_bp.route("/policies", methods=["POST"])
def create_policy():
    try:
        policy_data = request.get_json()

        service = NessusScanService()
        result = service.create_policy(policy_data)

        return jsonify(result), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@nessus_bp.route("/templates", methods=["GET"])
def list_templates():
    try:
        service = NessusScanService()
        response = service.list_templates()
        return jsonify(response), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@nessus_bp.route("/scans", methods=["POST"])
def create_scan():
    try:
        payload = request.get_json()

        # Instantiate the service and call the create_scan method
        service = NessusScanService()
        result = service.create_scan(payload)

        return jsonify(result), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@nessus_bp.route("/scans/<scan_id>/launch", methods=["POST"])
def launch_scan(scan_id):
    try:
        # Instantiate the service and call the launch_scan method
        service = NessusScanService()
        result = service.launch_scan(scan_id)

        return jsonify(result), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@nessus_bp.route("/scans/<scan_id>", methods=["GET"])
def get_scan_details(scan_id):
    try:
        service = NessusScanService()
        result = service.get_scan_details(scan_id)

        return jsonify(result), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@nessus_bp.route("/scans/<scan_id>", methods=["DELETE"])
def delete_scan(scan_id):
    try:
        service = NessusScanService()
        result = service.delete_scan(scan_id)

        return jsonify(result), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@nessus_bp.route("/scans/<scan_id>/export", methods=["POST"])
def export_scan(scan_id):
    try:
        # Get format from request body or query parameters, default to 'nessus'
        if request.is_json and request.get_json():
            format = request.get_json().get('format', 'nessus')
        else:
            format = request.args.get('format', 'nessus')

        service = NessusScanService()
        result = service.export_scan(scan_id, format)

        return jsonify(result), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@nessus_bp.route("/scans/<scan_id>/export/<file_id>/download", methods=["GET"])
def download_export(scan_id, file_id):
    try:
        service = NessusScanService()
        result = service.download_export(scan_id, file_id)

        return jsonify(result), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@nessus_bp.route("/scans/<scan_id>/export/<file_id>/status", methods=["GET"])
def export_status(scan_id, file_id):
    try:
        service = NessusScanService()
        result = service.export_status(scan_id, file_id)

        return jsonify(result), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@nessus_bp.route("/plugins/plugin/<plugin_id>", methods=["GET"])
def get_plugin_details(plugin_id):
    try:
        service = NessusScanService()
        result = service.get_plugin_details(plugin_id)

        return jsonify(result), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@nessus_bp.route("/plugins/families", methods=["GET"])
def list_plugin_families():
    try:
        service = NessusScanService()
        result = service.list_plugin_families()

        return jsonify(result), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@nessus_bp.route("/plugins/families/<family_id>", methods=["GET"])
def get_family_plugins(family_id):
    try:
        service = NessusScanService()
        result = service.get_family_plugins(family_id)

        return jsonify(result), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500
