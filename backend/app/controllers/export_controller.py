"""
Contrôleur pour les exports de rapports
"""
from flask import Blueprint, request, jsonify, send_file
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.services.pdf_report_service import PDFReportService
from app.services.malware_pdf_service import MalwarePDFService
from app.models.scan_model import get_scan_by_id, get_scans_from_db
from app.models.malware_model import get_malware_analysis_by_id
from app.extensions import mongo
from app.utils.decorators import handle_options
import tempfile
import os
import csv
import io
from datetime import datetime
from bson import ObjectId

# Créer le blueprint pour les exports
export_bp = Blueprint('export', __name__, url_prefix='/export')

@export_bp.route('/scan/<scan_id>/pdf', methods=['GET', 'OPTIONS'])
@handle_options
@jwt_required()
def export_scan_pdf(scan_id):
    """
    Exporte un rapport de scan en PDF
    
    Args:
        scan_id: ID du scan à exporter
        
    Returns:
        PDF file: Rapport de scan en format PDF
    """
    try:
        # Récupérer l'utilisateur actuel
        current_user_id = get_jwt_identity()

        # Récupérer l'utilisateur depuis MongoDB
        # current_user_id peut être soit un username soit un ObjectId
        user = None
        if ObjectId.is_valid(current_user_id):
            user = mongo.db.users.find_one({'_id': ObjectId(current_user_id)})
        else:
            user = mongo.db.users.find_one({'username': current_user_id})

        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Récupérer les données du scan
        # Si l'utilisateur n'est pas admin, filtrer par user_id
        user_filter = None if user.get('role') == 'admin' else current_user_id
        scan_data = get_scan_by_id(scan_id, user_filter)
        
        if not scan_data:
            return jsonify({'error': 'Scan not found or access denied'}), 404
        
        # Vérifier que le scan est terminé
        if scan_data.get('status') not in ['completed', 'stopped', 'failed']:
            return jsonify({
                'error': 'Cannot export report for running scan',
                'message': 'Please wait for the scan to complete before exporting the report'
            }), 400
        
        # Générer le rapport PDF
        pdf_service = PDFReportService()
        pdf_buffer = pdf_service.generate_scan_report(scan_data)
        
        # Créer un fichier temporaire pour le PDF
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            temp_file.write(pdf_buffer.getvalue())
            temp_file_path = temp_file.name
        
        # Générer le nom du fichier
        target = scan_data.get('target', 'unknown').replace('/', '_').replace(':', '_')
        category = scan_data.get('category', 'scan')
        scan_type = scan_data.get('scan_type', 'basic')
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        filename = f"PICA_Report_{category}_{scan_type}_{target}_{timestamp}.pdf"
        
        # Envoyer le fichier
        return send_file(
            temp_file_path,
            as_attachment=True,
            download_name=filename,
            mimetype='application/pdf'
        )
        
    except Exception as e:
        print(f"❌ Error exporting scan PDF: {str(e)}")
        return jsonify({
            'error': 'Failed to export PDF report',
            'details': str(e)
        }), 500
    
    finally:
        # Nettoyer le fichier temporaire
        try:
            if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
        except:
            pass

@export_bp.route('/scan/<scan_id>/json', methods=['GET', 'OPTIONS'])
@handle_options
@jwt_required()
def export_scan_json(scan_id):
    """
    Exporte les données de scan en format JSON
    
    Args:
        scan_id: ID du scan à exporter
        
    Returns:
        JSON: Données complètes du scan
    """
    try:
        # Récupérer l'utilisateur actuel
        current_user_id = get_jwt_identity()

        # Récupérer l'utilisateur depuis MongoDB
        user = None
        if ObjectId.is_valid(current_user_id):
            user = mongo.db.users.find_one({'_id': ObjectId(current_user_id)})
        else:
            user = mongo.db.users.find_one({'username': current_user_id})

        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Récupérer les données du scan
        # Si l'utilisateur n'est pas admin, filtrer par user_id
        user_filter = None if user.get('role') == 'admin' else current_user_id
        scan_data = get_scan_by_id(scan_id, user_filter)
        
        if not scan_data:
            return jsonify({'error': 'Scan not found or access denied'}), 404
        
        # Préparer les données d'export
        export_data = {
            'export_metadata': {
                'export_date': datetime.utcnow().isoformat(),
                'export_format': 'json',
                'exported_by': current_user_id,
                'scan_id': scan_id
            },
            'scan_data': scan_data
        }
        
        return jsonify(export_data), 200
        
    except Exception as e:
        print(f"❌ Error exporting scan JSON: {str(e)}")
        return jsonify({
            'error': 'Failed to export JSON data',
            'details': str(e)
        }), 500

@export_bp.route('/scan/<scan_id>/summary', methods=['GET', 'OPTIONS'])
@handle_options
@jwt_required()
def export_scan_summary(scan_id):
    """
    Exporte un résumé du scan en format JSON
    
    Args:
        scan_id: ID du scan à exporter
        
    Returns:
        JSON: Résumé du scan avec statistiques principales
    """
    try:
        # Récupérer l'utilisateur actuel
        current_user_id = get_jwt_identity()

        # Récupérer l'utilisateur depuis MongoDB
        user = None
        if ObjectId.is_valid(current_user_id):
            user = mongo.db.users.find_one({'_id': ObjectId(current_user_id)})
        else:
            user = mongo.db.users.find_one({'username': current_user_id})

        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Récupérer les données du scan
        user_filter = None if user.get('role') == 'admin' else current_user_id
        scan_data = get_scan_by_id(scan_id, user_filter)
        
        if not scan_data:
            return jsonify({'error': 'Scan not found or access denied'}), 404
        
        # Calculer les statistiques
        results = scan_data.get('results', {})
        vulnerabilities = results.get('vulnerabilities', [])
        ports = results.get('ports', [])
        
        # Compter les vulnérabilités par sévérité
        vuln_stats = {'high': 0, 'medium': 0, 'low': 0, 'info': 0}
        for vuln in vulnerabilities:
            severity = vuln.get('severity', 'info').lower()
            if severity in vuln_stats:
                vuln_stats[severity] += 1
            else:
                vuln_stats['info'] += 1
        
        # Compter les services
        services = {}
        for port in ports:
            service = port.get('service', 'Unknown')
            services[service] = services.get(service, 0) + 1
        
        # Préparer le résumé
        summary = {
            'export_metadata': {
                'export_date': datetime.utcnow().isoformat(),
                'export_format': 'summary',
                'exported_by': current_user_id,
                'scan_id': scan_id
            },
            'scan_info': {
                'scan_id': scan_data.get('scan_id'),
                'target': scan_data.get('target'),
                'category': scan_data.get('category'),
                'scan_type': scan_data.get('scan_type'),
                'status': scan_data.get('status'),
                'start_time': scan_data.get('start_time'),
                'end_time': scan_data.get('end_time'),
                'tools': scan_data.get('tools', []),
                'user_id': scan_data.get('user_id')
            },
            'statistics': {
                'total_vulnerabilities': len(vulnerabilities),
                'vulnerabilities_by_severity': vuln_stats,
                'total_open_ports': len(ports),
                'services_detected': len(services),
                'top_services': dict(sorted(services.items(), key=lambda x: x[1], reverse=True)[:5])
            },
            'risk_assessment': {
                'risk_level': _calculate_risk_level(vuln_stats),
                'critical_issues': vuln_stats['high'],
                'requires_immediate_attention': vuln_stats['high'] > 0,
                'overall_score': _calculate_security_score(vuln_stats, len(ports))
            }
        }
        
        return jsonify(summary), 200
        
    except Exception as e:
        print(f"❌ Error exporting scan summary: {str(e)}")
        return jsonify({
            'error': 'Failed to export summary',
            'details': str(e)
        }), 500

def _calculate_risk_level(vuln_stats):
    """Calcule le niveau de risque basé sur les vulnérabilités"""
    if vuln_stats['high'] > 0:
        return 'CRITICAL'
    elif vuln_stats['medium'] > 3:
        return 'HIGH'
    elif vuln_stats['medium'] > 0 or vuln_stats['low'] > 5:
        return 'MEDIUM'
    else:
        return 'LOW'

def _calculate_security_score(vuln_stats, port_count):
    """Calcule un score de sécurité sur 100"""
    base_score = 100
    
    # Déductions pour les vulnérabilités
    base_score -= vuln_stats['high'] * 25  # -25 points par vulnérabilité critique
    base_score -= vuln_stats['medium'] * 10  # -10 points par vulnérabilité moyenne
    base_score -= vuln_stats['low'] * 3  # -3 points par vulnérabilité faible
    base_score -= vuln_stats['info'] * 1  # -1 point par information
    
    # Déduction pour les ports ouverts (surface d'attaque)
    if port_count > 20:
        base_score -= 15
    elif port_count > 10:
        base_score -= 10
    elif port_count > 5:
        base_score -= 5
    
    # S'assurer que le score reste entre 0 et 100
    return max(0, min(100, base_score))

@export_bp.route('/scans/csv', methods=['GET', 'OPTIONS'])
@handle_options
@jwt_required()
def export_all_scans_csv():
    """
    Exporte tous les scans en format CSV

    Returns:
        CSV file: Fichier CSV contenant tous les scans et leurs détails
    """
    try:
        # Récupérer l'utilisateur actuel
        current_user_id = get_jwt_identity()

        # Récupérer l'utilisateur depuis MongoDB
        user = None
        if ObjectId.is_valid(current_user_id):
            user = mongo.db.users.find_one({'_id': ObjectId(current_user_id)})
        else:
            user = mongo.db.users.find_one({'username': current_user_id})

        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Récupérer tous les scans
        # Si l'utilisateur n'est pas admin, filtrer par user_id
        user_filter = None if user.get('role') == 'admin' else current_user_id

        # Récupérer les scans depuis MongoDB
        if user_filter:
            scans = list(mongo.db.scans.find({'user_id': user_filter}).sort('start_time', -1))
        else:
            scans = list(mongo.db.scans.find({}).sort('start_time', -1))

        if not scans:
            return jsonify({'error': 'No scans found'}), 404

        # Créer le CSV en mémoire
        output = io.StringIO()
        writer = csv.writer(output)

        # En-têtes du CSV avec détails complets
        headers = [
            'ID du Scan',
            'Cible',
            'Catégorie',
            'Type de Scan',
            'Statut',
            'Date de Début',
            'Date de Fin',
            'Durée (secondes)',
            'Utilisateur',
            'Outils Utilisés',
            'Score de Sécurité',
            'Niveau de Risque',
            'Type d\'Élément',  # 'Vulnérabilité' ou 'Port'
            'Nom/Titre',
            'Description',
            'Sévérité',
            'Port',
            'Protocole',
            'Service',
            'Version',
            'État',
            'Score CVSS',
            'CVE',
            'Solution',
            'Détails Supplémentaires'
        ]

        writer.writerow(headers)

        # Traiter chaque scan
        for scan in scans:
            # Calculer les statistiques générales
            results = scan.get('results', {})
            vulnerabilities = results.get('vulnerabilities', [])
            ports = results.get('ports', [])

            # Compter les vulnérabilités par sévérité
            vuln_stats = {'high': 0, 'medium': 0, 'low': 0, 'info': 0}
            for vuln in vulnerabilities:
                severity = vuln.get('severity', 'info').lower()
                if severity in vuln_stats:
                    vuln_stats[severity] += 1
                else:
                    vuln_stats['info'] += 1

            # Calculer les métriques générales
            duration = _calculate_scan_duration(scan)
            security_score = _calculate_security_score_csv(vuln_stats, len(ports))
            risk_level = _calculate_risk_level_csv(vuln_stats)
            tools = ', '.join(scan.get('tools', []))

            # Données communes du scan
            scan_base_data = [
                scan.get('scan_id', ''),
                scan.get('target', ''),
                scan.get('category', ''),
                scan.get('scan_type', ''),
                scan.get('status', ''),
                _format_datetime_csv(scan.get('start_time')),
                _format_datetime_csv(scan.get('end_time')),
                duration,
                scan.get('user_id', ''),
                tools,
                security_score,
                risk_level
            ]

            # Traiter les vulnérabilités (une ligne par vulnérabilité)
            if vulnerabilities:
                for vuln in vulnerabilities:
                    vuln_row = scan_base_data + [
                        'Vulnérabilité',  # Type d'élément
                        vuln.get('name', vuln.get('title', 'Vulnérabilité sans nom')),  # Nom/Titre
                        vuln.get('description', ''),  # Description
                        vuln.get('severity', '').upper(),  # Sévérité
                        vuln.get('port', ''),  # Port
                        '',  # Protocole (vide pour vulnérabilités)
                        vuln.get('service', ''),  # Service
                        '',  # Version (vide pour vulnérabilités)
                        '',  # État (vide pour vulnérabilités)
                        vuln.get('cvss', ''),  # Score CVSS
                        vuln.get('cve', ''),  # CVE
                        vuln.get('solution', ''),  # Solution
                        vuln.get('details', vuln.get('output', ''))  # Détails supplémentaires
                    ]
                    writer.writerow(vuln_row)

            # Traiter les ports (une ligne par port)
            if ports:
                for port in ports:
                    port_row = scan_base_data + [
                        'Port',  # Type d'élément
                        f"Port {port.get('port', 'N/A')}",  # Nom/Titre
                        f"Port ouvert détecté sur {port.get('service', 'service inconnu')}",  # Description
                        '',  # Sévérité (vide pour ports)
                        port.get('port', ''),  # Port
                        port.get('protocol', ''),  # Protocole
                        port.get('service', ''),  # Service
                        port.get('version', ''),  # Version
                        port.get('state', ''),  # État
                        '',  # Score CVSS (vide pour ports)
                        '',  # CVE (vide pour ports)
                        '',  # Solution (vide pour ports)
                        port.get('product', port.get('extrainfo', ''))  # Détails supplémentaires
                    ]
                    writer.writerow(port_row)

            # Si aucune vulnérabilité ni port, créer une ligne de résumé
            if not vulnerabilities and not ports:
                summary_row = scan_base_data + [
                    'Résumé',  # Type d'élément
                    'Aucune vulnérabilité ou port détecté',  # Nom/Titre
                    'Ce scan n\'a détecté aucune vulnérabilité ni port ouvert',  # Description
                    '',  # Sévérité
                    '',  # Port
                    '',  # Protocole
                    '',  # Service
                    '',  # Version
                    '',  # État
                    '',  # Score CVSS
                    '',  # CVE
                    '',  # Solution
                    'Scan terminé sans détection'  # Détails supplémentaires
                ]
                writer.writerow(summary_row)

        # Préparer le fichier pour téléchargement
        output.seek(0)

        # Créer un fichier temporaire
        with tempfile.NamedTemporaryFile(mode='w+', delete=False, suffix='.csv', encoding='utf-8') as temp_file:
            temp_file.write(output.getvalue())
            temp_file_path = temp_file.name

        # Générer le nom du fichier
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"PICA_All_Scans_Export_{timestamp}.csv"

        # Envoyer le fichier
        return send_file(
            temp_file_path,
            as_attachment=True,
            download_name=filename,
            mimetype='text/csv'
        )

    except Exception as e:
        print(f"❌ Error exporting all scans CSV: {str(e)}")
        return jsonify({
            'error': 'Failed to export CSV',
            'details': str(e)
        }), 500

    finally:
        # Nettoyer le fichier temporaire
        try:
            if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
        except:
            pass

def _calculate_scan_duration(scan_data):
    """Calcule la durée du scan en secondes"""
    start_time = scan_data.get('start_time')
    end_time = scan_data.get('end_time')

    if not start_time or not end_time:
        return 0

    try:
        if isinstance(start_time, str):
            start = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
        else:
            start = start_time

        if isinstance(end_time, str):
            end = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
        else:
            end = end_time

        duration = end - start
        return int(duration.total_seconds())
    except:
        return 0

def _format_datetime_csv(dt_str):
    """Formate une chaîne datetime pour CSV"""
    if not dt_str:
        return ''
    try:
        if isinstance(dt_str, str):
            dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
        else:
            dt = dt_str
        return dt.strftime('%Y-%m-%d %H:%M:%S')
    except:
        return str(dt_str)

def _calculate_security_score_csv(vuln_stats, port_count):
    """Calcule un score de sécurité sur 100 pour CSV"""
    base_score = 100

    # Déductions pour les vulnérabilités
    base_score -= vuln_stats['high'] * 25
    base_score -= vuln_stats['medium'] * 10
    base_score -= vuln_stats['low'] * 3
    base_score -= vuln_stats['info'] * 1

    # Déduction pour les ports ouverts
    if port_count > 20:
        base_score -= 15
    elif port_count > 10:
        base_score -= 10
    elif port_count > 5:
        base_score -= 5

    return max(0, min(100, base_score))

def _calculate_risk_level_csv(vuln_stats):
    """Calcule le niveau de risque pour CSV"""
    if vuln_stats['high'] > 0:
        return 'CRITIQUE'
    elif vuln_stats['medium'] > 3:
        return 'ÉLEVÉ'
    elif vuln_stats['medium'] > 0 or vuln_stats['low'] > 5:
        return 'MODÉRÉ'
    else:
        return 'FAIBLE'

@export_bp.route('/phishing/csv', methods=['GET', 'OPTIONS'])
@handle_options
@jwt_required()
def export_all_phishing_csv():
    """
    Exporte toutes les analyses de phishing en format CSV

    Returns:
        CSV file: Fichier CSV contenant toutes les analyses de phishing et leurs détails
    """
    try:
        # Récupérer l'utilisateur actuel
        current_user_id = get_jwt_identity()

        # Récupérer l'utilisateur depuis MongoDB
        user = None
        if ObjectId.is_valid(current_user_id):
            user = mongo.db.users.find_one({'_id': ObjectId(current_user_id)})
        else:
            user = mongo.db.users.find_one({'username': current_user_id})

        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Récupérer toutes les analyses de phishing
        # Si l'utilisateur n'est pas admin, filtrer par user_id
        if user.get('role') == 'admin':
            analyses = list(mongo.db.phishing_analyses.find({}).sort('created_at', -1))
        else:
            analyses = list(mongo.db.phishing_analyses.find({'user_id': current_user_id}).sort('created_at', -1))

        if not analyses:
            return jsonify({'error': 'No phishing analyses found'}), 404

        # Créer le CSV en mémoire
        output = io.StringIO()
        writer = csv.writer(output)

        # En-têtes du CSV pour phishing
        headers = [
            'ID de l\'Analyse',
            'URL Analysée',
            'Domaine',
            'Utilisateur',
            'Type d\'Analyse',
            'Statut',
            'Date de Création',
            'Date de Fin',
            'Durée (secondes)',
            'Score de Risque (%)',
            'Niveau de Risque',
            'Est Phishing',
            'Nombre de Vérifications',
            'Vérifications Échouées',
            'Détail de Vérification',
            'Type de Vérification',
            'Résultat',
            'Sévérité',
            'Description',
            'Recommandation'
        ]

        writer.writerow(headers)

        # Traiter chaque analyse
        for analysis in analyses:
            # Données de base de l'analyse
            analysis_id = analysis.get('analysis_id', str(analysis.get('_id', '')))
            url = analysis.get('url', '')
            user_id = analysis.get('user_id', '')
            analysis_type = analysis.get('analysis_type', 'phishing_detection')
            status = analysis.get('status', 'unknown')
            created_at = _format_datetime_csv(analysis.get('created_at'))
            completed_at = _format_datetime_csv(analysis.get('completed_at'))

            # Calculer la durée
            duration = _calculate_phishing_duration(analysis)

            # Extraire les résultats
            result = analysis.get('result', {})
            domain = result.get('domain', '')
            risk_score = result.get('risk_score', 0)
            likelihood = result.get('likelihood', 'Unknown')
            is_phishing = result.get('is_phishing', False)

            # Traiter les vérifications (checks)
            checks = result.get('checks', [])
            total_checks = len(checks)
            failed_checks = len([check for check in checks if not check.get('passed', True)])

            # Données communes de l'analyse
            analysis_base_data = [
                analysis_id,
                url,
                domain,
                user_id,
                analysis_type,
                status,
                created_at,
                completed_at,
                duration,
                risk_score,
                likelihood,
                'Oui' if is_phishing else 'Non',
                total_checks,
                failed_checks
            ]

            # Traiter chaque vérification (une ligne par vérification)
            if checks:
                for check in checks:
                    check_row = analysis_base_data + [
                        check.get('name', 'Vérification inconnue'),  # Détail de vérification
                        check.get('category', 'General'),  # Type de vérification
                        'Failed' if not check.get('passed', True) else 'Success',  # Résultat
                        _get_check_severity(check),  # Sévérité
                        check.get('description', ''),  # Description
                        check.get('recommendation', '')  # Recommandation
                    ]
                    writer.writerow(check_row)
            else:
                # Si aucune vérification, créer une ligne de résumé
                summary_row = analysis_base_data + [
                    'Résumé de l\'analyse',  # Détail de vérification
                    'Summary',  # Type de vérification
                    'Terminé',  # Résultat
                    _get_risk_severity(risk_score),  # Sévérité
                    f'Analyse terminée avec un score de risque de {risk_score}%',  # Description
                    _get_phishing_recommendation(risk_score, is_phishing)  # Recommandation
                ]
                writer.writerow(summary_row)

        # Préparer le fichier pour téléchargement
        output.seek(0)

        # Créer un fichier temporaire
        with tempfile.NamedTemporaryFile(mode='w+', delete=False, suffix='.csv', encoding='utf-8') as temp_file:
            temp_file.write(output.getvalue())
            temp_file_path = temp_file.name

        # Générer le nom du fichier
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"PICA_Phishing_Export_{timestamp}.csv"

        # Envoyer le fichier
        return send_file(
            temp_file_path,
            as_attachment=True,
            download_name=filename,
            mimetype='text/csv'
        )

    except Exception as e:
        print(f"❌ Error exporting phishing CSV: {str(e)}")
        return jsonify({
            'error': 'Failed to export phishing CSV',
            'details': str(e)
        }), 500

    finally:
        # Nettoyer le fichier temporaire
        try:
            if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
        except:
            pass

def _calculate_phishing_duration(analysis_data):
    """Calcule la durée de l'analyse de phishing en secondes"""
    created_at = analysis_data.get('created_at')
    completed_at = analysis_data.get('completed_at')

    if not created_at or not completed_at:
        return 0

    try:
        if isinstance(created_at, str):
            start = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
        else:
            start = created_at

        if isinstance(completed_at, str):
            end = datetime.fromisoformat(completed_at.replace('Z', '+00:00'))
        else:
            end = completed_at

        duration = end - start
        return int(duration.total_seconds())
    except:
        return 0

def _get_check_severity(check):
    """Détermine la sévérité d'une vérification"""
    if not check.get('passed', True):
        risk_increase = check.get('risk_increase', 0)
        if risk_increase >= 20:
            return 'CRITIQUE'
        elif risk_increase >= 10:
            return 'ÉLEVÉ'
        elif risk_increase >= 5:
            return 'MOYEN'
        else:
            return 'FAIBLE'
    return 'INFO'

def _get_risk_severity(risk_score):
    """Détermine la sévérité basée sur le score de risque"""
    if risk_score >= 80:
        return 'CRITIQUE'
    elif risk_score >= 60:
        return 'ÉLEVÉ'
    elif risk_score >= 40:
        return 'MOYEN'
    elif risk_score >= 20:
        return 'FAIBLE'
    else:
        return 'INFO'

def _get_phishing_recommendation(risk_score, is_phishing):
    """Génère une recommandation basée sur l'analyse"""
    if is_phishing or risk_score >= 70:
        return 'BLOQUER IMMÉDIATEMENT - URL très suspecte, ne pas visiter'
    elif risk_score >= 50:
        return 'PRUDENCE REQUISE - Vérifier l\'authenticité avant de visiter'
    elif risk_score >= 30:
        return 'ATTENTION - Quelques indicateurs suspects détectés'
    else:
        return 'URL semble légitime - Risque faible détecté'

@export_bp.route('/malware/<analysis_id>/pdf', methods=['GET', 'OPTIONS'])
@handle_options
@jwt_required()
def export_malware_pdf(analysis_id):
    """
    Exporte un rapport d'analyse de malware en PDF

    Args:
        analysis_id: ID de l'analyse de malware à exporter

    Returns:
        PDF file: Rapport d'analyse de malware en format PDF
    """
    try:
        # Récupérer l'utilisateur actuel
        current_user_id = get_jwt_identity()

        # Récupérer l'utilisateur depuis MongoDB
        user = None
        if ObjectId.is_valid(current_user_id):
            user = mongo.db.users.find_one({'_id': ObjectId(current_user_id)})
        else:
            user = mongo.db.users.find_one({'username': current_user_id})

        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Récupérer les données de l'analyse de malware
        # Utiliser la fonction du modèle avec filtrage par utilisateur si nécessaire
        user_filter = None if user.get('role') == 'admin' else current_user_id
        malware_data = get_malware_analysis_by_id(analysis_id, user_filter)

        if not malware_data:
            return jsonify({'error': 'Malware analysis not found'}), 404

        # Vérifier que l'analyse est terminée
        # Pour les analyses de malware, si elles sont en base, elles sont généralement terminées
        # Vérifier s'il y a des résultats d'analyse
        if not malware_data.get('threat_level') and not malware_data.get('analysis_details') and not malware_data.get('detection_details'):
            return jsonify({
                'error': 'Cannot export report for incomplete analysis',
                'message': 'Analysis appears to be incomplete. Please wait for the analysis to finish.'
            }), 400

        # Générer le rapport PDF
        pdf_service = MalwarePDFService()
        pdf_buffer = pdf_service.generate_malware_report(malware_data)

        # Créer un fichier temporaire pour le PDF
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            temp_file.write(pdf_buffer.getvalue())
            temp_file_path = temp_file.name

        # Générer le nom du fichier
        filename = malware_data.get('file_info', {}).get('filename', 'unknown_file')
        # Nettoyer le nom de fichier
        safe_filename = "".join(c for c in filename if c.isalnum() or c in (' ', '-', '_')).rstrip()
        threat_level = malware_data.get('threat_level', 'unknown')
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        pdf_filename = f"PICA_Malware_Report_{safe_filename}_{threat_level}_{timestamp}.pdf"

        # Envoyer le fichier
        return send_file(
            temp_file_path,
            as_attachment=True,
            download_name=pdf_filename,
            mimetype='application/pdf'
        )

    except Exception as e:
        print(f"❌ Error exporting malware PDF: {str(e)}")
        return jsonify({
            'error': 'Failed to export malware PDF report',
            'details': str(e)
        }), 500

    finally:
        # Nettoyer le fichier temporaire
        try:
            if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
        except:
            pass

@export_bp.route('/malware/csv', methods=['GET', 'OPTIONS'])
@handle_options
@jwt_required()
def export_all_malware_csv():
    """
    Exporte toutes les analyses de malware en format CSV

    Returns:
        CSV file: Fichier CSV contenant toutes les analyses de malware et leurs détails
    """
    try:
        # Récupérer l'utilisateur actuel
        current_user_id = get_jwt_identity()

        # Récupérer l'utilisateur depuis MongoDB
        user = None
        if ObjectId.is_valid(current_user_id):
            user = mongo.db.users.find_one({'_id': ObjectId(current_user_id)})
        else:
            user = mongo.db.users.find_one({'username': current_user_id})

        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Récupérer toutes les analyses de malware
        # Si l'utilisateur n'est pas admin, filtrer par user_id
        if user.get('role') == 'admin':
            analyses = list(mongo.db.malware.find({}).sort('created_at', -1))
        else:
            analyses = list(mongo.db.malware.find({'user_id': current_user_id}).sort('created_at', -1))

        if not analyses:
            return jsonify({'error': 'No malware analyses found'}), 404

        # Créer le CSV en mémoire
        output = io.StringIO()
        writer = csv.writer(output)

        # En-têtes du CSV pour malware
        headers = [
            'ID de l\'Analyse',
            'Nom du Fichier',
            'Type de Fichier',
            'Taille (bytes)',
            'Utilisateur',
            'Date de Création',
            'Date d\'Analyse',
            'Durée (secondes)',
            'Niveau de Menace',
            'Score de Confiance (%)',
            'Hash MD5',
            'Hash SHA1',
            'Hash SHA256',
            'Nombre de Détections',
            'Nombre d\'IOCs',
            'Type d\'Élément',  # 'Détection', 'IOC', ou 'Résumé'
            'Catégorie',
            'Détail',
            'Sévérité',
            'Description',
            'Recommandation'
        ]

        writer.writerow(headers)

        # Traiter chaque analyse
        for analysis in analyses:
            # Données de base de l'analyse
            analysis_id = analysis.get('analysis_id', str(analysis.get('_id', '')))
            file_info = analysis.get('file_info', {})
            filename = file_info.get('filename', 'Fichier inconnu')
            file_type = file_info.get('file_type', 'N/A')
            file_size = file_info.get('size', 0)
            user_id = analysis.get('user_id', '')
            created_at = _format_datetime_csv(analysis.get('created_at'))
            analysis_timestamp = _format_datetime_csv(analysis.get('analysis_timestamp'))

            # Calculer la durée
            duration = _calculate_malware_duration(analysis)

            # Extraire les résultats
            threat_level = analysis.get('threat_level', 'UNKNOWN').upper()
            confidence = analysis.get('confidence_pct', 0)

            # Hashes
            hashes = file_info.get('hashes', {})
            md5_hash = hashes.get('md5', '')
            sha1_hash = hashes.get('sha1', '')
            sha256_hash = hashes.get('sha256', '')

            # Compter les détections et IOCs
            detection_details = analysis.get('detection_details', [])
            iocs = analysis.get('iocs', {})
            iocs_count = _count_malware_iocs(iocs)

            # Données communes de l'analyse
            analysis_base_data = [
                analysis_id,
                filename,
                file_type,
                file_size,
                user_id,
                created_at,
                analysis_timestamp,
                duration,
                threat_level,
                confidence,
                md5_hash,
                sha1_hash,
                sha256_hash,
                len(detection_details),
                iocs_count
            ]

            # Traiter les détections (une ligne par détection)
            if detection_details:
                for detection in detection_details:
                    detection_text = str(detection)
                    severity = _get_malware_detection_severity(detection_text)

                    detection_row = analysis_base_data + [
                        'Détection',  # Type d'élément
                        'Malware Detection',  # Catégorie
                        detection_text[:100] + ('...' if len(detection_text) > 100 else ''),  # Détail (tronqué)
                        severity,  # Sévérité
                        f'Détection de malware: {detection_text[:50]}...' if len(detection_text) > 50 else detection_text,  # Description
                        _get_malware_detection_recommendation(severity)  # Recommandation
                    ]
                    writer.writerow(detection_row)

            # Traiter les IOCs (une ligne par type d'IOC)
            if iocs:
                for ioc_type, ioc_list in iocs.items():
                    if ioc_list and isinstance(ioc_list, list):
                        for ioc_value in ioc_list[:5]:  # Limiter à 5 par type pour éviter un CSV trop volumineux
                            ioc_row = analysis_base_data + [
                                'IOC',  # Type d'élément
                                ioc_type.upper(),  # Catégorie (IPS, DOMAINS, URLS, etc.)
                                str(ioc_value),  # Détail
                                _get_ioc_severity(ioc_type),  # Sévérité
                                f'Indicateur de compromission détecté: {ioc_type} - {ioc_value}',  # Description
                                f'Bloquer/surveiller ce {ioc_type} dans votre infrastructure'  # Recommandation
                            ]
                            writer.writerow(ioc_row)

                        # Si plus de 5 IOCs, ajouter une ligne de résumé
                        if len(ioc_list) > 5:
                            summary_row = analysis_base_data + [
                                'IOC',  # Type d'élément
                                ioc_type.upper(),  # Catégorie
                                f'... et {len(ioc_list) - 5} autres {ioc_type}',  # Détail
                                _get_ioc_severity(ioc_type),  # Sévérité
                                f'{len(ioc_list)} {ioc_type} au total détectés',  # Description
                                f'Analyser tous les {ioc_type} pour une protection complète'  # Recommandation
                            ]
                            writer.writerow(summary_row)

            # Si aucune détection ni IOC, créer une ligne de résumé
            if not detection_details and not any(iocs.values()):
                summary_row = analysis_base_data + [
                    'Résumé',  # Type d'élément
                    'Analysis Summary',  # Catégorie
                    f'Analyse terminée - Niveau: {threat_level}',  # Détail
                    _get_threat_level_severity(threat_level),  # Sévérité
                    f'Analyse de malware terminée avec un niveau de menace {threat_level} et {confidence}% de confiance',  # Description
                    _get_threat_level_recommendation(threat_level)  # Recommandation
                ]
                writer.writerow(summary_row)

        # Préparer le fichier pour téléchargement
        output.seek(0)

        # Créer un fichier temporaire
        with tempfile.NamedTemporaryFile(mode='w+', delete=False, suffix='.csv', encoding='utf-8') as temp_file:
            temp_file.write(output.getvalue())
            temp_file_path = temp_file.name

        # Générer le nom du fichier
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"PICA_Malware_Export_{timestamp}.csv"

        # Envoyer le fichier
        return send_file(
            temp_file_path,
            as_attachment=True,
            download_name=filename,
            mimetype='text/csv'
        )

    except Exception as e:
        print(f"❌ Error exporting malware CSV: {str(e)}")
        return jsonify({
            'error': 'Failed to export malware CSV',
            'details': str(e)
        }), 500

    finally:
        # Nettoyer le fichier temporaire
        try:
            if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
        except:
            pass

def _calculate_malware_duration(analysis_data):
    """Calcule la durée de l'analyse de malware en secondes"""
    created_at = analysis_data.get('created_at')
    analysis_timestamp = analysis_data.get('analysis_timestamp')

    if not created_at or not analysis_timestamp:
        return 0

    try:
        if isinstance(created_at, str):
            start = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
        else:
            start = created_at

        if isinstance(analysis_timestamp, str):
            end = datetime.fromisoformat(analysis_timestamp.replace('Z', '+00:00'))
        else:
            end = analysis_timestamp

        duration = end - start
        return int(duration.total_seconds())
    except:
        return 0

def _count_malware_iocs(iocs):
    """Compte le nombre total d'IOCs de malware"""
    total = 0
    if isinstance(iocs, dict):
        for ioc_list in iocs.values():
            if isinstance(ioc_list, list):
                total += len(ioc_list)
    return total

def _get_malware_detection_severity(detection_text):
    """Détermine la sévérité d'une détection de malware"""
    detection_lower = detection_text.lower()

    if any(keyword in detection_lower for keyword in ['critical', 'malware', 'virus', 'trojan', 'ransomware']):
        return 'CRITIQUE'
    elif any(keyword in detection_lower for keyword in ['suspicious', 'warning', 'potential', 'adware']):
        return 'ÉLEVÉ'
    elif any(keyword in detection_lower for keyword in ['medium', 'moderate', 'packer']):
        return 'MOYEN'
    else:
        return 'FAIBLE'

def _get_ioc_severity(ioc_type):
    """Détermine la sévérité basée sur le type d'IOC"""
    if ioc_type.lower() in ['ips', 'domains']:
        return 'ÉLEVÉ'
    elif ioc_type.lower() in ['urls', 'emails']:
        return 'MOYEN'
    else:
        return 'FAIBLE'

def _get_threat_level_severity(threat_level):
    """Détermine la sévérité basée sur le niveau de menace"""
    threat_level = threat_level.upper()
    if threat_level == 'CRITICAL':
        return 'CRITIQUE'
    elif threat_level == 'HIGH':
        return 'ÉLEVÉ'
    elif threat_level == 'MEDIUM':
        return 'MOYEN'
    elif threat_level in ['LOW', 'CLEAN']:
        return 'FAIBLE'
    else:
        return 'INCONNU'

def _get_malware_detection_recommendation(severity):
    """Génère une recommandation basée sur la sévérité de détection"""
    if severity == 'CRITIQUE':
        return 'ISOLER IMMÉDIATEMENT - Quarantaine et analyse approfondie requises'
    elif severity == 'ÉLEVÉ':
        return 'ATTENTION - Surveillance renforcée et vérifications supplémentaires'
    elif severity == 'MOYEN':
        return 'PRUDENCE - Analyser le contexte et surveiller le comportement'
    else:
        return 'INFORMATION - Conserver pour référence et surveillance normale'

def _get_threat_level_recommendation(threat_level):
    """Génère une recommandation basée sur le niveau de menace"""
    threat_level = threat_level.upper()
    if threat_level == 'CRITICAL':
        return 'ACTION IMMÉDIATE - Isoler le fichier et scanner tous les systèmes'
    elif threat_level == 'HIGH':
        return 'PRIORITÉ ÉLEVÉE - Quarantaine et analyse de sécurité approfondie'
    elif threat_level == 'MEDIUM':
        return 'SURVEILLANCE - Monitoring continu et précautions supplémentaires'
    elif threat_level == 'LOW':
        return 'VIGILANCE - Surveillance normale et bonnes pratiques de sécurité'
    elif threat_level == 'CLEAN':
        return 'AUCUNE ACTION - Fichier semble légitime, surveillance de routine'
    else:
        return 'ANALYSE REQUISE - Statut indéterminé, investigation supplémentaire nécessaire'

@export_bp.route('/incidents/csv', methods=['GET', 'OPTIONS'])
@handle_options
@jwt_required()
def export_all_incidents_csv():
    """
    Exporte tous les incidents et tickets en format CSV

    Returns:
        CSV file: Fichier CSV contenant tous les incidents et tickets avec leurs détails
    """
    try:
        current_user_id = get_jwt_identity()
        print(f"📊 Exporting all incidents to CSV for user: {current_user_id}")

        # Récupérer tous les tickets depuis MongoDB
        tickets_collection = mongo.db.tickets
        tickets_cursor = tickets_collection.find({})
        tickets = list(tickets_cursor)

        # Récupérer tous les incidents depuis MongoDB
        incidents_collection = mongo.db.incidents
        incidents_cursor = incidents_collection.find({})
        incidents = list(incidents_cursor)

        print(f"📊 Found {len(tickets)} tickets and {len(incidents)} incidents to export")

        # Créer le fichier CSV en mémoire
        output = io.StringIO()
        writer = csv.writer(output)

        # En-têtes pour les tickets
        ticket_headers = [
            'Type', 'ID', 'Short Description', 'Category', 'Subcategory',
            'Impact', 'Urgency', 'Priority', 'Status', 'Assignment Group',
            'Assigned To', 'Configuration Item', 'Contact Type', 'Location',
            'Creator', 'Created Date', 'Updated Date', 'Resolution Notes'
        ]

        # En-têtes pour les incidents
        incident_headers = [
            'Type', 'ID', 'Title', 'Description', 'Severity', 'Status',
            'Affected Systems', 'Impact Assessment', 'Root Cause',
            'Resolution', 'Creator', 'Created Date', 'Updated Date'
        ]

        # Utiliser les en-têtes les plus complets
        all_headers = [
            'Type', 'ID', 'Title/Short Description', 'Description/Details',
            'Category', 'Subcategory', 'Severity/Impact', 'Urgency', 'Priority',
            'Status', 'Assignment Group', 'Assigned To', 'Configuration Item',
            'Contact Type', 'Location', 'Affected Systems', 'Impact Assessment',
            'Root Cause', 'Resolution/Notes', 'Creator', 'Created Date', 'Updated Date'
        ]

        writer.writerow(all_headers)

        # Écrire les données des tickets
        for ticket in tickets:
            row = [
                'Ticket',  # Type
                str(ticket.get('_id', '')),  # ID
                ticket.get('short_description', ''),  # Title/Short Description
                ticket.get('description', ''),  # Description/Details
                ticket.get('category', ''),  # Category
                ticket.get('subcategory', ''),  # Subcategory
                ticket.get('impact', ''),  # Severity/Impact
                ticket.get('urgency', ''),  # Urgency
                ticket.get('priority', ''),  # Priority
                ticket.get('status', ''),  # Status
                ticket.get('assignment_group', ''),  # Assignment Group
                ticket.get('assigned_to', ''),  # Assigned To
                ticket.get('configuration_item', ''),  # Configuration Item
                ticket.get('contact_type', ''),  # Contact Type
                ticket.get('location', ''),  # Location
                '',  # Affected Systems (vide pour tickets)
                '',  # Impact Assessment (vide pour tickets)
                '',  # Root Cause (vide pour tickets)
                ticket.get('resolution_notes', ''),  # Resolution/Notes
                ticket.get('created_by', ''),  # Creator
                ticket.get('created_at', ''),  # Created Date
                ticket.get('updated_at', '')  # Updated Date
            ]
            writer.writerow(row)

        # Écrire les données des incidents
        for incident in incidents:
            row = [
                'Incident',  # Type
                str(incident.get('_id', '')),  # ID
                incident.get('title', ''),  # Title/Short Description
                incident.get('description', ''),  # Description/Details
                '',  # Category (vide pour incidents)
                '',  # Subcategory (vide pour incidents)
                incident.get('severity', ''),  # Severity/Impact
                '',  # Urgency (vide pour incidents)
                '',  # Priority (vide pour incidents)
                incident.get('status', ''),  # Status
                '',  # Assignment Group (vide pour incidents)
                '',  # Assigned To (vide pour incidents)
                '',  # Configuration Item (vide pour incidents)
                '',  # Contact Type (vide pour incidents)
                '',  # Location (vide pour incidents)
                ', '.join(incident.get('affected_systems', [])) if incident.get('affected_systems') else '',  # Affected Systems
                incident.get('impact_assessment', ''),  # Impact Assessment
                incident.get('root_cause', ''),  # Root Cause
                incident.get('resolution', ''),  # Resolution/Notes
                incident.get('created_by', ''),  # Creator
                incident.get('created_at', ''),  # Created Date
                incident.get('updated_at', '')  # Updated Date
            ]
            writer.writerow(row)

        # Créer un fichier temporaire
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv', encoding='utf-8') as temp_file:
            temp_file.write(output.getvalue())
            temp_file_path = temp_file.name

        # Générer le nom du fichier
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"PICA_Incidents_Export_{timestamp}.csv"

        # Envoyer le fichier
        return send_file(
            temp_file_path,
            as_attachment=True,
            download_name=filename,
            mimetype='text/csv'
        )

    except Exception as e:
        print(f"❌ Error exporting incidents CSV: {str(e)}")
        return jsonify({
            'error': 'Failed to export incidents CSV',
            'details': str(e)
        }), 500

    finally:
        # Nettoyer le fichier temporaire
        try:
            if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
        except:
            pass

# Enregistrer les routes d'erreur
@export_bp.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Export endpoint not found'}), 404

@export_bp.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error during export'}), 500
