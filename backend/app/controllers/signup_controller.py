from flask import request, jsonify, current_app
from ..extensions import mongo
from flask_jwt_extended import create_access_token, decode_token
from jwt.exceptions import ExpiredSignatureError
from ..utils.helpers import (
    is_valid_name, is_valid_date, is_strong_password,
    is_valid_email, is_valid_username
)
from ..utils.email_utils import send_async_email
from ..services.email_template_service import EmailTemplateService
from datetime import timedelta, datetime, timezone


def register():
    data = request.get_json()
    required = ["first_name", "last_name", "date_of_birth", "username", "gender", "email", "password"]
    if not all(k in data for k in required):
        return jsonify({"msg": "Missing fields"}), 400

    if not is_valid_name(data["first_name"]) or not is_valid_name(data["last_name"]):
        return jsonify({"msg": "Invalid name"}), 400
    if not is_valid_date(data["date_of_birth"]):
        return jsonify({"msg": "Date must be DD/MM/YYYY"}), 400
    if not is_valid_username(data["username"]):
        return jsonify({"msg": "Invalid username format"}), 400
    if not is_valid_email(data["email"]):
        return jsonify({"msg": "Invalid email format"}), 400
    if not is_strong_password(data["password"]):
        return jsonify({"msg": "Password must contain at least 8 characters, one uppercase, one lowercase, one number and one symbol"}), 400

    if mongo.db.users.find_one({"username": data["username"]}):
        return jsonify({"msg": "Username already taken"}), 400
    if mongo.db.users.find_one({"email": data["email"]}):
        return jsonify({"msg": "Email unavailable"}), 400

    data["role"] = "user"
    data["banned"] = False
    data["email_verified"] = False
    data["2fa_enabled"] = False
    data["created_at"] = datetime.utcnow()

    user = current_app.user_datastore.create_user(**data, active=True)

    # Create a token that expires in 10 minutes
    verify_token = create_access_token(
        identity=data["email"],
        expires_delta=timedelta(minutes=10),
        additional_claims={"verify": True}
    )

    confirm_url = f"http://localhost:5173/auth/email-confirmed?token={verify_token}"

    # Send beautiful confirmation email using template
    EmailTemplateService.send_confirmation_email(
        email=data["email"],
        first_name=data["first_name"],
        confirm_url=confirm_url
    )

    return jsonify({"msg": "Registration successful! Check your email to confirm your account."}), 201


def confirm_email():
    token = request.args.get("token")
    if not token:
        return jsonify({"msg": "Missing confirmation token"}), 400

    try:
        decoded = decode_token(token)
    except ExpiredSignatureError:
        return jsonify({"msg": "This confirmation link has expired (10 minutes maximum).", "expired": True}), 400
    except Exception:
        return jsonify({"msg": "Invalid confirmation link.", "invalid": True}), 400

    if not decoded.get("verify"):
        return jsonify({"msg": "Invalid confirmation token"}), 403

    email = decoded.get("sub")
    if not email:
        return jsonify({"msg": "Malformed token"}), 400

    # Check if this token has already been used
    used_token = mongo.db.used_tokens.find_one({"token": token})
    if used_token:
        return jsonify({
            "msg": "This confirmation link has already been used.",
            "already_used": True
        }), 400

    # Check if user exists
    user = mongo.db.users.find_one({"email": email})
    if not user:
        return jsonify({"msg": "User not found"}), 404

    # Check if email is already confirmed
    if user.get("email_verified", False):
        # Mark token as used to prevent reuse
        mongo.db.used_tokens.insert_one({
            "token": token,
            "email": email,
            "used_at": datetime.now(timezone.utc)
        })
        return jsonify({
            "msg": "This confirmation link has already been used.",
            "already_used": True
        }), 400

    # First use: confirm email
    # Mark token as used
    mongo.db.used_tokens.insert_one({
        "token": token,
        "email": email,
        "used_at": datetime.now(timezone.utc)
    })

    # Update user
    result = mongo.db.users.update_one(
        {"email": email},
        {"$set": {"email_verified": True}}
    )

    if result.modified_count == 0:
        return jsonify({"msg": "Error during confirmation. Please try again."}), 500

    return jsonify({"msg": "Email confirmed successfully! You can now log in.", "success": True}), 200
