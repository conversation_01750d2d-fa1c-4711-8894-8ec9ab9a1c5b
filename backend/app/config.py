import os
from dotenv import load_dotenv

load_dotenv()


class Config:
    SECRET_KEY = os.getenv("SECRET_KEY")
    JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY")
    JWT_ACCESS_TOKEN_EXPIRES = 7200  # 2 hours
    JWT_REFRESH_TOKEN_EXPIRES = 2592000  # 30 days
    MONGO_URI = os.getenv("MONGO_URI")

    MAIL_SERVER = 'smtp.gmail.com'
    MAIL_PORT = 587
    MAIL_USE_TLS = True
    MAIL_USERNAME = os.getenv("MAIL_USERNAME")
    MAIL_PASSWORD = os.getenv("MAIL_PASSWORD")

    # Telegram Bot Configuration
    TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN")

    SECURITY_PASSWORD_HASH = "pbkdf2_sha512"
    SECURITY_PASSWORD_SALT = os.getenv("SECURITY_PASSWORD_SALT")
    SECURITY_REGISTERABLE = False
    SECURITY_SEND_REGISTER_EMAIL = False

    # === OpenVAS ===
    OPENVAS_USERNAME = os.getenv("OPENVAS_USERNAME")
    OPENVAS_PASSWORD = os.getenv("OPENVAS_PASSWORD")
    OPENVAS_SOCKET_PATH = os.getenv("OPENVAS_SOCKET_PATH")
    # === Nessus ===
    NESSUS_URL = os.getenv("NESSUS_URL")
    NESSUS_ACCESS_KEY = os.getenv("NESSUS_ACCESS_KEY")
    NESSUS_SECRET_KEY = os.getenv("NESSUS_SECRET_KEY")
    NESSUS_VERIFY_SSL = os.getenv("NESSUS_VERIFY_SSL", "False").lower() == "true"
