from datetime import datetime
from bson import ObjectId
from flask_jwt_extended import get_jwt_identity
from app.extensions import mongo
import uuid


def get_vulnerabilities_collection():
    """Obtenir la collection des vulnérabilités"""
    try:
        return mongo.db.vulnerabilities
    except Exception as e:
        print(f"❌ MongoDB vulnerabilities collection error: {str(e)}")
        return None


def save_vulnerability_to_db(vulnerability_data, scan_id=None, user_id=None):
    """
    Sauvegarder une vulnérabilité dans MongoDB
    
    Args:
        vulnerability_data (dict): Données de la vulnérabilité
        scan_id (str): ID du scan qui a détecté cette vulnérabilité
        user_id (str): ID de l'utilisateur (optionnel, récupéré du JWT si non fourni)
    
    Returns:
        str: ID de la vulnérabilité sauvegardée ou None en cas d'erreur
    """
    try:
        collection = get_vulnerabilities_collection()
        if collection is None:
            print("❌ MongoDB vulnerabilities collection is None")
            return None

        # Préparer les données à sauvegarder
        data_to_save = vulnerability_data.copy()
        
        # Ajouter les métadonnées
        data_to_save['vulnerability_id'] = str(uuid.uuid4())
        data_to_save['created_at'] = datetime.utcnow()
        data_to_save['updated_at'] = datetime.utcnow()
        data_to_save['scan_id'] = scan_id
        data_to_save['user_id'] = user_id or get_jwt_identity()
        
        # Standardiser les champs obligatoires
        data_to_save.setdefault('name', 'Unknown Vulnerability')
        data_to_save.setdefault('description', 'No description available')
        data_to_save.setdefault('severity', 'unknown')
        data_to_save.setdefault('tool', 'unknown')
        data_to_save.setdefault('category', 'general')
        data_to_save.setdefault('status', 'detected')  # detected, verified, false_positive, fixed
        
        # Normaliser la sévérité
        severity_mapping = {
            'critical': 'critical',
            'high': 'high', 
            'medium': 'medium',
            'low': 'low',
            'info': 'info',
            'informational': 'info',
            'unknown': 'unknown'
        }
        original_severity = str(data_to_save.get('severity', 'unknown')).lower()
        data_to_save['severity'] = severity_mapping.get(original_severity, 'unknown')
        
        # Calculer un score CVSS si disponible
        if 'cvss_base' in data_to_save and data_to_save['cvss_base']:
            try:
                cvss_score = float(data_to_save['cvss_base'])
                data_to_save['cvss_score'] = cvss_score
                
                # Mapper le score CVSS à la sévérité si pas déjà définie correctement
                if data_to_save['severity'] == 'unknown':
                    if cvss_score >= 9.0:
                        data_to_save['severity'] = 'critical'
                    elif cvss_score >= 7.0:
                        data_to_save['severity'] = 'high'
                    elif cvss_score >= 4.0:
                        data_to_save['severity'] = 'medium'
                    elif cvss_score > 0.0:
                        data_to_save['severity'] = 'low'
                    else:
                        data_to_save['severity'] = 'info'
            except (ValueError, TypeError):
                data_to_save['cvss_score'] = None
        
        # Nettoyer les champs MongoDB internes
        for key in ['mongo_id', '_id']:
            data_to_save.pop(key, None)

        # Vérifier si cette vulnérabilité existe déjà (éviter les doublons)
        existing_vuln = None
        if 'id' in data_to_save or 'cve' in data_to_save:
            query = {}
            if 'id' in data_to_save and data_to_save['id']:
                query['id'] = data_to_save['id']
            elif 'cve' in data_to_save and data_to_save['cve']:
                query['cve'] = data_to_save['cve']
            
            if query:
                query['target'] = data_to_save.get('target', data_to_save.get('host', ''))
                existing_vuln = collection.find_one(query)
        
        if existing_vuln:
            # Mettre à jour la vulnérabilité existante
            update_data = {
                'updated_at': datetime.utcnow(),
                'last_detected': datetime.utcnow(),
                'detection_count': existing_vuln.get('detection_count', 1) + 1
            }
            
            # Ajouter le scan_id à la liste des scans qui ont détecté cette vulnérabilité
            if scan_id:
                scan_ids = existing_vuln.get('scan_ids', [])
                if scan_id not in scan_ids:
                    scan_ids.append(scan_id)
                    update_data['scan_ids'] = scan_ids
            
            collection.update_one(
                {'_id': existing_vuln['_id']},
                {'$set': update_data}
            )
            print(f"✅ Updated existing vulnerability {existing_vuln.get('vulnerability_id')} in MongoDB")
            return str(existing_vuln['_id'])
        else:
            # Créer une nouvelle vulnérabilité
            data_to_save['first_detected'] = datetime.utcnow()
            data_to_save['last_detected'] = datetime.utcnow()
            data_to_save['detection_count'] = 1
            data_to_save['scan_ids'] = [scan_id] if scan_id else []
            
            result = collection.insert_one(data_to_save)
            print(f"✅ Vulnerability saved to MongoDB with ID: {result.inserted_id}")
            return str(result.inserted_id)

    except Exception as e:
        print(f"❌ Error saving vulnerability to MongoDB: {str(e)}")
        return None


def get_vulnerabilities_from_db(limit=50, user_id=None, filters=None):
    """
    Récupérer les vulnérabilités depuis MongoDB
    
    Args:
        limit (int): Nombre maximum de vulnérabilités à retourner
        user_id (str): ID utilisateur pour filtrer (None pour admin)
        filters (dict): Filtres additionnels (severity, tool, status, etc.)
    
    Returns:
        list: Liste des vulnérabilités
    """
    try:
        collection = get_vulnerabilities_collection()
        if collection is None:
            print("❌ MongoDB vulnerabilities collection is None")
            return []

        # Construire la requête de filtrage
        query = {}
        
        # Filtre utilisateur (non-admin seulement)
        if user_id:
            query['user_id'] = user_id
            print(f"🔍 Filtering vulnerabilities for user: {user_id}")
        else:
            print("🔍 Retrieving all vulnerabilities (admin access)")
        
        # Appliquer les filtres additionnels
        if filters:
            if 'severity' in filters and filters['severity']:
                if isinstance(filters['severity'], list):
                    query['severity'] = {'$in': filters['severity']}
                else:
                    query['severity'] = filters['severity']
            
            if 'tool' in filters and filters['tool']:
                if isinstance(filters['tool'], list):
                    query['tool'] = {'$in': filters['tool']}
                else:
                    query['tool'] = filters['tool']
            
            if 'status' in filters and filters['status']:
                query['status'] = filters['status']
            
            if 'category' in filters and filters['category']:
                query['category'] = filters['category']
            
            if 'target' in filters and filters['target']:
                query['$or'] = [
                    {'target': {'$regex': filters['target'], '$options': 'i'}},
                    {'host': {'$regex': filters['target'], '$options': 'i'}}
                ]
            
            if 'cve' in filters and filters['cve']:
                query['cve'] = {'$regex': filters['cve'], '$options': 'i'}

        # Récupérer les vulnérabilités triées par date de dernière détection
        vulnerabilities = list(collection.find(query)
                             .sort('last_detected', -1)
                             .limit(limit))

        # Convertir ObjectId en string et formater les dates
        for vuln in vulnerabilities:
            vuln['_id'] = str(vuln['_id'])
            if 'created_at' in vuln:
                vuln['created_at'] = vuln['created_at'].isoformat()
            if 'updated_at' in vuln:
                vuln['updated_at'] = vuln['updated_at'].isoformat()
            if 'first_detected' in vuln:
                vuln['first_detected'] = vuln['first_detected'].isoformat()
            if 'last_detected' in vuln:
                vuln['last_detected'] = vuln['last_detected'].isoformat()

        print(f"✅ Retrieved {len(vulnerabilities)} vulnerabilities from MongoDB")
        return vulnerabilities

    except Exception as e:
        print(f"❌ Error retrieving vulnerabilities from MongoDB: {str(e)}")
        return []


def get_vulnerability_by_id(vulnerability_id, user_id=None):
    """
    Récupérer une vulnérabilité spécifique par ID
    
    Args:
        vulnerability_id (str): ID de la vulnérabilité
        user_id (str): ID utilisateur pour filtrer (None pour admin)
    
    Returns:
        dict: Vulnérabilité ou None si non trouvée
    """
    try:
        collection = get_vulnerabilities_collection()
        if collection is None:
            print("❌ MongoDB vulnerabilities collection is None")
            return None

        query = {'$or': [{'vulnerability_id': vulnerability_id}]}
        if ObjectId.is_valid(vulnerability_id):
            query['$or'].append({'_id': ObjectId(vulnerability_id)})

        # Ajouter le filtre utilisateur si spécifié (non-admin)
        if user_id:
            query['user_id'] = user_id
            print(f"🔍 Filtering vulnerability {vulnerability_id} for user: {user_id}")

        vulnerability = collection.find_one(query)

        if vulnerability:
            vulnerability['_id'] = str(vulnerability['_id'])
            if 'created_at' in vulnerability:
                vulnerability['created_at'] = vulnerability['created_at'].isoformat()
            if 'updated_at' in vulnerability:
                vulnerability['updated_at'] = vulnerability['updated_at'].isoformat()
            if 'first_detected' in vulnerability:
                vulnerability['first_detected'] = vulnerability['first_detected'].isoformat()
            if 'last_detected' in vulnerability:
                vulnerability['last_detected'] = vulnerability['last_detected'].isoformat()
            print(f"✅ Retrieved vulnerability {vulnerability_id} from MongoDB")
            return vulnerability
        else:
            print(f"❌ Vulnerability {vulnerability_id} not found in MongoDB collection or access denied")
            return None

    except Exception as e:
        print(f"❌ Error retrieving vulnerability {vulnerability_id} from MongoDB: {str(e)}")
        return None


def update_vulnerability_status(vulnerability_id, status, notes=None, user_id=None):
    """
    Mettre à jour le statut d'une vulnérabilité
    
    Args:
        vulnerability_id (str): ID de la vulnérabilité
        status (str): Nouveau statut (detected, verified, false_positive, fixed)
        notes (str): Notes optionnelles
        user_id (str): ID utilisateur pour filtrer (None pour admin)
    
    Returns:
        bool: True si mise à jour réussie, False sinon
    """
    try:
        collection = get_vulnerabilities_collection()
        if collection is None:
            print("❌ MongoDB vulnerabilities collection is None")
            return False

        query = {'$or': [{'vulnerability_id': vulnerability_id}]}
        if ObjectId.is_valid(vulnerability_id):
            query['$or'].append({'_id': ObjectId(vulnerability_id)})

        # Ajouter le filtre utilisateur si spécifié (non-admin)
        if user_id:
            query['user_id'] = user_id

        update_data = {
            'status': status,
            'updated_at': datetime.utcnow()
        }
        
        if notes:
            update_data['notes'] = notes

        result = collection.update_one(query, {'$set': update_data})
        
        if result.modified_count > 0:
            print(f"✅ Updated vulnerability {vulnerability_id} status to {status}")
            return True
        else:
            print(f"❌ Vulnerability {vulnerability_id} not found or no changes made")
            return False

    except Exception as e:
        print(f"❌ Error updating vulnerability {vulnerability_id}: {str(e)}")
        return False


def get_vulnerability_statistics(user_id=None):
    """
    Obtenir des statistiques sur les vulnérabilités

    Args:
        user_id (str): ID utilisateur pour filtrer (None pour admin)

    Returns:
        dict: Statistiques des vulnérabilités
    """
    try:
        collection = get_vulnerabilities_collection()
        if collection is None:
            print("❌ MongoDB vulnerabilities collection is None")
            return {}

        # Construire la requête de base
        match_query = {}
        if user_id:
            match_query['user_id'] = user_id

        # Pipeline d'agrégation pour les statistiques
        pipeline = [
            {'$match': match_query},
            {
                '$group': {
                    '_id': None,
                    'total': {'$sum': 1},
                    'critical': {'$sum': {'$cond': [{'$eq': ['$severity', 'critical']}, 1, 0]}},
                    'high': {'$sum': {'$cond': [{'$eq': ['$severity', 'high']}, 1, 0]}},
                    'medium': {'$sum': {'$cond': [{'$eq': ['$severity', 'medium']}, 1, 0]}},
                    'low': {'$sum': {'$cond': [{'$eq': ['$severity', 'low']}, 1, 0]}},
                    'info': {'$sum': {'$cond': [{'$eq': ['$severity', 'info']}, 1, 0]}},
                    'detected': {'$sum': {'$cond': [{'$eq': ['$status', 'detected']}, 1, 0]}},
                    'verified': {'$sum': {'$cond': [{'$eq': ['$status', 'verified']}, 1, 0]}},
                    'false_positive': {'$sum': {'$cond': [{'$eq': ['$status', 'false_positive']}, 1, 0]}},
                    'fixed': {'$sum': {'$cond': [{'$eq': ['$status', 'fixed']}, 1, 0]}}
                }
            }
        ]

        result = list(collection.aggregate(pipeline))

        if result:
            stats = result[0]
            stats.pop('_id', None)
            return stats
        else:
            return {
                'total': 0, 'critical': 0, 'high': 0, 'medium': 0, 'low': 0, 'info': 0,
                'detected': 0, 'verified': 0, 'false_positive': 0, 'fixed': 0
            }

    except Exception as e:
        print(f"❌ Error getting vulnerability statistics: {str(e)}")
        return {}


def delete_vulnerability(vulnerability_id, user_id=None):
    """
    Supprimer une vulnérabilité

    Args:
        vulnerability_id (str): ID de la vulnérabilité
        user_id (str): ID utilisateur pour filtrer (None pour admin)

    Returns:
        bool: True si suppression réussie, False sinon
    """
    try:
        collection = get_vulnerabilities_collection()
        if collection is None:
            print("❌ MongoDB vulnerabilities collection is None")
            return False

        query = {'$or': [{'vulnerability_id': vulnerability_id}]}
        if ObjectId.is_valid(vulnerability_id):
            query['$or'].append({'_id': ObjectId(vulnerability_id)})

        # Ajouter le filtre utilisateur si spécifié (non-admin)
        if user_id:
            query['user_id'] = user_id

        result = collection.delete_one(query)

        if result.deleted_count > 0:
            print(f"✅ Deleted vulnerability {vulnerability_id}")
            return True
        else:
            print(f"❌ Vulnerability {vulnerability_id} not found or access denied")
            return False

    except Exception as e:
        print(f"❌ Error deleting vulnerability {vulnerability_id}: {str(e)}")
        return False


def get_vulnerabilities_by_scan(scan_id, user_id=None):
    """
    Récupérer toutes les vulnérabilités détectées par un scan spécifique

    Args:
        scan_id (str): ID du scan
        user_id (str): ID utilisateur pour filtrer (None pour admin)

    Returns:
        list: Liste des vulnérabilités du scan
    """
    try:
        collection = get_vulnerabilities_collection()
        if collection is None:
            print("❌ MongoDB vulnerabilities collection is None")
            return []

        query = {'scan_ids': scan_id}

        # Ajouter le filtre utilisateur si spécifié (non-admin)
        if user_id:
            query['user_id'] = user_id

        vulnerabilities = list(collection.find(query).sort('severity_order', 1))

        # Convertir ObjectId en string et formater les dates
        for vuln in vulnerabilities:
            vuln['_id'] = str(vuln['_id'])
            if 'created_at' in vuln:
                vuln['created_at'] = vuln['created_at'].isoformat()
            if 'updated_at' in vuln:
                vuln['updated_at'] = vuln['updated_at'].isoformat()
            if 'first_detected' in vuln:
                vuln['first_detected'] = vuln['first_detected'].isoformat()
            if 'last_detected' in vuln:
                vuln['last_detected'] = vuln['last_detected'].isoformat()

        print(f"✅ Retrieved {len(vulnerabilities)} vulnerabilities for scan {scan_id}")
        return vulnerabilities

    except Exception as e:
        print(f"❌ Error retrieving vulnerabilities for scan {scan_id}: {str(e)}")
        return []


def normalize_vulnerability_data(vuln_data, tool_name, target=None):
    """
    Normaliser les données de vulnérabilité selon le format standard

    Args:
        vuln_data (dict): Données brutes de la vulnérabilité
        tool_name (str): Nom de l'outil qui a détecté la vulnérabilité
        target (str): Cible du scan (IP ou URL)

    Returns:
        dict: Données normalisées de la vulnérabilité
    """
    normalized = {
        'name': vuln_data.get('name', 'Unknown Vulnerability'),
        'description': vuln_data.get('description', 'No description available'),
        'severity': vuln_data.get('severity', 'unknown'),
        'tool': tool_name,
        'category': vuln_data.get('category', 'general'),
        'target': target or vuln_data.get('target', vuln_data.get('host', '')),
    }

    # Champs optionnels
    optional_fields = [
        'id', 'cve', 'cvss_base', 'cvss_score', 'port', 'protocol', 'service',
        'nvt_oid', 'threat', 'solution', 'reference', 'evidence', 'attack',
        'param', 'payload', 'injection_type', 'uri', 'method', 'osvdb_id',
        'cweid', 'wascid', 'confidence', 'risk'
    ]

    for field in optional_fields:
        if field in vuln_data and vuln_data[field]:
            normalized[field] = vuln_data[field]

    # Mapper les champs spécifiques aux outils
    if tool_name == 'openvas':
        if 'host' in vuln_data:
            normalized['target'] = vuln_data['host']
        if 'threat' in vuln_data:
            normalized['threat_level'] = vuln_data['threat']

    elif tool_name == 'owasp_zap':
        if 'risk' in vuln_data:
            normalized['risk_level'] = vuln_data['risk']
        if 'url' in vuln_data:
            normalized['target_url'] = vuln_data['url']

    elif tool_name == 'nikto':
        if 'target_ip' in vuln_data:
            normalized['target'] = vuln_data['target_ip']
        if 'target_hostname' in vuln_data:
            normalized['hostname'] = vuln_data['target_hostname']
        if 'target_port' in vuln_data:
            normalized['port'] = vuln_data['target_port']

    # Ajouter un ordre de sévérité pour le tri
    severity_order = {
        'critical': 1,
        'high': 2,
        'medium': 3,
        'low': 4,
        'info': 5,
        'unknown': 6
    }
    normalized['severity_order'] = severity_order.get(normalized['severity'], 6)

    return normalized
