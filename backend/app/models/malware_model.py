"""
Modèle pour la gestion des analyses de malware dans MongoDB.
Fournit les fonctions CRUD pour la collection 'malware'.
"""

from datetime import datetime, timezone
from bson import ObjectId
from flask_jwt_extended import get_jwt_identity
from typing import Dict, Any, List, Optional

from ..extensions import mongo


def get_malware_collection():
    """Obtenir la collection des analyses de malware."""
    try:
        return mongo.db.malware
    except Exception as e:
        print(f"❌ MongoDB malware collection error: {str(e)}")
        return None


def save_malware_analysis_to_db(analysis_data: Dict[str, Any]) -> Optional[str]:
    """
    Sauvegarder une analyse de malware dans MongoDB.
    
    Args:
        analysis_data: Données de l'analyse de malware
        
    Returns:
        str: ID de l'analyse sauvegardée ou None en cas d'erreur
    """
    try:
        collection = get_malware_collection()
        if collection is None:
            print("❌ MongoDB malware collection is None")
            return None

        # Préparer les données à sauvegarder
        data_to_save = analysis_data.copy()
        data_to_save['created_at'] = datetime.now(timezone.utc)
        data_to_save['updated_at'] = datetime.now(timezone.utc)
        
        # Ajouter l'ID utilisateur depuis le JWT
        try:
            user_id = get_jwt_identity()
            if user_id:
                data_to_save['user_id'] = user_id
        except Exception:
            # Si pas de JWT, utiliser l'user_id fourni dans les données
            pass

        # Nettoyer les clés MongoDB
        for key in ['mongo_id', '_id']:
            data_to_save.pop(key, None)

        # Ajouter un index de recherche pour les requêtes
        if 'filename' in data_to_save:
            data_to_save['search_filename'] = data_to_save['filename'].lower()
        
        if 'original_filename' in data_to_save:
            data_to_save['search_original_filename'] = data_to_save['original_filename'].lower()

        # Insérer dans la collection
        result = collection.insert_one(data_to_save)
        print(f"✅ Malware analysis saved to MongoDB with ID: {result.inserted_id}")
        return str(result.inserted_id)

    except Exception as e:
        print(f"❌ Error saving malware analysis to MongoDB: {str(e)}")
        return None


def get_malware_analyses_from_db(limit: int = 50, user_id: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Récupérer les analyses de malware depuis MongoDB.
    
    Args:
        limit: Nombre maximum d'analyses à récupérer
        user_id: ID utilisateur pour filtrer (None = toutes les analyses)
        
    Returns:
        List: Liste des analyses de malware
    """
    try:
        collection = get_malware_collection()
        if collection is None:
            print("❌ MongoDB malware collection is None")
            return []

        # Construire la requête de filtrage
        query = {}
        if user_id:
            query['user_id'] = user_id
            print(f"🔍 Filtering malware analyses for user: {user_id}")
        else:
            print("🔍 Retrieving all malware analyses (admin access)")

        # Trier par date de création (plus récent en premier)
        analyses = list(
            collection.find(query)
            .sort([('created_at', -1), ('analysis_timestamp', -1)])
            .limit(limit)
        )
        
        # Convertir les ObjectId en string et formater les dates
        for analysis in analyses:
            analysis['_id'] = str(analysis['_id'])
            if 'created_at' in analysis:
                analysis['created_at'] = analysis['created_at'].isoformat()
            if 'updated_at' in analysis:
                analysis['updated_at'] = analysis['updated_at'].isoformat()
            if 'analysis_timestamp' in analysis and isinstance(analysis['analysis_timestamp'], datetime):
                analysis['analysis_timestamp'] = analysis['analysis_timestamp'].isoformat()
        
        print(f"✅ Retrieved {len(analyses)} malware analyses from MongoDB")
        return analyses

    except Exception as e:
        print(f"❌ Error retrieving malware analyses from MongoDB: {str(e)}")
        return []


def get_malware_analysis_by_id(analysis_id: str, user_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """
    Récupérer une analyse de malware spécifique par ID.
    
    Args:
        analysis_id: ID de l'analyse
        user_id: ID utilisateur pour filtrer (None = pas de filtre)
        
    Returns:
        Dict: Analyse de malware ou None si non trouvée
    """
    try:
        collection = get_malware_collection()
        if collection is None:
            print("❌ MongoDB malware collection is None")
            return None

        # Construire la requête avec différents types d'ID
        query = {'$or': []}
        
        # Recherche par ObjectId MongoDB
        if ObjectId.is_valid(analysis_id):
            query['$or'].append({'_id': ObjectId(analysis_id)})
        
        # Recherche par scan_id ou analysis_id personnalisé
        query['$or'].extend([
            {'scan_id': analysis_id},
            {'analysis_id': analysis_id}
        ])

        # Ajouter le filtre utilisateur si spécifié (non-admin)
        if user_id:
            query['user_id'] = user_id
            print(f"🔍 Filtering malware analysis {analysis_id} for user: {user_id}")

        analysis = collection.find_one(query)

        if analysis:
            analysis['_id'] = str(analysis['_id'])
            if 'created_at' in analysis:
                analysis['created_at'] = analysis['created_at'].isoformat()
            if 'updated_at' in analysis:
                analysis['updated_at'] = analysis['updated_at'].isoformat()
            if 'analysis_timestamp' in analysis and isinstance(analysis['analysis_timestamp'], datetime):
                analysis['analysis_timestamp'] = analysis['analysis_timestamp'].isoformat()
            
            print(f"✅ Retrieved malware analysis {analysis_id} from MongoDB")
            return analysis
        else:
            print(f"❌ Malware analysis {analysis_id} not found in MongoDB or access denied")
            return None

    except Exception as e:
        print(f"❌ Error retrieving malware analysis {analysis_id} from MongoDB: {str(e)}")
        return None


def update_malware_analysis_in_db(analysis_id: str, update_data: Dict[str, Any], user_id: Optional[str] = None) -> bool:
    """
    Mettre à jour une analyse de malware dans MongoDB.
    
    Args:
        analysis_id: ID de l'analyse à mettre à jour
        update_data: Données à mettre à jour
        user_id: ID utilisateur pour filtrer (None = pas de filtre)
        
    Returns:
        bool: True si mise à jour réussie, False sinon
    """
    try:
        collection = get_malware_collection()
        if collection is None:
            print("❌ MongoDB malware collection is None")
            return False

        # Construire la requête
        query = {'$or': []}
        if ObjectId.is_valid(analysis_id):
            query['$or'].append({'_id': ObjectId(analysis_id)})
        query['$or'].extend([
            {'scan_id': analysis_id},
            {'analysis_id': analysis_id}
        ])

        # Ajouter le filtre utilisateur si spécifié
        if user_id:
            query['user_id'] = user_id

        # Préparer les données de mise à jour
        update_data_copy = update_data.copy()
        update_data_copy['updated_at'] = datetime.now(timezone.utc)
        
        # Nettoyer les clés MongoDB
        for key in ['mongo_id', '_id']:
            update_data_copy.pop(key, None)

        # Effectuer la mise à jour
        result = collection.update_one(query, {'$set': update_data_copy})
        
        if result.matched_count > 0:
            print(f"✅ Malware analysis {analysis_id} updated in MongoDB")
            return True
        else:
            print(f"❌ Malware analysis {analysis_id} not found for update")
            return False

    except Exception as e:
        print(f"❌ Error updating malware analysis {analysis_id} in MongoDB: {str(e)}")
        return False


def delete_malware_analysis_from_db(analysis_id: str, user_id: Optional[str] = None) -> bool:
    """
    Supprimer une analyse de malware de MongoDB.
    
    Args:
        analysis_id: ID de l'analyse à supprimer
        user_id: ID utilisateur pour filtrer (None = pas de filtre)
        
    Returns:
        bool: True si suppression réussie, False sinon
    """
    try:
        collection = get_malware_collection()
        if collection is None:
            print("❌ MongoDB malware collection is None")
            return False

        # Construire la requête
        query = {'$or': []}
        if ObjectId.is_valid(analysis_id):
            query['$or'].append({'_id': ObjectId(analysis_id)})
        query['$or'].extend([
            {'scan_id': analysis_id},
            {'analysis_id': analysis_id}
        ])

        # Ajouter le filtre utilisateur si spécifié
        if user_id:
            query['user_id'] = user_id

        # Effectuer la suppression
        result = collection.delete_one(query)
        
        if result.deleted_count > 0:
            print(f"✅ Malware analysis {analysis_id} deleted from MongoDB")
            return True
        else:
            print(f"❌ Malware analysis {analysis_id} not found for deletion")
            return False

    except Exception as e:
        print(f"❌ Error deleting malware analysis {analysis_id} from MongoDB: {str(e)}")
        return False


def get_malware_stats_from_db(user_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Récupérer les statistiques des analyses de malware.
    
    Args:
        user_id: ID utilisateur pour filtrer (None = toutes les analyses)
        
    Returns:
        Dict: Statistiques des analyses
    """
    try:
        collection = get_malware_collection()
        if collection is None:
            print("❌ MongoDB malware collection is None")
            return {}

        # Construire la requête de filtrage
        match_query = {}
        if user_id:
            match_query['user_id'] = user_id

        # Pipeline d'agrégation pour les statistiques
        pipeline = [
            {'$match': match_query},
            {'$group': {
                '_id': None,
                'total_analyses': {'$sum': 1},
                'critical_threats': {'$sum': {'$cond': [{'$eq': ['$threat_level', 'CRITICAL']}, 1, 0]}},
                'high_threats': {'$sum': {'$cond': [{'$eq': ['$threat_level', 'HIGH']}, 1, 0]}},
                'medium_threats': {'$sum': {'$cond': [{'$eq': ['$threat_level', 'MEDIUM']}, 1, 0]}},
                'low_threats': {'$sum': {'$cond': [{'$eq': ['$threat_level', 'LOW']}, 1, 0]}},
                'clean_files': {'$sum': {'$cond': [{'$eq': ['$threat_level', 'CLEAN']}, 1, 0]}},
                'avg_confidence': {'$avg': '$confidence_pct'},
                'avg_malware_score': {'$avg': '$malware_score'}
            }}
        ]

        result = list(collection.aggregate(pipeline))
        
        if result:
            stats = result[0]
            stats.pop('_id', None)  # Supprimer l'_id de l'agrégation
            # Arrondir les moyennes
            if 'avg_confidence' in stats and stats['avg_confidence']:
                stats['avg_confidence'] = round(stats['avg_confidence'], 2)
            if 'avg_malware_score' in stats and stats['avg_malware_score']:
                stats['avg_malware_score'] = round(stats['avg_malware_score'], 2)
        else:
            stats = {
                'total_analyses': 0,
                'critical_threats': 0,
                'high_threats': 0,
                'medium_threats': 0,
                'low_threats': 0,
                'clean_files': 0,
                'avg_confidence': 0,
                'avg_malware_score': 0
            }

        print(f"✅ Retrieved malware statistics from MongoDB")
        return stats

    except Exception as e:
        print(f"❌ Error retrieving malware statistics from MongoDB: {str(e)}")
        return {}
