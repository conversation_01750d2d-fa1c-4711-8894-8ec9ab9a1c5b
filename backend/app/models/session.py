from datetime import datetime, timed<PERSON><PERSON>
from bson import ObjectId
from app.extensions import mongo

class SessionManager:
    """Manages user sessions and login history"""
    
    @staticmethod
    def create_session(user_id, ip_address, user_agent, remember_me=False):
        """Create a new session record when user logs in"""
        session_data = {
            "user_id": ObjectId(user_id),
            "session_id": str(ObjectId()),  # Unique session identifier
            "ip_address": ip_address,
            "user_agent": user_agent,
            "login_time": datetime.utcnow(),
            "logout_time": None,
            "is_active": True,
            "remember_me": remember_me,
            "last_activity": datetime.utcnow(),
            "location": SessionManager._get_location_from_ip(ip_address),
            "device_info": SessionManager._parse_user_agent(user_agent)
        }
        
        result = mongo.db.user_sessions.insert_one(session_data)
        session_data["_id"] = result.inserted_id
        
        # Update user's last login time and increment login count
        mongo.db.users.update_one(
            {"_id": ObjectId(user_id)},
            {
                "$set": {"last_login": datetime.utcnow()},
                "$inc": {"login_count": 1}
            }
        )
        
        return session_data
    
    @staticmethod
    def end_session(session_id):
        """End a session when user logs out"""
        mongo.db.user_sessions.update_one(
            {"session_id": session_id},
            {
                "$set": {
                    "logout_time": datetime.utcnow(),
                    "is_active": False
                }
            }
        )
    
    @staticmethod
    def update_session_activity(session_id):
        """Update last activity time for a session"""
        mongo.db.user_sessions.update_one(
            {"session_id": session_id},
            {"$set": {"last_activity": datetime.utcnow()}}
        )
    
    @staticmethod
    def get_user_sessions(user_id, limit=50, active_only=False):
        """Get all sessions for a user"""
        query = {"user_id": ObjectId(user_id)}
        if active_only:
            query["is_active"] = True
        
        sessions = mongo.db.user_sessions.find(query).sort("login_time", -1).limit(limit)
        return list(sessions)
    
    @staticmethod
    def get_active_sessions(user_id):
        """Get all active sessions for a user"""
        return SessionManager.get_user_sessions(user_id, active_only=True)
    
    @staticmethod
    def end_all_user_sessions(user_id, except_session_id=None):
        """End all sessions for a user (useful for security purposes)"""
        query = {"user_id": ObjectId(user_id), "is_active": True}
        if except_session_id:
            query["session_id"] = {"$ne": except_session_id}
        
        mongo.db.user_sessions.update_many(
            query,
            {
                "$set": {
                    "logout_time": datetime.utcnow(),
                    "is_active": False
                }
            }
        )
    
    @staticmethod
    def cleanup_expired_sessions():
        """Clean up sessions that have been inactive for too long"""
        # Mark sessions as inactive if no activity for 24 hours
        cutoff_time = datetime.utcnow() - timedelta(hours=24)
        
        mongo.db.user_sessions.update_many(
            {
                "is_active": True,
                "last_activity": {"$lt": cutoff_time}
            },
            {
                "$set": {
                    "logout_time": datetime.utcnow(),
                    "is_active": False
                }
            }
        )
    
    @staticmethod
    def get_session_stats(user_id):
        """Get session statistics for a user"""
        pipeline = [
            {"$match": {"user_id": ObjectId(user_id)}},
            {
                "$group": {
                    "_id": None,
                    "total_sessions": {"$sum": 1},
                    "active_sessions": {
                        "$sum": {"$cond": [{"$eq": ["$is_active", True]}, 1, 0]}
                    },
                    "first_login": {"$min": "$login_time"},
                    "last_login": {"$max": "$login_time"}
                }
            }
        ]
        
        result = list(mongo.db.user_sessions.aggregate(pipeline))
        return result[0] if result else {
            "total_sessions": 0,
            "active_sessions": 0,
            "first_login": None,
            "last_login": None
        }
    
    @staticmethod
    def _get_location_from_ip(ip_address):
        """Get approximate location from IP address (placeholder)"""
        # In a real implementation, you would use a service like GeoIP
        # For now, return a placeholder
        if ip_address in ["127.0.0.1", "localhost"]:
            return "Local Development"
        return "Unknown Location"
    
    @staticmethod
    def _parse_user_agent(user_agent):
        """Parse user agent to extract device and browser info"""
        if not user_agent:
            return {"browser": "Unknown", "os": "Unknown", "device": "Unknown"}
        
        # Simple user agent parsing (in production, use a proper library)
        browser = "Unknown"
        os = "Unknown"
        device = "Desktop"
        
        user_agent_lower = user_agent.lower()
        
        # Browser detection
        if "chrome" in user_agent_lower:
            browser = "Chrome"
        elif "firefox" in user_agent_lower:
            browser = "Firefox"
        elif "safari" in user_agent_lower:
            browser = "Safari"
        elif "edge" in user_agent_lower:
            browser = "Edge"
        
        # OS detection
        if "windows" in user_agent_lower:
            os = "Windows"
        elif "mac" in user_agent_lower:
            os = "macOS"
        elif "linux" in user_agent_lower:
            os = "Linux"
        elif "android" in user_agent_lower:
            os = "Android"
            device = "Mobile"
        elif "iphone" in user_agent_lower or "ipad" in user_agent_lower:
            os = "iOS"
            device = "Mobile" if "iphone" in user_agent_lower else "Tablet"
        
        return {
            "browser": browser,
            "os": os,
            "device": device,
            "raw": user_agent
        }

def get_session_collection(mongo):
    """Get the user sessions collection"""
    return mongo.db.user_sessions
