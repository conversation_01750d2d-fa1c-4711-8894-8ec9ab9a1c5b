from flask_security import UserDatastore
from app.utils.hash_utils import hash_password_custom

class PyMongoUserDatastore(UserDatastore):
    def __init__(self, db, user_collection_name="users"):
        self.db = db
        self.user_collection = db[user_collection_name]

    def find_user(self, **kwargs):
        return self.user_collection.find_one(kwargs)

    def create_user(self, **kwargs):
        if "password" in kwargs:
            kwargs["password"] = hash_password_custom(kwargs["password"])
        result = self.user_collection.insert_one(kwargs)
        kwargs["_id"] = result.inserted_id
        return kwargs

    def get_user(self, identifier):
        return self.find_user(email=identifier)

    def save(self, user):
        self.user_collection.replace_one({"_id": user["_id"]}, user)
        return user
