"""
Activity Log Model for PICA Platform
Tracks all user activities for security auditing and compliance
"""

from datetime import datetime
from bson import ObjectId
from app.extensions import mongo
from typing import Dict, List, Optional, Any
import json


class ActivityLogManager:
    """Manager for user activity logging with immutable audit trail"""
    
    # Activity categories for better organization
    CATEGORIES = {
        'AUTH': 'Authentication',
        'SCAN': 'Security Scanning',
        'EXPORT': 'Data Export',
        'USER_MGMT': 'User Management',
        'INCIDENT': 'Incident Management',
        'CONFIG': 'Configuration',
        'SECURITY': 'Security Events',
        'SYSTEM': 'System Operations'
    }
    
    # Activity types with descriptions
    ACTIVITY_TYPES = {
        # Authentication
        'LOGIN_SUCCESS': 'Successful login',
        'LOGIN_FAILED': 'Failed login attempt',
        'LOGOUT': 'User logout',
        'SESSION_TERMINATED': 'Session terminated by admin',
        'PASSWORD_CHANGED': 'Password changed',
        'MFA_ENABLED': 'Multi-factor authentication enabled',
        'MFA_DISABLED': 'Multi-factor authentication disabled',
        
        # Security Scanning
        'SCAN_STARTED': 'Security scan initiated',
        'SCAN_COMPLETED': 'Security scan completed',
        'SCAN_STOPPED': 'Security scan stopped',
        'SCAN_DELETED': 'Security scan deleted',
        'TARGET_ADDED': 'Scan target added',
        'TARGET_REMOVED': 'Scan target removed',
        
        # Data Export
        'REPORT_EXPORTED': 'Report exported',
        'DATA_DOWNLOADED': 'Data downloaded',
        'BACKUP_CREATED': 'Backup created',
        
        # User Management
        'USER_CREATED': 'User account created',
        'USER_UPDATED': 'User account updated',
        'USER_DELETED': 'User account deleted',
        'USER_BANNED': 'User account banned',
        'USER_UNBANNED': 'User account unbanned',
        'ROLE_CHANGED': 'User role changed',
        
        # Incident Management
        'INCIDENT_CREATED': 'Security incident created',
        'INCIDENT_UPDATED': 'Security incident updated',
        'INCIDENT_CLOSED': 'Security incident closed',
        'TICKET_CREATED': 'Support ticket created',
        'TICKET_UPDATED': 'Support ticket updated',
        'TICKET_CLOSED': 'Support ticket closed',
        
        # Configuration
        'SETTINGS_CHANGED': 'System settings changed',
        'INTEGRATION_CONFIGURED': 'Integration configured',
        
        # Security Events
        'SUSPICIOUS_ACTIVITY': 'Suspicious activity detected',
        'UNAUTHORIZED_ACCESS': 'Unauthorized access attempt',
        'DATA_BREACH_DETECTED': 'Potential data breach detected',
        
        # System Operations
        'SYSTEM_BACKUP': 'System backup performed',
        'SYSTEM_MAINTENANCE': 'System maintenance performed'
    }
    
    @staticmethod
    def log_activity(
        user_id: str,
        activity_type: str,
        category: str,
        details: Dict[str, Any] = None,
        ip_address: str = None,
        user_agent: str = None,
        success: bool = True,
        target_user_id: str = None,
        resource_id: str = None,
        resource_type: str = None
    ) -> str:
        """
        Log a user activity with comprehensive details
        
        Args:
            user_id: ID of the user performing the action
            activity_type: Type of activity (from ACTIVITY_TYPES)
            category: Category of activity (from CATEGORIES)
            details: Additional details about the activity
            ip_address: IP address of the user
            user_agent: User agent string
            success: Whether the activity was successful
            target_user_id: ID of target user (for user management actions)
            resource_id: ID of affected resource
            resource_type: Type of affected resource
            
        Returns:
            str: ID of the created log entry
        """
        try:
            # Get user information
            user = mongo.db.users.find_one({"_id": ObjectId(user_id)})
            username = user.get('username', 'Unknown') if user else 'System'
            
            # Create activity log entry
            activity_data = {
                "user_id": ObjectId(user_id) if user_id else None,
                "username": username,
                "activity_type": activity_type,
                "activity_description": ActivityLogManager.ACTIVITY_TYPES.get(activity_type, activity_type),
                "category": category,
                "category_description": ActivityLogManager.CATEGORIES.get(category, category),
                "details": details or {},
                "ip_address": ip_address,
                "user_agent": user_agent,
                "success": success,
                "target_user_id": ObjectId(target_user_id) if target_user_id else None,
                "resource_id": resource_id,
                "resource_type": resource_type,
                "timestamp": datetime.utcnow(),
                "created_at": datetime.utcnow(),
                # Immutability fields
                "is_immutable": True,
                "hash": None  # Could add hash for integrity verification
            }
            
            # Insert the log entry
            result = mongo.db.activity_logs.insert_one(activity_data)
            
            return str(result.inserted_id)
            
        except Exception as e:
            print(f"❌ Error logging activity: {str(e)}")
            return None
    
    @staticmethod
    def get_user_activities(
        user_id: str,
        limit: int = 100,
        offset: int = 0,
        category: str = None,
        activity_type: str = None,
        start_date: datetime = None,
        end_date: datetime = None,
        success_only: bool = None
    ) -> Dict[str, Any]:
        """
        Get activities for a specific user with filtering options
        
        Args:
            user_id: ID of the user
            limit: Maximum number of activities to return
            offset: Number of activities to skip
            category: Filter by category
            activity_type: Filter by activity type
            start_date: Filter activities after this date
            end_date: Filter activities before this date
            success_only: Filter only successful activities
            
        Returns:
            Dict containing activities and metadata
        """
        try:
            # Build query
            query = {"user_id": ObjectId(user_id)}
            
            if category:
                query["category"] = category
            
            if activity_type:
                query["activity_type"] = activity_type
            
            if start_date or end_date:
                date_query = {}
                if start_date:
                    date_query["$gte"] = start_date
                if end_date:
                    date_query["$lte"] = end_date
                query["timestamp"] = date_query
            
            if success_only is not None:
                query["success"] = success_only
            
            # Get total count
            total_count = mongo.db.activity_logs.count_documents(query)
            
            # Get activities with pagination
            activities = list(
                mongo.db.activity_logs.find(query)
                .sort("timestamp", -1)
                .skip(offset)
                .limit(limit)
            )
            
            # Convert ObjectIds to strings
            for activity in activities:
                activity["_id"] = str(activity["_id"])
                if activity.get("user_id"):
                    activity["user_id"] = str(activity["user_id"])
                if activity.get("target_user_id"):
                    activity["target_user_id"] = str(activity["target_user_id"])
            
            return {
                "activities": activities,
                "total_count": total_count,
                "limit": limit,
                "offset": offset,
                "has_more": (offset + limit) < total_count
            }
            
        except Exception as e:
            print(f"❌ Error getting user activities: {str(e)}")
            return {
                "activities": [],
                "total_count": 0,
                "limit": limit,
                "offset": offset,
                "has_more": False,
                "error": str(e)
            }
    
    @staticmethod
    def get_all_activities(
        limit: int = 100,
        offset: int = 0,
        category: str = None,
        activity_type: str = None,
        start_date: datetime = None,
        end_date: datetime = None,
        user_id: str = None
    ) -> Dict[str, Any]:
        """
        Get all activities across all users (admin only)
        """
        try:
            # Build query
            query = {}
            
            if user_id:
                query["user_id"] = ObjectId(user_id)
            
            if category:
                query["category"] = category
            
            if activity_type:
                query["activity_type"] = activity_type
            
            if start_date or end_date:
                date_query = {}
                if start_date:
                    date_query["$gte"] = start_date
                if end_date:
                    date_query["$lte"] = end_date
                query["timestamp"] = date_query
            
            # Get total count
            total_count = mongo.db.activity_logs.count_documents(query)
            
            # Get activities with pagination
            activities = list(
                mongo.db.activity_logs.find(query)
                .sort("timestamp", -1)
                .skip(offset)
                .limit(limit)
            )
            
            # Convert ObjectIds to strings
            for activity in activities:
                activity["_id"] = str(activity["_id"])
                if activity.get("user_id"):
                    activity["user_id"] = str(activity["user_id"])
                if activity.get("target_user_id"):
                    activity["target_user_id"] = str(activity["target_user_id"])
            
            return {
                "activities": activities,
                "total_count": total_count,
                "limit": limit,
                "offset": offset,
                "has_more": (offset + limit) < total_count
            }
            
        except Exception as e:
            print(f"❌ Error getting all activities: {str(e)}")
            return {
                "activities": [],
                "total_count": 0,
                "limit": limit,
                "offset": offset,
                "has_more": False,
                "error": str(e)
            }
    
    @staticmethod
    def get_activity_statistics(user_id: str = None) -> Dict[str, Any]:
        """
        Get activity statistics for a user or system-wide
        """
        try:
            match_stage = {}
            if user_id:
                match_stage["user_id"] = ObjectId(user_id)
            
            pipeline = []
            if match_stage:
                pipeline.append({"$match": match_stage})
            
            pipeline.extend([
                {
                    "$group": {
                        "_id": {
                            "category": "$category",
                            "success": "$success"
                        },
                        "count": {"$sum": 1}
                    }
                },
                {
                    "$group": {
                        "_id": "$_id.category",
                        "total": {"$sum": "$count"},
                        "successful": {
                            "$sum": {
                                "$cond": [{"$eq": ["$_id.success", True]}, "$count", 0]
                            }
                        },
                        "failed": {
                            "$sum": {
                                "$cond": [{"$eq": ["$_id.success", False]}, "$count", 0]
                            }
                        }
                    }
                }
            ])
            
            stats = list(mongo.db.activity_logs.aggregate(pipeline))
            
            return {
                "statistics": stats,
                "categories": ActivityLogManager.CATEGORIES,
                "activity_types": ActivityLogManager.ACTIVITY_TYPES
            }
            
        except Exception as e:
            print(f"❌ Error getting activity statistics: {str(e)}")
            return {
                "statistics": [],
                "categories": ActivityLogManager.CATEGORIES,
                "activity_types": ActivityLogManager.ACTIVITY_TYPES,
                "error": str(e)
            }
