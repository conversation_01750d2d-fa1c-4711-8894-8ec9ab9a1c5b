from flask import Flask
from .config import Config
import os
from .extensions import mongo, jwt, mail, security, cors, socketio
from .routes.auth_routes import auth_bp
from .routes.admin.admin_routes import admin_bp
from .routes.admin.activity_routes import activity_bp
from .routes.scan_routes import scan_bp
from .routes.phishing_routes import phishing_routes_bp
from .routes.vulnerability_routes import vulnerability_bp as vuln_bp
from .controllers.phishing_controller import phishing_bp
from .controllers.malware_controller import malware_bp
from .controllers.export_controller import export_bp
from .controllers.incident_controller import incident_bp
from .controllers.analysis_controller import analysis_bp
from .models.user_datastore_pymongo import PyMongoUserDatastore


def create_app():
    # Configure template folder for email templates
    template_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'templates')
    app = Flask(__name__, template_folder=template_dir)
    app.config.from_object(Config)

    # Configuration CORS pour permettre les requêtes depuis le frontend
    cors.init_app(app, resources={
        r"/*": {
            "origins": [
                "http://localhost:3000",
                "http://localhost:5173",
                "http://localhost:5174",
                "http://127.0.0.1:3000",
                "http://127.0.0.1:5173",
                "http://127.0.0.1:5174"
            ],
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization"],
            "supports_credentials": True
        }
    })

    # CORS est déjà géré par Flask-CORS ci-dessus - pas besoin de middleware manuel

    # 2. Initialise Mongo
    mongo.init_app(app)

    # 3. Initialise les autres extensions
    jwt.init_app(app)
    mail.init_app(app)

    # Configure JWT callbacks for session validation
    @jwt.additional_claims_loader
    def add_claims_to_jwt(identity):
        return {}

    @jwt.token_in_blocklist_loader
    def check_if_token_revoked(jwt_header, jwt_payload):
        """Check if the token's session is still active and user is not banned"""
        session_id = jwt_payload.get('session_id')
        if session_id:
            from app.models.session import SessionManager
            session = mongo.cx.get_database("pica").user_sessions.find_one({"session_id": session_id})
            if not session or not session.get("is_active", False):
                return True  # Token is revoked (session ended)

            # Check if user still exists and is not banned
            user = mongo.cx.get_database("pica").users.find_one({"_id": session.get("user_id")})
            if not user:
                # User has been deleted, revoke token
                SessionManager.end_session(session_id)
                return True  # Token is revoked (user deleted)

            if user.get("banned", False):
                # User is banned, revoke token
                SessionManager.end_session(session_id)
                return True  # Token is revoked (user banned)

            # Update last activity
            SessionManager.update_session_activity(session_id)
        return False  # Token is valid

    # 3.1. Initialise Socket.IO avec CORS
    socketio.init_app(app, cors_allowed_origins=[
        "http://localhost:3000",
        "http://localhost:5173",
        "http://localhost:5174",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5173",
        "http://127.0.0.1:5174"
    ])

    # 4. Configure Flask-Security avec la base Mongo
    user_datastore = PyMongoUserDatastore(mongo.cx.get_database("pica"))
    security.init_app(app, user_datastore)
    app.user_datastore = user_datastore  # Exposé pour utilisation ailleurs

    # 5. Enregistre les blueprints
    app.register_blueprint(auth_bp, url_prefix="/auth")
    app.register_blueprint(admin_bp, url_prefix="/admin")
    app.register_blueprint(activity_bp)  # activity_bp already has url_prefix='/admin/activity'
    app.register_blueprint(scan_bp, url_prefix="/scan")  # contient Nessus + OpenVAS
    app.register_blueprint(phishing_routes_bp, url_prefix="/api")  # contient detection phishing
    app.register_blueprint(vuln_bp, url_prefix="/api/vulnerabilities")  # contient gestion vulnérabilités
    app.register_blueprint(malware_bp, url_prefix="/api/malware")  # contient analyse de malware
    app.register_blueprint(export_bp, url_prefix="/api/export")  # contient exports de rapports
    app.register_blueprint(incident_bp, url_prefix="/api/incident")  # contient gestion des incidents
    app.register_blueprint(analysis_bp, url_prefix="/api")  # contient gestion des analyses IA
    # Register phishing blueprint directly as well for testing
    app.register_blueprint(phishing_bp, url_prefix="/api/phishing-direct")

    # 6. Importer les gestionnaires WebSocket
    from .utils import websocket

    return app
