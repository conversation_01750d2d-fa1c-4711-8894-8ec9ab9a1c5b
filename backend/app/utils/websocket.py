"""
WebSocket pour notifications en temps réel - PICA
"""

from flask_socketio import emit, join_room, leave_room, disconnect
from flask_jwt_extended import decode_token
from app.extensions import socketio
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class WebSocketManager:
    def __init__(self):
        self.connected_users = {}  # {session_id: user_data}
        self.admin_sessions = set()  # Sessions d'admins

    def add_user(self, session_id, user_data):
        """Ajouter un utilisateur connecté"""
        self.connected_users[session_id] = user_data

        if user_data.get('role') == 'admin':
            self.admin_sessions.add(session_id)
            join_room('admins')
            print(f"✅ Admin {user_data.get('username')} connected to WebSocket")

    def remove_user(self, session_id):
        """Supprimer un utilisateur déconnecté"""
        if session_id in self.connected_users:
            user_data = self.connected_users[session_id]

            if user_data.get('role') == 'admin':
                self.admin_sessions.discard(session_id)
                leave_room('admins')
                print(f"❌ Admin {user_data.get('username')} disconnected from WebSocket")

            del self.connected_users[session_id]

    def notify_admins(self, event_type, data):
        """Envoyer notification à tous les admins connectés"""
        if self.admin_sessions:
            socketio.emit(event_type, data, room='admins')
            print(f"📡 WebSocket notification sent to {len(self.admin_sessions)} admins: {event_type}")
            return True
        return False

# Instance globale
websocket_manager = WebSocketManager()

@socketio.on('connect')
def handle_connect(auth):
    """Gérer connexion WebSocket"""
    try:
        if not auth or 'token' not in auth:
            print("❌ WebSocket: No token provided")
            disconnect()
            return False

        # Décoder le token JWT
        try:
            decoded_token = decode_token(auth['token'])
            user_data = {
                'id': decoded_token.get('id'),
                'username': decoded_token.get('username'),
                'role': decoded_token.get('role', 'user')
            }
        except Exception as e:
            print(f"❌ WebSocket: Invalid token - {str(e)}")
            disconnect()
            return False

        # Ajouter l'utilisateur
        from flask import request
        session_id = request.sid
        websocket_manager.add_user(session_id, user_data)

        # Confirmer la connexion
        emit('connected', {
            'message': f"Connected as {user_data['username']}",
            'role': user_data['role'],
            'timestamp': datetime.now().isoformat()
        })

        return True

    except Exception as e:
        print(f"❌ WebSocket connection error: {str(e)}")
        disconnect()
        return False

@socketio.on('disconnect')
def handle_disconnect():
    """Gérer déconnexion WebSocket"""
    try:
        from flask import request
        session_id = request.sid
        websocket_manager.remove_user(session_id)
    except Exception as e:
        print(f"❌ WebSocket disconnect error: {str(e)}")

@socketio.on('ping')
def handle_ping():
    """Maintenir la connexion"""
    emit('pong', {'timestamp': datetime.now().isoformat()})

# Fonctions de notification

def notify_new_incident(incident_data):
    """Notifier nouvel incident"""
    notification = {
        'type': 'new_incident',
        'incident_id': incident_data.get('incident_id'),
        'title': incident_data.get('title'),
        'severity': incident_data.get('severity'),
        'message': f"🚨 New Incident #{incident_data.get('incident_id')}: {incident_data.get('title')}",
        'timestamp': datetime.now().isoformat()
    }
    return websocket_manager.notify_admins('new_incident', notification)

def notify_incident_updated(incident_data):
    """Notifier mise à jour incident"""
    notification = {
        'type': 'incident_updated',
        'incident_id': incident_data.get('incident_id'),
        'title': incident_data.get('title'),
        'message': f"📝 Incident #{incident_data.get('incident_id')} updated",
        'timestamp': datetime.now().isoformat()
    }
    return websocket_manager.notify_admins('incident_updated', notification)

def notify_new_ticket(ticket_data):
    """Notifier nouveau ticket"""
    notification = {
        'type': 'new_ticket',
        'ticket_number': ticket_data.get('ticket_number'),
        'title': ticket_data.get('short_description'),
        'message': f"🎫 New Ticket #{ticket_data.get('ticket_number')}: {ticket_data.get('short_description')}",
        'timestamp': datetime.now().isoformat()
    }
    return websocket_manager.notify_admins('new_ticket', notification)

def notify_ticket_assigned(ticket_data, assigned_to):
    """Notifier assignation ticket"""
    notification = {
        'type': 'ticket_assigned',
        'ticket_number': ticket_data.get('ticket_number'),
        'assigned_to': assigned_to,
        'message': f"👤 Ticket #{ticket_data.get('ticket_number')} assigned to {assigned_to}",
        'timestamp': datetime.now().isoformat()
    }
    return websocket_manager.notify_admins('ticket_assigned', notification)

def notify_critical_alert(alert_data):
    """Notifier alerte critique"""
    notification = {
        'type': 'critical_alert',
        'message': alert_data.get('message'),
        'timestamp': datetime.now().isoformat()
    }
