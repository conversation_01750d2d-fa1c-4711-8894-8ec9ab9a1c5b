"""
Utilitaires pour la gestion des réponses API dans PICA.
Fournit des fonctions standardisées pour les réponses de succès et d'erreur.
"""

from flask import jsonify
from typing import Any, Dict, Optional

def success_response(data: Any = None, message: str = "Success", status_code: int = 200) -> Dict[str, Any]:
    """
    Crée une réponse de succès standardisée.
    
    Args:
        data: Données à inclure dans la réponse
        message: Message de succès
        status_code: Code de statut HTTP
        
    Returns:
        Dict: Réponse JSON standardisée
    """
    response = {
        'success': True,
        'message': message,
        'data': data
    }
    
    return jsonify(response), status_code

def error_response(message: str = "An error occurred", error: Optional[str] = None, 
                  status_code: int = 400, data: Any = None) -> Dict[str, Any]:
    """
    Crée une réponse d'erreur standardisée.
    
    Args:
        message: Message d'erreur principal
        error: Détails de l'erreur (optionnel)
        status_code: Code de statut HTTP
        data: Données supplémentaires (optionnel)
        
    Returns:
        Dict: Réponse JSON d'erreur standardisée
    """
    response = {
        'success': False,
        'message': message
    }
    
    if error:
        response['error'] = error
    
    if data:
        response['data'] = data
    
    return jsonify(response), status_code

def paginated_response(data: list, page: int, per_page: int, total: int, 
                      message: str = "Data retrieved successfully") -> Dict[str, Any]:
    """
    Crée une réponse paginée standardisée.
    
    Args:
        data: Liste des données
        page: Numéro de page actuelle
        per_page: Nombre d'éléments par page
        total: Nombre total d'éléments
        message: Message de succès
        
    Returns:
        Dict: Réponse JSON paginée
    """
    total_pages = (total + per_page - 1) // per_page
    
    response = {
        'success': True,
        'message': message,
        'data': data,
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': total,
            'total_pages': total_pages,
            'has_next': page < total_pages,
            'has_prev': page > 1
        }
    }
    
    return jsonify(response), 200

def validation_error_response(errors: Dict[str, list]) -> Dict[str, Any]:
    """
    Crée une réponse d'erreur de validation standardisée.
    
    Args:
        errors: Dictionnaire des erreurs de validation par champ
        
    Returns:
        Dict: Réponse JSON d'erreur de validation
    """
    response = {
        'success': False,
        'message': 'Validation failed',
        'errors': errors
    }
    
    return jsonify(response), 422
