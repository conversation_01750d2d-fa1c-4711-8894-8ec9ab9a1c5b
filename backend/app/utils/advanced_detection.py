"""
Module de détection avancée pour le service de phishing - PICA

Ce module fournit des fonctions de détection avancée pour identifier
les techniques sophistiquées de phishing et d'usurpation d'identité.
"""

from urllib.parse import urlparse
from bs4 import BeautifulSoup
import re

# Liste des marques populaires pour la détection d'usurpation
POPULAR_BRANDS = [
    'paypal', 'amazon', 'apple', 'microsoft', 'google', 'facebook', 'instagram',
    'twitter', 'linkedin', 'ebay', 'netflix', 'spotify', 'adobe', 'dropbox',
    'github', 'gitlab', 'stackoverflow', 'reddit', 'youtube', 'gmail',
    'outlook', 'yahoo', 'hotmail', 'icloud', 'onedrive', 'googledrive',
    'banking', 'bank', 'credit', 'visa', 'mastercard', 'amex', 'discover',
    'wells', 'chase', 'citibank', 'hsbc', 'santander', 'barclays'
]

def detect_brand_impersonation(url, html_content=None):
    """
    Détecte si un site web usurpe l'identité d'une marque populaire.
    
    Args:
        url (str): L'URL à analyser
        html_content (str): Contenu HTML optionnel de la page
    
    Returns:
        dict: Dictionnaire avec le résultat de la détection
    """
    parsed_url = urlparse(url)
    domain = parsed_url.netloc.lower()
    
    # Supprimer le préfixe www. s'il est présent
    if domain.startswith('www.'):
        domain = domain[4:]
    
    # Diviser le domaine par points pour obtenir le nom de domaine principal
    domain_parts = domain.split('.')
    main_domain = domain_parts[0] if domain_parts else ""
    
    # Rechercher les marques détectées dans le domaine
    detected_brands = []
    for brand in POPULAR_BRANDS:
        # Vérifier la présence de la marque dans le domaine mais pas une correspondance exacte
        if brand in main_domain and brand != main_domain:
            detected_brands.append(brand)
    
    # Si nous avons du contenu HTML, vérifier les mentions dans le contenu
    brand_mentions_in_content = []
    if html_content:
        soup = BeautifulSoup(html_content, 'lxml')
        text_content = soup.get_text().lower()
        
        for brand in POPULAR_BRANDS:
            if brand in text_content:
                brand_mentions_in_content.append(brand)
    
    # Analyser les résultats
    if detected_brands:
        return {
            'name': 'Brand Impersonation',
            'result': 'Failed',
            'description': f'Domain appears to impersonate: {", ".join(detected_brands)}',
            'weight': 8,
            'high_risk': True
        }
    elif brand_mentions_in_content and len(brand_mentions_in_content) > 2:
        return {
            'name': 'Brand Mentions',
            'result': 'Warning',
            'description': f'Page mentions multiple brands: {", ".join(brand_mentions_in_content[:3])}',
            'weight': 3
        }
    else:
        return {
            'name': 'Brand Impersonation',
            'result': 'Passed',
            'description': 'No brand impersonation detected',
            'weight': 1
        }

def detect_homoglyph_attack(url):
    """
    Détecte les attaques par homoglyphes dans l'URL.
    
    Args:
        url (str): L'URL à analyser
    
    Returns:
        dict: Dictionnaire avec le résultat de la détection
    """
    parsed_url = urlparse(url)
    domain = parsed_url.netloc.lower()
    
    # Caractères homoglyphes courants
    homoglyph_chars = {
        'a': ['à', 'á', 'â', 'ã', 'ä', 'å', 'ā', 'ă', 'ą', 'α'],
        'e': ['è', 'é', 'ê', 'ë', 'ē', 'ĕ', 'ė', 'ę', 'ě'],
        'i': ['ì', 'í', 'î', 'ï', 'ĩ', 'ī', 'ĭ', 'į', 'ı'],
        'o': ['ò', 'ó', 'ô', 'õ', 'ö', 'ø', 'ō', 'ŏ', 'ő', 'ο'],
        'u': ['ù', 'ú', 'û', 'ü', 'ũ', 'ū', 'ŭ', 'ů', 'ű', 'ų'],
        'c': ['ç', 'ć', 'ĉ', 'ċ', 'č'],
        'n': ['ñ', 'ń', 'ņ', 'ň', 'ŉ'],
        's': ['ś', 'ŝ', 'ş', 'š'],
        'y': ['ý', 'ÿ', 'ŷ'],
        'z': ['ź', 'ż', 'ž']
    }
    
    # Vérifier la présence de caractères homoglyphes
    suspicious_chars = []
    for char in domain:
        for normal_char, variants in homoglyph_chars.items():
            if char in variants:
                suspicious_chars.append(char)
    
    if suspicious_chars:
        return {
            'name': 'Homoglyph Attack',
            'result': 'Failed',
            'description': f'Domain contains suspicious characters: {", ".join(set(suspicious_chars))}',
            'weight': 7,
            'high_risk': True
        }
    else:
        return {
            'name': 'Homoglyph Attack',
            'result': 'Passed',
            'description': 'No homoglyph characters detected',
            'weight': 1
        }

def detect_obfuscation_techniques(html_content):
    """
    Détecte les techniques d'obfuscation dans le contenu HTML.
    
    Args:
        html_content (str): Le contenu HTML à analyser
    
    Returns:
        dict: Dictionnaire avec le résultat de la détection
    """
    if not html_content:
        return None
        
    soup = BeautifulSoup(html_content, 'lxml')
    
    # Vérifier les éléments invisibles
    invisible_elements = 0
    suspicious_styles = ['display:none', 'visibility:hidden', 'opacity:0']
    
    for tag in soup.find_all(style=True):
        style = tag.get('style', '').lower()
        if any(s in style for s in suspicious_styles):
            invisible_elements += 1
    
    # Vérifier le JavaScript obfusqué
    scripts = soup.find_all('script')
    obfuscated_js = 0
    
    for script in scripts:
        script_content = script.string if script.string else ""
        if script_content and (
            'eval(' in script_content or 
            'escape(' in script_content or 
            'unescape(' in script_content or
            'String.fromCharCode(' in script_content or
            re.search(r'\\x[0-9a-f]{2}', script_content) or
            re.search(r'\\u[0-9a-f]{4}', script_content)
        ):
            obfuscated_js += 1
    
    # Vérifier les iframes cachées
    hidden_iframes = 0
    iframes = soup.find_all('iframe')
    
    for iframe in iframes:
        style = iframe.get('style', '').lower()
        if any(s in style for s in suspicious_styles) or iframe.get('width', '') == '0' or iframe.get('height', '') == '0':
            hidden_iframes += 1
    
    total_suspicious = invisible_elements + obfuscated_js + hidden_iframes
    
    if total_suspicious > 3:
        return {
            'name': 'Content Obfuscation',
            'result': 'Failed',
            'description': f'Multiple obfuscation techniques detected: {total_suspicious} suspicious elements',
            'weight': 6,
            'high_risk': True
        }
    elif total_suspicious > 0:
        return {
            'name': 'Content Obfuscation',
            'result': 'Warning',
            'description': f'Some obfuscation detected: {total_suspicious} suspicious elements',
            'weight': 3
        }
    else:
        return {
            'name': 'Content Obfuscation',
            'result': 'Passed',
            'description': 'No obfuscation techniques detected',
            'weight': 1
        }

def detect_data_uri_scheme(url, html_content=None):
    """
    Détecte l'utilisation de schémas URI de données.
    
    Args:
        url (str): L'URL à analyser
        html_content (str): Contenu HTML optionnel
    
    Returns:
        dict: Dictionnaire avec le résultat de la détection
    """
    # Vérifier si l'URL elle-même utilise un schéma URI de données
    if url.startswith('data:'):
        return {
            'name': 'Data URI Scheme',
            'result': 'Failed',
            'description': 'URL uses data URI scheme which can hide malicious code',
            'weight': 5,
            'high_risk': True
        }
    
    # Si nous avons du HTML, vérifier les URI de données dans les ressources
    data_uri_count = 0
    if html_content:
        soup = BeautifulSoup(html_content, 'lxml')
        
        for tag in soup.find_all(['img', 'script', 'iframe', 'embed', 'object', 'a']):
            for attr in ['src', 'href', 'data']:
                if attr in tag.attrs and tag[attr].startswith('data:'):
                    data_uri_count += 1
    
    if data_uri_count > 0:
        return {
            'name': 'Data URI Resources',
            'result': 'Failed',
            'description': f'Page contains {data_uri_count} data URI resources',
            'weight': 4,
            'high_risk': data_uri_count > 3
        }
    else:
        return {
            'name': 'Data URI Scheme',
            'result': 'Passed',
            'description': 'No data URI schemes detected',
            'weight': 1
        }

def detect_suspicious_redirects(html_content):
    """
    Détecte les redirections suspectes côté client.
    
    Args:
        html_content (str): Le contenu HTML à analyser
    
    Returns:
        dict: Dictionnaire avec le résultat de la détection
    """
    if not html_content:
        return None
        
    soup = BeautifulSoup(html_content, 'lxml')
    
    # Vérifier les redirections meta refresh
    meta_redirects = soup.find_all('meta', attrs={'http-equiv': lambda x: x and x.lower() == 'refresh'})
    meta_redirect_count = len(meta_redirects)
    
    # Vérifier les redirections JavaScript
    js_redirects = 0
    scripts = soup.find_all('script')
    redirect_patterns = [
        'window.location', 'document.location', 
        'location.href', 'location.replace', 
        'location=', 'location ='
    ]
    
    for script in scripts:
        script_content = script.string if script.string else ""
        if script_content and any(pattern in script_content for pattern in redirect_patterns):
            js_redirects += 1
    
    total_redirects = meta_redirect_count + js_redirects
    
    if total_redirects > 2:
        return {
            'name': 'Suspicious Redirects',
            'result': 'Failed',
            'description': f'Multiple client-side redirects detected: {total_redirects}',
            'weight': 5,
            'high_risk': True
        }
    elif total_redirects > 0:
        return {
            'name': 'Suspicious Redirects',
            'result': 'Warning',
            'description': f'Client-side redirects detected: {total_redirects}',
            'weight': 3
        }
    else:
        return {
            'name': 'Suspicious Redirects',
            'result': 'Passed',
            'description': 'No suspicious redirects detected',
            'weight': 1
        }

def detect_cloaking_techniques(html_content):
    """
    Détecte les techniques de masquage (cloaking).
    
    Args:
        html_content (str): Contenu HTML de la page
    
    Returns:
        dict: Dictionnaire avec le résultat de la détection
    """
    if not html_content:
        return None

    soup = BeautifulSoup(html_content, 'lxml')

    # Vérifier les scripts de détection d'user-agent
    ua_detection = False
    scripts = soup.find_all('script')
    ua_patterns = [
        'navigator.userAgent', 'navigator.appName', 'navigator.appVersion',
        'navigator.vendor', 'navigator.platform'
    ]

    for script in scripts:
        script_content = script.string if script.string else ""
        if script_content and any(pattern in script_content for pattern in ua_patterns):
            ua_detection = True
            break

    if ua_detection:
        return {
            'name': 'Cloaking Techniques',
            'result': 'Warning',
            'description': 'User-agent detection scripts found (potential cloaking)',
            'weight': 4
        }
    else:
        return {
            'name': 'Cloaking Techniques',
            'result': 'Passed',
            'description': 'No cloaking techniques detected',
            'weight': 1
        }
