from functools import wraps
from flask import request, jsonify
from flask_jwt_extended import verify_jwt_in_request, get_jwt
from app.extensions import mongo
from app.models.session import Session<PERSON>anager

def session_required(fn):
    """Decorator to check if the session is still active and user is not banned"""
    @wraps(fn)
    def wrapper(*args, **kwargs):
        verify_jwt_in_request()
        claims = get_jwt()
        session_id = claims.get("session_id")

        if session_id:
            # Check if session is still active in database
            session = mongo.db.user_sessions.find_one({"session_id": session_id})
            if not session or not session.get("is_active", False):
                return jsonify({"msg": "Session has been terminated", "code": "SESSION_TERMINATED"}), 401

            # Check if user still exists and is not banned
            user = mongo.db.users.find_one({"_id": session.get("user_id")})
            if not user:
                # User has been deleted, terminate the session
                SessionManager.end_session(session_id)
                return jsonify({"msg": "Account has been deleted", "code": "ACCOUNT_DELETED"}), 403

            if user.get("banned", False):
                # User is banned, terminate the session
                SessionManager.end_session(session_id)
                return jsonify({"msg": "Account has been banned", "code": "ACCOUNT_BANNED"}), 403

            # Update last activity
            SessionManager.update_session_activity(session_id)

        return fn(*args, **kwargs)
    return wrapper

def admin_required(fn):
    @wraps(fn)
    def wrapper(*args, **kwargs):
        verify_jwt_in_request()
        claims = get_jwt()
        session_id = claims.get("session_id")

        # Check session validity first
        if session_id:
            session = mongo.db.user_sessions.find_one({"session_id": session_id})
            if not session or not session.get("is_active", False):
                return jsonify({"msg": "Session has been terminated", "code": "SESSION_TERMINATED"}), 401

            # Update last activity
            SessionManager.update_session_activity(session_id)

        # Check admin role
        if claims.get("role") != "admin":
            return jsonify({"msg": "Admin access required"}), 403
        return fn(*args, **kwargs)
    return wrapper

def handle_options(f):
    """Decorator to handle OPTIONS requests for CORS"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if request.method == 'OPTIONS':
            return jsonify({}), 200
        return f(*args, **kwargs)
    return decorated_function
