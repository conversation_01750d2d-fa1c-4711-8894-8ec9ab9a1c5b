"""
Module d'intégrations API pour le service de phishing - PICA

Ce module fournit des intégrations avec des services externes
pour vérifier la réputation des URLs et domaines.
"""

import requests
import time
from urllib.parse import urlparse

# Configuration des APIs (à configurer avec de vraies clés API)
VIRUSTOTAL_API_KEY = None  # Remplacer par une vraie clé API
URLSCAN_API_KEY = None     # Remplacer par une vraie clé API

def check_reputation(url):
    """
    Vérifie la réputation d'une URL auprès de services externes.
    
    Args:
        url (str): L'URL à vérifier
    
    Returns:
        dict: Dictionnaire avec le résultat de la vérification ou None
    """
    # Pour l'instant, retourner une vérification basique
    # En production, ceci devrait interroger de vrais services
    
    parsed_url = urlparse(url)
    domain = parsed_url.netloc
    
    # Simulation d'une vérification de réputation
    # En réalité, ceci devrait appeler VirusTotal, URLScan.io, etc.
    
    # Liste de domaines connus comme malveillants (exemple)
    known_malicious_domains = [
        'phishing-example.com',
        'fake-paypal.net',
        'malicious-site.org',
        'scam-bank.com'
    ]
    
    # Liste de domaines suspects
    suspicious_patterns = [
        'secure-update',
        'account-verify',
        'login-verify',
        'security-check',
        'urgent-action'
    ]
    
    # Vérifier si le domaine est dans la liste noire
    if domain in known_malicious_domains:
        return {
            'name': 'External Reputation',
            'result': 'Failed',
            'description': f'Domain {domain} is flagged as malicious by security services',
            'weight': 10,
            'high_risk': True
        }
    
    # Vérifier les motifs suspects dans le domaine
    for pattern in suspicious_patterns:
        if pattern in domain:
            return {
                'name': 'External Reputation',
                'result': 'Warning',
                'description': f'Domain contains suspicious pattern: {pattern}',
                'weight': 4
            }
    
    # Si aucun problème détecté
    return {
        'name': 'External Reputation',
        'result': 'Passed',
        'description': 'No reputation issues found',
        'weight': 1
    }

def check_virustotal(url):
    """
    Vérifie une URL auprès de VirusTotal.
    
    Args:
        url (str): L'URL à vérifier
    
    Returns:
        dict: Résultat de la vérification VirusTotal ou None
    """
    if not VIRUSTOTAL_API_KEY:
        return None
    
    try:
        # URL de l'API VirusTotal v3
        vt_url = "https://www.virustotal.com/vtapi/v2/url/report"
        
        params = {
            'apikey': VIRUSTOTAL_API_KEY,
            'resource': url
        }
        
        response = requests.get(vt_url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('response_code') == 1:
                positives = data.get('positives', 0)
                total = data.get('total', 0)
                
                if positives > 0:
                    return {
                        'name': 'VirusTotal Check',
                        'result': 'Failed',
                        'description': f'{positives}/{total} security vendors flagged this URL as malicious',
                        'weight': 8,
                        'high_risk': positives > 3
                    }
                else:
                    return {
                        'name': 'VirusTotal Check',
                        'result': 'Passed',
                        'description': f'Clean scan result from {total} security vendors',
                        'weight': 1
                    }
            else:
                return {
                    'name': 'VirusTotal Check',
                    'result': 'Warning',
                    'description': 'URL not found in VirusTotal database',
                    'weight': 2
                }
        
    except Exception as e:
        return {
            'name': 'VirusTotal Check',
            'result': 'Warning',
            'description': f'VirusTotal check failed: {str(e)}',
            'weight': 1
        }
    
    return None

def check_urlscan(url):
    """
    Vérifie une URL auprès de URLScan.io.
    
    Args:
        url (str): L'URL à vérifier
    
    Returns:
        dict: Résultat de la vérification URLScan ou None
    """
    if not URLSCAN_API_KEY:
        return None
    
    try:
        # Soumettre l'URL pour scan
        submit_url = "https://urlscan.io/api/v1/scan/"
        
        headers = {
            'API-Key': URLSCAN_API_KEY,
            'Content-Type': 'application/json'
        }
        
        data = {
            'url': url,
            'visibility': 'private'
        }
        
        response = requests.post(submit_url, headers=headers, json=data, timeout=10)
        
        if response.status_code == 200:
            scan_data = response.json()
            scan_id = scan_data.get('uuid')
            
            # Attendre que le scan soit terminé
            time.sleep(5)
            
            # Récupérer les résultats
            result_url = f"https://urlscan.io/api/v1/result/{scan_id}/"
            result_response = requests.get(result_url, timeout=10)
            
            if result_response.status_code == 200:
                result_data = result_response.json()
                
                # Analyser les résultats
                verdicts = result_data.get('verdicts', {})
                overall = verdicts.get('overall', {})
                
                if overall.get('malicious', False):
                    return {
                        'name': 'URLScan Check',
                        'result': 'Failed',
                        'description': 'URLScan.io flagged this URL as malicious',
                        'weight': 7,
                        'high_risk': True
                    }
                elif overall.get('suspicious', False):
                    return {
                        'name': 'URLScan Check',
                        'result': 'Warning',
                        'description': 'URLScan.io flagged this URL as suspicious',
                        'weight': 4
                    }
                else:
                    return {
                        'name': 'URLScan Check',
                        'result': 'Passed',
                        'description': 'URLScan.io found no issues with this URL',
                        'weight': 1
                    }
        
    except Exception as e:
        return {
            'name': 'URLScan Check',
            'result': 'Warning',
            'description': f'URLScan check failed: {str(e)}',
            'weight': 1
        }
    
    return None

def check_google_safe_browsing(url):
    """
    Vérifie une URL auprès de Google Safe Browsing.
    
    Args:
        url (str): L'URL à vérifier
    
    Returns:
        dict: Résultat de la vérification Google Safe Browsing ou None
    """
    # Cette fonction nécessiterait une clé API Google Safe Browsing
    # Pour l'instant, retourner None (pas implémenté)
    return None

def check_phishtank(url):
    """
    Vérifie une URL auprès de PhishTank.
    
    Args:
        url (str): L'URL à vérifier
    
    Returns:
        dict: Résultat de la vérification PhishTank ou None
    """
    try:
        # PhishTank API (gratuite mais limitée)
        phishtank_url = "http://checkurl.phishtank.com/checkurl/"
        
        data = {
            'url': url,
            'format': 'json'
        }
        
        response = requests.post(phishtank_url, data=data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('results', {}).get('in_database', False):
                if result['results'].get('valid', False):
                    return {
                        'name': 'PhishTank Check',
                        'result': 'Failed',
                        'description': 'URL is listed in PhishTank database as phishing',
                        'weight': 9,
                        'high_risk': True
                    }
            
            return {
                'name': 'PhishTank Check',
                'result': 'Passed',
                'description': 'URL not found in PhishTank phishing database',
                'weight': 1
            }
        
    except Exception as e:
        return {
            'name': 'PhishTank Check',
            'result': 'Warning',
            'description': f'PhishTank check failed: {str(e)}',
            'weight': 1
        }
    
    return None

def get_domain_reputation(domain):
    """
    Obtient la réputation d'un domaine auprès de plusieurs sources.
    
    Args:
        domain (str): Le domaine à vérifier
    
    Returns:
        dict: Dictionnaire avec les informations de réputation
    """
    reputation_data = {
        'domain': domain,
        'reputation_score': 0,
        'sources': [],
        'is_malicious': False,
        'is_suspicious': False
    }
    
    # Ici, on pourrait interroger plusieurs sources de réputation
    # Pour l'instant, retourner des données de base
    
    # Simulation de vérification de réputation
    suspicious_tlds = ['.tk', '.ml', '.ga', '.cf', '.click', '.download']
    
    for tld in suspicious_tlds:
        if domain.endswith(tld):
            reputation_data['is_suspicious'] = True
            reputation_data['reputation_score'] = 30
            reputation_data['sources'].append({
                'name': 'TLD Analysis',
                'score': 30,
                'reason': f'Domain uses suspicious TLD: {tld}'
            })
            break
    
    return reputation_data
