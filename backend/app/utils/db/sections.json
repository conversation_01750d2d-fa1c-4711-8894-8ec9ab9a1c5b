{"source": "https://github.com/cylance/PyPackerDetect", "last_update": "Jun 26 2021", "sections": {"acceptable": [".text"], "alternative": [".code", "text", ".text0", ".text1", ".text2", ".text3"], "delphi-bss": [".BSS", "BSS", ".bss"], "delphi": [".itext", "CODE"], "driver": ["INIT"], "known": [".00cfg", ".BSS", ".CLR_UEF", ".CRT", ".DATA", ".arch", ".autoload_text", ".bindat", ".bootdat", ".bss", ".buildid", ".code", ".complua", ".cormeta", ".cygwin_dll_common", ".data", ".data1", ".data2", ".data3", ".debug", ".debug$F", ".debug$P", ".debug$S", ".debug$T", ".didat", ".didata", ".drectve ", ".edata", ".eh_fram", ".eh_frame", ".export", ".fasm", ".fini", ".flat", ".gfids", ".giats", ".gljmp", ".glue_7", ".glue_7t", ".got", ".idata", ".idlsym", ".impdata", ".init", ".itext", ".ndata", ".orpc", ".pdata", ".plt", ".rdata", ".reloc", ".rodata", ".rsrc", ".sbss", ".script", ".sdata", ".shared", ".srdata", ".stab", ".stabstr", ".sxdata", ".text", ".text0", ".text1", ".text2", ".text3", ".textbss", ".tls", ".tls$", ".udata", ".vsdata", ".wixburn", ".wpp_sf ", ".xdata", "/113", "/14", "/29", "/4", "/41", "/55", "/67", "BSS", "CODE", "DATA", "DGROUP", "INIT", "PAGE", "Shared", "edata", "idata", "minATL", "nv_FatBi", "nv_fatb", "rdata", "sdata", "shared", "testdata", "text"]}}