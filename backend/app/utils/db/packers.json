{"packers": {".aspack": "Aspack packer", ".adata": "Aspack packer/Armadillo packer", "ASPack": "Aspack packer", ".ASPack": "ASPAck Protector", ".boom": "The Boomerang List Builder (config+exe xored with a single byte key 0x77)", ".ccg": "<PERSON><PERSON> (Chinese Packer)", ".charmve": "Added by the PIN tool", "BitArts": "Crunch 2.0 Packer", "DAStub": "DAStub Dragon Armor protector", "!EPack": "Epack packer", ".ecode": "Built with EPL", ".edata": "Built with EPL", ".enigma1": "Enigma Protector", ".enigma2": "Enigma Protector", "FSG!": "FSG packer (not a section name, but a good identifier)", ".imrsiv": "special section used for applications that can be loaded to OS desktop bands.", ".gentee": "Gentee installer", "kkrunchy": "<PERSON><PERSON><PERSON><PERSON> Packer", "lz32.dll": "Crinkler", ".mackt": "ImpRec-created section", ".MaskPE": "Mask<PERSON><PERSON> Packer", "MEW": "<PERSON><PERSON> packer", ".mnbvcx1": "most likely associated with Firseria PUP downloaders", ".mnbvcx2": "most likely associated with Firseria PUP downloaders", ".MPRESS1": "Mpress Packer", ".MPRESS2": "Mpress Packer", ".neolite": "Neolite Packer", ".neolit": "Neolite Packer", ".nsp1": "NsPack packer", ".nsp0": "NsPack packer", ".nsp2": "NsPack packer", "nsp1": "NsPack packer", "nsp0": "NsPack packer", "nsp2": "NsPack packer", ".packed": "Unknown Packer", "PEPACK!!": "<PERSON><PERSON><PERSON>", "pebundle": "PEBundle Packer", "PEBundle": "PEBundle Packer", "PEC2TO": "PECompact packer", "PECompact2": "PECompact packer (not a section name, but a good identifier)", "PEC2": "PECompact packer", "pec": "PECompact packer", "pec1": "PECompact packer", "pec2": "PECompact packer", "pec3": "PECompact packer", "pec4": "PECompact packer", "pec5": "PECompact packer", "pec6": "PECompact packer", "PEC2MO": "PECompact packer", "PELOCKnt": "PELock Protector", ".perplex": "Perplex PE-Protector", "PESHiELD": "PEShield Packer", ".petite": "<PERSON><PERSON> Packer", ".pinclie": "Added by the PIN tool", "ProCrypt": "ProCrypt Packer", ".RLPack": "<PERSON><PERSON><PERSON> Packer (second section)", ".rmnet": "Ramnit virus marker", "RCryptor": "R<PERSON><PERSON><PERSON> Packer", ".RPCrypt": "R<PERSON><PERSON><PERSON> Packer", ".seau": "SeauSFX Packer", ".sforce3": "StarForce Protection", ".shrink1": "<PERSON><PERSON><PERSON>", ".shrink2": "<PERSON><PERSON><PERSON>", ".shrink3": "<PERSON><PERSON><PERSON>", ".spack": "Simple Pack (by bagie)", ".svkp": "SVKP packer", "Themida": "<PERSON><PERSON> Packer", ".Themida": "<PERSON><PERSON> Packer", ".taz": "Some version os PESpin", ".tsuarch": "T<PERSON><PERSON><PERSON><PERSON>", ".tsustub": "T<PERSON><PERSON><PERSON><PERSON>", ".Upack": "Upack packer", ".ByDwing": "<PERSON><PERSON>er", "UPX0": "UPX packer", "UPX1": "UPX packer", "UPX2": "UPX packer", "UPX3": "UPX packer", "UPX!": "UPX packer", ".UPX0": "UPX Packer", ".UPX1": "UPX Packer", ".UPX2": "UPX Packer", ".vmp0": "VMProtect packer", ".vmp1": "VMProtect packer", ".vmp2": "VMProtect packer", "VProtect": "Vprotect Packer", ".winapi": "Added by API Override tool", "WinLicen": "WinLicense (Themida) Protector", "_winzip_": "<PERSON><PERSON><PERSON> Self-Extractor", ".WWPACK": "WWPACK Packer", ".WWP32": "WWPACK Packer (WWPack32)", ".yP": "Y0da Protector", ".y0da": "Y0da Protector", ".alien": "<PERSON><PERSON><PERSON> Packer"}}