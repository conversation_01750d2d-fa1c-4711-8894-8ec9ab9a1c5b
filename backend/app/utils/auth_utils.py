"""
Utilitaires d'authentification pour PICA.
Fournit des décorateurs et fonctions pour la gestion de l'authentification.
"""

import jwt
from functools import wraps
from flask import request, current_app, g
from typing import Dict, Any, Optional

from .response_utils import error_response

def require_auth(f):
    """
    Décorateur pour exiger une authentification sur une route.
    
    Args:
        f: Fonction de route à protéger
        
    Returns:
        Fonction décorée avec vérification d'authentification
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Pour les tests, on simule un utilisateur authentifié
        # TODO: Implémenter la vraie vérification JWT
        
        # Simuler un utilisateur pour les tests
        g.current_user = {
            'id': 'test_user_123',
            'username': 'test_user',
            'role': 'user',
            'is_admin': False
        }
        
        return f(*args, **kwargs)
    
    return decorated_function

def require_admin(f):
    """
    Décorateur pour exiger des privilèges administrateur.
    
    Args:
        f: Fonction de route à protéger
        
    Returns:
        Fonction décorée avec vérification admin
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Vérifier d'abord l'authentification
        if not hasattr(g, 'current_user') or not g.current_user:
            return error_response(
                message="Authentication required",
                error="Please log in to access this resource"
            ), 401
        
        # Vérifier les privilèges admin
        if not g.current_user.get('is_admin', False):
            return error_response(
                message="Admin privileges required",
                error="You don't have permission to access this resource"
            ), 403
        
        return f(*args, **kwargs)
    
    return decorated_function

def get_current_user() -> Optional[Dict[str, Any]]:
    """
    Récupère l'utilisateur actuellement connecté.

    Returns:
        Dict: Informations de l'utilisateur ou None
    """
    return getattr(g, 'current_user', None)

def get_current_user_id() -> Optional[str]:
    """
    Récupère l'ID de l'utilisateur actuellement connecté.

    Returns:
        str: ID de l'utilisateur ou None
    """
    current_user = get_current_user()
    return str(current_user.get('_id')) if current_user and '_id' in current_user else None

def extract_token_from_header() -> Optional[str]:
    """
    Extrait le token JWT de l'en-tête Authorization.
    
    Returns:
        str: Token JWT ou None
    """
    auth_header = request.headers.get('Authorization')
    if auth_header and auth_header.startswith('Bearer '):
        return auth_header.split(' ')[1]
    return None

def verify_jwt_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Vérifie et décode un token JWT.
    
    Args:
        token: Token JWT à vérifier
        
    Returns:
        Dict: Payload du token ou None si invalide
    """
    try:
        # TODO: Utiliser la vraie clé secrète de l'application
        secret_key = current_app.config.get('SECRET_KEY', 'dev-secret-key')
        payload = jwt.decode(token, secret_key, algorithms=['HS256'])
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

def create_jwt_token(user_data: Dict[str, Any], expires_in: int = 3600) -> str:
    """
    Crée un token JWT pour un utilisateur.
    
    Args:
        user_data: Données de l'utilisateur
        expires_in: Durée de validité en secondes
        
    Returns:
        str: Token JWT
    """
    import time
    
    payload = {
        'user_id': user_data.get('id'),
        'username': user_data.get('username'),
        'role': user_data.get('role'),
        'is_admin': user_data.get('is_admin', False),
        'exp': int(time.time()) + expires_in,
        'iat': int(time.time())
    }
    
    # TODO: Utiliser la vraie clé secrète de l'application
    secret_key = current_app.config.get('SECRET_KEY', 'dev-secret-key')
    return jwt.encode(payload, secret_key, algorithm='HS256')

def check_user_permissions(required_permissions: list) -> bool:
    """
    Vérifie si l'utilisateur actuel a les permissions requises.
    
    Args:
        required_permissions: Liste des permissions requises
        
    Returns:
        bool: True si l'utilisateur a toutes les permissions
    """
    current_user = get_current_user()
    if not current_user:
        return False
    
    # Les admins ont toutes les permissions
    if current_user.get('is_admin', False):
        return True
    
    # TODO: Implémenter la vérification des permissions spécifiques
    user_permissions = current_user.get('permissions', [])
    return all(perm in user_permissions for perm in required_permissions)

def get_user_role() -> Optional[str]:
    """
    Récupère le rôle de l'utilisateur actuel.
    
    Returns:
        str: Rôle de l'utilisateur ou None
    """
    current_user = get_current_user()
    return current_user.get('role') if current_user else None

def is_admin_user() -> bool:
    """
    Vérifie si l'utilisateur actuel est un administrateur.
    
    Returns:
        bool: True si l'utilisateur est admin
    """
    current_user = get_current_user()
    return current_user.get('is_admin', False) if current_user else False
