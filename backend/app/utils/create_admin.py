from app.extensions import mongo
from app.utils.hash_utils import hash_password_custom
from datetime import datetime

def create_admin():
    email = "<EMAIL>"
    username = "admin"
    password = "Admin123!"
    role = "admin"
    banned = "false"
 


    if mongo.db.users.find_one({"email": email}):
        print("Admin already exists.")
        return

    hashed_password = hash_password_custom(password)

    admin_user = {
        "username": username,
        "email": email,
        "password": hashed_password,
        "role": role,
        "first_name": "Admin",
        "last_name": "<PERSON><PERSON><PERSON>",
        "gender": "Female",
        "date of birth": "22/05/2000",
        "active": True,
        "banned": False,
        "email_verified": True,
        "2fa_enabled": False,
        "created_at": datetime.utcnow()
    }

    mongo.db.users.insert_one(admin_user)
    print("Admin user created successfully.")
