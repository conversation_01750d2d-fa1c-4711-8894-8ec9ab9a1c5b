import threading
from flask import current_app
from flask_mail import Message
from ..extensions import mail

def send_email_async(app, msg):
    """Send email asynchronously in a separate thread"""
    with app.app_context():
        try:
            mail.send(msg)
        except Exception as e:
            # Log the error but don't crash the main thread
            print(f"Error sending email: {e}")

def send_async_email(subject, recipients, body, sender=None):
    """
    Send email asynchronously without blocking the main thread
    
    Args:
        subject (str): Email subject
        recipients (list): List of recipient email addresses
        body (str): Email body content
        sender (str, optional): Sender email address
    """
    if sender is None:
        sender = "<EMAIL>"
    
    msg = Message(
        subject=subject,
        sender=sender,
        recipients=recipients,
        body=body
    )
    
    # Get current app context
    app = current_app._get_current_object()
    
    # Send email in a separate thread
    thread = threading.Thread(
        target=send_email_async,
        args=(app, msg)
    )
    thread.daemon = True
    thread.start()
    
    return True
