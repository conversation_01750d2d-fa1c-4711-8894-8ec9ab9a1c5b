"""
Activity Logger Utility for PICA Platform
Provides decorators and utilities for automatic activity logging
"""

from functools import wraps
from flask import request, g
from flask_jwt_extended import get_jwt_identity, get_jwt
from app.models.activity_log import ActivityLogManager
from typing import Dict, Any, Optional
import json


def get_client_ip():
    """Get the real client IP address"""
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.remote_addr


def get_user_agent():
    """Get the user agent string"""
    return request.headers.get('User-Agent', 'Unknown')


def log_activity(
    activity_type: str,
    category: str,
    details: Dict[str, Any] = None,
    success: bool = True,
    target_user_id: str = None,
    resource_id: str = None,
    resource_type: str = None
):
    """
    Log an activity for the current user
    
    Args:
        activity_type: Type of activity
        category: Category of activity
        details: Additional details
        success: Whether the activity was successful
        target_user_id: ID of target user (for user management)
        resource_id: ID of affected resource
        resource_type: Type of affected resource
    """
    try:
        user_id = get_jwt_identity()
        ip_address = get_client_ip()
        user_agent = get_user_agent()
        
        ActivityLogManager.log_activity(
            user_id=user_id,
            activity_type=activity_type,
            category=category,
            details=details,
            ip_address=ip_address,
            user_agent=user_agent,
            success=success,
            target_user_id=target_user_id,
            resource_id=resource_id,
            resource_type=resource_type
        )
    except Exception as e:
        print(f"❌ Error in log_activity: {str(e)}")


def activity_logger(
    activity_type: str,
    category: str,
    get_details: callable = None,
    get_target_user_id: callable = None,
    get_resource_info: callable = None
):
    """
    Decorator to automatically log activities
    
    Args:
        activity_type: Type of activity to log
        category: Category of activity
        get_details: Function to extract details from request/response
        get_target_user_id: Function to extract target user ID
        get_resource_info: Function to extract resource information
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            success = True
            details = {}
            target_user_id = None
            resource_id = None
            resource_type = None
            
            try:
                # Execute the original function
                result = f(*args, **kwargs)
                
                # Extract details if function provided
                if get_details:
                    try:
                        details = get_details(request, result, *args, **kwargs)
                    except Exception as e:
                        details = {"error_extracting_details": str(e)}
                
                # Extract target user ID if function provided
                if get_target_user_id:
                    try:
                        target_user_id = get_target_user_id(request, result, *args, **kwargs)
                    except Exception as e:
                        details["error_extracting_target_user"] = str(e)
                
                # Extract resource info if function provided
                if get_resource_info:
                    try:
                        resource_info = get_resource_info(request, result, *args, **kwargs)
                        if isinstance(resource_info, dict):
                            resource_id = resource_info.get('id')
                            resource_type = resource_info.get('type')
                        elif isinstance(resource_info, tuple):
                            resource_id, resource_type = resource_info
                    except Exception as e:
                        details["error_extracting_resource"] = str(e)
                
                return result
                
            except Exception as e:
                success = False
                details["error"] = str(e)
                raise
            
            finally:
                # Log the activity
                try:
                    log_activity(
                        activity_type=activity_type,
                        category=category,
                        details=details,
                        success=success,
                        target_user_id=target_user_id,
                        resource_id=resource_id,
                        resource_type=resource_type
                    )
                except Exception as log_error:
                    print(f"❌ Failed to log activity: {str(log_error)}")
        
        return decorated_function
    return decorator


# Specific activity loggers for common actions

def log_login_attempt(username: str, success: bool, ip_address: str = None, details: Dict = None):
    """Log a login attempt"""
    try:
        from app.extensions import mongo
        from bson import ObjectId
        
        # Find user by username
        user = mongo.db.users.find_one({"username": username})
        user_id = str(user["_id"]) if user else None
        
        ActivityLogManager.log_activity(
            user_id=user_id,
            activity_type='LOGIN_SUCCESS' if success else 'LOGIN_FAILED',
            category='AUTH',
            details=details or {"username": username},
            ip_address=ip_address or get_client_ip(),
            user_agent=get_user_agent(),
            success=success
        )
    except Exception as e:
        print(f"❌ Error logging login attempt: {str(e)}")


def log_logout(user_id: str, details: Dict = None):
    """Log a logout"""
    try:
        ActivityLogManager.log_activity(
            user_id=user_id,
            activity_type='LOGOUT',
            category='AUTH',
            details=details or {},
            ip_address=get_client_ip(),
            user_agent=get_user_agent(),
            success=True
        )
    except Exception as e:
        print(f"❌ Error logging logout: {str(e)}")


def log_session_terminated(user_id: str, terminated_by: str, details: Dict = None):
    """Log a session termination by admin"""
    try:
        ActivityLogManager.log_activity(
            user_id=user_id,
            activity_type='SESSION_TERMINATED',
            category='SECURITY',
            details=details or {"terminated_by": terminated_by},
            ip_address=get_client_ip(),
            user_agent=get_user_agent(),
            success=True
        )
    except Exception as e:
        print(f"❌ Error logging session termination: {str(e)}")


def log_user_management(action: str, target_user_id: str, details: Dict = None):
    """Log user management actions"""
    try:
        activity_types = {
            'create': 'USER_CREATED',
            'update': 'USER_UPDATED',
            'delete': 'USER_DELETED',
            'ban': 'USER_BANNED',
            'unban': 'USER_UNBANNED',
            'role_change': 'ROLE_CHANGED'
        }
        
        activity_type = activity_types.get(action, 'USER_UPDATED')
        
        log_activity(
            activity_type=activity_type,
            category='USER_MGMT',
            details=details or {},
            target_user_id=target_user_id
        )
    except Exception as e:
        print(f"❌ Error logging user management: {str(e)}")


def log_scan_activity(action: str, scan_id: str, details: Dict = None):
    """Log security scan activities"""
    try:
        activity_types = {
            'start': 'SCAN_STARTED',
            'complete': 'SCAN_COMPLETED',
            'stop': 'SCAN_STOPPED',
            'delete': 'SCAN_DELETED'
        }
        
        activity_type = activity_types.get(action, 'SCAN_STARTED')
        
        log_activity(
            activity_type=activity_type,
            category='SCAN',
            details=details or {},
            resource_id=scan_id,
            resource_type='security_scan'
        )
    except Exception as e:
        print(f"❌ Error logging scan activity: {str(e)}")


def log_export_activity(export_type: str, resource_id: str = None, details: Dict = None):
    """Log data export activities"""
    try:
        log_activity(
            activity_type='REPORT_EXPORTED',
            category='EXPORT',
            details=details or {"export_type": export_type},
            resource_id=resource_id,
            resource_type=export_type
        )
    except Exception as e:
        print(f"❌ Error logging export activity: {str(e)}")


def log_incident_activity(action: str, incident_id: str, details: Dict = None):
    """Log incident management activities"""
    try:
        activity_types = {
            'create': 'INCIDENT_CREATED',
            'update': 'INCIDENT_UPDATED',
            'close': 'INCIDENT_CLOSED',
            'create_ticket': 'TICKET_CREATED',
            'update_ticket': 'TICKET_UPDATED',
            'close_ticket': 'TICKET_CLOSED'
        }
        
        activity_type = activity_types.get(action, 'INCIDENT_UPDATED')
        
        log_activity(
            activity_type=activity_type,
            category='INCIDENT',
            details=details or {},
            resource_id=incident_id,
            resource_type='incident' if 'incident' in action else 'ticket'
        )
    except Exception as e:
        print(f"❌ Error logging incident activity: {str(e)}")


def log_security_event(event_type: str, details: Dict = None, severity: str = 'medium'):
    """Log security events"""
    try:
        activity_types = {
            'suspicious': 'SUSPICIOUS_ACTIVITY',
            'unauthorized': 'UNAUTHORIZED_ACCESS',
            'breach': 'DATA_BREACH_DETECTED'
        }
        
        activity_type = activity_types.get(event_type, 'SUSPICIOUS_ACTIVITY')
        
        event_details = details or {}
        event_details['severity'] = severity
        
        log_activity(
            activity_type=activity_type,
            category='SECURITY',
            details=event_details
        )
    except Exception as e:
        print(f"❌ Error logging security event: {str(e)}")


# Helper functions for extracting information from requests

def extract_user_creation_details(request, result, *args, **kwargs):
    """Extract details for user creation"""
    data = request.get_json() or {}
    return {
        "username": data.get("username"),
        "email": data.get("email"),
        "role": data.get("role", "user"),
        "created_by_admin": True
    }


def extract_scan_details(request, result, *args, **kwargs):
    """Extract details for scan operations"""
    data = request.get_json() or {}
    return {
        "target": data.get("target_url") or data.get("target"),
        "scan_type": data.get("scan_type"),
        "tools": data.get("tools", [])
    }


def extract_target_user_from_url(request, result, *args, **kwargs):
    """Extract target user ID from URL parameters"""
    return kwargs.get('user_id') or request.view_args.get('user_id')


def extract_resource_from_url(request, result, *args, **kwargs):
    """Extract resource information from URL"""
    resource_id = kwargs.get('scan_id') or kwargs.get('incident_id') or kwargs.get('ticket_id')
    
    if 'scan' in request.endpoint:
        resource_type = 'security_scan'
    elif 'incident' in request.endpoint:
        resource_type = 'incident'
    elif 'ticket' in request.endpoint:
        resource_type = 'ticket'
    else:
        resource_type = 'unknown'
    
    return resource_id, resource_type
