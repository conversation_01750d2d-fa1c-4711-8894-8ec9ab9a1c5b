"""
Routes pour le service de detection de phishing - PICA

Ce module configure les routes pour l'API de detection de phishing,
incluant l'analyse d'URLs, la gestion de l'historique et l'administration.

Auteur: PICA Team
Version: 1.0
"""

from flask import Blueprint
from app.controllers.phishing_controller import phishing_bp

# Creer le blueprint principal pour le phishing
phishing_routes_bp = Blueprint('phishing_routes', __name__)

# Enregistrer le blueprint phishing
phishing_routes_bp.register_blueprint(phishing_bp, url_prefix='/phishing')
