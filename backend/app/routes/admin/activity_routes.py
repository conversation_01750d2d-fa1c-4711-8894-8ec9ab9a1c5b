"""
Activity Log Routes for PICA Platform
Admin-only endpoints for accessing user activity logs
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required
from app.utils.decorators import admin_required, handle_options
from app.models.activity_log import ActivityLogManager
from app.utils.activity_logger import log_activity
from datetime import datetime, timedelta
from bson import ObjectId

activity_bp = Blueprint('activity', __name__, url_prefix='/admin/activity')


@activity_bp.route('/users/<user_id>/activities', methods=['GET', 'OPTIONS'])
@handle_options
@admin_required
def get_user_activities(user_id):
    """Get activity logs for a specific user"""
    try:
        # Get query parameters
        limit = int(request.args.get('limit', 50))
        offset = int(request.args.get('offset', 0))
        category = request.args.get('category')
        activity_type = request.args.get('activity_type')
        success_only = request.args.get('success_only')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # Parse dates if provided
        start_datetime = None
        end_datetime = None
        
        if start_date:
            try:
                start_datetime = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            except ValueError:
                return jsonify({"msg": "Invalid start_date format. Use ISO format."}), 400
        
        if end_date:
            try:
                end_datetime = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            except ValueError:
                return jsonify({"msg": "Invalid end_date format. Use ISO format."}), 400
        
        # Parse success_only
        success_filter = None
        if success_only is not None:
            success_filter = success_only.lower() == 'true'
        
        # Validate limit
        if limit > 1000:
            limit = 1000
        
        # Get activities
        result = ActivityLogManager.get_user_activities(
            user_id=user_id,
            limit=limit,
            offset=offset,
            category=category,
            activity_type=activity_type,
            start_date=start_datetime,
            end_date=end_datetime,
            success_only=success_filter
        )
        
        # Log this admin action
        log_activity(
            activity_type='USER_ACTIVITY_VIEWED',
            category='USER_MGMT',
            details={
                "target_user_id": user_id,
                "filters": {
                    "category": category,
                    "activity_type": activity_type,
                    "success_only": success_only,
                    "date_range": f"{start_date} to {end_date}" if start_date or end_date else None
                }
            },
            target_user_id=user_id
        )
        
        return jsonify(result), 200
        
    except Exception as e:
        return jsonify({"msg": "Failed to fetch user activities", "error": str(e)}), 500


@activity_bp.route('/activities', methods=['GET', 'OPTIONS'])
@handle_options
@admin_required
def get_all_activities():
    """Get all activity logs across all users"""
    try:
        # Get query parameters
        limit = int(request.args.get('limit', 50))
        offset = int(request.args.get('offset', 0))
        category = request.args.get('category')
        activity_type = request.args.get('activity_type')
        user_id = request.args.get('user_id')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # Parse dates if provided
        start_datetime = None
        end_datetime = None
        
        if start_date:
            try:
                start_datetime = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            except ValueError:
                return jsonify({"msg": "Invalid start_date format. Use ISO format."}), 400
        
        if end_date:
            try:
                end_datetime = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            except ValueError:
                return jsonify({"msg": "Invalid end_date format. Use ISO format."}), 400
        
        # Validate limit
        if limit > 1000:
            limit = 1000
        
        # Get activities
        result = ActivityLogManager.get_all_activities(
            limit=limit,
            offset=offset,
            category=category,
            activity_type=activity_type,
            start_date=start_datetime,
            end_date=end_datetime,
            user_id=user_id
        )
        
        # Log this admin action
        log_activity(
            activity_type='SYSTEM_ACTIVITY_VIEWED',
            category='USER_MGMT',
            details={
                "filters": {
                    "category": category,
                    "activity_type": activity_type,
                    "user_id": user_id,
                    "date_range": f"{start_date} to {end_date}" if start_date or end_date else None
                }
            }
        )
        
        return jsonify(result), 200
        
    except Exception as e:
        return jsonify({"msg": "Failed to fetch activities", "error": str(e)}), 500


@activity_bp.route('/statistics', methods=['GET', 'OPTIONS'])
@handle_options
@admin_required
def get_activity_statistics():
    """Get activity statistics"""
    try:
        user_id = request.args.get('user_id')
        
        result = ActivityLogManager.get_activity_statistics(user_id=user_id)
        
        # Log this admin action
        log_activity(
            activity_type='ACTIVITY_STATS_VIEWED',
            category='USER_MGMT',
            details={"target_user_id": user_id} if user_id else {}
        )
        
        return jsonify(result), 200
        
    except Exception as e:
        return jsonify({"msg": "Failed to fetch activity statistics", "error": str(e)}), 500


@activity_bp.route('/categories', methods=['GET', 'OPTIONS'])
@handle_options
@admin_required
def get_activity_categories():
    """Get available activity categories and types"""
    try:
        return jsonify({
            "categories": ActivityLogManager.CATEGORIES,
            "activity_types": ActivityLogManager.ACTIVITY_TYPES
        }), 200
        
    except Exception as e:
        return jsonify({"msg": "Failed to fetch activity categories", "error": str(e)}), 500


@activity_bp.route('/recent', methods=['GET', 'OPTIONS'])
@handle_options
@admin_required
def get_recent_activities():
    """Get recent activities across all users"""
    try:
        # Get activities from the last 24 hours
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(hours=24)
        
        limit = int(request.args.get('limit', 20))
        category = request.args.get('category')
        
        result = ActivityLogManager.get_all_activities(
            limit=limit,
            offset=0,
            category=category,
            start_date=start_date,
            end_date=end_date
        )
        
        return jsonify(result), 200
        
    except Exception as e:
        return jsonify({"msg": "Failed to fetch recent activities", "error": str(e)}), 500


@activity_bp.route('/export', methods=['POST', 'OPTIONS'])
@handle_options
@admin_required
def export_activities():
    """Export activity logs (for compliance and auditing)"""
    try:
        data = request.get_json() or {}
        
        # Get parameters
        user_id = data.get('user_id')
        category = data.get('category')
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        format_type = data.get('format', 'json')  # json, csv
        
        # Parse dates
        start_datetime = None
        end_datetime = None
        
        if start_date:
            start_datetime = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        
        if end_date:
            end_datetime = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
        
        # Get all matching activities (no limit for export)
        if user_id:
            result = ActivityLogManager.get_user_activities(
                user_id=user_id,
                limit=10000,  # Large limit for export
                category=category,
                start_date=start_datetime,
                end_date=end_datetime
            )
        else:
            result = ActivityLogManager.get_all_activities(
                limit=10000,  # Large limit for export
                category=category,
                start_date=start_datetime,
                end_date=end_datetime
            )
        
        # Log the export action
        log_activity(
            activity_type='REPORT_EXPORTED',
            category='EXPORT',
            details={
                "export_type": "activity_logs",
                "format": format_type,
                "filters": {
                    "user_id": user_id,
                    "category": category,
                    "date_range": f"{start_date} to {end_date}" if start_date or end_date else None
                },
                "record_count": len(result.get('activities', []))
            }
        )
        
        # Return the data (frontend will handle the actual file generation)
        return jsonify({
            "data": result.get('activities', []),
            "metadata": {
                "export_date": datetime.utcnow().isoformat(),
                "total_records": len(result.get('activities', [])),
                "filters": {
                    "user_id": user_id,
                    "category": category,
                    "start_date": start_date,
                    "end_date": end_date
                }
            }
        }), 200
        
    except Exception as e:
        return jsonify({"msg": "Failed to export activities", "error": str(e)}), 500


@activity_bp.route('/search', methods=['POST', 'OPTIONS'])
@handle_options
@admin_required
def search_activities():
    """Search activities with advanced filters"""
    try:
        data = request.get_json() or {}
        
        # Get search parameters
        query = data.get('query', '')
        user_id = data.get('user_id')
        category = data.get('category')
        activity_type = data.get('activity_type')
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        success_only = data.get('success_only')
        limit = data.get('limit', 50)
        offset = data.get('offset', 0)
        
        # Parse dates
        start_datetime = None
        end_datetime = None
        
        if start_date:
            start_datetime = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        
        if end_date:
            end_datetime = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
        
        # For now, use the existing get_all_activities method
        # In a more advanced implementation, you could add text search capabilities
        result = ActivityLogManager.get_all_activities(
            limit=limit,
            offset=offset,
            category=category,
            activity_type=activity_type,
            start_date=start_datetime,
            end_date=end_datetime,
            user_id=user_id
        )
        
        # If there's a text query, filter the results
        if query:
            filtered_activities = []
            query_lower = query.lower()
            
            for activity in result.get('activities', []):
                # Search in username, activity description, and details
                searchable_text = f"{activity.get('username', '')} {activity.get('activity_description', '')} {str(activity.get('details', {}))}"
                if query_lower in searchable_text.lower():
                    filtered_activities.append(activity)
            
            result['activities'] = filtered_activities
            result['total_count'] = len(filtered_activities)
        
        # Log the search action
        log_activity(
            activity_type='ACTIVITY_SEARCH_PERFORMED',
            category='USER_MGMT',
            details={
                "search_query": query,
                "filters": {
                    "user_id": user_id,
                    "category": category,
                    "activity_type": activity_type
                },
                "results_count": len(result.get('activities', []))
            }
        )
        
        return jsonify(result), 200
        
    except Exception as e:
        return jsonify({"msg": "Failed to search activities", "error": str(e)}), 500
