"""
Routes pour la gestion des vulnérabilités
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
from app.services.vulnerability_service import VulnerabilityService
from app.utils.decorators import handle_options, admin_required

vulnerability_bp = Blueprint('vulnerabilities', __name__)


@vulnerability_bp.route('/', methods=['GET', 'OPTIONS'])
@handle_options
@jwt_required()
def get_vulnerabilities():
    """
    Récupérer la liste des vulnérabilités avec filtres optionnels
    """
    try:
        # Récupérer les informations de l'utilisateur
        claims = get_jwt()
        current_user_id = get_jwt_identity()
        user_role = claims.get('role', 'user')
        
        # Paramètres de requête
        limit = int(request.args.get('limit', 50))
        severity = request.args.get('severity')
        tool = request.args.get('tool')
        status = request.args.get('status')
        category = request.args.get('category')
        target = request.args.get('target')
        cve = request.args.get('cve')
        
        # Construire les filtres
        filters = {}
        if severity:
            if ',' in severity:
                filters['severity'] = severity.split(',')
            else:
                filters['severity'] = severity
        
        if tool:
            if ',' in tool:
                filters['tool'] = tool.split(',')
            else:
                filters['tool'] = tool
        
        if status:
            filters['status'] = status
        
        if category:
            filters['category'] = category
        
        if target:
            filters['target'] = target
        
        if cve:
            filters['cve'] = cve
        
        # Filtrer par utilisateur si pas admin
        user_filter = None if user_role == 'admin' else current_user_id
        
        # Récupérer les vulnérabilités
        vulnerabilities = VulnerabilityService.get_vulnerabilities(
            limit=limit,
            user_id=user_filter,
            filters=filters
        )
        
        return jsonify({
            'vulnerabilities': vulnerabilities,
            'total': len(vulnerabilities),
            'filters_applied': filters,
            'user_role': user_role
        }), 200
        
    except Exception as e:
        print(f"❌ Error getting vulnerabilities: {e}")
        return jsonify({'error': str(e)}), 500


@vulnerability_bp.route('/<vulnerability_id>', methods=['GET', 'OPTIONS'])
@handle_options
@jwt_required()
def get_vulnerability(vulnerability_id):
    """
    Récupérer une vulnérabilité spécifique
    """
    try:
        # Récupérer les informations de l'utilisateur
        claims = get_jwt()
        current_user_id = get_jwt_identity()
        user_role = claims.get('role', 'user')
        
        # Filtrer par utilisateur si pas admin
        user_filter = None if user_role == 'admin' else current_user_id
        
        vulnerability = VulnerabilityService.get_vulnerability(
            vulnerability_id=vulnerability_id,
            user_id=user_filter
        )
        
        if not vulnerability:
            return jsonify({'error': 'Vulnerability not found or access denied'}), 404
        
        return jsonify(vulnerability), 200
        
    except Exception as e:
        print(f"❌ Error getting vulnerability {vulnerability_id}: {e}")
        return jsonify({'error': str(e)}), 500


@vulnerability_bp.route('/<vulnerability_id>/status', methods=['PUT', 'OPTIONS'])
@handle_options
@jwt_required()
def update_vulnerability_status(vulnerability_id):
    """
    Mettre à jour le statut d'une vulnérabilité
    """
    try:
        # Récupérer les informations de l'utilisateur
        claims = get_jwt()
        current_user_id = get_jwt_identity()
        user_role = claims.get('role', 'user')
        
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        status = data.get('status')
        notes = data.get('notes')
        
        if not status:
            return jsonify({'error': 'Status is required'}), 400
        
        # Filtrer par utilisateur si pas admin
        user_filter = None if user_role == 'admin' else current_user_id
        
        success = VulnerabilityService.update_status(
            vulnerability_id=vulnerability_id,
            status=status,
            notes=notes,
            user_id=user_filter
        )
        
        if not success:
            return jsonify({'error': 'Failed to update vulnerability status or access denied'}), 400
        
        return jsonify({
            'message': 'Vulnerability status updated successfully',
            'vulnerability_id': vulnerability_id,
            'new_status': status
        }), 200
        
    except Exception as e:
        print(f"❌ Error updating vulnerability status {vulnerability_id}: {e}")
        return jsonify({'error': str(e)}), 500


@vulnerability_bp.route('/statistics', methods=['GET', 'OPTIONS'])
@handle_options
@jwt_required()
def get_vulnerability_statistics():
    """
    Obtenir les statistiques des vulnérabilités
    """
    try:
        # Récupérer les informations de l'utilisateur
        claims = get_jwt()
        current_user_id = get_jwt_identity()
        user_role = claims.get('role', 'user')
        
        # Filtrer par utilisateur si pas admin
        user_filter = None if user_role == 'admin' else current_user_id
        
        statistics = VulnerabilityService.get_statistics(user_id=user_filter)
        summary = VulnerabilityService.get_vulnerability_summary(user_id=user_filter)
        
        return jsonify({
            'statistics': statistics,
            'summary': summary,
            'user_role': user_role
        }), 200
        
    except Exception as e:
        print(f"❌ Error getting vulnerability statistics: {e}")
        return jsonify({'error': str(e)}), 500


@vulnerability_bp.route('/scan/<scan_id>', methods=['GET', 'OPTIONS'])
@handle_options
@jwt_required()
def get_scan_vulnerabilities(scan_id):
    """
    Récupérer toutes les vulnérabilités d'un scan spécifique
    """
    try:
        # Récupérer les informations de l'utilisateur
        claims = get_jwt()
        current_user_id = get_jwt_identity()
        user_role = claims.get('role', 'user')
        
        # Filtrer par utilisateur si pas admin
        user_filter = None if user_role == 'admin' else current_user_id
        
        vulnerabilities = VulnerabilityService.get_scan_vulnerabilities(
            scan_id=scan_id,
            user_id=user_filter
        )
        
        return jsonify({
            'scan_id': scan_id,
            'vulnerabilities': vulnerabilities,
            'total': len(vulnerabilities)
        }), 200
        
    except Exception as e:
        print(f"❌ Error getting vulnerabilities for scan {scan_id}: {e}")
        return jsonify({'error': str(e)}), 500


@vulnerability_bp.route('/search', methods=['GET', 'OPTIONS'])
@handle_options
@jwt_required()
def search_vulnerabilities():
    """
    Rechercher des vulnérabilités
    """
    try:
        # Récupérer les informations de l'utilisateur
        claims = get_jwt()
        current_user_id = get_jwt_identity()
        user_role = claims.get('role', 'user')
        
        query = request.args.get('q', '').strip()
        limit = int(request.args.get('limit', 50))
        
        if not query:
            return jsonify({'error': 'Search query is required'}), 400
        
        # Filtrer par utilisateur si pas admin
        user_filter = None if user_role == 'admin' else current_user_id
        
        vulnerabilities = VulnerabilityService.search_vulnerabilities(
            query=query,
            user_id=user_filter,
            limit=limit
        )
        
        return jsonify({
            'query': query,
            'vulnerabilities': vulnerabilities,
            'total': len(vulnerabilities)
        }), 200
        
    except Exception as e:
        print(f"❌ Error searching vulnerabilities: {e}")
        return jsonify({'error': str(e)}), 500


@vulnerability_bp.route('/top', methods=['GET', 'OPTIONS'])
@handle_options
@jwt_required()
def get_top_vulnerabilities():
    """
    Obtenir les vulnérabilités les plus critiques
    """
    try:
        # Récupérer les informations de l'utilisateur
        claims = get_jwt()
        current_user_id = get_jwt_identity()
        user_role = claims.get('role', 'user')
        
        limit = int(request.args.get('limit', 10))
        
        # Filtrer par utilisateur si pas admin
        user_filter = None if user_role == 'admin' else current_user_id
        
        vulnerabilities = VulnerabilityService.get_top_vulnerabilities(
            user_id=user_filter,
            limit=limit
        )
        
        return jsonify({
            'top_vulnerabilities': vulnerabilities,
            'total': len(vulnerabilities),
            'criteria': 'Critical and High severity, unresolved'
        }), 200
        
    except Exception as e:
        print(f"❌ Error getting top vulnerabilities: {e}")
        return jsonify({'error': str(e)}), 500


@vulnerability_bp.route('/<vulnerability_id>', methods=['DELETE', 'OPTIONS'])
@handle_options
@jwt_required()
@admin_required
def delete_vulnerability(vulnerability_id):
    """
    Supprimer une vulnérabilité (admin seulement)
    """
    try:
        success = VulnerabilityService.delete_vulnerability(vulnerability_id)
        
        if not success:
            return jsonify({'error': 'Failed to delete vulnerability or vulnerability not found'}), 400
        
        return jsonify({
            'message': 'Vulnerability deleted successfully',
            'vulnerability_id': vulnerability_id
        }), 200
        
    except Exception as e:
        print(f"❌ Error deleting vulnerability {vulnerability_id}: {e}")
        return jsonify({'error': str(e)}), 500
