"""
Service pour générer des rapports PDF des analyses de malware
"""
import os
import json
from datetime import datetime
from io import BytesIO
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.colors import HexColor, black, white, red, orange, yellow, green
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from reportlab.pdfgen import canvas
from reportlab.lib import colors


class MalwarePDFService:
    """Service pour générer des rapports PDF des analyses de malware"""
    
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
    
    def _setup_custom_styles(self):
        """Configuration des styles personnalisés pour malware"""
        
        # Style pour le titre principal
        self.styles.add(ParagraphStyle(
            name='MalwareTitle',
            parent=self.styles['Title'],
            fontSize=32,
            spaceAfter=40,
            spaceBefore=20,
            textColor=HexColor('#dc2626'),
            alignment=TA_CENTER,
            fontName='Helvetica-Bold'
        ))
        
        # Style pour le sous-titre
        self.styles.add(ParagraphStyle(
            name='MalwareSubtitle',
            parent=self.styles['Normal'],
            fontSize=16,
            spaceAfter=30,
            textColor=HexColor('#64748b'),
            alignment=TA_CENTER,
            fontName='Helvetica'
        ))
        
        # Style pour les titres de section
        self.styles.add(ParagraphStyle(
            name='MalwareHeading1',
            parent=self.styles['Heading1'],
            fontSize=20,
            spaceAfter=15,
            spaceBefore=25,
            textColor=white,
            backColor=HexColor('#dc2626'),
            borderWidth=0,
            borderPadding=12,
            fontName='Helvetica-Bold',
            leftIndent=0,
            rightIndent=0
        ))
        
        # Style pour les sous-sections
        self.styles.add(ParagraphStyle(
            name='MalwareHeading2',
            parent=self.styles['Heading2'],
            fontSize=16,
            spaceAfter=10,
            spaceBefore=15,
            textColor=HexColor('#1e293b'),
            fontName='Helvetica-Bold',
            borderWidth=0,
            borderColor=HexColor('#e2e8f0'),
            borderPadding=8,
            backColor=HexColor('#f8fafc')
        ))
        
        # Style pour le contenu normal
        self.styles.add(ParagraphStyle(
            name='MalwareNormal',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=8,
            textColor=HexColor('#334155'),
            fontName='Helvetica',
            leading=14
        ))
        
        # Styles pour les alertes de malware
        self.styles.add(ParagraphStyle(
            name='ThreatCritical',
            parent=self.styles['Normal'],
            fontSize=12,
            spaceAfter=12,
            textColor=HexColor('#991b1b'),
            backColor=HexColor('#fef2f2'),
            borderWidth=2,
            borderColor=HexColor('#dc2626'),
            borderPadding=12,
            fontName='Helvetica-Bold'
        ))
        
        self.styles.add(ParagraphStyle(
            name='ThreatHigh',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=10,
            textColor=HexColor('#dc2626'),
            backColor=HexColor('#fef2f2'),
            borderWidth=1,
            borderColor=HexColor('#fca5a5'),
            borderPadding=10,
            fontName='Helvetica'
        ))
        
        self.styles.add(ParagraphStyle(
            name='ThreatMedium',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=10,
            textColor=HexColor('#d97706'),
            backColor=HexColor('#fffbeb'),
            borderWidth=1,
            borderColor=HexColor('#fcd34d'),
            borderPadding=10,
            fontName='Helvetica'
        ))
        
        self.styles.add(ParagraphStyle(
            name='ThreatLow',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=10,
            textColor=HexColor('#059669'),
            backColor=HexColor('#f0fdf4'),
            borderWidth=1,
            borderColor=HexColor('#86efac'),
            borderPadding=10,
            fontName='Helvetica'
        ))
        
        # Style pour les codes et hashes
        self.styles.add(ParagraphStyle(
            name='CodeBlock',
            parent=self.styles['Normal'],
            fontSize=9,
            spaceAfter=8,
            textColor=HexColor('#374151'),
            backColor=HexColor('#f9fafb'),
            borderWidth=1,
            borderColor=HexColor('#d1d5db'),
            borderPadding=8,
            fontName='Courier',
            leading=11
        ))

    def generate_malware_report(self, analysis_data):
        """
        Génère un rapport PDF pour une analyse de malware
        
        Args:
            analysis_data: Données de l'analyse depuis MongoDB
            
        Returns:
            BytesIO: Buffer contenant le PDF généré
        """
        buffer = BytesIO()
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        
        # Construire le contenu du rapport
        story = []
        
        # Page de titre
        story.extend(self._build_title_page(analysis_data))
        story.append(PageBreak())
        
        # Résumé exécutif
        story.extend(self._build_executive_summary(analysis_data))
        story.append(PageBreak())
        
        # Informations du fichier
        story.extend(self._build_file_info(analysis_data))
        
        # Détections et menaces
        if analysis_data.get('detection_details'):
            story.append(PageBreak())
            story.extend(self._build_detections_section(analysis_data))
        
        # Analyse technique
        if analysis_data.get('analysis_details'):
            story.append(PageBreak())
            story.extend(self._build_technical_analysis(analysis_data))
        
        # IOCs (Indicators of Compromise)
        if analysis_data.get('iocs'):
            story.append(PageBreak())
            story.extend(self._build_iocs_section(analysis_data))
        
        # Recommandations
        story.append(PageBreak())
        story.extend(self._build_recommendations(analysis_data))
        
        # Construire le PDF
        doc.build(story)
        buffer.seek(0)
        return buffer

    def _build_title_page(self, analysis_data):
        """Construit une page de titre pour l'analyse de malware"""
        story = []
        
        # Espacement initial
        story.append(Spacer(1, 80))
        
        # Logo et titre principal
        story.append(Paragraph("🛡️ PICA", self.styles['MalwareTitle']))
        story.append(Paragraph("Rapport d'Analyse de Malware", self.styles['MalwareSubtitle']))
        story.append(Spacer(1, 60))
        
        # Titre du rapport avec niveau de menace
        threat_level = analysis_data.get('threat_level', 'UNKNOWN').upper()
        threat_icons = {
            'CRITICAL': '🔴',
            'HIGH': '🟠', 
            'MEDIUM': '🟡',
            'LOW': '🟢',
            'CLEAN': '✅'
        }
        
        icon = threat_icons.get(threat_level, '⚠️')
        story.append(Paragraph(f"{icon} Analyse de Fichier Suspect", self.styles['MalwareHeading1']))
        story.append(Paragraph(f"Niveau de Menace: {threat_level}", self.styles['MalwareHeading2']))
        story.append(Spacer(1, 40))
        
        # Informations de base
        file_info = analysis_data.get('file_info', {})
        filename = file_info.get('filename', 'Fichier inconnu')
        
        basic_info = [
            ['📁 Fichier analysé', filename],
            ['🔍 Type d\'analyse', 'Analyse de malware complète'],
            ['⚡ Niveau de menace', threat_level],
            ['📊 Score de confiance', f"{analysis_data.get('confidence_pct', 0)}%"],
            ['📅 Date d\'analyse', self._format_datetime(analysis_data.get('created_at'))],
            ['🆔 ID d\'analyse', analysis_data.get('analysis_id', 'N/A')],
        ]
        
        table = Table(basic_info, colWidths=[2.2*inch, 3.8*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), HexColor('#dc2626')),
            ('TEXTCOLOR', (0, 0), (0, -1), white),
            ('BACKGROUND', (1, 0), (1, -1), HexColor('#f8fafc')),
            ('TEXTCOLOR', (1, 0), (1, -1), HexColor('#1e293b')),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#e2e8f0')),
            ('LINEWIDTH', (0, 0), (-1, -1), 0.5),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 12),
            ('RIGHTPADDING', (0, 0), (-1, -1), 12),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))
        
        story.append(table)
        story.append(Spacer(1, 40))
        
        # Avertissement de sécurité
        if threat_level in ['CRITICAL', 'HIGH']:
            story.append(Paragraph(
                "⚠️ <b>AVERTISSEMENT DE SÉCURITÉ</b><br/>"
                "Ce fichier présente des caractéristiques suspectes. "
                "Ne pas exécuter ou ouvrir sans précautions appropriées. "
                "Consultez les recommandations de sécurité dans ce rapport.",
                self.styles['ThreatCritical']
            ))
        
        return story

    def _format_datetime(self, dt_str):
        """Formate une chaîne datetime"""
        if not dt_str:
            return 'N/A'
        try:
            if isinstance(dt_str, str):
                dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
            else:
                dt = dt_str
            return dt.strftime('%d/%m/%Y %H:%M:%S')
        except:
            return str(dt_str)

    def _build_executive_summary(self, analysis_data):
        """Construit le résumé exécutif"""
        story = []

        story.append(Paragraph("📊 Résumé Exécutif", self.styles['MalwareHeading1']))
        story.append(Spacer(1, 20))

        # Évaluation globale
        threat_level = analysis_data.get('threat_level', 'UNKNOWN').upper()
        confidence = analysis_data.get('confidence_pct', 0)

        # Déterminer le style basé sur le niveau de menace
        threat_style_map = {
            'CRITICAL': 'ThreatCritical',
            'HIGH': 'ThreatHigh',
            'MEDIUM': 'ThreatMedium',
            'LOW': 'ThreatLow',
            'CLEAN': 'ThreatLow'
        }

        threat_style = self.styles.get(threat_style_map.get(threat_level, 'ThreatMedium'))

        # Résumé principal
        summary_text = self._get_threat_summary(threat_level, confidence)
        story.append(Paragraph(
            f"<b>🎯 Évaluation: {threat_level}</b><br/>"
            f"📊 Confiance: {confidence}%<br/><br/>"
            f"{summary_text}",
            threat_style
        ))

        story.append(Spacer(1, 20))

        # Statistiques détaillées
        detection_count = len(analysis_data.get('detection_details', []))
        iocs_count = self._count_iocs(analysis_data.get('iocs', {}))

        stats_data = [
            ['📊 Métrique', '🔢 Valeur', '📝 Description'],
            ['🔍 Détections', str(detection_count), 'Éléments suspects identifiés'],
            ['🚨 IOCs', str(iocs_count), 'Indicateurs de compromission'],
            ['📊 Confiance', f"{confidence}%", 'Niveau de certitude de l\'analyse'],
            ['⚡ Menace', threat_level, 'Niveau de risque évalué']
        ]

        stats_table = Table(stats_data, colWidths=[2*inch, 1.5*inch, 2.5*inch])
        stats_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor('#dc2626')),
            ('TEXTCOLOR', (0, 0), (-1, 0), white),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('ALIGN', (1, 1), (1, -1), 'CENTER'),
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#e2e8f0')),
            ('LINEWIDTH', (0, 0), (-1, -1), 0.5),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 10),
            ('RIGHTPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [HexColor('#f8fafc'), white]),
        ]))

        story.append(stats_table)

        return story

    def _build_file_info(self, analysis_data):
        """Construit la section d'informations du fichier"""
        story = []

        story.append(Paragraph("📁 Informations du Fichier", self.styles['MalwareHeading1']))
        story.append(Spacer(1, 20))

        file_info = analysis_data.get('file_info', {})
        hashes = file_info.get('hashes', {})

        # Informations de base du fichier
        file_data = [
            ['📝 Propriété', '📊 Valeur'],
            ['📁 Nom du fichier', file_info.get('filename', 'N/A')],
            ['📋 Type de fichier', file_info.get('file_type', 'N/A')],
            ['📏 Taille', self._format_file_size(file_info.get('size', 0))],
        ]

        # Ajouter les hashes si disponibles
        if hashes.get('md5'):
            file_data.append(['🔐 MD5', hashes.get('md5')])
        if hashes.get('sha1'):
            file_data.append(['🔐 SHA1', hashes.get('sha1')])
        if hashes.get('sha256'):
            file_data.append(['🔐 SHA256', hashes.get('sha256')])

        file_table = Table(file_data, colWidths=[2*inch, 4*inch])
        file_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor('#f3f4f6')),
            ('TEXTCOLOR', (0, 0), (-1, 0), HexColor('#374151')),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (1, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#e5e7eb')),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 10),
            ('RIGHTPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))

        story.append(file_table)

        return story

    def _get_threat_summary(self, threat_level, confidence):
        """Génère un résumé basé sur le niveau de menace"""
        if threat_level == 'CRITICAL':
            return "DANGER CRITIQUE - Ce fichier présente des caractéristiques de malware avancé. Isolation immédiate recommandée."
        elif threat_level == 'HIGH':
            return "RISQUE ÉLEVÉ - Plusieurs indicateurs de malware détectés. Analyse approfondie et quarantaine recommandées."
        elif threat_level == 'MEDIUM':
            return "RISQUE MODÉRÉ - Quelques caractéristiques suspectes identifiées. Surveillance et vérifications supplémentaires conseillées."
        elif threat_level == 'LOW':
            return "RISQUE FAIBLE - Peu d'indicateurs suspects. Le fichier semble relativement sûr mais reste à surveiller."
        elif threat_level == 'CLEAN':
            return "FICHIER PROPRE - Aucun indicateur de malware détecté. Le fichier semble légitime."
        else:
            return "STATUT INCONNU - L'analyse n'a pas pu déterminer le niveau de risque avec certitude."

    def _count_iocs(self, iocs):
        """Compte le nombre total d'IOCs"""
        total = 0
        if isinstance(iocs, dict):
            total += len(iocs.get('ips', []))
            total += len(iocs.get('domains', []))
            total += len(iocs.get('urls', []))
            total += len(iocs.get('emails', []))
        return total

    def _format_file_size(self, size_bytes):
        """Formate la taille du fichier"""
        if not size_bytes or size_bytes == 0:
            return 'N/A'

        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"

    def _build_detections_section(self, analysis_data):
        """Construit la section des détections"""
        story = []

        story.append(Paragraph("🔍 Détections et Menaces", self.styles['MalwareHeading1']))
        story.append(Spacer(1, 20))

        detection_details = analysis_data.get('detection_details', [])

        if not detection_details:
            story.append(Paragraph(
                "✅ Aucune détection spécifique trouvée lors de l'analyse.",
                self.styles['ThreatLow']
            ))
            return story

        story.append(Paragraph(f"🚨 {len(detection_details)} détection(s) identifiée(s)", self.styles['MalwareHeading2']))
        story.append(Spacer(1, 10))

        # Afficher chaque détection
        for i, detection in enumerate(detection_details, 1):
            detection_text = str(detection)

            # Déterminer la sévérité basée sur le contenu
            severity_style = self._get_detection_severity_style(detection_text)

            story.append(Paragraph(
                f"<b>{i}. Détection:</b><br/>{detection_text}",
                severity_style
            ))
            story.append(Spacer(1, 8))

        return story

    def _build_technical_analysis(self, analysis_data):
        """Construit la section d'analyse technique"""
        story = []

        story.append(Paragraph("🔬 Analyse Technique", self.styles['MalwareHeading1']))
        story.append(Spacer(1, 20))

        analysis_details = analysis_data.get('analysis_details', {})

        # Chaînes suspectes
        suspicious_strings = analysis_details.get('suspicious_strings', [])
        if suspicious_strings:
            story.append(Paragraph("🔍 Chaînes Suspectes", self.styles['MalwareHeading2']))
            story.append(Spacer(1, 10))

            strings_data = [['Type', 'Chaîne', 'Contexte']]
            for item in suspicious_strings[:10]:  # Limiter à 10 pour éviter un PDF trop long
                string_type = item.get('type', 'Unknown') if isinstance(item, dict) else 'String'
                string_value = item.get('string', str(item)) if isinstance(item, dict) else str(item)
                context = item.get('context', '') if isinstance(item, dict) else ''

                # Tronquer les chaînes trop longues
                if len(string_value) > 50:
                    string_value = string_value[:47] + "..."

                strings_data.append([string_type, string_value, context])

            strings_table = Table(strings_data, colWidths=[1.5*inch, 3*inch, 1.5*inch])
            strings_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), HexColor('#dc2626')),
                ('TEXTCOLOR', (0, 0), (-1, 0), white),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('GRID', (0, 0), (-1, -1), 1, HexColor('#e5e7eb')),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 8),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [HexColor('#fef2f2'), white]),
            ]))

            story.append(strings_table)

            if len(suspicious_strings) > 10:
                story.append(Spacer(1, 5))
                story.append(Paragraph(
                    f"... et {len(suspicious_strings) - 10} autres chaînes suspectes",
                    self.styles['MalwareNormal']
                ))

            story.append(Spacer(1, 15))

        # Analyse de packing
        packing_analysis = analysis_details.get('packing_analysis', {})
        if packing_analysis:
            story.append(Paragraph("📦 Analyse de Packing", self.styles['MalwareHeading2']))
            story.append(Spacer(1, 10))

            packing_status = packing_analysis.get('status', 'UNKNOWN')
            is_packed = packing_analysis.get('is_packed', False)

            if is_packed:
                story.append(Paragraph(
                    f"⚠️ <b>Packing détecté:</b> {packing_status}<br/>"
                    f"Le fichier semble être compressé ou obfusqué, ce qui peut indiquer une tentative de dissimulation.",
                    self.styles['ThreatHigh']
                ))
            else:
                story.append(Paragraph(
                    f"✅ <b>Aucun packing détecté:</b> {packing_status}",
                    self.styles['ThreatLow']
                ))

            story.append(Spacer(1, 15))

        return story

    def _build_iocs_section(self, analysis_data):
        """Construit la section des IOCs"""
        story = []

        story.append(Paragraph("🚨 Indicateurs de Compromission (IOCs)", self.styles['MalwareHeading1']))
        story.append(Spacer(1, 20))

        iocs = analysis_data.get('iocs', {})

        if not any(iocs.values()):
            story.append(Paragraph(
                "✅ Aucun indicateur de compromission détecté.",
                self.styles['ThreatLow']
            ))
            return story

        # IPs suspectes
        ips = iocs.get('ips', [])
        if ips:
            story.append(Paragraph("🌐 Adresses IP Suspectes", self.styles['MalwareHeading2']))
            story.append(Spacer(1, 10))

            for ip in ips[:10]:  # Limiter à 10
                story.append(Paragraph(f"• {ip}", self.styles['CodeBlock']))

            if len(ips) > 10:
                story.append(Paragraph(f"... et {len(ips) - 10} autres IPs", self.styles['MalwareNormal']))

            story.append(Spacer(1, 15))

        # Domaines suspects
        domains = iocs.get('domains', [])
        if domains:
            story.append(Paragraph("🌍 Domaines Suspects", self.styles['MalwareHeading2']))
            story.append(Spacer(1, 10))

            for domain in domains[:10]:
                story.append(Paragraph(f"• {domain}", self.styles['CodeBlock']))

            if len(domains) > 10:
                story.append(Paragraph(f"... et {len(domains) - 10} autres domaines", self.styles['MalwareNormal']))

            story.append(Spacer(1, 15))

        # URLs suspectes
        urls = iocs.get('urls', [])
        if urls:
            story.append(Paragraph("🔗 URLs Suspectes", self.styles['MalwareHeading2']))
            story.append(Spacer(1, 10))

            for url in urls[:5]:  # Limiter à 5 car les URLs sont plus longues
                # Tronquer les URLs très longues
                display_url = url if len(url) <= 80 else url[:77] + "..."
                story.append(Paragraph(f"• {display_url}", self.styles['CodeBlock']))

            if len(urls) > 5:
                story.append(Paragraph(f"... et {len(urls) - 5} autres URLs", self.styles['MalwareNormal']))

            story.append(Spacer(1, 15))

        return story

    def _build_recommendations(self, analysis_data):
        """Construit la section des recommandations"""
        story = []

        story.append(Paragraph("💡 Recommandations de Sécurité", self.styles['MalwareHeading1']))
        story.append(Spacer(1, 20))

        threat_level = analysis_data.get('threat_level', 'UNKNOWN').upper()

        # Recommandations basées sur le niveau de menace
        recommendations = self._get_threat_recommendations(threat_level)

        # Recommandations spécifiques du système
        system_recommendations = analysis_data.get('recommendations', [])

        # Afficher les recommandations générales
        story.append(Paragraph("🎯 Actions Recommandées", self.styles['MalwareHeading2']))
        story.append(Spacer(1, 10))

        for i, rec in enumerate(recommendations, 1):
            story.append(Paragraph(f"{i}. {rec}", self.styles['MalwareNormal']))
            story.append(Spacer(1, 5))

        # Afficher les recommandations spécifiques si disponibles
        if system_recommendations:
            story.append(Spacer(1, 15))
            story.append(Paragraph("🔧 Recommandations Techniques", self.styles['MalwareHeading2']))
            story.append(Spacer(1, 10))

            for i, rec in enumerate(system_recommendations, 1):
                story.append(Paragraph(f"{i}. {rec}", self.styles['MalwareNormal']))
                story.append(Spacer(1, 5))

        return story

    def _get_detection_severity_style(self, detection_text):
        """Détermine le style basé sur le contenu de la détection"""
        detection_lower = detection_text.lower()

        if any(keyword in detection_lower for keyword in ['critical', 'malware', 'virus', 'trojan']):
            return self.styles['ThreatCritical']
        elif any(keyword in detection_lower for keyword in ['suspicious', 'warning', 'potential']):
            return self.styles['ThreatHigh']
        elif any(keyword in detection_lower for keyword in ['medium', 'moderate']):
            return self.styles['ThreatMedium']
        else:
            return self.styles['ThreatLow']

    def _get_threat_recommendations(self, threat_level):
        """Génère des recommandations basées sur le niveau de menace"""
        base_recommendations = [
            "Maintenir les systèmes antivirus à jour",
            "Effectuer des sauvegardes régulières des données importantes",
            "Sensibiliser les utilisateurs aux bonnes pratiques de sécurité",
            "Surveiller les activités réseau suspectes"
        ]

        if threat_level == 'CRITICAL':
            return [
                "🚨 ISOLER IMMÉDIATEMENT le fichier et le système infecté",
                "🔒 QUARANTAINE complète du fichier et analyse approfondie",
                "🛡️ SCANNER tous les systèmes connectés",
                "📞 ALERTER l'équipe de sécurité informatique",
                "🔄 RESTAURER depuis une sauvegarde propre si nécessaire"
            ] + base_recommendations
        elif threat_level == 'HIGH':
            return [
                "⚠️ Placer le fichier en quarantaine",
                "🔍 Effectuer une analyse approfondie du système",
                "🛡️ Renforcer la surveillance du réseau",
                "📋 Documenter l'incident pour analyse"
            ] + base_recommendations
        elif threat_level == 'MEDIUM':
            return [
                "🔍 Surveiller le fichier et son comportement",
                "📊 Effectuer des analyses complémentaires",
                "🛡️ Renforcer les mesures de sécurité préventives"
            ] + base_recommendations
        else:
            return [
                "✅ Continuer la surveillance normale",
                "📊 Conserver les logs pour référence future"
            ] + base_recommendations
