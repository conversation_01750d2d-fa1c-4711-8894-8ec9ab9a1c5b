"""
Service pour la gestion des templates d'email avec design PICA
"""
import threading
from flask import current_app, render_template
from flask_mail import Message
from ..extensions import mail
from datetime import datetime


class EmailTemplateService:
    """Service pour envoyer des emails avec templates HTML stylés"""
    
    @staticmethod
    def send_email_async(app, msg):
        """Send email asynchronously in a separate thread"""
        with app.app_context():
            try:
                mail.send(msg)
                print(f"✅ Email sent successfully to {msg.recipients}")
            except Exception as e:
                print(f"❌ Error sending email: {e}")

    @staticmethod
    def send_templated_email(template_name, subject, recipients, template_data=None, sender=None):
        """
        Send email using HTML template
        
        Args:
            template_name (str): Name of the template file (without .html)
            subject (str): Email subject
            recipients (list): List of recipient email addresses
            template_data (dict): Data to pass to the template
            sender (str, optional): Sender email address
        """
        if sender is None:
            sender = "<EMAIL>"
        
        if template_data is None:
            template_data = {}
        
        # Add common template data
        template_data.update({
            'subject': subject,
            'current_year': datetime.now().year,
            'platform_url': 'http://localhost:5173'
        })
        
        try:
            # Render HTML template
            html_body = render_template(f"{template_name}.html", **template_data)
            
            # Create message with HTML content
            msg = Message(
                subject=subject,
                sender=sender,
                recipients=recipients,
                html=html_body
            )
            
            # Get current app context
            app = current_app._get_current_object()
            
            # Send email in a separate thread
            thread = threading.Thread(
                target=EmailTemplateService.send_email_async,
                args=(app, msg)
            )
            thread.daemon = True
            thread.start()
            
            print(f"📧 Templated email queued for {recipients}: {subject}")
            return True
            
        except Exception as e:
            print(f"❌ Error preparing templated email: {e}")
            return False

    @staticmethod
    def send_confirmation_email(email, first_name, confirm_url):
        """Send email confirmation with beautiful template"""
        return EmailTemplateService.send_templated_email(
            template_name="email_confirmation",
            subject="PICA - Confirm your email address",
            recipients=[email],
            template_data={
                'first_name': first_name,
                'confirm_url': confirm_url
            }
        )

    @staticmethod
    def send_password_reset_email(email, reset_link):
        """Send password reset email with beautiful template"""
        return EmailTemplateService.send_templated_email(
            template_name="password_reset",
            subject="PICA - Password Reset Request",
            recipients=[email],
            template_data={
                'reset_link': reset_link
            }
        )

    @staticmethod
    def send_otp_email(email, otp):
        """Send OTP verification email with beautiful template"""
        return EmailTemplateService.send_templated_email(
            template_name="otp_verification",
            subject="PICA - Login Verification Code",
            recipients=[email],
            template_data={
                'otp': otp
            }
        )

    @staticmethod
    def send_incident_notification_email(email, notification_type, item_type, action, data):
        """Send incident notification email with beautiful template"""

        # Prepare template data from incident/ticket data
        template_data = {
            'notification_type': notification_type,
            'item_type': item_type,
            'action': action,
            'platform_url': 'http://localhost:5173/incidents'
        }

        # Add all data fields to template
        if isinstance(data, dict):
            template_data.update(data)

            # Format created date if present
            if 'created_at' in data:
                try:
                    if isinstance(data['created_at'], str):
                        template_data['created_date'] = data['created_at']
                    else:
                        template_data['created_date'] = data['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                except:
                    template_data['created_date'] = str(data.get('created_at', 'N/A'))

        return EmailTemplateService.send_templated_email(
            template_name="incident_notification",
            subject=f"PICA - {notification_type}",
            recipients=[email],
            template_data=template_data
        )

    @staticmethod
    def send_2fa_code_email(email, user_name, verification_code, login_details=None):
        """Send 2FA verification code email with beautiful template"""

        return EmailTemplateService.send_templated_email(
            template_name="2fa_code",
            subject="🔐 PICA - Two-Factor Authentication Code",
            recipients=[email],
            template_data={
                'user_name': user_name,
                'verification_code': verification_code,
                'login_details': login_details or {},
                'platform_url': 'http://localhost:5173'
            }
        )

    @staticmethod
    def send_user_notification_email(email, user_name, notification_type, title, message, data=None):
        """Send user notification email with beautiful template"""

        # Map notification types to user-friendly subjects
        subject_map = {
            'security_alerts': '🛡️ Security Alert',
            'login_alerts': '🔑 Login Alert',
            'profile_changes': '👤 Profile Updated',
            'analysis_completed': '📊 Analysis Complete',
            'system_updates': '🔄 System Update'
        }

        subject = f"PICA - {subject_map.get(notification_type, '🔔 Notification')}"

        # Prepare template data
        template_data = {
            'user_name': user_name,
            'notification_type': notification_type,
            'title': title,
            'message': message,
            'data': data or {},
            'platform_url': 'http://localhost:5173'
        }

        # Format timestamp if present in data
        if data and 'timestamp' in data:
            try:
                from datetime import datetime
                if isinstance(data['timestamp'], str):
                    # Try to parse ISO format
                    dt = datetime.fromisoformat(data['timestamp'].replace('Z', '+00:00'))
                    template_data['data']['timestamp'] = dt.strftime('%Y-%m-%d %H:%M:%S UTC')
            except Exception:
                # Keep original if parsing fails
                pass

        return EmailTemplateService.send_templated_email(
            template_name="user_notification",
            subject=subject,
            recipients=[email],
            template_data=template_data
        )


# Convenience functions for backward compatibility
def send_confirmation_email(email, first_name, confirm_url):
    """Backward compatibility function"""
    return EmailTemplateService.send_confirmation_email(email, first_name, confirm_url)

def send_password_reset_email(email, reset_link):
    """Backward compatibility function"""
    return EmailTemplateService.send_password_reset_email(email, reset_link)

def send_otp_email(email, otp):
    """Backward compatibility function"""
    return EmailTemplateService.send_otp_email(email, otp)

def send_incident_notification_email(email, notification_type, item_type, action, data):
    """Backward compatibility function"""
    return EmailTemplateService.send_incident_notification_email(
        email, notification_type, item_type, action, data
    )

def send_user_notification_email(email, user_name, notification_type, title, message, data=None):
    """Backward compatibility function"""
    return EmailTemplateService.send_user_notification_email(
        email, user_name, notification_type, title, message, data
    )

def send_2fa_code_email(email, user_name, verification_code, login_details=None):
    """Backward compatibility function"""
    return EmailTemplateService.send_2fa_code_email(
        email, user_name, verification_code, login_details
    )
