import subprocess
import json
import xml.etree.ElementTree as ET
from datetime import datetime
import uuid
import os
import tempfile
from typing import Dict, List, Optional

class NiktoService:
    """Service pour l'intégration avec Nikto Web Scanner"""
    
    def __init__(self):
        self.nikto_path = self._find_nikto_path()
        self.temp_dir = tempfile.gettempdir()
    
    def _find_nikto_path(self) -> str:
        """Trouver le chemin vers Nikto"""
        possible_paths = [
            '/usr/bin/nikto',
            '/usr/local/bin/nikto',
            '/opt/nikto/program/nikto.pl',
            'nikto'
        ]
        
        for path in possible_paths:
            try:
                result = subprocess.run([path, '-Version'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    return path
            except (subprocess.TimeoutExpired, FileNotFoundError):
                continue
        
        return 'nikto'  # Fallback
    
    def is_available(self) -> bool:
        """Vérifier si Nikto est disponible"""
        try:
            result = subprocess.run([self.nikto_path, '-Version'], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    def scan_target(self, target: str, options: Dict = None, logger=None) -> Dict:
        """
        Lancer un scan Nikto sur une cible
        
        Args:
            target: URL ou IP à scanner
            options: Options de scan personnalisées
            
        Returns:
            Dictionnaire avec les résultats du scan
        """
        scan_id = str(uuid.uuid4())
        output_file = os.path.join(self.temp_dir, f"nikto_scan_{scan_id}.xml")
        
        # Construire la commande Nikto
        cmd = [
            self.nikto_path,
            '-h', target,
            '-Format', 'xml',
            '-output', output_file,
            '-ask', 'no',  # Ne pas demander de confirmation
            '-Cgidirs', 'all',  # Scanner tous les répertoires CGI
            '-maxtime', '3600'  # Timeout de 1 heure
        ]
        
        # Ajouter des options personnalisées
        if options:
            if options.get('ssl'):
                cmd.extend(['-ssl'])
            if options.get('port'):
                cmd.extend(['-port', str(options['port'])])
            if options.get('plugins'):
                cmd.extend(['-Plugins', options['plugins']])
            if options.get('timeout'):
                cmd.extend(['-timeout', str(options['timeout'])])
        
        try:
            # Exécuter Nikto avec logging
            start_time = datetime.utcnow()
            print(f"🔧 Executing Nikto command: {' '.join(cmd)}")

            # Exécuter avec Popen pour avoir un contrôle en temps réel
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

            if logger:
                logger.log_tool_progress('nikto', 60, 'Nikto process started, scanning in progress...')

            # Attendre avec des vérifications périodiques
            import time
            elapsed = 0
            max_time = 600  # 10 minutes max
            check_interval = 30  # Vérifier toutes les 30 secondes

            while process.poll() is None and elapsed < max_time:
                time.sleep(check_interval)
                elapsed += check_interval

                if logger:
                    progress = min(60 + (elapsed / max_time) * 30, 90)  # 60% à 90%
                    logger.log_tool_progress('nikto', int(progress), f'Nikto still running... ({elapsed}s elapsed)')

                print(f"⏳ Nikto still running... {elapsed}s elapsed")

            # Récupérer les résultats
            if process.poll() is None:
                # Timeout
                if logger:
                    logger.log_tool_error('nikto', f'Timeout after {max_time} seconds', 'timeout')

                process.terminate()
                try:
                    stdout, stderr = process.communicate(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                    stdout, stderr = process.communicate()

                return {
                    'scan_id': scan_id,
                    'target': target,
                    'status': 'timeout',
                    'error': f'Scan timeout after {max_time} seconds',
                    'start_time': start_time.isoformat()
                }
            else:
                # Processus terminé normalement
                stdout, stderr = process.communicate()
                end_time = datetime.utcnow()

                print(f"✅ Nikto execution completed. Return code: {process.returncode}")
                print(f"📊 Nikto stdout length: {len(stdout)} chars")
                print(f"📊 Nikto stderr length: {len(stderr)} chars")

                # Parser les résultats
                vulnerabilities = self._parse_nikto_output(output_file)
                print(f"🔍 Nikto found {len(vulnerabilities)} vulnerabilities")

                # Nettoyer le fichier temporaire
                if os.path.exists(output_file):
                    os.remove(output_file)

                return {
                    'scan_id': scan_id,
                    'target': target,
                    'status': 'completed' if process.returncode == 0 else 'failed',
                    'start_time': start_time.isoformat(),
                    'end_time': end_time.isoformat(),
                    'duration': (end_time - start_time).total_seconds(),
                    'vulnerabilities': vulnerabilities,
                    'total_vulnerabilities': len(vulnerabilities),
                    'nikto_version': self._get_nikto_version(),
                    'command_used': ' '.join(cmd),
                    'raw_output': stdout,
                    'errors': stderr if stderr else None
                }
            
        except subprocess.TimeoutExpired:
            return {
                'scan_id': scan_id,
                'target': target,
                'status': 'timeout',
                'error': 'Scan timeout after 1 hour',
                'start_time': start_time.isoformat()
            }
        except Exception as e:
            return {
                'scan_id': scan_id,
                'target': target,
                'status': 'error',
                'error': str(e),
                'start_time': start_time.isoformat()
            }
    
    def _parse_nikto_output(self, output_file: str) -> List[Dict]:
        """Parser la sortie XML de Nikto"""
        vulnerabilities = []
        
        if not os.path.exists(output_file):
            return vulnerabilities
        
        try:
            tree = ET.parse(output_file)
            root = tree.getroot()
            
            for scan in root.findall('.//scandetails'):
                target_ip = scan.get('targetip', '')
                target_hostname = scan.get('targethostname', '')
                target_port = scan.get('targetport', '')
                
                for item in scan.findall('.//item'):
                    vuln = {
                        'id': item.get('id', ''),
                        'osvdb_id': item.get('osvdbid', ''),
                        'method': item.get('method', 'GET'),
                        'uri': item.get('uri', ''),
                        'description': item.text.strip() if item.text else '',
                        'target_ip': target_ip,
                        'target_hostname': target_hostname,
                        'target_port': target_port,
                        'severity': self._determine_severity(item.text or ''),
                        'category': 'web_vulnerability',
                        'tool': 'nikto'
                    }
                    vulnerabilities.append(vuln)
                    
        except ET.ParseError as e:
            print(f"Erreur lors du parsing XML Nikto: {e}")
        except Exception as e:
            print(f"Erreur lors du traitement des résultats Nikto: {e}")
        
        return vulnerabilities
    
    def _determine_severity(self, description: str) -> str:
        """Déterminer la sévérité basée sur la description"""
        description_lower = description.lower()
        
        critical_keywords = ['sql injection', 'remote code execution', 'arbitrary file']
        high_keywords = ['xss', 'cross-site scripting', 'directory traversal', 'file inclusion']
        medium_keywords = ['information disclosure', 'weak authentication', 'session']
        
        if any(keyword in description_lower for keyword in critical_keywords):
            return 'critical'
        elif any(keyword in description_lower for keyword in high_keywords):
            return 'high'
        elif any(keyword in description_lower for keyword in medium_keywords):
            return 'medium'
        else:
            return 'low'
    
    def _get_nikto_version(self) -> str:
        """Obtenir la version de Nikto"""
        try:
            result = subprocess.run([self.nikto_path, '-Version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                return result.stdout.strip()
        except:
            pass
        return 'Unknown'
    
    def get_scan_profiles(self) -> List[Dict]:
        """Obtenir les profils de scan disponibles"""
        return [
            {
                'name': 'quick',
                'description': 'Scan rapide des vulnérabilités communes',
                'options': {'timeout': 300}
            },
            {
                'name': 'comprehensive',
                'description': 'Scan complet avec tous les plugins',
                'options': {'plugins': 'ALL', 'timeout': 1800}
            },
            {
                'name': 'ssl_focused',
                'description': 'Scan focalisé sur les vulnérabilités SSL/TLS',
                'options': {'ssl': True, 'plugins': 'ssl'}
            },
            {
                'name': 'cgi_focused',
                'description': 'Scan focalisé sur les vulnérabilités CGI',
                'options': {'plugins': 'cgi'}
            }
        ]
    
    def validate_target(self, target: str) -> Dict:
        """Valider une cible avant le scan"""
        import re
        
        # Regex pour URL
        url_pattern = re.compile(
            r'^https?://'  # http:// ou https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domaine
            r'localhost|'  # localhost
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # IP
            r'(?::\d+)?'  # port optionnel
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        # Regex pour IP
        ip_pattern = re.compile(r'^(\d{1,3}\.){3}\d{1,3}$')
        
        if url_pattern.match(target) or ip_pattern.match(target):
            return {'valid': True, 'message': 'Target is valid'}
        else:
            return {'valid': False, 'message': 'Invalid target format. Use URL or IP address.'}
    
    def get_status(self) -> Dict:
        """Obtenir le statut du service Nikto"""
        return {
            'service': 'nikto',
            'available': self.is_available(),
            'version': self._get_nikto_version(),
            'path': self.nikto_path,
            'profiles': len(self.get_scan_profiles())
        }
