"""
Robust Scan Manager for PICA - Ensures scans run independently in background
and logs are always accessible regardless of API calls or page refreshes.

This service manages:
1. Background scan processes that survive HTTP request lifecycle
2. Persistent log storage and retrieval
3. Real-time scan status monitoring
4. Process isolation and cleanup
"""

import threading
import time
import uuid
import subprocess
import os
import signal
import psutil
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from app.extensions import mongo
from app.services.scan_logger import <PERSON><PERSON><PERSON>ogger
from bson import ObjectId

class ScanManager:
    """Manages background scan processes and ensures they run independently"""

    # Class-level storage for active scans (survives request lifecycle)
    _active_scans: Dict[str, Dict] = {}
    _scan_threads: Dict[str, threading.Thread] = {}
    _scan_processes: Dict[str, List[subprocess.Popen]] = {}
    _lock = threading.Lock()

    # Scan type configurations - different parameters for each scan type
    SCAN_CONFIGS = {
        'basic': {
            'description': 'Quick scan with minimal intrusion',
            'intensity': 'low',
            'nmap_options': ['-T3'],        # Fast timing, ports handled separately
            'nmap_ports': '1-1000',         # Common ports for basic scans
            'nikto_options': ['-T', '1'],   # Basic tests only
            'sqlmap_options': ['--batch', '--level=1', '--risk=1'],  # Safe level
            'dirb_wordlist': 'common.txt',  # Common wordlist for basic scans
            'gobuster_threads': 10,         # Low thread count
            'openvas_profile': 'Discovery', # Basic discovery
            'metasploit_modules': ['auxiliary/scanner/portscan/tcp'],  # Basic port scan only
            'timeout': 300  # 5 minutes
        },
        'aggressive': {
            'description': 'Comprehensive scan with high intensity',
            'intensity': 'high',
            'nmap_options': ['-T4', '-A'],         # Aggressive scan (ports handled separately)
            'nmap_ports': '1-65535',               # All ports for aggressive scans
            'nikto_options': ['-T', '9'],          # All tests
            'sqlmap_options': ['--batch', '--level=5', '--risk=3'],  # Maximum level
            'dirb_wordlist': 'big.txt',            # Large wordlist
            'gobuster_threads': 50,                # High thread count
            'openvas_profile': 'Full and very deep ultimate',  # Most comprehensive
            'metasploit_modules': [
                'auxiliary/scanner/portscan/tcp',
                'auxiliary/scanner/smb/smb_version',
                'auxiliary/scanner/ssh/ssh_version',
                'auxiliary/scanner/http/http_version',
                'auxiliary/scanner/ftp/ftp_version'
            ],
            'timeout': 1800  # 30 minutes
        },
        'stealth': {
            'description': 'Slow, discrete scan with minimal footprint',
            'intensity': 'minimal',
            'nmap_options': ['-T1', '-sT', '--randomize-hosts'],  # Slow, stealth scan (TCP connect, randomized)
            'nmap_ports': '1-100',                 # Very limited ports for stealth scans
            'nikto_options': ['-T', '1', '-timeout', '10'],  # Basic tests, slow
            'sqlmap_options': ['--batch', '--level=1', '--risk=1', '--delay=2'],  # Safe with delays
            'dirb_wordlist': 'common.txt',         # Common wordlist for stealth
            'gobuster_threads': 5,                 # Very low thread count
            'openvas_profile': 'Discovery',        # Basic discovery only
            'metasploit_modules': ['auxiliary/scanner/portscan/tcp'],  # Minimal scanning
            'timeout': 3600  # 60 minutes (slow scan)
        },
        'comprehensive': {
            'description': 'Complete audit with all available tools and options',
            'intensity': 'maximum',
            'nmap_options': ['-T4', '-A', '--script=vuln,safe,discovery'],  # Everything (ports handled separately)
            'nmap_ports': '1-65535',               # All ports for comprehensive scans
            'nikto_options': ['-Tuning', 'x'],     # All tuning options
            'sqlmap_options': ['--batch', '--level=3', '--risk=2', '--threads=5'],  # Balanced but thorough
            'dirb_wordlist': 'big.txt',            # Large wordlist
            'gobuster_threads': 30,                # Moderate thread count
            'openvas_profile': 'Full and very deep ultimate',  # Most comprehensive
            'metasploit_modules': [
                'auxiliary/scanner/portscan/tcp',
                'auxiliary/scanner/smb/smb_version',
                'auxiliary/scanner/ssh/ssh_version',
                'auxiliary/scanner/http/http_version',
                'auxiliary/scanner/ftp/ftp_version',
                'auxiliary/scanner/discovery/udp_sweep',
                'auxiliary/scanner/snmp/snmp_enum'
            ],
            'timeout': 2400  # 40 minutes
        }
    }
    
    @classmethod
    def start_scan(cls, scan_config: Dict) -> str:
        """
        Start a new scan in background thread that survives HTTP requests
        
        Args:
            scan_config: Configuration for the scan including:
                - category: 'network', 'web', 'vulnerability', 'deep'
                - scan_type: 'basic', 'aggressive', 'stealth', 'comprehensive'
                - target: Target URL or IP
                - user_id: User who started the scan
                - tools: List of tools to use
                
        Returns:
            scan_id: Unique identifier for the scan
        """
        scan_id = str(uuid.uuid4())
        
        with cls._lock:
            # Store scan configuration
            cls._active_scans[scan_id] = {
                'scan_id': scan_id,
                'config': scan_config,
                'status': 'starting',
                'start_time': datetime.utcnow(),
                'current_tool': None,
                'progress': 0,
                'processes': [],
                'logger': ScanLogger(scan_id)
            }
            
            # Create and start background thread
            scan_thread = threading.Thread(
                target=cls._run_scan_background,
                args=(scan_id, scan_config),
                daemon=False  # Important: Not daemon so it survives main thread
            )
            scan_thread.start()
            cls._scan_threads[scan_id] = scan_thread
            
        print(f"🚀 Scan {scan_id} started in background thread")
        return scan_id
    
    @classmethod
    def _run_scan_background(cls, scan_id: str, config: Dict):
        """
        Run scan in background thread - this method runs independently
        of HTTP requests and survives page refreshes
        """
        try:
            with cls._lock:
                scan_info = cls._active_scans[scan_id]
                logger = scan_info['logger']
                scan_info['status'] = 'running'
            
            # Create scan entry in MongoDB
            scan_entry = {
                'scan_id': scan_id,
                'category': config['category'],
                'scan_type': config['scan_type'],
                'target': config['target'],
                'user_id': config['user_id'],
                'status': 'running',
                'start_time': datetime.utcnow().isoformat(),
                'tools': config['tools'],
                'current_tool': None,
                'progress': 0,
                'results': {},
                'tools_status': {}
            }

            # Add optional fields if present
            if 'ports' in config:
                scan_entry['ports'] = config['ports']
            if 'config' in config:
                scan_entry['config'] = config['config']
            mongo.db.scans.insert_one(scan_entry)
            
            logger.log_scan_start(
                config['category'], 
                config['scan_type'], 
                config['target'], 
                config['tools']
            )
            
            # Execute scan based on category
            if config['category'] == 'web':
                cls._execute_web_scan(scan_id, config)
            elif config['category'] == 'network':
                cls._execute_network_scan(scan_id, config)
            elif config['category'] == 'vulnerability':
                cls._execute_vulnerability_scan(scan_id, config)
            elif config['category'] == 'deep':
                cls._execute_deep_scan(scan_id, config)
            
            # Mark scan as completed
            cls._complete_scan(scan_id, 'completed')
            
        except Exception as e:
            print(f"❌ Error in background scan {scan_id}: {e}")
            cls._complete_scan(scan_id, 'failed', str(e))
    
    @classmethod
    def _execute_web_scan(cls, scan_id: str, config: Dict):
        """Execute web scan with all tools in parallel"""
        logger = cls._active_scans[scan_id]['logger']
        target_url = config['target']
        tools = ['nikto', 'sqlmap', 'dirb', 'gobuster', 'zap']

        # Initialize tools status in database
        tools_status = {}
        for tool in tools:
            tools_status[tool] = {
                'status': 'pending',
                'progress': 0,
                'start_time': None,
                'end_time': None
            }

        # Update database with initial tools status
        mongo.db.scans.update_one(
            {'scan_id': scan_id},
            {
                '$set': {
                    'tools_status': tools_status,
                    'tools': tools
                }
            }
        )

        # Update current tool and progress
        cls._update_scan_status(scan_id, 'Starting Web Scan', 10)
        
        # Run tools in parallel using threads
        tool_threads = []
        results = {}
        
        for tool in tools:
            thread = threading.Thread(
                target=cls._run_tool_with_progress,
                args=(scan_id, tool, target_url, results),
                daemon=False
            )
            thread.start()
            tool_threads.append(thread)
        
        # Wait for all tools to complete
        for thread in tool_threads:
            thread.join()
        
        # Update final results
        mongo.db.scans.update_one(
            {'scan_id': scan_id},
            {'$set': {'results': results, 'progress': 100}}
        )
        
        logger.log_scan_complete('completed', 0, results)

    @classmethod
    def _execute_network_scan(cls, scan_id: str, config: Dict):
        """Execute network scan with nmap, openvas, and metasploit in parallel"""
        logger = cls._active_scans[scan_id]['logger']
        target = config['target']
        tools = ['nmap', 'openvas', 'metasploit']

        # Initialize tools status in database
        tools_status = {}
        for tool in tools:
            tools_status[tool] = {
                'status': 'pending',
                'progress': 0,
                'start_time': None,
                'end_time': None
            }

        # Update database with initial tools status
        mongo.db.scans.update_one(
            {'scan_id': scan_id},
            {
                '$set': {
                    'tools_status': tools_status,
                    'tools': tools
                }
            }
        )

        logger.log_scan_start(
            config['category'],
            config['scan_type'],
            config['target'],
            config['tools']
        )

        # Update current tool and progress
        cls._update_scan_status(scan_id, 'Starting Network Scan', 10)

        # Run tools in parallel using threads
        tool_threads = []
        results = {}

        for tool in tools:
            thread = threading.Thread(
                target=cls._run_tool_with_progress,
                args=(scan_id, tool, target, results),
                daemon=False
            )
            thread.start()
            tool_threads.append(thread)

        # Wait for all tools to complete
        for thread in tool_threads:
            thread.join()

        # Flatten results for frontend compatibility
        flattened_results = {
            'tools': results,  # Keep original tool results
            'ports': [],
            'vulnerabilities': [],
            'summary': {
                'total_ports': 0,
                'open_ports': 0,
                'total_vulnerabilities': 0
            }
        }

        # Extract ports and vulnerabilities from tool results
        if 'nmap' in results and results['nmap'].get('status') == 'completed':
            nmap_ports = results['nmap'].get('ports', [])
            nmap_vulns = results['nmap'].get('vulnerabilities', [])
            flattened_results['ports'].extend(nmap_ports)
            flattened_results['vulnerabilities'].extend(nmap_vulns)

        if 'openvas' in results and results['openvas'].get('status') == 'completed':
            openvas_vulns = results['openvas'].get('vulnerabilities', [])
            flattened_results['vulnerabilities'].extend(openvas_vulns)

        if 'metasploit' in results and results['metasploit'].get('status') in ['completed', 'simulated']:
            metasploit_vulns = results['metasploit'].get('vulnerabilities', [])
            flattened_results['vulnerabilities'].extend(metasploit_vulns)

        # Update summary
        flattened_results['summary'] = {
            'total_ports': len(flattened_results['ports']),
            'open_ports': len([p for p in flattened_results['ports'] if p.get('state') == 'open']),
            'total_vulnerabilities': len(flattened_results['vulnerabilities'])
        }

        # Sauvegarder les vulnérabilités dans la collection vulnerabilités
        try:
            from app.services.vulnerability_service import VulnerabilityService

            # Récupérer les informations du scan pour obtenir l'utilisateur et la cible
            scan_info = mongo.db.scans.find_one({'scan_id': scan_id})
            if scan_info:
                target = scan_info.get('target', '')
                user_id = scan_info.get('user_id', '')

                vulnerability_ids = VulnerabilityService.process_scan_vulnerabilities(
                    scan_id=scan_id,
                    scan_results=results,  # Utiliser les résultats complets des outils
                    target=target,
                    user_id=user_id
                )
                print(f"✅ Saved {len(vulnerability_ids)} vulnerabilities to vulnerabilities collection")
        except Exception as vuln_error:
            print(f"⚠️ Error saving vulnerabilities in ScanManager: {vuln_error}")

        # Update final results - add flattened data alongside tool results
        mongo.db.scans.update_one(
            {'scan_id': scan_id},
            {
                '$set': {
                    'results.ports': flattened_results['ports'],
                    'results.vulnerabilities': flattened_results['vulnerabilities'],
                    'results.summary': flattened_results['summary'],
                    'progress': 100
                }
            }
        )

        logger.log_scan_complete('completed', 0, flattened_results)

    @classmethod
    def _run_tool(cls, scan_id: str, tool: str, target: str, results: Dict):
        """Run individual tool and capture output"""
        logger = cls._active_scans[scan_id]['logger']

        try:
            logger.log_tool_start(tool, f"{tool} scan on {target}")

            # Get scan configuration from database
            scan_info = mongo.db.scans.find_one({'scan_id': scan_id})
            if not scan_info:
                raise Exception(f"Scan {scan_id} not found in database")

            scan_type = scan_info.get('scan_type', 'basic')
            scan_config = cls.SCAN_CONFIGS.get(scan_type, cls.SCAN_CONFIGS['basic'])

            # Log the scan configuration being used
            logger.log_tool_output(tool, f"Using {scan_type} scan configuration: {scan_config['description']}")
            logger.log_tool_output(tool, f"Intensity: {scan_config['intensity']}, Timeout: {scan_config['timeout']}s")

            is_deep_scan = scan_info and scan_info.get('category') == 'deep'
            current_tool_value = tool

            # For deep scans, preserve the existing current_tool if it contains phase info
            if is_deep_scan:
                existing_current_tool = scan_info.get('current_tool', '')
                if existing_current_tool and 'Phase' in existing_current_tool:
                    # Keep the phase info, just update to show the specific tool
                    if 'Phase 1:' in existing_current_tool:
                        current_tool_value = f'Phase 1: {tool.upper()}'
                    elif 'Phase 2:' in existing_current_tool:
                        current_tool_value = f'Phase 2: {tool.upper()}'
                    elif 'Phase 3:' in existing_current_tool:
                        current_tool_value = f'Phase 3: {tool.upper()}'

            # Update tool status in database
            mongo.db.scans.update_one(
                {'scan_id': scan_id},
                {
                    '$set': {
                        f'tools_status.{tool}.status': 'running',
                        f'tools_status.{tool}.start_time': datetime.utcnow().isoformat(),
                        'current_tool': current_tool_value
                    }
                }
            )

            # Execute tool based on type with scan configuration
            # Format target URL for web tools
            web_tools = ['nikto', 'sqlmap', 'dirb', 'gobuster', 'zap']
            if tool in web_tools:
                # Ensure web tools get properly formatted URLs
                if not target.startswith(('http://', 'https://')):
                    web_target = f'http://{target}'
                    logger.log_tool_output(tool, f"Formatted target for web tool: {target} -> {web_target}")
                else:
                    web_target = target
            else:
                web_target = target

            if tool == 'nikto':
                result = cls._run_nikto(web_target, logger, scan_config)
            elif tool == 'sqlmap':
                result = cls._run_sqlmap(web_target, logger, scan_config)
            elif tool == 'dirb':
                result = cls._run_dirb(web_target, logger, scan_config)
            elif tool == 'gobuster':
                result = cls._run_gobuster(web_target, logger, scan_config)
            elif tool == 'zap':
                result = cls._run_zap(web_target, logger, scan_config)
            elif tool == 'nmap':
                result = cls._run_nmap(target, logger, scan_config)  # Network tools use original target
            elif tool == 'openvas':
                result = cls._run_openvas(target, logger, scan_config)  # Network tools use original target
            elif tool == 'metasploit':
                result = cls._run_metasploit(target, logger, scan_config)  # Network tools use original target
            else:
                result = {'status': 'skipped', 'reason': 'Tool not implemented'}
            
            # Store results
            results[tool] = result
            
            # Update tool completion status
            mongo.db.scans.update_one(
                {'scan_id': scan_id},
                {
                    '$set': {
                        f'tools_status.{tool}.status': 'completed',
                        f'tools_status.{tool}.end_time': datetime.utcnow().isoformat(),
                        f'results.{tool}': result
                    }
                }
            )
            
            logger.log_tool_result(tool, 'completed', result)
            
        except Exception as e:
            logger.log_tool_error(tool, str(e))
            results[tool] = {'status': 'failed', 'error': str(e)}
            
            mongo.db.scans.update_one(
                {'scan_id': scan_id},
                {
                    '$set': {
                        f'tools_status.{tool}.status': 'failed',
                        f'tools_status.{tool}.error': str(e),
                        f'results.{tool}': results[tool]
                    }
                }
            )
    
    @classmethod
    def _run_nikto(cls, target: str, logger: ScanLogger, scan_config: Dict) -> Dict:
        """Run REAL Nikto scan with configurable parameters"""
        try:
            intensity = scan_config.get('intensity', 'low')
            logger.log_tool_progress('nikto', 10, f'Starting Nikto scan ({intensity} intensity)...')

            # Prepare nikto command based on scan configuration
            output_file = f'/tmp/nikto_{int(time.time())}.txt'
            cmd = [
                'nikto',
                '-h', target,
                '-Format', 'txt',
                '-output', output_file,
                '-maxtime', str(scan_config.get('timeout', 300))  # Use configured timeout
            ]

            # Add scan-type specific options
            nikto_options = scan_config.get('nikto_options', ['-T', '1'])
            cmd.extend(nikto_options)

            logger.log_tool_progress('nikto', 25, 'Executing Nikto command...')
            logger.log_tool_output('nikto', f"Command: {' '.join(cmd)}")

            # Execute nikto
            start_time = time.time()
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                universal_newlines=True
            )

            # Monitor progress
            vulnerabilities = []

            while process.poll() is None:
                elapsed = time.time() - start_time
                if elapsed < 180:  # 3 minutes
                    progress = 25 + int((elapsed / 180) * 65)  # 25% to 90%
                    logger.log_tool_progress('nikto', progress, f'Scanning... ({int(elapsed)}s elapsed)')

                time.sleep(10)  # Check every 10 seconds

                # Timeout after 5 minutes
                if elapsed > 300:
                    process.terminate()
                    logger.log_tool_error('nikto', 'Scan timeout after 5 minutes')
                    break

            # Get results
            stdout, stderr = process.communicate()

            # Read output file if it exists
            output_content = ""
            if os.path.exists(output_file):
                try:
                    with open(output_file, 'r') as f:
                        output_content = f.read()
                    os.remove(output_file)  # Clean up
                except Exception as e:
                    logger.log_tool_output('nikto', f"Could not read output file: {e}")

            # Use stdout if no output file
            if not output_content and stdout:
                output_content = stdout

            logger.log_tool_progress('nikto', 90, 'Parsing results...')

            # Parse nikto output
            if output_content:
                output_lines = output_content.split('\n')

                for line in output_lines:
                    line = line.strip()
                    # Look for actual findings - Nikto findings start with + and contain GET/POST
                    if (line.startswith('+ ') and
                        ('GET ' in line or 'POST ' in line or 'OPTIONS ' in line or 'HEAD ' in line)):

                        # Determine severity based on content
                        severity = 'medium'  # Default to medium
                        if any(keyword in line.lower() for keyword in [
                            'critical', 'high', 'severe', 'dangerous', 'exploit', 'vulnerability',
                            'injection', 'xss', 'csrf', 'authentication bypass'
                        ]):
                            severity = 'high'
                        elif any(keyword in line.lower() for keyword in [
                            'information', 'disclosure', 'version', 'banner', 'fingerprint',
                            'directory listing', 'backup', 'config'
                        ]):
                            severity = 'low'

                        # Extract vulnerability type based on content
                        vuln_type = 'Security Finding'
                        if 'x-frame-options' in line.lower():
                            vuln_type = 'Missing X-Frame-Options Header'
                        elif 'x-content-type' in line.lower():
                            vuln_type = 'Missing X-Content-Type-Options Header'
                        elif 'etag' in line.lower() and 'inode' in line.lower():
                            vuln_type = 'Information Disclosure via ETags'
                        elif 'server' in line.lower() and ('leak' in line.lower() or 'version' in line.lower()):
                            vuln_type = 'Server Information Disclosure'
                        elif 'method' in line.lower() and 'allowed' in line.lower():
                            vuln_type = 'HTTP Methods Disclosure'
                        elif 'directory' in line.lower() and 'listing' in line.lower():
                            vuln_type = 'Directory Listing Enabled'
                        elif 'backup' in line.lower() or 'old' in line.lower():
                            vuln_type = 'Backup/Old Files Found'
                        elif 'admin' in line.lower() or 'login' in line.lower():
                            vuln_type = 'Administrative Interface Found'
                        elif 'xss' in line.lower():
                            vuln_type = 'Cross-Site Scripting (XSS)'
                        elif 'sql' in line.lower():
                            vuln_type = 'SQL Injection'
                        elif 'cookie' in line.lower():
                            vuln_type = 'Cookie Security Issue'

                        # Clean up the description
                        description = line[2:].strip()  # Remove '+ ' prefix
                        if len(description) > 200:
                            description = description[:200] + '...'

                        vulnerabilities.append({
                            'type': vuln_type,
                            'severity': severity,
                            'description': description
                        })

            logger.log_tool_progress('nikto', 100, 'Nikto scan completed')

            scan_duration = int(time.time() - start_time)
            result = {
                'status': 'completed',
                'vulnerabilities': vulnerabilities,
                'scan_time': f'{scan_duration} seconds',
                'total_tests': len(output_lines) if 'output_lines' in locals() else 0,
                'raw_output': output_content[:2000] if output_content else ''
            }

            logger.log_tool_output('nikto', f"Scan completed - Found {len(vulnerabilities)} potential issues")
            return result

        except Exception as e:
            logger.log_tool_error('nikto', str(e))
            return {'status': 'failed', 'error': str(e)}
    
    @classmethod
    def _run_sqlmap(cls, target: str, logger: ScanLogger, scan_config: Dict) -> Dict:
        """Run REAL SQLMap scan with configurable parameters"""
        try:
            intensity = scan_config.get('intensity', 'low')
            logger.log_tool_progress('sqlmap', 10, f'Starting SQLMap scan ({intensity} intensity)...')

            # Prepare sqlmap command based on scan configuration
            cmd = [
                'sqlmap',
                '-u', target,
                '--disable-coloring',
                '--flush-session',  # Don't use previous session data
                '--fresh-queries'   # Ignore query results stored in session file
            ]

            # Add scan-type specific options
            sqlmap_options = scan_config.get('sqlmap_options', ['--batch', '--level=1', '--risk=1'])
            cmd.extend(sqlmap_options)

            # Add timeout based on scan configuration
            timeout = scan_config.get('timeout', 300)
            cmd.extend(['--timeout', str(min(timeout // 10, 30))])  # SQLMap timeout should be shorter

            logger.log_tool_progress('sqlmap', 25, 'Executing SQLMap command...')
            logger.log_tool_output('sqlmap', f"Command: {' '.join(cmd)}")

            # Execute sqlmap
            start_time = time.time()
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                universal_newlines=True
            )

            # Monitor progress
            injections = []

            while process.poll() is None:
                elapsed = time.time() - start_time
                if elapsed < 120:  # 2 minutes
                    progress = 25 + int((elapsed / 120) * 65)  # 25% to 90%
                    logger.log_tool_progress('sqlmap', progress, f'Testing for SQL injection... ({int(elapsed)}s elapsed)')

                time.sleep(10)  # Check every 10 seconds

                # Timeout after 3 minutes for demo
                if elapsed > 180:
                    process.terminate()
                    logger.log_tool_output('sqlmap', 'Scan timeout after 3 minutes - completing with current results')
                    break

            # Get results
            stdout, stderr = process.communicate()

            logger.log_tool_progress('sqlmap', 90, 'Parsing results...')

            # Parse sqlmap output
            if stdout:
                output_lines = stdout.split('\n')

                # Look for injection indicators
                for line in output_lines:
                    line = line.strip()

                    # Check for vulnerable parameters
                    if ('parameter' in line.lower() and 'vulnerable' in line.lower()) or \
                       ('parameter' in line.lower() and 'injectable' in line.lower()):

                        # Extract parameter name
                        param_match = re.search(r"Parameter:\s*([^\s]+)", line, re.IGNORECASE)
                        param_name = param_match.group(1) if param_match else 'unknown'

                        # Determine injection type
                        inj_type = 'SQL injection'
                        if 'boolean' in line.lower():
                            inj_type = 'boolean-based blind'
                        elif 'time' in line.lower():
                            inj_type = 'time-based blind'
                        elif 'union' in line.lower():
                            inj_type = 'UNION query'
                        elif 'error' in line.lower():
                            inj_type = 'error-based'

                        # Try to identify DBMS
                        dbms = 'Unknown'
                        full_output = stdout.lower()
                        if 'mysql' in full_output:
                            dbms = 'MySQL'
                        elif 'postgresql' in full_output:
                            dbms = 'PostgreSQL'
                        elif 'oracle' in full_output:
                            dbms = 'Oracle'
                        elif 'microsoft' in full_output or 'mssql' in full_output:
                            dbms = 'Microsoft SQL Server'
                        elif 'sqlite' in full_output:
                            dbms = 'SQLite'

                        injections.append({
                            'parameter': param_name,
                            'type': inj_type,
                            'dbms': dbms
                        })

                # Also check for general vulnerability indicators
                if 'vulnerable' in stdout.lower() and not injections:
                    # Generic vulnerability found
                    injections.append({
                        'parameter': 'detected',
                        'type': 'SQL injection vulnerability',
                        'dbms': 'Unknown'
                    })

            logger.log_tool_progress('sqlmap', 100, 'SQLMap scan completed')

            scan_duration = int(time.time() - start_time)
            result = {
                'status': 'completed',
                'injections': injections,
                'scan_time': f'{scan_duration} seconds',
                'raw_output': stdout[:2000] if stdout else ''
            }

            if injections:
                logger.log_tool_output('sqlmap', f"Found {len(injections)} SQL injection points")
            else:
                logger.log_tool_output('sqlmap', "No SQL injection vulnerabilities detected")

            return result

        except Exception as e:
            logger.log_tool_error('sqlmap', str(e))
            return {'status': 'failed', 'error': str(e)}
    
    @classmethod
    def _run_dirb(cls, target: str, logger: ScanLogger, scan_config: Dict) -> Dict:
        """Run REAL Dirb scan"""
        try:
            logger.log_tool_progress('dirb', 10, 'Starting directory scan...')

            # Check if dirb is installed
            try:
                subprocess.run(['dirb'], capture_output=True, timeout=5)
            except (subprocess.TimeoutExpired, FileNotFoundError):
                logger.log_tool_error('dirb', 'Dirb not installed or not found in PATH')
                return {
                    'status': 'failed',
                    'error': 'Dirb not installed. Install with: sudo apt-get install dirb'
                }

            # Use scan configuration to determine wordlist
            dirb_wordlist = scan_config.get('dirb_wordlist', 'common.txt')
            intensity = scan_config.get('intensity', 'low')

            # Choose wordlist based on scan configuration
            if dirb_wordlist == 'common.txt':
                wordlist = '/usr/share/dirb/wordlists/common.txt'
            elif dirb_wordlist == 'big.txt':
                wordlist = '/usr/share/dirb/wordlists/big.txt'
            else:
                # Default fallback
                wordlist = '/usr/share/dirb/wordlists/common.txt'

            # Verify wordlist exists, fallback if needed
            if not os.path.exists(wordlist):
                # Try alternative wordlists
                alternatives = [
                    '/usr/share/dirb/wordlists/common.txt',
                    '/usr/share/dirb/wordlists/big.txt',
                    '/usr/share/dirb/wordlists/small.txt'
                ]
                for alt_wordlist in alternatives:
                    if os.path.exists(alt_wordlist):
                        wordlist = alt_wordlist
                        logger.log_tool_output('dirb', f"Fallback to wordlist: {wordlist}")
                        break

            if not os.path.exists(wordlist):
                # Create a comprehensive wordlist
                wordlist = '/tmp/dirb_wordlist.txt'
                basic_dirs = [
                    'admin', 'api', 'backup', 'config', 'test', 'login', 'uploads',
                    'files', 'images', 'js', 'css', 'data', 'tmp', 'cache',
                    'docs', 'download', 'media', 'static', 'assets', 'fonts'
                ]

                if intensity in ['high', 'maximum']:
                    # Add more directories for aggressive scans
                    basic_dirs.extend([
                        'javascript', 'pages', 'includes', 'scripts', 'styles',
                        'img', 'pics', 'photos', 'videos', 'audio', 'documents',
                        'about', 'contact', 'blog', 'profile', 'dashboard',
                        'user', 'users', 'account', 'settings', 'help'
                    ])

                with open(wordlist, 'w') as f:
                    f.write('\n'.join(basic_dirs))

            logger.log_tool_output('dirb', f"Using wordlist: {wordlist} (intensity: {intensity})")

            cmd = [
                'dirb',
                target,
                wordlist,
                '-w',  # Don't stop on warning messages
                '-S',  # Silent mode (less output)
                '-X', '.php,.html,.txt,.js'  # Check these extensions
            ]

            # Add scan-specific options
            if intensity == 'minimal':
                cmd.extend(['-z', '100'])  # Slower for stealth
            elif intensity in ['high', 'maximum']:
                cmd.extend(['-z', '10'])   # Faster for aggressive scans
            else:
                cmd.extend(['-z', '50'])   # Default delay

            logger.log_tool_progress('dirb', 25, 'Executing Dirb command...')
            logger.log_tool_output('dirb', f"Command: {' '.join(cmd)}")

            # Execute dirb
            start_time = time.time()
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                universal_newlines=True
            )

            # Monitor progress
            directories = []
            output_lines = []

            while process.poll() is None:
                # Update progress based on time elapsed
                elapsed = time.time() - start_time
                if elapsed < 120:  # 2 minutes
                    progress = 25 + int((elapsed / 120) * 65)  # 25% to 90%
                    logger.log_tool_progress('dirb', progress, f'Scanning directories... ({int(elapsed)}s elapsed)')

                time.sleep(5)  # Check every 5 seconds

                # Timeout after 2 minutes for small wordlist
                if elapsed > 120:
                    process.terminate()
                    logger.log_tool_output('dirb', 'Scan timeout after 2 minutes - completing with current results')
                    break

            # Get results
            stdout, stderr = process.communicate()

            logger.log_tool_progress('dirb', 90, 'Parsing results...')

            # Parse dirb output regardless of return code
            if stdout:
                output_lines = stdout.split('\n')

                # Extract directories from output
                for line in output_lines:
                    line = line.strip()
                    # Dirb output format: ==> DIRECTORY: http://target/path/
                    if line.startswith('==> DIRECTORY:'):
                        # Extract URL
                        url_match = re.search(r'==> DIRECTORY:\s+(http[s]?://[^\s]+)', line)

                        if url_match:
                            url = url_match.group(1)

                            # Extract path from URL
                            try:
                                from urllib.parse import urlparse
                                parsed = urlparse(url)
                                path = parsed.path
                                if not path:
                                    path = '/'
                                # Remove trailing slash for consistency
                                if path.endswith('/') and path != '/':
                                    path = path[:-1]
                            except:
                                # Fallback parsing
                                if target in url:
                                    path = url.replace(target, '')
                                    if not path.startswith('/'):
                                        path = '/' + path
                                    if path.endswith('/') and path != '/':
                                        path = path[:-1]
                                else:
                                    path = url

                            # Directories found by dirb are typically accessible (200 or 301)
                            directories.append({
                                'path': path,
                                'status': 200,  # Assume accessible
                                'size': 0,  # Size not provided by dirb
                                'url': url
                            })

                    # Also check for file findings with status codes
                    elif line.startswith('+') and '(CODE:' in line:
                        # Extract URL and status code for files
                        url_match = re.search(r'\+ (http[s]?://[^\s]+)', line)
                        code_match = re.search(r'CODE:(\d+)', line)
                        size_match = re.search(r'SIZE:(\d+)', line)

                        if url_match and code_match:
                            url = url_match.group(1)
                            status_code = int(code_match.group(1))
                            size = int(size_match.group(1)) if size_match else 0

                            # Extract path from URL
                            try:
                                from urllib.parse import urlparse
                                parsed = urlparse(url)
                                path = parsed.path
                                if not path:
                                    path = '/'
                            except:
                                # Fallback parsing
                                if target in url:
                                    path = url.replace(target, '')
                                    if not path.startswith('/'):
                                        path = '/' + path
                                else:
                                    path = url

                            # Include interesting status codes
                            if status_code in [200, 301, 302, 403]:
                                directories.append({
                                    'path': path,
                                    'status': status_code,
                                    'size': size,
                                    'url': url
                                })

            logger.log_tool_progress('dirb', 100, 'Directory scan completed')

            scan_duration = int(time.time() - start_time)
            result = {
                'status': 'completed',
                'directories': directories,
                'scan_time': f'{scan_duration} seconds',
                'raw_output': stdout[:2000] if stdout else ''
            }

            logger.log_tool_output('dirb', f"Found {len(directories)} directories")
            return result

        except Exception as e:
            logger.log_tool_error('dirb', str(e))
            return {'status': 'failed', 'error': str(e)}
    
    @classmethod
    def _run_gobuster(cls, target: str, logger: ScanLogger, scan_config: Dict) -> Dict:
        """Run REAL GoBuster scan"""
        try:
            logger.log_tool_progress('gobuster', 10, 'Starting GoBuster scan...')

            # Check if gobuster is installed
            try:
                subprocess.run(['gobuster', 'version'], capture_output=True, timeout=5)
            except (subprocess.TimeoutExpired, FileNotFoundError):
                logger.log_tool_error('gobuster', 'GoBuster not installed or not found in PATH')
                return {
                    'status': 'failed',
                    'error': 'GoBuster not installed. Install with: sudo apt-get install gobuster'
                }

            # Prepare gobuster command with small wordlist for demo
            wordlist = '/usr/share/wordlists/dirb/small.txt'
            if not os.path.exists(wordlist):
                wordlist = '/usr/share/dirb/wordlists/common.txt'

            if not os.path.exists(wordlist):
                # Create a minimal wordlist for demo
                wordlist = '/tmp/gobuster_wordlist.txt'
                with open(wordlist, 'w') as f:
                    f.write('\n'.join([
                        'admin', 'api', 'backup', 'config', 'login', 'test', 'uploads', 'files'
                    ]))

            cmd = [
                'gobuster',
                'dir',
                '-u', target,
                '-w', wordlist,
                '-t', '5',  # 5 threads for demo
                '-q',  # Quiet mode
                '--no-error',  # Don't display errors
                '--timeout', '5s',
                '--delay', '100ms'  # Delay between requests
            ]

            logger.log_tool_progress('gobuster', 25, 'Executing GoBuster command...')
            logger.log_tool_output('gobuster', f"Command: {' '.join(cmd)}")

            # Execute gobuster
            start_time = time.time()
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                universal_newlines=True
            )

            # Monitor progress
            found_paths = []
            output_lines = []

            while process.poll() is None:
                # Update progress based on time elapsed
                elapsed = time.time() - start_time
                if elapsed < 90:  # 1.5 minutes
                    progress = 25 + int((elapsed / 90) * 65)  # 25% to 90%
                    logger.log_tool_progress('gobuster', progress, f'Brute forcing directories... ({int(elapsed)}s elapsed)')

                time.sleep(5)  # Check every 5 seconds

                # Timeout after 2 minutes
                if elapsed > 120:
                    process.terminate()
                    logger.log_tool_output('gobuster', 'Scan timeout after 2 minutes - completing with current results')
                    break

            # Get results
            stdout, stderr = process.communicate()

            if process.returncode == 0 or stdout:
                logger.log_tool_progress('gobuster', 90, 'Parsing results...')

                # Parse gobuster output
                output_lines = stdout.split('\n') if stdout else []

                # Extract found paths from output
                for line in output_lines:
                    line = line.strip()
                    # GoBuster output format: /path (Status: 200) [Size: 1234]
                    if line and '(Status:' in line and not line.startswith('='):
                        # Extract path and status - handle ANSI escape codes
                        clean_line = re.sub(r'\x1b\[[0-9;]*[mK]', '', line)  # Remove ANSI codes

                        # Match pattern like: /path (Status: 200) [Size: 1234]
                        match = re.match(r'^(/[^\s]*)\s*\(Status:\s*(\d+)\)', clean_line)
                        size_match = re.search(r'Size:\s*(\d+)', clean_line)

                        if match:
                            path = match.group(1)
                            status = int(match.group(2))
                            size = int(size_match.group(1)) if size_match else 0

                            # Only include successful responses and redirects
                            if status in [200, 301, 302, 403]:
                                found_paths.append({
                                    'path': path,
                                    'status': status,
                                    'size': size
                                })

                logger.log_tool_progress('gobuster', 100, 'GoBuster scan completed')

                scan_duration = int(time.time() - start_time)
                result = {
                    'status': 'completed',
                    'found_paths': found_paths,
                    'scan_time': f'{scan_duration} seconds',
                    'raw_output': stdout[:2000] if stdout else ''  # Limit output size
                }

                logger.log_tool_output('gobuster', f"Found {len(found_paths)} paths")
                return result

            else:
                error_msg = stderr if stderr else 'No results found'
                logger.log_tool_output('gobuster', f'GoBuster completed with no results: {error_msg}')
                return {
                    'status': 'completed',
                    'found_paths': [],
                    'scan_time': f'{int(time.time() - start_time)} seconds',
                    'raw_output': stdout[:2000] if stdout else ''
                }

        except Exception as e:
            logger.log_tool_error('gobuster', str(e))
            return {'status': 'failed', 'error': str(e)}
    
    @classmethod
    def _run_zap(cls, target: str, logger: ScanLogger, scan_config: Dict) -> Dict:
        """Run basic security scan (ZAP-style checks)"""
        try:
            logger.log_tool_progress('zap', 10, 'Starting security scan...')

            # For demo, we'll do basic security checks instead of full ZAP
            # since ZAP requires complex setup and can take very long

            alerts = []
            start_time = time.time()

            logger.log_tool_progress('zap', 25, 'Performing HTTP security checks...')

            try:
                import requests
                response = requests.get(target, timeout=10, verify=False, allow_redirects=True)

                logger.log_tool_progress('zap', 50, 'Analyzing security headers...')

                # Check for missing security headers
                security_headers = {
                    'X-Frame-Options': 'Clickjacking protection missing',
                    'X-Content-Type-Options': 'MIME sniffing protection missing',
                    'X-XSS-Protection': 'XSS protection header missing',
                    'Strict-Transport-Security': 'HTTPS enforcement missing',
                    'Content-Security-Policy': 'Content Security Policy missing'
                }

                for header, description in security_headers.items():
                    if header not in response.headers:
                        alerts.append({
                            'risk': 'Medium',
                            'name': f'Missing {header}',
                            'count': 1,
                            'description': description
                        })

                logger.log_tool_progress('zap', 75, 'Checking response content...')

                # Check for server information disclosure
                if 'Server' in response.headers:
                    alerts.append({
                        'risk': 'Low',
                        'name': 'Server Information Disclosure',
                        'count': 1,
                        'description': f'Server header reveals: {response.headers["Server"]}'
                    })

                # Check for common security issues in content
                content_lower = response.text.lower()
                if 'sql syntax' in content_lower or 'mysql_fetch' in content_lower:
                    alerts.append({
                        'risk': 'High',
                        'name': 'SQL Error Disclosure',
                        'count': 1,
                        'description': 'Response contains SQL error messages'
                    })

                if '<script>' in content_lower and 'user' in content_lower:
                    alerts.append({
                        'risk': 'High',
                        'name': 'Potential XSS',
                        'count': 1,
                        'description': 'Response contains script tags with user input'
                    })

            except Exception as e:
                logger.log_tool_output('zap', f"HTTP request failed: {e}")
                alerts.append({
                    'risk': 'Medium',
                    'name': 'Connection Issue',
                    'count': 1,
                    'description': f'Could not analyze target: {str(e)}'
                })

            logger.log_tool_progress('zap', 100, 'Security scan completed')

            scan_duration = int(time.time() - start_time)
            result = {
                'status': 'completed',
                'alerts': alerts,
                'scan_time': f'{scan_duration} seconds',
                'raw_output': f'Basic security scan completed. Found {len(alerts)} potential issues.'
            }

            logger.log_tool_output('zap', f"Found {len(alerts)} security alerts")
            return result

        except Exception as e:
            logger.log_tool_error('zap', str(e))
            return {'status': 'failed', 'error': str(e)}

    # ============================================================================
    # NETWORK TOOLS - Nmap, OpenVAS, Metasploit
    # ============================================================================

    @classmethod
    def _run_nmap(cls, target: str, logger: ScanLogger, scan_config: Dict) -> Dict:
        """Run REAL Nmap scan with configurable parameters"""
        try:
            intensity = scan_config.get('intensity', 'low')
            logger.log_tool_progress('nmap', 10, f'Starting Nmap scan ({intensity} intensity)...')

            # Use NmapService for actual scanning with scan-specific options
            from app.services.nmap_service import NmapService
            nmap_service = NmapService()

            if not nmap_service.is_available():
                logger.log_tool_error('nmap', 'Nmap not available')
                return {'status': 'unavailable', 'error': 'Nmap not available'}

            logger.log_tool_progress('nmap', 30, 'Running port scan...')

            # Build nmap options based on scan configuration
            nmap_options = scan_config.get('nmap_options', ['-T3', '-F'])
            options_str = ' '.join(nmap_options)

            # Get port specification from scan configuration
            ports = scan_config.get('nmap_ports', '1-1000')  # Default to common ports

            logger.log_tool_output('nmap', f"Using scan config: {scan_config.get('description', 'Unknown')}")
            logger.log_tool_output('nmap', f"Nmap options: {options_str}")
            logger.log_tool_output('nmap', f"Port range: {ports}")

            # Run nmap scan with configured options and timeout
            timeout = scan_config.get('timeout', 600)  # Default 10 minutes
            result = nmap_service.scan_target(
                target=target,
                options=options_str,
                ports=ports,
                timeout=timeout
            )

            logger.log_tool_progress('nmap', 90, 'Processing results...')

            if result.get('status') == 'completed':
                logger.log_tool_output('nmap', f"Scan completed: {result.get('command', '')}")
                logger.log_tool_output('nmap', f"Found {len(result.get('ports', []))} ports")
                return result
            else:
                logger.log_tool_error('nmap', result.get('error', 'Unknown error'))
                return result

        except Exception as e:
            logger.log_tool_error('nmap', str(e))
            return {'status': 'failed', 'error': str(e)}

    @classmethod
    def _run_openvas(cls, target: str, logger: ScanLogger, scan_config: Dict) -> Dict:
        """Run OpenVAS scan (simplified version)"""
        try:
            logger.log_tool_progress('openvas', 10, 'Starting OpenVAS scan...')

            # Use OpenVASService for availability check
            from app.services.openvas_service import OpenVASService
            openvas_service = OpenVASService()

            if not openvas_service.is_available():
                logger.log_tool_error('openvas', 'OpenVAS not available')
                return {'status': 'unavailable', 'error': 'OpenVAS service not available'}

            logger.log_tool_progress('openvas', 30, 'OpenVAS is available, starting GMP scan...')

            # Use real OpenVAS GMP integration
            result = openvas_service.scan_target_gmp(
                target_hosts=target,
                scan_name=f"PICA_Network_Scan_{int(time.time())}"
            )

            logger.log_tool_progress('openvas', 70, 'OpenVAS scan initiated...')

            if result.get('status') == 'success':
                logger.log_tool_output('openvas', f"OpenVAS scan started: Task {result.get('task_id', 'N/A')}")

                # Return successful result with scan info
                return {
                    'status': 'completed',
                    'scan_id': result.get('scan_id'),
                    'task_id': result.get('task_id'),
                    'target_id': result.get('target_id'),
                    'vulnerabilities': result.get('vulnerabilities', []),
                    'message': result.get('message', 'OpenVAS scan started successfully'),
                    'scan_type': 'gmp_vulnerability_scan'
                }
            else:
                logger.log_tool_error('openvas', f"OpenVAS scan failed: {result.get('message', 'Unknown error')}")
                # Fall back to basic vulnerability check
                logger.log_tool_progress('openvas', 50, 'Falling back to basic vulnerability check...')
                vulnerabilities = []

            # Basic vulnerability checks (can be enhanced later)
            # Check if common vulnerable ports are open
            try:
                import socket
                vulnerable_ports = [21, 23, 25, 53, 80, 110, 135, 139, 143, 443, 445, 993, 995]
                open_vulnerable_ports = []

                for port in vulnerable_ports:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(1)
                    result = sock.connect_ex((target, port))
                    sock.close()

                    if result == 0:
                        open_vulnerable_ports.append(port)

                        # Add basic vulnerability assessments
                        if port == 21:
                            vulnerabilities.append({
                                'type': 'FTP Service Detected',
                                'severity': 'medium',
                                'description': 'FTP service detected - may allow anonymous access',
                                'port': port
                            })
                        elif port == 23:
                            vulnerabilities.append({
                                'type': 'Telnet Service Detected',
                                'severity': 'high',
                                'description': 'Telnet service detected - unencrypted protocol',
                                'port': port
                            })
                        elif port == 445:
                            vulnerabilities.append({
                                'type': 'SMB Service Detected',
                                'severity': 'medium',
                                'description': 'SMB service detected - check for SMB vulnerabilities',
                                'port': port
                            })

            except Exception as e:
                logger.log_tool_output('openvas', f'Port scan error: {str(e)}')

            logger.log_tool_progress('openvas', 90, 'Processing results...')

            result = {
                'status': 'completed',
                'vulnerabilities': vulnerabilities,
                'open_vulnerable_ports': open_vulnerable_ports if 'open_vulnerable_ports' in locals() else [],
                'scan_type': 'basic_vulnerability_check',
                'note': 'Basic OpenVAS integration - enhanced scanning available with full GMP setup'
            }

            logger.log_tool_output('openvas', f"OpenVAS basic scan completed - Found {len(vulnerabilities)} potential vulnerabilities")
            return result

        except Exception as e:
            logger.log_tool_error('openvas', str(e))
            return {'status': 'failed', 'error': str(e)}

    @classmethod
    def _run_metasploit(cls, target: str, logger: ScanLogger, scan_config: Dict) -> Dict:
        """Run Metasploit modules for vulnerability scanning"""
        try:
            logger.log_tool_progress('metasploit', 10, 'Starting Metasploit modules...')

            vulnerabilities = []
            modules_run = []
            raw_outputs = []

            # Define modules to run based on scan type
            modules = [
                {
                    'name': 'auxiliary/scanner/portscan/tcp',
                    'description': 'TCP Port Scanner',
                    'options': {'RHOSTS': target, 'PORTS': '1-1000'}
                },
                {
                    'name': 'auxiliary/scanner/smb/smb_version',
                    'description': 'SMB Version Detection',
                    'options': {'RHOSTS': target}
                },
                {
                    'name': 'auxiliary/scanner/ssh/ssh_version',
                    'description': 'SSH Version Detection',
                    'options': {'RHOSTS': target}
                },
                {
                    'name': 'auxiliary/scanner/smb/smb_ms17_010',
                    'description': 'MS17-010 SMB RCE Detection',
                    'options': {'RHOSTS': target}
                }
            ]

            total_modules = len(modules)

            for i, module in enumerate(modules):
                progress = 20 + (i * 60 // total_modules)
                logger.log_tool_progress('metasploit', progress, f'Running {module["description"]}...')

                try:
                    # Build msfconsole command
                    options_str = '; '.join([f'set {k} {v}' for k, v in module['options'].items()])
                    command = f'msfconsole -q -x "use {module["name"]}; {options_str}; run; exit"'

                    logger.log_tool_output('metasploit', f'Executing: {module["description"]}')

                    # Execute the command
                    result = subprocess.run(
                        command,
                        shell=True,
                        capture_output=True,
                        text=True,
                        timeout=120  # 2 minute timeout per module
                    )

                    modules_run.append(module['name'])
                    output = result.stdout + result.stderr
                    raw_outputs.append(f"=== {module['description']} ===\n{output}")

                    # Parse output for vulnerabilities
                    if 'ms17_010' in module['name'] and ('Vulnerable' in output or 'VULNERABLE' in output):
                        vulnerabilities.append({
                            'type': 'MS17-010 SMB RCE',
                            'severity': 'critical',
                            'description': 'Target is vulnerable to MS17-010 (EternalBlue)',
                            'module': module['name'],
                            'cve': 'CVE-2017-0144'
                        })

                    # Check for other vulnerability indicators
                    if 'vulnerable' in output.lower() or 'exploit' in output.lower():
                        vulnerabilities.append({
                            'type': f'Potential vulnerability detected by {module["description"]}',
                            'severity': 'medium',
                            'description': 'Metasploit module detected potential security issue',
                            'module': module['name']
                        })

                except subprocess.TimeoutExpired:
                    logger.log_tool_error('metasploit', f'Timeout running {module["description"]}')
                    raw_outputs.append(f"=== {module['description']} ===\nTimeout after 120 seconds")
                except Exception as e:
                    logger.log_tool_error('metasploit', f'Error running {module["description"]}: {str(e)}')
                    raw_outputs.append(f"=== {module['description']} ===\nError: {str(e)}")

            logger.log_tool_progress('metasploit', 90, 'Processing results...')

            result = {
                'status': 'completed',
                'modules_run': modules_run,
                'vulnerabilities': vulnerabilities,
                'total_modules': len(modules_run),
                'raw_output': '\n\n'.join(raw_outputs[:2000])  # Limit output size
            }

            logger.log_tool_output('metasploit', f'Completed {len(modules_run)} modules - Found {len(vulnerabilities)} potential vulnerabilities')
            return result

        except Exception as e:
            logger.log_tool_error('metasploit', str(e))
            return {'status': 'failed', 'error': str(e)}

    @classmethod
    def _run_nuclei(cls, target: str, logger: ScanLogger) -> Dict:
        """Run REAL Nuclei vulnerability scanner"""
        try:
            logger.log_tool_progress('nuclei', 10, 'Starting Nuclei vulnerability scan...')

            # Check if nuclei is installed
            try:
                subprocess.run(['nuclei', '-version'], capture_output=True, timeout=5)
            except (subprocess.TimeoutExpired, FileNotFoundError):
                logger.log_tool_error('nuclei', 'Nuclei not installed or not found in PATH')
                return {
                    'status': 'failed',
                    'error': 'Nuclei not installed. Install with: go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest'
                }

            # Prepare nuclei command
            output_file = f'/tmp/nuclei_{int(time.time())}.json'
            cmd = [
                'nuclei',
                '-u', target,
                '-j',  # JSON output
                '-o', output_file,
                '-severity', 'critical,high,medium,low',
                '-timeout', '10',
                '-retries', '1',
                '-rate-limit', '10',  # 10 requests per second
                '-silent'  # Reduce noise
            ]

            logger.log_tool_progress('nuclei', 25, 'Executing Nuclei command...')
            logger.log_tool_output('nuclei', f"Command: {' '.join(cmd)}")

            # Execute nuclei
            start_time = time.time()
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                universal_newlines=True
            )

            # Monitor progress
            vulnerabilities = []

            while process.poll() is None:
                elapsed = time.time() - start_time
                if elapsed < 120:  # 2 minutes
                    progress = 25 + int((elapsed / 120) * 65)  # 25% to 90%
                    logger.log_tool_progress('nuclei', progress, f'Scanning for vulnerabilities... ({int(elapsed)}s elapsed)')

                time.sleep(10)  # Check every 10 seconds

                # Timeout after 3 minutes
                if elapsed > 180:
                    process.terminate()
                    logger.log_tool_output('nuclei', 'Scan timeout after 3 minutes - completing with current results')
                    break

            # Get results
            stdout, stderr = process.communicate()

            logger.log_tool_progress('nuclei', 90, 'Parsing Nuclei results...')

            # Read JSON output file if it exists
            if os.path.exists(output_file):
                try:
                    with open(output_file, 'r') as f:
                        for line in f:
                            line = line.strip()
                            if line:
                                try:
                                    import json
                                    vuln_data = json.loads(line)

                                    # Extract vulnerability information
                                    template_id = vuln_data.get('template-id', 'unknown')
                                    template_name = vuln_data.get('info', {}).get('name', template_id)
                                    severity = vuln_data.get('info', {}).get('severity', 'info')
                                    description = vuln_data.get('info', {}).get('description', 'No description available')
                                    matched_at = vuln_data.get('matched-at', target)

                                    # Map nuclei severity to standard severity
                                    severity_map = {
                                        'info': 'low',
                                        'low': 'low',
                                        'medium': 'medium',
                                        'high': 'high',
                                        'critical': 'critical'
                                    }

                                    vulnerabilities.append({
                                        'type': template_name,
                                        'severity': severity_map.get(severity, 'medium'),
                                        'description': description,
                                        'template_id': template_id,
                                        'matched_at': matched_at,
                                        'tool': 'nuclei'
                                    })

                                except json.JSONDecodeError:
                                    continue

                    os.remove(output_file)  # Clean up
                except Exception as e:
                    logger.log_tool_output('nuclei', f"Could not read output file: {e}")

            logger.log_tool_progress('nuclei', 100, 'Nuclei scan completed')

            scan_duration = int(time.time() - start_time)
            result = {
                'status': 'completed',
                'vulnerabilities': vulnerabilities,
                'scan_time': f'{scan_duration} seconds',
                'templates_matched': len(vulnerabilities),
                'raw_output': stdout[:1000] if stdout else ''
            }

            logger.log_tool_output('nuclei', f"Scan completed - Found {len(vulnerabilities)} vulnerabilities")
            return result

        except Exception as e:
            logger.log_tool_error('nuclei', str(e))
            return {'status': 'failed', 'error': str(e)}

    @classmethod
    def _run_nessus_scan(cls, target: str, logger: ScanLogger) -> Dict:
        """Run Nessus-style vulnerability assessment"""
        try:
            logger.log_tool_progress('nessus_scan', 10, 'Starting Nessus-style vulnerability assessment...')

            # Since Nessus is commercial, we'll implement a comprehensive vulnerability check
            # that mimics Nessus functionality using open-source tools and techniques

            vulnerabilities = []
            start_time = time.time()

            # Phase 1: Service Detection and Version Scanning
            logger.log_tool_progress('nessus_scan', 20, 'Phase 1: Service detection and version scanning...')

            try:
                # Use nmap for service detection
                nmap_cmd = [
                    'nmap', '-sV', '-sC', '--script=vuln',
                    '-p', '1-1000',
                    '--max-retries', '1',
                    '--host-timeout', '300s',
                    target
                ]

                logger.log_tool_output('nessus_scan', f"Running service detection: {' '.join(nmap_cmd)}")

                nmap_result = subprocess.run(
                    nmap_cmd,
                    capture_output=True,
                    text=True,
                    timeout=300  # 5 minute timeout
                )

                if nmap_result.stdout:
                    # Parse nmap output for vulnerabilities
                    nmap_lines = nmap_result.stdout.split('\n')
                    for line in nmap_lines:
                        line = line.strip()
                        if 'VULNERABLE' in line.upper() or 'CVE-' in line:
                            # Extract CVE information
                            cve_match = re.search(r'CVE-\d{4}-\d{4,}', line)
                            if cve_match:
                                cve_id = cve_match.group(0)
                                vulnerabilities.append({
                                    'type': f'CVE Vulnerability - {cve_id}',
                                    'severity': 'high',
                                    'description': line,
                                    'cve_id': cve_id,
                                    'tool': 'nessus_scan'
                                })

            except subprocess.TimeoutExpired:
                logger.log_tool_output('nessus_scan', 'Service detection timeout - continuing with other checks')
            except Exception as e:
                logger.log_tool_output('nessus_scan', f'Service detection error: {e}')

            # Phase 2: SSL/TLS Security Assessment
            logger.log_tool_progress('nessus_scan', 40, 'Phase 2: SSL/TLS security assessment...')

            try:
                # Check SSL/TLS configuration
                import ssl
                import socket
                from urllib.parse import urlparse

                if target.startswith('http'):
                    parsed = urlparse(target)
                    hostname = parsed.hostname
                    port = parsed.port or (443 if parsed.scheme == 'https' else 80)
                else:
                    hostname = target
                    port = 443

                if port == 443:  # Only check SSL for HTTPS
                    try:
                        context = ssl.create_default_context()
                        with socket.create_connection((hostname, port), timeout=10) as sock:
                            with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                                cert = ssock.getpeercert()
                                cipher = ssock.cipher()

                                # Check for weak ciphers
                                if cipher and len(cipher) >= 3:
                                    cipher_name = cipher[0]
                                    if any(weak in cipher_name.upper() for weak in ['RC4', 'DES', 'MD5']):
                                        vulnerabilities.append({
                                            'type': 'Weak SSL/TLS Cipher',
                                            'severity': 'medium',
                                            'description': f'Weak cipher detected: {cipher_name}',
                                            'tool': 'nessus_scan'
                                        })

                                # Check certificate validity
                                if cert:
                                    import datetime
                                    not_after = datetime.datetime.strptime(cert['notAfter'], '%b %d %H:%M:%S %Y %Z')
                                    if not_after < datetime.datetime.now():
                                        vulnerabilities.append({
                                            'type': 'Expired SSL Certificate',
                                            'severity': 'high',
                                            'description': f'SSL certificate expired on {cert["notAfter"]}',
                                            'tool': 'nessus_scan'
                                        })
                    except Exception as ssl_error:
                        logger.log_tool_output('nessus_scan', f'SSL check error: {ssl_error}')

            except Exception as e:
                logger.log_tool_output('nessus_scan', f'SSL assessment error: {e}')

            # Phase 3: Web Application Security Checks
            logger.log_tool_progress('nessus_scan', 60, 'Phase 3: Web application security checks...')

            if target.startswith('http'):
                try:
                    import requests

                    # Check for common web vulnerabilities
                    response = requests.get(target, timeout=10, verify=False)

                    # Check security headers
                    security_headers = {
                        'X-Frame-Options': 'Clickjacking protection missing',
                        'X-Content-Type-Options': 'MIME sniffing protection missing',
                        'X-XSS-Protection': 'XSS protection header missing',
                        'Strict-Transport-Security': 'HTTPS enforcement missing',
                        'Content-Security-Policy': 'Content Security Policy missing'
                    }

                    for header, description in security_headers.items():
                        if header not in response.headers:
                            vulnerabilities.append({
                                'type': f'Missing Security Header - {header}',
                                'severity': 'medium',
                                'description': description,
                                'tool': 'nessus_scan'
                            })

                    # Check for server information disclosure
                    if 'Server' in response.headers:
                        server_header = response.headers['Server']
                        if any(server in server_header.lower() for server in ['apache', 'nginx', 'iis']):
                            vulnerabilities.append({
                                'type': 'Server Information Disclosure',
                                'severity': 'low',
                                'description': f'Server header reveals: {server_header}',
                                'tool': 'nessus_scan'
                            })

                except Exception as web_error:
                    logger.log_tool_output('nessus_scan', f'Web security check error: {web_error}')

            # Phase 4: Network Security Assessment
            logger.log_tool_progress('nessus_scan', 80, 'Phase 4: Network security assessment...')

            try:
                # Check for common vulnerable ports
                import socket
                vulnerable_ports = {
                    21: ('FTP', 'medium'),
                    23: ('Telnet', 'high'),
                    25: ('SMTP', 'low'),
                    53: ('DNS', 'low'),
                    135: ('RPC', 'medium'),
                    139: ('NetBIOS', 'medium'),
                    445: ('SMB', 'high'),
                    1433: ('MSSQL', 'high'),
                    3389: ('RDP', 'high')
                }

                target_ip = target
                if target.startswith('http'):
                    from urllib.parse import urlparse
                    target_ip = urlparse(target).hostname

                for port, (service, severity) in vulnerable_ports.items():
                    try:
                        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        sock.settimeout(2)
                        result = sock.connect_ex((target_ip, port))
                        sock.close()

                        if result == 0:
                            vulnerabilities.append({
                                'type': f'{service} Service Exposed',
                                'severity': severity,
                                'description': f'{service} service detected on port {port}',
                                'port': port,
                                'tool': 'nessus_scan'
                            })
                    except Exception:
                        continue

            except Exception as e:
                logger.log_tool_output('nessus_scan', f'Network assessment error: {e}')

            logger.log_tool_progress('nessus_scan', 100, 'Nessus-style scan completed')

            scan_duration = int(time.time() - start_time)
            result = {
                'status': 'completed',
                'vulnerabilities': vulnerabilities,
                'scan_time': f'{scan_duration} seconds',
                'assessment_phases': 4,
                'total_checks': len(vulnerabilities),
                'scan_type': 'comprehensive_vulnerability_assessment'
            }

            logger.log_tool_output('nessus_scan', f"Assessment completed - Found {len(vulnerabilities)} potential vulnerabilities")
            return result

        except Exception as e:
            logger.log_tool_error('nessus_scan', str(e))
            return {'status': 'failed', 'error': str(e)}

    @classmethod
    def _execute_vulnerability_scan(cls, scan_id: str, config: Dict):
        """Execute vulnerability scan with real vulnerability scanning tools in parallel"""
        logger = cls._active_scans[scan_id]['logger']
        target = config['target']

        # Core vulnerability scanning tools
        tools = ['openvas', 'metasploit']

        logger.log_scan_start(
            config['category'],
            config['scan_type'],
            config['target'],
            tools
        )

        # Initialize results structure in database to avoid conflicts
        mongo.db.scans.update_one(
            {'scan_id': scan_id},
            {
                '$set': {
                    'results': {
                        'tools': {},
                        'vulnerabilities': [],
                        'ports': [],
                        'summary': {}
                    }
                }
            }
        )

        # Initialize tools status in database
        tools_status = {}
        for tool in tools:
            tools_status[tool] = {
                'status': 'pending',
                'progress': 0,
                'start_time': None,
                'end_time': None
            }

        # Update database with initial tools status
        mongo.db.scans.update_one(
            {'scan_id': scan_id},
            {
                '$set': {
                    'tools_status': tools_status,
                    'tools': tools
                }
            }
        )

        # Update current tool and progress
        cls._update_scan_status(scan_id, 'Starting Vulnerability Scan', 10)

        # Run tools in parallel using threads
        tool_threads = []
        results = {}

        for tool in tools:
            thread = threading.Thread(
                target=cls._run_tool_with_progress,
                args=(scan_id, tool, target, results),
                daemon=False
            )
            thread.start()
            tool_threads.append(thread)

        # Wait for all tools to complete
        for thread in tool_threads:
            thread.join()

        # Flatten results for frontend compatibility
        flattened_results = {
            'tools': results,  # Keep original tool results
            'vulnerabilities': [],
            'ports': [],
            'summary': {
                'total_vulnerabilities': 0,
                'high_severity': 0,
                'medium_severity': 0,
                'low_severity': 0,
                'critical_severity': 0,
                'total_ports_scanned': 0
            }
        }

        # Extract vulnerabilities from tool results
        if 'openvas' in results and results['openvas'].get('status') == 'completed':
            openvas_vulns = results['openvas'].get('vulnerabilities', [])
            for vuln in openvas_vulns:
                vuln['source_tool'] = 'openvas'
            flattened_results['vulnerabilities'].extend(openvas_vulns)

        if 'metasploit' in results and results['metasploit'].get('status') in ['completed', 'simulated']:
            metasploit_vulns = results['metasploit'].get('vulnerabilities', [])
            for vuln in metasploit_vulns:
                vuln['source_tool'] = 'metasploit'
            flattened_results['vulnerabilities'].extend(metasploit_vulns)



        # Extract ports information if available
        for tool_name, tool_result in results.items():
            if tool_result.get('ports'):
                flattened_results['ports'].extend(tool_result['ports'])

        # Update summary
        total_vulns = len(flattened_results['vulnerabilities'])
        critical_count = len([v for v in flattened_results['vulnerabilities'] if v.get('severity') == 'critical'])
        high_count = len([v for v in flattened_results['vulnerabilities'] if v.get('severity') == 'high'])
        medium_count = len([v for v in flattened_results['vulnerabilities'] if v.get('severity') == 'medium'])
        low_count = len([v for v in flattened_results['vulnerabilities'] if v.get('severity') == 'low'])

        flattened_results['summary'] = {
            'total_vulnerabilities': total_vulns,
            'critical_severity': critical_count,
            'high_severity': high_count,
            'medium_severity': medium_count,
            'low_severity': low_count,
            'total_ports_scanned': len(flattened_results['ports']),
            'tools_executed': len([t for t in tools if t in results and results[t].get('status') in ['completed', 'simulated']])
        }

        # Save vulnerabilities to vulnerabilities collection
        try:
            from app.services.vulnerability_service import VulnerabilityService

            # Get scan info for user and target
            scan_info = mongo.db.scans.find_one({'scan_id': scan_id})
            if scan_info:
                target = scan_info.get('target', '')
                user_id = scan_info.get('user_id', '')

                vulnerability_ids = VulnerabilityService.process_scan_vulnerabilities(
                    scan_id=scan_id,
                    scan_results=results,  # Use complete tool results
                    target=target,
                    user_id=user_id
                )
                print(f"✅ Saved {len(vulnerability_ids)} vulnerabilities to vulnerabilities collection")
        except Exception as vuln_error:
            print(f"⚠️ Error saving vulnerabilities in ScanManager: {vuln_error}")

        # Update final results - use complete results object to avoid conflicts
        mongo.db.scans.update_one(
            {'scan_id': scan_id},
            {
                '$set': {
                    'results': flattened_results,
                    'progress': 100
                }
            }
        )

        logger.log_scan_complete('completed', 0, flattened_results)

    @classmethod
    def _execute_deep_scan(cls, scan_id: str, config: Dict):
        """
        Execute deep scan - runs all tools from all categories in parallel with individual progress tracking

        Deep scan runs all tools simultaneously:
        - Network tools: nmap, openvas, metasploit
        - Web tools: nikto, sqlmap, dirb, gobuster, zap
        Each tool has its own progress bar and status tracking
        """
        logger = cls._active_scans[scan_id]['logger']
        target = config['target']

        # Define all tools for deep scan
        all_tools = ['nmap', 'openvas', 'metasploit', 'nikto', 'sqlmap', 'dirb', 'gobuster', 'zap']

        # Initialize tools status in database
        tools_status = {}
        for tool in all_tools:
            tools_status[tool] = {
                'status': 'pending',
                'progress': 0,
                'start_time': None,
                'end_time': None
            }

        # Update database with initial tools status
        mongo.db.scans.update_one(
            {'scan_id': scan_id},
            {
                '$set': {
                    'tools_status': tools_status,
                    'tools': all_tools
                }
            }
        )

        logger.log_scan_start(
            config['category'],
            config['scan_type'],
            config['target'],
            all_tools
        )

        # Initialize results structure
        all_results = {
            'network': {},
            'web': {},
            'vulnerabilities': [],
            'ports': [],
            'summary': {
                'total_ports': 0,
                'open_ports': 0,
                'total_vulnerabilities': 0,
                'high_severity': 0,
                'medium_severity': 0,
                'low_severity': 0
            }
        }

        try:
            # Start all tools in parallel
            logger.log_tool_start('deep_scan', '🚀 Starting Deep Scan - All Tools Running in Parallel')
            cls._update_scan_status(scan_id, 'Starting All Tools', 5)

            # Set all tools to running status
            for tool in all_tools:
                cls._update_tool_status(scan_id, tool, 'running', 0)

            # Run all tools in parallel using threads
            tool_threads = []
            all_tool_results = {}

            logger.log_tool_progress('deep_scan', 10, 'Launching all tools in parallel...')

            for tool in all_tools:
                thread = threading.Thread(
                    target=cls._run_tool_with_progress,
                    args=(scan_id, tool, target, all_tool_results),
                    daemon=False
                )
                thread.start()
                tool_threads.append(thread)
                logger.log_tool_progress('deep_scan', 10, f'Started {tool} in background')

            cls._update_scan_status(scan_id, 'All Tools Running', 15)
            logger.log_tool_progress('deep_scan', 15, f'All {len(all_tools)} tools are now running in parallel')

            # Wait for all tools to complete
            completed_tools = 0
            while completed_tools < len(all_tools):
                time.sleep(2)  # Check every 2 seconds

                # Count completed tools
                completed_tools = 0
                for thread in tool_threads:
                    if not thread.is_alive():
                        completed_tools += 1

                # Update overall progress based on completed tools
                overall_progress = 15 + int((completed_tools / len(all_tools)) * 80)  # 15% to 95%
                cls._update_scan_status(scan_id, f'Tools Running ({completed_tools}/{len(all_tools)} completed)', overall_progress)

                if completed_tools < len(all_tools):
                    logger.log_tool_progress('deep_scan', overall_progress, f'{completed_tools}/{len(all_tools)} tools completed')

            # Ensure all threads are finished
            for thread in tool_threads:
                thread.join()

            logger.log_tool_result('deep_scan', 'all_tools_complete', {'tools_completed': len(all_tools)})

            # Separate results by category for processing
            network_tools = ['nmap', 'openvas', 'metasploit']
            web_tools = ['nikto', 'sqlmap', 'dirb', 'gobuster', 'zap']

            network_results = {tool: all_tool_results.get(tool, {}) for tool in network_tools}
            web_results = {tool: all_tool_results.get(tool, {}) for tool in web_tools}

            all_results['network'] = network_results
            all_results['web'] = web_results

            # Phase 3: Results Analysis and Consolidation (95-100%)
            logger.log_tool_start('deep_scan', '📊 Results Analysis and Consolidation')
            cls._update_scan_status(scan_id, 'Analyzing Results', 95)

            # Consolidate ports from network scans
            logger.log_tool_progress('deep_scan', 96, 'Consolidating port scan results...')

            # Extract ports from Nmap
            if 'nmap' in network_results and network_results['nmap'].get('status') == 'completed':
                nmap_ports = network_results['nmap'].get('ports', [])
                all_results['ports'].extend(nmap_ports)
                logger.log_tool_progress('deep_scan', 96, f'Found {len(nmap_ports)} ports from nmap')

            # Extract ports from Metasploit (parse from raw output)
            if 'metasploit' in network_results and network_results['metasploit'].get('status') == 'completed':
                metasploit_output = network_results['metasploit'].get('raw_output', '')
                metasploit_ports = cls._extract_ports_from_metasploit(metasploit_output)
                all_results['ports'].extend(metasploit_ports)
                logger.log_tool_progress('deep_scan', 97, f'Found {len(metasploit_ports)} ports from metasploit')

            cls._update_scan_status(scan_id, 'Consolidating Vulnerabilities', 97)

            # Consolidate vulnerabilities from all tools
            logger.log_tool_progress('deep_scan', 97, 'Consolidating vulnerability results...')
            all_vulnerabilities = []

            # Network vulnerabilities
            for tool in ['nmap', 'openvas', 'metasploit']:
                if tool in network_results and network_results[tool].get('status') in ['completed', 'simulated']:
                    tool_vulns = network_results[tool].get('vulnerabilities', [])
                    for vuln in tool_vulns:
                        vuln['source_tool'] = tool
                        vuln['category'] = 'network'
                    all_vulnerabilities.extend(tool_vulns)
                    if tool_vulns:
                        logger.log_tool_progress('deep_scan', 84, f'Added {len(tool_vulns)} vulnerabilities from {tool}')

            # Web vulnerabilities
            for tool in ['nikto', 'sqlmap', 'dirb', 'gobuster', 'zap']:
                if tool in web_results and web_results[tool].get('status') == 'completed':
                    if tool == 'nikto':
                        tool_vulns = web_results[tool].get('vulnerabilities', [])
                    elif tool == 'sqlmap':
                        # Convert SQLMap injections to vulnerabilities
                        injections = web_results[tool].get('injections', [])
                        tool_vulns = []
                        for inj in injections:
                            tool_vulns.append({
                                'type': f"SQL Injection - {inj.get('type', 'Unknown')}",
                                'severity': 'high',
                                'description': f"SQL injection found in parameter: {inj.get('parameter', 'unknown')}",
                                'parameter': inj.get('parameter'),
                                'dbms': inj.get('dbms')
                            })
                    elif tool == 'zap':
                        # Convert ZAP alerts to vulnerabilities
                        alerts = web_results[tool].get('alerts', [])
                        tool_vulns = []
                        for alert in alerts:
                            severity = 'low'
                            if alert.get('risk', '').lower() == 'high':
                                severity = 'high'
                            elif alert.get('risk', '').lower() == 'medium':
                                severity = 'medium'

                            tool_vulns.append({
                                'type': alert.get('name', 'Security Issue'),
                                'severity': severity,
                                'description': alert.get('description', 'No description available')
                            })
                    else:
                        tool_vulns = []

                    for vuln in tool_vulns:
                        vuln['source_tool'] = tool
                        vuln['category'] = 'web'
                    all_vulnerabilities.extend(tool_vulns)
                    if tool_vulns:
                        logger.log_tool_progress('deep_scan', 98, f'Added {len(tool_vulns)} vulnerabilities from {tool}')

            all_results['vulnerabilities'] = all_vulnerabilities

            cls._update_scan_status(scan_id, 'Calculating Summary', 98)

            # Calculate final summary
            logger.log_tool_progress('deep_scan', 98, 'Calculating final summary...')
            total_ports = len(all_results['ports'])
            open_ports = len([p for p in all_results['ports'] if p.get('state') == 'open'])
            total_vulns = len(all_vulnerabilities)
            high_count = len([v for v in all_vulnerabilities if v.get('severity') == 'high'])
            medium_count = len([v for v in all_vulnerabilities if v.get('severity') == 'medium'])
            low_count = len([v for v in all_vulnerabilities if v.get('severity') == 'low'])

            all_results['summary'] = {
                'total_ports': total_ports,
                'open_ports': open_ports,
                'total_vulnerabilities': total_vulns,
                'high_severity': high_count,
                'medium_severity': medium_count,
                'low_severity': low_count,
                'scan_phases': 1,  # All tools run in parallel now
                'tools_executed': len(all_tools)
            }

            logger.log_tool_progress('deep_scan', 99, f'Summary: {total_ports} ports, {total_vulns} vulnerabilities')
            cls._update_scan_status(scan_id, 'Finalizing', 99)

            # Save vulnerabilities to vulnerabilities collection
            try:
                logger.log_tool_progress('deep_scan', 99, 'Saving vulnerabilities to database...')
                cls._update_scan_status(scan_id, 'Saving Results', 99)
                from app.services.vulnerability_service import VulnerabilityService

                scan_info = mongo.db.scans.find_one({'scan_id': scan_id})
                if scan_info:
                    target = scan_info.get('target', '')
                    user_id = scan_info.get('user_id', '')

                    # Combine all tool results for vulnerability service
                    combined_results = {**network_results, **web_results}

                    vulnerability_ids = VulnerabilityService.process_scan_vulnerabilities(
                        scan_id=scan_id,
                        scan_results=combined_results,
                        target=target,
                        user_id=user_id
                    )
                    logger.log_tool_progress('deep_scan', 98, f'Saved {len(vulnerability_ids)} vulnerabilities to database')
                    print(f"✅ Deep scan saved {len(vulnerability_ids)} vulnerabilities to vulnerabilities collection")
            except Exception as vuln_error:
                logger.log_tool_error('deep_scan', f'Error saving vulnerabilities: {vuln_error}')
                print(f"⚠️ Error saving vulnerabilities in deep scan: {vuln_error}")

            # Create flattened results for frontend compatibility
            logger.log_tool_progress('deep_scan', 99, 'Creating frontend-compatible results...')
            flattened_results = {
                # Include individual tool results at top level for frontend
                **network_results,  # nmap, openvas, metasploit
                **web_results,      # nikto, sqlmap, dirb, gobuster, zap
                # Include aggregated data
                'ports': all_results['ports'],
                'vulnerabilities': all_results['vulnerabilities'],
                'summary': all_results['summary'],
                # Keep original structure for detailed analysis
                'detailed_results': {
                    'network': network_results,
                    'web': web_results,
                    'summary': all_results['summary']
                }
            }

            # Update final results in database
            logger.log_tool_progress('deep_scan', 99, 'Finalizing scan results...')
            cls._update_scan_status(scan_id, 'Completing Deep Scan', 99)
            mongo.db.scans.update_one(
                {'scan_id': scan_id},
                {
                    '$set': {
                        'results': flattened_results,
                        'progress': 100
                    }
                }
            )

            logger.log_tool_result('deep_scan', 'completed', {
                'total_ports': total_ports,
                'total_vulnerabilities': total_vulns,
                'phases_completed': 1,  # All tools run in parallel
                'tools_executed': len(all_tools)
            })
            logger.log_scan_complete('completed', 0, all_results)

        except Exception as e:
            logger.log_tool_error('deep_scan', f'Deep scan failed: {str(e)}')
            cls._update_scan_status(scan_id, 'failed', 100)
            raise e

    @classmethod
    def _extract_ports_from_metasploit(cls, metasploit_output: str) -> List[Dict]:
        """Extract open ports from Metasploit output"""
        ports = []
        try:
            # Parse Metasploit output for open ports
            # Format: "*************:22 - TCP OPEN"
            import re
            port_pattern = r'(\d+\.\d+\.\d+\.\d+):(\d+) - TCP OPEN'
            matches = re.findall(port_pattern, metasploit_output)

            for ip, port in matches:
                ports.append({
                    'port': int(port),
                    'state': 'open',
                    'protocol': 'tcp',
                    'service': cls._guess_service_from_port(int(port)),
                    'source_tool': 'metasploit'
                })

            # Remove duplicates
            seen_ports = set()
            unique_ports = []
            for port_info in ports:
                port_key = port_info['port']
                if port_key not in seen_ports:
                    seen_ports.add(port_key)
                    unique_ports.append(port_info)

            return unique_ports
        except Exception as e:
            print(f"Error extracting ports from Metasploit: {e}")
            return []

    @classmethod
    def _guess_service_from_port(cls, port: int) -> str:
        """Guess service name from port number"""
        common_ports = {
            21: 'ftp', 22: 'ssh', 23: 'telnet', 25: 'smtp', 53: 'dns',
            80: 'http', 110: 'pop3', 143: 'imap', 443: 'https', 465: 'smtps',
            993: 'imaps', 995: 'pop3s', 3389: 'rdp', 5432: 'postgresql',
            3306: 'mysql', 1433: 'mssql', 6379: 'redis', 27017: 'mongodb'
        }
        return common_ports.get(port, 'unknown')

    @classmethod
    def _update_scan_status(cls, scan_id: str, current_tool: str, progress: int):
        """Update scan status in both memory and database"""
        with cls._lock:
            if scan_id in cls._active_scans:
                cls._active_scans[scan_id]['current_tool'] = current_tool
                cls._active_scans[scan_id]['progress'] = progress

        mongo.db.scans.update_one(
            {'scan_id': scan_id},
            {
                '$set': {
                    'current_tool': current_tool,
                    'progress': progress,
                    'last_update': datetime.utcnow().isoformat()
                }
            }
        )

    @classmethod
    def _update_tool_status(cls, scan_id: str, tool: str, status: str, progress: int):
        """Update individual tool status in database"""
        update_data = {
            f'tools_status.{tool}.status': status,
            f'tools_status.{tool}.progress': progress,
            'last_update': datetime.utcnow().isoformat()
        }

        if status == 'running':
            update_data[f'tools_status.{tool}.start_time'] = datetime.utcnow().isoformat()
        elif status in ['completed', 'failed']:
            update_data[f'tools_status.{tool}.end_time'] = datetime.utcnow().isoformat()

        mongo.db.scans.update_one(
            {'scan_id': scan_id},
            {'$set': update_data}
        )

    @classmethod
    def _run_tool_with_progress(cls, scan_id: str, tool: str, target: str, results: Dict):
        """Run a tool and update its individual progress"""
        try:
            # Update tool status to running
            cls._update_tool_status(scan_id, tool, 'running', 0)

            # Run the tool with progress tracking
            cls._run_tool_with_live_progress(scan_id, tool, target, results)

            # Update tool status to completed
            cls._update_tool_status(scan_id, tool, 'completed', 100)

        except Exception as e:
            # Update tool status to failed
            cls._update_tool_status(scan_id, tool, 'failed', 0)
            results[tool] = {'status': 'failed', 'error': str(e)}

    @classmethod
    def _run_tool_with_live_progress(cls, scan_id: str, tool: str, target: str, results: Dict):
        """Run individual tool with live progress updates"""
        logger = cls._active_scans[scan_id]['logger']

        try:
            logger.log_tool_start(tool, f"{tool} scan on {target}")

            # Get scan configuration from database
            scan_info = mongo.db.scans.find_one({'scan_id': scan_id})
            if not scan_info:
                raise Exception(f"Scan {scan_id} not found in database")

            scan_type = scan_info.get('scan_type', 'basic')
            scan_config = cls.SCAN_CONFIGS.get(scan_type, cls.SCAN_CONFIGS['basic'])

            # Log the scan configuration being used
            logger.log_tool_output(tool, f"Using {scan_type} scan configuration: {scan_config['description']}")
            logger.log_tool_output(tool, f"Intensity: {scan_config['intensity']}, Timeout: {scan_config['timeout']}s")

            # Format target URL for web tools
            web_tools = ['nikto', 'sqlmap', 'dirb', 'gobuster', 'zap']
            if tool in web_tools:
                # Ensure web tools get properly formatted URLs
                if not target.startswith(('http://', 'https://')):
                    web_target = f'http://{target}'
                    logger.log_tool_output(tool, f"Formatted target for web tool: {target} -> {web_target}")
                else:
                    web_target = target
            else:
                web_target = target

            # Execute tool based on type with scan configuration and progress tracking
            if tool == 'nikto':
                result = cls._run_nikto_with_progress(scan_id, web_target, logger, scan_config)
            elif tool == 'sqlmap':
                result = cls._run_sqlmap_with_progress(scan_id, web_target, logger, scan_config)
            elif tool == 'dirb':
                result = cls._run_dirb_with_progress(scan_id, web_target, logger, scan_config)
            elif tool == 'gobuster':
                result = cls._run_gobuster_with_progress(scan_id, web_target, logger, scan_config)
            elif tool == 'zap':
                result = cls._run_zap_with_progress(scan_id, web_target, logger, scan_config)
            elif tool == 'nmap':
                result = cls._run_nmap_with_progress(scan_id, target, logger, scan_config)
            elif tool == 'openvas':
                result = cls._run_openvas_with_progress(scan_id, target, logger, scan_config)
            elif tool == 'metasploit':
                result = cls._run_metasploit_with_progress(scan_id, target, logger, scan_config)
            else:
                result = {'status': 'skipped', 'reason': 'Tool not implemented'}

            # Store results
            results[tool] = result

            # Update tool completion status in database
            mongo.db.scans.update_one(
                {'scan_id': scan_id},
                {
                    '$set': {
                        f'results.{tool}': result
                    }
                }
            )

            logger.log_tool_result(tool, 'completed', result)

        except Exception as e:
            logger.log_tool_error(tool, str(e))
            results[tool] = {'status': 'failed', 'error': str(e)}

            mongo.db.scans.update_one(
                {'scan_id': scan_id},
                {
                    '$set': {
                        f'results.{tool}': results[tool]
                    }
                }
            )

    # Progress-enabled tool methods
    @classmethod
    def _run_sqlmap_with_progress(cls, scan_id: str, target: str, logger: ScanLogger, scan_config: Dict) -> Dict:
        """Run REAL SQLMap scan with live progress updates"""
        try:
            intensity = scan_config.get('intensity', 'low')
            cls._update_tool_status(scan_id, 'sqlmap', 'running', 10)
            logger.log_tool_progress('sqlmap', 10, f'Starting SQLMap scan ({intensity} intensity)...')

            # Build SQLMap command based on intensity
            if intensity == 'low':
                cmd = ['sqlmap', '-u', target, '--batch', '--level=1', '--risk=1', '--threads=1']
            elif intensity == 'medium':
                cmd = ['sqlmap', '-u', target, '--batch', '--level=2', '--risk=2', '--threads=2']
            elif intensity == 'high':
                cmd = ['sqlmap', '-u', target, '--batch', '--level=3', '--risk=3', '--threads=3']
            else:  # comprehensive
                cmd = ['sqlmap', '-u', target, '--batch', '--level=5', '--risk=3', '--threads=5', '--tamper=space2comment']

            cls._update_tool_status(scan_id, 'sqlmap', 'running', 20)
            logger.log_tool_output('sqlmap', f'Command: {" ".join(cmd)}')

            # Start process
            start_time = time.time()
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd='/tmp'
            )

            cls._update_tool_status(scan_id, 'sqlmap', 'running', 25)

            # Monitor progress with live updates
            injections = []
            while process.poll() is None:
                elapsed = time.time() - start_time
                if elapsed < 120:  # 2 minutes
                    progress = 25 + int((elapsed / 120) * 65)  # 25% to 90%
                    cls._update_tool_status(scan_id, 'sqlmap', 'running', progress)
                    logger.log_tool_progress('sqlmap', progress, f'Testing for SQL injection... ({int(elapsed)}s elapsed)')

                time.sleep(5)  # Update every 5 seconds

                # Timeout after 3 minutes for demo
                if elapsed > 180:
                    process.terminate()
                    logger.log_tool_output('sqlmap', 'Scan timeout after 3 minutes - completing with current results')
                    break

            # Get results
            stdout, stderr = process.communicate()
            cls._update_tool_status(scan_id, 'sqlmap', 'running', 90)
            logger.log_tool_progress('sqlmap', 90, 'Parsing results...')

            # Parse sqlmap output
            if stdout:
                output_lines = stdout.split('\n')
                for line in output_lines:
                    if 'Parameter:' in line and 'is vulnerable' in line:
                        injections.append({
                            'parameter': line.split('Parameter:')[1].split('is vulnerable')[0].strip(),
                            'type': 'SQL Injection',
                            'dbms': 'Unknown'
                        })

            cls._update_tool_status(scan_id, 'sqlmap', 'running', 95)

            return {
                'status': 'completed',
                'injections': injections,
                'raw_output': stdout,
                'vulnerabilities': [
                    {
                        'type': 'SQL Injection',
                        'severity': 'high',
                        'description': f'SQL injection found in parameter: {inj["parameter"]}',
                        'parameter': inj['parameter'],
                        'dbms': inj['dbms']
                    } for inj in injections
                ]
            }

        except Exception as e:
            cls._update_tool_status(scan_id, 'sqlmap', 'failed', 0)
            logger.log_tool_error('sqlmap', str(e))
            return {'status': 'failed', 'error': str(e)}

    @classmethod
    def _run_dirb_with_progress(cls, scan_id: str, target: str, logger: ScanLogger, scan_config: Dict) -> Dict:
        """Run REAL Dirb scan with live progress updates"""
        try:
            intensity = scan_config.get('intensity', 'low')
            cls._update_tool_status(scan_id, 'dirb', 'running', 10)
            logger.log_tool_progress('dirb', 10, f'Starting Dirb directory scan ({intensity} intensity)...')

            # Build Dirb command based on intensity
            if intensity == 'low':
                wordlist = '/usr/share/dirb/wordlists/small.txt'
            elif intensity == 'medium':
                wordlist = '/usr/share/dirb/wordlists/common.txt'
            else:  # high or comprehensive
                wordlist = '/usr/share/dirb/wordlists/big.txt'

            cmd = ['dirb', target, wordlist, '-w']
            cls._update_tool_status(scan_id, 'dirb', 'running', 20)
            logger.log_tool_output('dirb', f'Command: {" ".join(cmd)}')

            # Start process
            start_time = time.time()
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            cls._update_tool_status(scan_id, 'dirb', 'running', 25)

            # Monitor progress with live updates
            directories = []
            while process.poll() is None:
                elapsed = time.time() - start_time
                if elapsed < 120:  # 2 minutes
                    progress = 25 + int((elapsed / 120) * 65)  # 25% to 90%
                    cls._update_tool_status(scan_id, 'dirb', 'running', progress)
                    logger.log_tool_progress('dirb', progress, f'Scanning directories... ({int(elapsed)}s elapsed)')

                time.sleep(5)  # Update every 5 seconds

                # Timeout after 2 minutes for small wordlist
                if elapsed > 120:
                    process.terminate()
                    logger.log_tool_output('dirb', 'Scan timeout after 2 minutes - completing with current results')
                    break

            # Get results
            stdout, stderr = process.communicate()
            cls._update_tool_status(scan_id, 'dirb', 'running', 90)
            logger.log_tool_progress('dirb', 90, 'Parsing results...')

            # Parse dirb output
            if stdout:
                output_lines = stdout.split('\n')
                for line in output_lines:
                    if '==> DIRECTORY:' in line:
                        directory = line.split('==> DIRECTORY:')[1].strip()
                        directories.append({'path': directory, 'type': 'directory'})
                    elif '+ ' in line and 'http' in line:
                        parts = line.split()
                        if len(parts) >= 2:
                            path = parts[1]
                            directories.append({'path': path, 'type': 'file'})

            cls._update_tool_status(scan_id, 'dirb', 'running', 95)

            return {
                'status': 'completed',
                'directories': directories,
                'raw_output': stdout,
                'found_paths': len(directories)
            }

        except Exception as e:
            cls._update_tool_status(scan_id, 'dirb', 'failed', 0)
            logger.log_tool_error('dirb', str(e))
            return {'status': 'failed', 'error': str(e)}

    @classmethod
    def _run_gobuster_with_progress(cls, scan_id: str, target: str, logger: ScanLogger, scan_config: Dict) -> Dict:
        """Run REAL GoBuster scan with live progress updates"""
        try:
            intensity = scan_config.get('intensity', 'low')
            cls._update_tool_status(scan_id, 'gobuster', 'running', 10)
            logger.log_tool_progress('gobuster', 10, f'Starting GoBuster directory scan ({intensity} intensity)...')

            # Build GoBuster command based on intensity
            if intensity == 'low':
                wordlist = '/usr/share/wordlists/dirb/small.txt'
                threads = '10'
            elif intensity == 'medium':
                wordlist = '/usr/share/wordlists/dirb/common.txt'
                threads = '20'
            else:  # high or comprehensive
                wordlist = '/usr/share/wordlists/dirb/big.txt'
                threads = '50'

            cmd = ['gobuster', 'dir', '-u', target, '-w', wordlist, '-t', threads, '-q']
            cls._update_tool_status(scan_id, 'gobuster', 'running', 20)
            logger.log_tool_output('gobuster', f'Command: {" ".join(cmd)}')

            # Start process
            start_time = time.time()
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            cls._update_tool_status(scan_id, 'gobuster', 'running', 25)

            # Monitor progress with live updates
            found_paths = []
            while process.poll() is None:
                elapsed = time.time() - start_time
                if elapsed < 90:  # 1.5 minutes
                    progress = 25 + int((elapsed / 90) * 65)  # 25% to 90%
                    cls._update_tool_status(scan_id, 'gobuster', 'running', progress)
                    logger.log_tool_progress('gobuster', progress, f'Brute forcing directories... ({int(elapsed)}s elapsed)')

                time.sleep(5)  # Update every 5 seconds

                # Timeout after 2 minutes
                if elapsed > 120:
                    process.terminate()
                    logger.log_tool_output('gobuster', 'Scan timeout after 2 minutes - completing with current results')
                    break

            # Get results
            stdout, stderr = process.communicate()
            cls._update_tool_status(scan_id, 'gobuster', 'running', 90)
            logger.log_tool_progress('gobuster', 90, 'Parsing results...')

            # Parse gobuster output
            if stdout:
                output_lines = stdout.split('\n')
                for line in output_lines:
                    if line.startswith('/'):
                        parts = line.split()
                        if len(parts) >= 2:
                            path = parts[0]
                            status_code = parts[1].strip('()')
                            found_paths.append({
                                'path': path,
                                'status_code': status_code,
                                'type': 'directory' if path.endswith('/') else 'file'
                            })

            cls._update_tool_status(scan_id, 'gobuster', 'running', 95)

            return {
                'status': 'completed',
                'directories': found_paths,
                'raw_output': stdout,
                'found_paths': len(found_paths)
            }

        except Exception as e:
            cls._update_tool_status(scan_id, 'gobuster', 'failed', 0)
            logger.log_tool_error('gobuster', str(e))
            return {'status': 'failed', 'error': str(e)}

    @classmethod
    def _run_nmap_with_progress(cls, scan_id: str, target: str, logger: ScanLogger, scan_config: Dict) -> Dict:
        """Run REAL Nmap scan with live progress updates"""
        try:
            cls._update_tool_status(scan_id, 'nmap', 'running', 10)
            logger.log_tool_progress('nmap', 10, 'Starting Nmap port scan...')

            # Simulate progress updates during scan
            for i in range(10, 90, 10):
                cls._update_tool_status(scan_id, 'nmap', 'running', i)
                time.sleep(2)  # Simulate scan time
                logger.log_tool_progress('nmap', i, f'Scanning ports... {i}% complete')

            # Call the original nmap method
            result = cls._run_nmap(target, logger, scan_config)
            cls._update_tool_status(scan_id, 'nmap', 'running', 95)
            return result

        except Exception as e:
            cls._update_tool_status(scan_id, 'nmap', 'failed', 0)
            logger.log_tool_error('nmap', str(e))
            return {'status': 'failed', 'error': str(e)}

    @classmethod
    def _run_nikto_with_progress(cls, scan_id: str, target: str, logger: ScanLogger, scan_config: Dict) -> Dict:
        """Run REAL Nikto scan with live progress updates"""
        try:
            cls._update_tool_status(scan_id, 'nikto', 'running', 10)
            logger.log_tool_progress('nikto', 10, 'Starting Nikto web vulnerability scan...')

            # Simulate progress updates during scan
            for i in range(10, 90, 15):
                cls._update_tool_status(scan_id, 'nikto', 'running', i)
                time.sleep(3)  # Simulate scan time
                logger.log_tool_progress('nikto', i, f'Scanning web vulnerabilities... {i}% complete')

            # Call the original nikto method
            result = cls._run_nikto(target, logger, scan_config)
            cls._update_tool_status(scan_id, 'nikto', 'running', 95)
            return result

        except Exception as e:
            cls._update_tool_status(scan_id, 'nikto', 'failed', 0)
            logger.log_tool_error('nikto', str(e))
            return {'status': 'failed', 'error': str(e)}

    @classmethod
    def _run_zap_with_progress(cls, scan_id: str, target: str, logger: ScanLogger, scan_config: Dict) -> Dict:
        """Run REAL ZAP scan with live progress updates"""
        try:
            cls._update_tool_status(scan_id, 'zap', 'running', 10)
            logger.log_tool_progress('zap', 10, 'Starting ZAP security scan...')

            # Simulate progress updates during scan
            for i in range(10, 90, 20):
                cls._update_tool_status(scan_id, 'zap', 'running', i)
                time.sleep(4)  # Simulate scan time
                logger.log_tool_progress('zap', i, f'ZAP security scanning... {i}% complete')

            # Call the original zap method
            result = cls._run_zap(target, logger, scan_config)
            cls._update_tool_status(scan_id, 'zap', 'running', 95)
            return result

        except Exception as e:
            cls._update_tool_status(scan_id, 'zap', 'failed', 0)
            logger.log_tool_error('zap', str(e))
            return {'status': 'failed', 'error': str(e)}

    @classmethod
    def _run_openvas_with_progress(cls, scan_id: str, target: str, logger: ScanLogger, scan_config: Dict) -> Dict:
        """Run REAL OpenVAS scan with live progress updates"""
        try:
            cls._update_tool_status(scan_id, 'openvas', 'running', 10)
            logger.log_tool_progress('openvas', 10, 'Starting OpenVAS vulnerability scan...')

            # Simulate progress updates during scan
            for i in range(10, 90, 10):
                cls._update_tool_status(scan_id, 'openvas', 'running', i)
                time.sleep(5)  # Simulate scan time
                logger.log_tool_progress('openvas', i, f'OpenVAS vulnerability scanning... {i}% complete')

            # Call the original openvas method
            result = cls._run_openvas(target, logger, scan_config)
            cls._update_tool_status(scan_id, 'openvas', 'running', 95)
            return result

        except Exception as e:
            cls._update_tool_status(scan_id, 'openvas', 'failed', 0)
            logger.log_tool_error('openvas', str(e))
            return {'status': 'failed', 'error': str(e)}

    @classmethod
    def _run_metasploit_with_progress(cls, scan_id: str, target: str, logger: ScanLogger, scan_config: Dict) -> Dict:
        """Run REAL Metasploit scan with live progress updates"""
        try:
            cls._update_tool_status(scan_id, 'metasploit', 'running', 10)
            logger.log_tool_progress('metasploit', 10, 'Starting Metasploit exploitation scan...')

            # Simulate progress updates during scan
            for i in range(10, 90, 15):
                cls._update_tool_status(scan_id, 'metasploit', 'running', i)
                time.sleep(3)  # Simulate scan time
                logger.log_tool_progress('metasploit', i, f'Metasploit scanning... {i}% complete')

            # Call the original metasploit method
            result = cls._run_metasploit(target, logger, scan_config)
            cls._update_tool_status(scan_id, 'metasploit', 'running', 95)
            return result

        except Exception as e:
            cls._update_tool_status(scan_id, 'metasploit', 'failed', 0)
            logger.log_tool_error('metasploit', str(e))
            return {'status': 'failed', 'error': str(e)}

    @classmethod
    def _complete_scan(cls, scan_id: str, status: str, error: str = None):
        """Mark scan as completed and cleanup"""
        end_time = datetime.utcnow()
        
        with cls._lock:
            if scan_id in cls._active_scans:
                scan_info = cls._active_scans[scan_id]
                duration = (end_time - scan_info['start_time']).total_seconds()
                scan_info['status'] = status
                scan_info['end_time'] = end_time
                
                # Log completion
                scan_info['logger'].log_scan_complete(status, duration)
                
                # Remove from active scans after a delay (keep for a while for status queries)
                def cleanup_later():
                    time.sleep(300)  # Keep for 5 minutes
                    with cls._lock:
                        cls._active_scans.pop(scan_id, None)
                        cls._scan_threads.pop(scan_id, None)
                        cls._scan_processes.pop(scan_id, None)
                
                threading.Thread(target=cleanup_later, daemon=True).start()
        
        # Update database
        update_data = {
            'status': status,
            'end_time': end_time.isoformat(),
            'progress': 100 if status == 'completed' else 0,
            'current_tool': None  # Clear current tool when scan completes
        }
        if error:
            update_data['error'] = error
        
        mongo.db.scans.update_one(
            {'scan_id': scan_id},
            {'$set': update_data}
        )
        
        print(f"🏁 Scan {scan_id} completed with status: {status}")
    
    @classmethod
    def get_scan_status(cls, scan_id: str) -> Optional[Dict]:
        """Get current scan status (from memory or database)"""
        # First check active scans in memory
        with cls._lock:
            if scan_id in cls._active_scans:
                scan_info = cls._active_scans[scan_id]
                return {
                    'scan_id': scan_id,
                    'status': scan_info['status'],
                    'current_tool': scan_info.get('current_tool'),
                    'progress': scan_info.get('progress', 0),
                    'start_time': scan_info['start_time'].isoformat(),
                    'is_active': True
                }
        
        # If not in memory, check database
        try:
            scan = mongo.db.scans.find_one({'scan_id': scan_id})
            if scan:
                return {
                    'scan_id': scan_id,
                    'status': scan.get('status'),
                    'current_tool': scan.get('current_tool'),
                    'progress': scan.get('progress', 0),
                    'start_time': scan.get('start_time'),
                    'end_time': scan.get('end_time'),
                    'is_active': False
                }
        except Exception as e:
            print(f"Error getting scan status: {e}")
        
        return None
    
    @classmethod
    def stop_scan(cls, scan_id: str) -> bool:
        """Stop a running scan"""
        try:
            with cls._lock:
                if scan_id in cls._active_scans:
                    scan_info = cls._active_scans[scan_id]
                    scan_info['status'] = 'stopping'
                    
                    # Stop any running processes
                    if scan_id in cls._scan_processes:
                        for process in cls._scan_processes[scan_id]:
                            try:
                                process.terminate()
                                time.sleep(1)
                                if process.poll() is None:
                                    process.kill()
                            except:
                                pass
                    
                    # Mark as stopped
                    cls._complete_scan(scan_id, 'stopped')
                    return True
            
            # Also update database if scan not in memory
            mongo.db.scans.update_one(
                {'scan_id': scan_id},
                {'$set': {'status': 'stopped', 'end_time': datetime.utcnow().isoformat()}}
            )
            return True
            
        except Exception as e:
            print(f"Error stopping scan: {e}")
            return False
    
    @classmethod
    def get_active_scans(cls) -> List[Dict]:
        """Get list of all active scans"""
        active_scans = []
        
        with cls._lock:
            for scan_id, scan_info in cls._active_scans.items():
                active_scans.append({
                    'scan_id': scan_id,
                    'status': scan_info['status'],
                    'current_tool': scan_info.get('current_tool'),
                    'progress': scan_info.get('progress', 0),
                    'start_time': scan_info['start_time'].isoformat(),
                    'config': scan_info['config']
                })
        
        return active_scans
    
    @classmethod
    def cleanup_old_scans(cls, hours: int = 24):
        """Cleanup old completed scans from memory"""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        with cls._lock:
            to_remove = []
            for scan_id, scan_info in cls._active_scans.items():
                if (scan_info.get('end_time', datetime.utcnow()) < cutoff_time and 
                    scan_info['status'] in ['completed', 'failed', 'stopped']):
                    to_remove.append(scan_id)
            
            for scan_id in to_remove:
                cls._active_scans.pop(scan_id, None)
                cls._scan_threads.pop(scan_id, None)
                cls._scan_processes.pop(scan_id, None)
        
        print(f"🧹 Cleaned up {len(to_remove)} old scans from memory")
        return len(to_remove)
