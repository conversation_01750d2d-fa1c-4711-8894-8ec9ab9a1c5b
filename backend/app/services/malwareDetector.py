"""
MalwareDetector - Classe utilitaire pour l'analyse technique de malware
Fournit des fonctionnalités avancées d'analyse PE, détection de packers, et analyse comportementale
"""

import os
import re
import json
import math
import hashlib
import warnings
from collections import defaultdict
from typing import Dict, List, Any, Optional, Tuple

# Core imports with error handling
try:
    import pefile
    PEFILE_AVAILABLE = True
except ImportError:
    PEFILE_AVAILABLE = False
    print("Warning: pefile module not found. PE analysis will be limited.")

try:
    import magic
    MAGIC_AVAILABLE = True
except ImportError:
    MAGIC_AVAILABLE = False
    print("Warning: python-magic is not installed. File type detection will be limited.")

try:
    import oletools.olevba3 as olevba
    OLETOOLS_AVAILABLE = True
except ImportError:
    OLETOOLS_AVAILABLE = False
    print("Warning: oletools is not installed. Office document analysis will be limited.")

try:
    import yara
    YARA_AVAILABLE = True
except ImportError:
    YARA_AVAILABLE = False
    print("Warning: yara-python is not installed. YARA rule matching will be disabled.")

# Suppress warnings
warnings.filterwarnings("ignore")

# Load packer and section databases
def load_packer_db():
    """Load packer and section databases from the hybrid-packing-detection project."""
    try:
        base_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '../utils','db')
        
        # Load packer signatures
        with open(os.path.join(base_path, 'packers.json'), 'r', encoding='utf-8') as f:
            packer_map = json.load(f)['packers']
        
        # Load known sections
        with open(os.path.join(base_path, 'sections.json'), 'r', encoding='utf-8') as f:
            known_sections = json.load(f)['sections']['known']
        
        return packer_map, known_sections
    except Exception as e:
        print(f"[!] Warning: Could not load packer databases: {e}")
        return {}, []

# Initialize packer detection databases
PACKER_MAP, KNOWN_SECTIONS = load_packer_db()

# Suspicious API sequences for unpacking detection
UNPACKING_SEQUENCES = [
    ['VirtualAlloc', 'WriteProcessMemory', 'CreateThread'],
    ['VirtualAlloc', 'VirtualProtect', 'CreateThread'],
    ['LoadLibrary', 'GetProcAddress', 'VirtualAlloc', 'VirtualProtect'],
    ['NtAllocateVirtualMemory', 'NtWriteVirtualMemory', 'NtCreateThreadEx']
]

# Individual suspicious APIs with their threat scores
SUSPICIOUS_APIS = {
    # Memory manipulation
    'VirtualAlloc': 3, 'VirtualProtect': 4, 'VirtualAllocEx': 3,
    'VirtualProtectEx': 4, 'WriteProcessMemory': 5, 'ReadProcessMemory': 3,
    'NtAllocateVirtualMemory': 3, 'NtProtectVirtualMemory': 4, 'NtWriteVirtualMemory': 5,
    
    # Thread manipulation
    'CreateRemoteThread': 7, 'CreateThread': 5, 'NtCreateThreadEx': 7,
    'RtlCreateUserThread': 6, 'QueueUserAPC': 4, 'SetWindowsHookEx': 3,
    
    # Process manipulation
    'OpenProcess': 3, 'DebugActiveProcess': 4, 'TerminateProcess': 3,
    'CreateProcessInternalW': 3, 'CreateProcessAsUserW': 4,
    
    # DLL/Function loading
    'LoadLibraryA': 2, 'LoadLibraryW': 2, 'GetProcAddress': 2,
    'GetModuleHandleA': 2, 'GetModuleHandleW': 2,
    
    # Anti-debugging
    'IsDebuggerPresent': 3, 'CheckRemoteDebuggerPresent': 3,
    'OutputDebugStringA': 2, 'OutputDebugStringW': 2,
    
    # Cryptography
    'CryptAcquireContext': 2, 'CryptCreateHash': 2, 'CryptDecrypt': 3,
    'CryptEncrypt': 3, 'CryptGenKey': 2, 'CryptGenRandom': 2
}

# YARA rules for pattern matching
YARA_RULES = """
rule suspicious_behavior {
    meta:
        description = "Detects suspicious Windows API calls"
        author = "MalwareDetector"
    strings:
        $create_process = {6A ?? 68 ?? ?? ?? ?? 68 ?? ?? ?? ?? 6A ?? 6A ?? 6A ?? 6A ?? 6A ?? 6A ?? 68 ?? ?? ?? ?? FF 15 ?? ?? ?? ??}
        $virtual_alloc = {6A ?? 68 ?? ?? ?? ?? 6A ?? 6A ?? 6A ?? 6A ?? FF 15 ?? ?? ?? ??}
        $load_library = {68 ?? ?? ?? ?? FF 15 ?? ?? ?? ??}
        $get_proc_address = {68 ?? ?? ?? ?? 50 FF 15 ?? ?? ?? ??}
        $reg_open_key = {68 ?? ?? ?? ?? 6A ?? 6A ?? 8D ?? ?? ?? ?? ?? 50 FF 15 ?? ?? ?? ??}
    condition:
        any of them
}
"""

# Compile YARA rules
yara_rules = None
if YARA_AVAILABLE:
    try:
        yara_rules = yara.compile(source=YARA_RULES)
    except Exception as e:
        print(f"Warning: Failed to compile YARA rules: {e}")
        YARA_AVAILABLE = False

class MalwareDetector:
    """
    Classe utilitaire pour l'analyse technique avancée de malware.
    
    Cette classe fournit des fonctionnalités spécialisées pour l'analyse PE,
    la détection de packers, l'analyse comportementale et l'extraction d'IOCs.
    """
    
    def __init__(self, yara_rules=None):
        """
        Initialise le détecteur de malware avec des règles YARA optionnelles.
        
        Args:
            yara_rules: Règles YARA pré-compilées pour la détection de patterns
        """
        self.yara_rules = yara_rules or yara_rules
        self.packer_map = PACKER_MAP
        self.known_sections = KNOWN_SECTIONS
        self.suspicious_apis = SUSPICIOUS_APIS
        self.unpacking_sequences = UNPACKING_SEQUENCES
        
        # Patterns regex pour IOCs
        self.ip_pattern = re.compile(
            r'\b(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b'
        )
        self.domain_pattern = re.compile(
            r'\b(?!-)(?:(?!\d+\.\d+\.\d+\.\d+)(?:[a-zA-Z0-9][a-zA-Z0-9\-]{0,61}[a-zA-Z0-9]?\.)+[a-zA-Z]{2,})\b'
        )
        self.url_pattern = re.compile(
            r'https?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        )
        self.email_pattern = re.compile(
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        )
        
        print("🔧 MalwareDetector initialized")

    def get_file_type(self, file_path: str) -> str:
        """Détermine le type de fichier en utilisant python-magic ou l'extension."""
        if MAGIC_AVAILABLE:
            try:
                file_type = magic.Magic(mime=True)
                return file_type.from_file(file_path)
            except Exception as e:
                print(f"Error determining file type with magic: {e}")
        
        # Fallback vers l'extension de fichier
        ext = os.path.splitext(file_path)[1].lower()
        if ext in ['.exe', '.dll', '.sys']:
            return 'application/x-dosexec'
        elif ext in ['.doc', '.docx', '.docm']:
            return 'application/msword'
        elif ext in ['.xls', '.xlsx', '.xlsm']:
            return 'application/vnd.ms-excel'
        elif ext in ['.ppt', '.pptx', '.pptm']:
            return 'application/vnd.ms-powerpoint'
        elif ext in ['.pdf']:
            return 'application/pdf'
        return 'application/octet-stream'

    def calculate_hashes(self, file_path: str) -> Optional[Dict[str, str]]:
        """Calcule les hashes MD5, SHA-1, et SHA-256 du fichier."""
        try:
            hashes = {}
            with open(file_path, 'rb') as f:
                file_data = f.read()
                
            hashes['md5'] = hashlib.md5(file_data).hexdigest()
            hashes['sha1'] = hashlib.sha1(file_data).hexdigest()
            hashes['sha256'] = hashlib.sha256(file_data).hexdigest()
            return hashes
        except Exception as e:
            print(f"Error calculating hashes: {e}")
            return None

    def calculate_entropy(self, data: bytes) -> float:
        """Calcule l'entropie de Shannon d'un tableau de bytes."""
        if not data:
            return 0.0
        
        entropy = 0.0
        data_len = len(data)
        freq_table = defaultdict(int)
        
        # Compter la fréquence de chaque valeur de byte
        for byte in data:
            freq_table[byte] += 1
        
        # Calculer l'entropie
        for count in freq_table.values():
            if count > 0:
                prob = count / data_len
                entropy -= prob * math.log2(prob)
                
        return entropy

    def extract_strings(self, data: bytes, min_length: int = 4) -> List[Tuple[str, str]]:
        """Extrait les strings ASCII et Unicode des données binaires."""
        strings = []

        # Strings ASCII
        pattern = b'[\x20-\x7E]{%d,}' % min_length
        for match in re.finditer(pattern, data):
            try:
                s = match.group(0).decode('ascii')
                strings.append(('ascii', s))
            except UnicodeDecodeError:
                pass

        # Strings Unicode (UTF-16-LE)
        try:
            utf16_data = data.decode('utf-16-le', errors='ignore').encode('utf-16-le')
            for match in re.finditer(b'(?:[\x20-\x7E]\x00){%d,}' % min_length, utf16_data):
                try:
                    s = match.group(0).decode('utf-16-le')
                    strings.append(('unicode', s))
                except UnicodeDecodeError:
                    pass
        except Exception:
            pass

        return strings

    def detect_packer(self, pe) -> Optional[str]:
        """Détecte les packers basés sur les noms de sections."""
        if not PEFILE_AVAILABLE:
            return None

        matches = set()
        for section in pe.sections:
            try:
                section_name = section.Name.decode('utf-8', errors='ignore').strip('\x00').lower()
                for key, value in self.packer_map.items():
                    if key.lower() in section_name:
                        matches.add(value)
            except Exception as e:
                print(f"[!] Error checking section for packer: {e}")
        return ", ".join(matches) if matches else None

    def analyze_sections(self, pe) -> Dict[str, Any]:
        """Analyse les sections PE pour détecter les signes de packing."""
        if not PEFILE_AVAILABLE:
            return {}

        results = {
            'sections': [],
            'suspicious_sections': {},
            'high_entropy_sections': [],
            'total_entropy': 0.0,
            'section_count': 0
        }

        # Calculer l'entropie pour chaque section
        total_size = 0
        weighted_entropy = 0.0

        for section in pe.sections:
            try:
                section_name = section.Name.decode('utf-8', errors='ignore').strip('\x00')
                section_data = section.get_data()
                section_size = len(section_data)
                if section_size == 0:
                    continue

                entropy = self.calculate_entropy(section_data)

                section_info = {
                    'name': section_name,
                    'virtual_size': section.Misc_VirtualSize,
                    'raw_size': section.SizeOfRawData,
                    'entropy': entropy,
                    'characteristics': section.Characteristics
                }

                # Vérifier les noms de sections suspects
                if section_name not in self.known_sections:
                    reasons = []
                    if section_name.strip('.').lower() not in [s.strip('.').lower() for s in self.known_sections]:
                        reasons.append('Unknown section name')

                    if entropy > 7.0:
                        reasons.append('High entropy (possible encryption/compression)')
                        results['high_entropy_sections'].append(section_name)

                    if reasons:
                        results['suspicious_sections'][section_name] = reasons

                results['sections'].append(section_info)

                # Calculer l'entropie pondérée
                total_size += section_size
                weighted_entropy += entropy * section_size

            except Exception as e:
                print(f"[!] Error analyzing section: {e}")

        if total_size > 0:
            results['total_entropy'] = weighted_entropy / total_size
        results['section_count'] = len(results['sections'])

        return results

    def analyze_imports(self, pe) -> Dict[str, Any]:
        """Analyse les imports PE pour détecter les APIs suspectes."""
        if not PEFILE_AVAILABLE:
            return {}

        results = {
            'imports': [],
            'suspicious_imports': [],
            'import_analysis': {
                'total_imports': 0,
                'suspicious_count': 0,
                'unpacking_sequences_found': []
            }
        }

        all_imports = []

        try:
            if hasattr(pe, 'DIRECTORY_ENTRY_IMPORT'):
                for entry in pe.DIRECTORY_ENTRY_IMPORT:
                    dll_name = entry.dll.decode('utf-8', errors='ignore')
                    dll_imports = []

                    for imp in entry.imports:
                        if imp.name:
                            func_name = imp.name.decode('utf-8', errors='ignore')
                            dll_imports.append(func_name)
                            all_imports.append(func_name)

                            # Vérifier si c'est une API suspecte
                            if func_name in self.suspicious_apis:
                                threat_score = self.suspicious_apis[func_name]
                                results['suspicious_imports'].append({
                                    'dll': dll_name,
                                    'function': func_name,
                                    'threat_score': threat_score,
                                    'description': self._get_api_description(func_name)
                                })

                    results['imports'].append({
                        'dll': dll_name,
                        'functions': dll_imports
                    })

                # Analyser les séquences de déballage
                for sequence in self.unpacking_sequences:
                    if all(api in all_imports for api in sequence):
                        results['import_analysis']['unpacking_sequences_found'].append(sequence)

                results['import_analysis']['total_imports'] = len(all_imports)
                results['import_analysis']['suspicious_count'] = len(results['suspicious_imports'])

        except Exception as e:
            print(f"[!] Error analyzing imports: {e}")

        return results

    def _get_api_description(self, api_name: str) -> str:
        """Retourne une description de l'API suspecte."""
        descriptions = {
            'VirtualAlloc': 'Allocates virtual memory',
            'VirtualProtect': 'Changes memory protection',
            'WriteProcessMemory': 'Writes to another process memory',
            'CreateRemoteThread': 'Creates thread in another process',
            'LoadLibrary': 'Loads a DLL',
            'GetProcAddress': 'Gets function address from DLL',
            'IsDebuggerPresent': 'Checks for debugger presence',
            'CreateProcess': 'Creates a new process'
        }
        return descriptions.get(api_name, 'Suspicious API function')

    def analyze_with_yara(self, file_path: str) -> List[Dict[str, Any]]:
        """Analyse le fichier avec les règles YARA."""
        if not self.yara_rules:
            return []

        matches = []
        try:
            yara_matches = self.yara_rules.match(file_path)

            for match in yara_matches:
                match_info = {
                    'rule': match.rule,
                    'namespace': match.namespace,
                    'tags': match.tags,
                    'meta': match.meta,
                    'strings': []
                }

                # Ajouter les strings matchées
                for string in match.strings:
                    match_info['strings'].append({
                        'identifier': string.identifier,
                        'instances': len(string.instances)
                    })

                matches.append(match_info)

        except Exception as e:
            print(f"YARA analysis failed: {e}")

        return matches

    def extract_iocs(self, file_path: str) -> Dict[str, List[str]]:
        """Extrait les indicateurs de compromission (IOCs) du fichier."""
        iocs = {
            'ip_addresses': [],
            'domains': [],
            'urls': [],
            'email_addresses': []
        }

        try:
            with open(file_path, 'rb') as f:
                content = f.read()

            # Convertir en texte pour l'analyse regex
            text_content = content.decode('utf-8', errors='ignore')

            # Extraire les adresses IP
            ip_matches = self.ip_pattern.findall(text_content)
            # Filtrer les IPs privées et invalides
            valid_ips = []
            for ip in ip_matches:
                if self._is_valid_public_ip(ip):
                    valid_ips.append(ip)
            iocs['ip_addresses'] = list(set(valid_ips))

            # Extraire les domaines
            domain_matches = self.domain_pattern.findall(text_content)
            # Filtrer les faux positifs
            filtered_domains = []
            for domain in domain_matches:
                if self._is_valid_domain(domain):
                    filtered_domains.append(domain)
            iocs['domains'] = list(set(filtered_domains))

            # Extraire les URLs
            url_matches = self.url_pattern.findall(text_content)
            iocs['urls'] = list(set(url_matches))

            # Extraire les adresses email
            email_matches = self.email_pattern.findall(text_content)
            iocs['email_addresses'] = list(set(email_matches))

        except Exception as e:
            print(f"IOC extraction failed: {e}")

        return iocs

    def _is_valid_public_ip(self, ip: str) -> bool:
        """Vérifie si une IP est publique et valide."""
        try:
            parts = [int(x) for x in ip.split('.')]

            # Vérifier les plages privées
            if parts[0] == 10:  # 10.0.0.0/8
                return False
            if parts[0] == 172 and 16 <= parts[1] <= 31:  # **********/12
                return False
            if parts[0] == 192 and parts[1] == 168:  # ***********/16
                return False
            if parts[0] == 127:  # *********/8 (localhost)
                return False
            if parts[0] == 169 and parts[1] == 254:  # ***********/16 (link-local)
                return False

            return True
        except:
            return False

    def _is_valid_domain(self, domain: str) -> bool:
        """Vérifie si un domaine semble valide et non un faux positif."""
        # Filtrer les domaines trop courts
        if len(domain) < 4:
            return False

        # Exclure les extensions de fichiers communes
        file_extensions = ['.exe', '.dll', '.txt', '.log', '.tmp', '.dat', '.bin']
        if any(domain.lower().endswith(ext) for ext in file_extensions):
            return False

        # Exclure les domaines avec trop de chiffres
        digit_ratio = sum(c.isdigit() for c in domain) / len(domain)
        if digit_ratio > 0.5:
            return False

        # Exclure les domaines avec des patterns suspects
        if domain.count('.') > 5:  # Trop de sous-domaines
            return False

        return True

    def analyze_pe_advanced(self, file_path: str) -> Dict[str, Any]:
        """Effectue une analyse PE avancée complète."""
        if not PEFILE_AVAILABLE:
            return {'error': 'pefile not available'}

        try:
            pe = pefile.PE(file_path)

            results = {
                'basic_info': self._get_pe_basic_info(pe),
                'sections': self.analyze_sections(pe),
                'imports': self.analyze_imports(pe),
                'packer_detected': self.detect_packer(pe),
                'anomalies': self._detect_pe_anomalies(pe)
            }

            pe.close()
            return results

        except Exception as e:
            return {'error': f'PE analysis failed: {e}'}

    def _get_pe_basic_info(self, pe) -> Dict[str, Any]:
        """Extrait les informations de base du fichier PE."""
        try:
            return {
                'architecture': 'x64' if pe.PE_TYPE == 0x20b else 'x86',
                'subsystem': pe.OPTIONAL_HEADER.Subsystem,
                'compilation_timestamp': pe.FILE_HEADER.TimeDateStamp,
                'entry_point': hex(pe.OPTIONAL_HEADER.AddressOfEntryPoint),
                'image_base': hex(pe.OPTIONAL_HEADER.ImageBase),
                'file_alignment': pe.OPTIONAL_HEADER.FileAlignment,
                'section_alignment': pe.OPTIONAL_HEADER.SectionAlignment
            }
        except Exception as e:
            print(f"Error getting PE basic info: {e}")
            return {}

    def _detect_pe_anomalies(self, pe) -> List[str]:
        """Détecte les anomalies dans le fichier PE."""
        anomalies = []

        try:
            # Vérifier l'entry point
            if pe.OPTIONAL_HEADER.AddressOfEntryPoint == 0:
                anomalies.append("Entry point is zero")

            # Vérifier les sections
            if len(pe.sections) < 2:
                anomalies.append("Too few sections")
            elif len(pe.sections) > 10:
                anomalies.append("Too many sections")

            # Vérifier les alignements
            if pe.OPTIONAL_HEADER.FileAlignment < 0x200:
                anomalies.append("Unusual file alignment")

            # Vérifier les timestamps
            import time
            current_time = int(time.time())
            if pe.FILE_HEADER.TimeDateStamp > current_time:
                anomalies.append("Future compilation timestamp")

        except Exception as e:
            anomalies.append(f"Error detecting anomalies: {e}")

        return anomalies
