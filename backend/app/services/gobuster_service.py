import subprocess
import json
import uuid
import tempfile
import os
from datetime import datetime
from typing import Dict, List, Optional

class GoBusterService:
    """Service for GoBuster integration"""

    def __init__(self):
        self.gobuster_path = self._find_gobuster_path()
        self.temp_dir = tempfile.gettempdir()
        self.wordlists = self._find_wordlists()

    def _find_gobuster_path(self) -> str:
        """Find the path to GoBuster"""
        possible_paths = [
            '/usr/bin/gobuster',
            '/usr/local/bin/gobuster',
            'gobuster'
        ]
        
        for path in possible_paths:
            try:
                result = subprocess.run([path, '--help'], capture_output=True, text=True, timeout=10)
                if result.returncode == 0 and 'gobuster' in result.stdout.lower():
                    return path
            except (subprocess.TimeoutExpired, FileNotFoundError):
                continue
        
        return 'gobuster'  # Fallback
    
    def _find_wordlists(self) -> Dict[str, str]:
        """Find available wordlists"""
        wordlists = {}
        possible_locations = [
            '/usr/share/wordlists/',
            '/usr/share/seclists/',
            '/opt/SecLists/',
            '/usr/share/dirb/wordlists/'
        ]

        common_wordlists = {
            'common': 'common.txt',
            'directory-list-2.3-medium': 'directory-list-2.3-medium.txt',
            'directory-list-2.3-small': 'directory-list-2.3-small.txt',
            'big': 'big.txt',
            'raft-medium-directories': 'raft-medium-directories.txt'
        }

        for location in possible_locations:
            if os.path.exists(location):
                for name, filename in common_wordlists.items():
                    # Search in multiple subdirectories
                    possible_paths = [
                        os.path.join(location, filename),
                        os.path.join(location, 'Discovery', 'Web-Content', filename),
                        os.path.join(location, 'dirb', filename),
                        os.path.join(location, 'dirbuster', filename)
                    ]

                    for full_path in possible_paths:
                        if os.path.exists(full_path):
                            wordlists[name] = full_path
                            break

        # Fallbacks if no wordlist found
        if not wordlists:
            wordlists = {
                'common': '/usr/share/wordlists/common.txt',
                'directory-list-2.3-medium': '/usr/share/wordlists/directory-list-2.3-medium.txt'
            }

        return wordlists
    
    def is_available(self) -> bool:
        """Check if GoBuster is available"""
        try:
            result = subprocess.run([self.gobuster_path, '--help'],
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0 and 'gobuster' in result.stdout.lower()
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False

    def scan_directories(self, target_url: str, wordlist: str = None, options: List[str] = None) -> Dict:
        """
        Launch a directory scan with GoBuster

        Args:
            target_url: URL to scan
            wordlist: Path to wordlist or predefined wordlist name
            options: Custom GoBuster options

        Returns:
            Dictionary with scan results
        """
        scan_id = str(uuid.uuid4())

        # Determine which wordlist to use
        if not wordlist:
            wordlist = self.wordlists.get('common', '/usr/share/wordlists/common.txt')
        elif wordlist in self.wordlists:
            wordlist = self.wordlists[wordlist]

        # Build GoBuster command
        cmd = [
            self.gobuster_path,
            'dir',
            '-u', target_url,
            '-w', wordlist,
            '-q',  # Silent mode
            '--no-error'  # Don't display errors
        ]

        # Add custom options
        if options:
            cmd.extend(options)
        
        try:
            # Execute GoBuster
            start_time = datetime.utcnow()
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30 minutes max
            end_time = datetime.utcnow()

            # Parse results
            directories = self._parse_gobuster_output(result.stdout, target_url)
            
            if result.returncode == 0:
                return {
                    'status': 'completed',
                    'scan_id': scan_id,
                    'target': target_url,
                    'wordlist': wordlist,
                    'start_time': start_time.isoformat(),
                    'end_time': end_time.isoformat(),
                    'scan_time': (end_time - start_time).total_seconds(),
                    'command': ' '.join(cmd),
                    'directories': directories,
                    'raw_output': result.stdout
                }
            else:
                return {
                    'status': 'failed',
                    'scan_id': scan_id,
                    'target': target_url,
                    'error': result.stderr,
                    'command': ' '.join(cmd)
                }
                
        except subprocess.TimeoutExpired:
            return {
                'status': 'timeout',
                'scan_id': scan_id,
                'target': target_url,
                'error': 'GoBuster scan timed out after 30 minutes',
                'command': ' '.join(cmd)
            }
        except Exception as e:
            return {
                'status': 'error',
                'scan_id': scan_id,
                'target': target_url,
                'error': str(e),
                'command': ' '.join(cmd)
            }
    
    def scan_files(self, target_url: str, wordlist: str = None, extensions: List[str] = None) -> Dict:
        """File scan with specific extensions"""
        options = []
        if extensions:
            options.extend(['-x', ','.join(extensions)])

        return self.scan_directories(target_url, wordlist, options)

    def _parse_gobuster_output(self, gobuster_output: str, base_url: str) -> List[Dict]:
        """Parse directories found from GoBuster output"""
        directories = []
        lines = gobuster_output.split('\n')
        
        for line in lines:
            line = line.strip()
            
            # Format GoBuster: /admin (Status: 200) [Size: 1234]
            if line and not line.startswith('=') and '(Status:' in line:
                parts = line.split('(Status:')
                if len(parts) >= 2:
                    path = parts[0].strip()
                    status_part = parts[1].split(')')[0].strip()
                    status_code = int(status_part) if status_part.isdigit() else 200
                    
                    # Extraire la taille si disponible
                    size = None
                    if '[Size:' in parts[1]:
                        size_part = parts[1].split('[Size:')[1].split(']')[0].strip()
                        if size_part.isdigit():
                            size = int(size_part)
                    
                    # Déterminer le type (fichier ou répertoire)
                    item_type = 'directory' if path.endswith('/') else 'file'
                    
                    directories.append({
                        'path': path,
                        'url': base_url.rstrip('/') + path,
                        'type': item_type,
                        'status': status_code,
                        'size': size,
                        'tool': 'gobuster'
                    })
        
        return directories
    
    def quick_scan(self, target_url: str) -> Dict:
        """Scan rapide avec wordlist petite"""
        wordlist = self.wordlists.get('directory-list-2.3-small', 'common')
        return self.scan_directories(target_url, wordlist, ['-t', '50'])  # 50 threads
    
    def comprehensive_scan(self, target_url: str) -> Dict:
        """Scan complet avec grande wordlist"""
        wordlist = self.wordlists.get('directory-list-2.3-medium', 'big')
        return self.scan_directories(target_url, wordlist, ['-t', '30'])  # 30 threads
    
    def stealth_scan(self, target_url: str) -> Dict:
        """Scan furtif avec peu de threads"""
        return self.scan_directories(target_url, 'common', ['-t', '5', '--delay', '1s'])
    
    def scan_with_extensions(self, target_url: str, extensions: List[str] = None) -> Dict:
        """Scan avec extensions de fichiers spécifiques"""
        if not extensions:
            extensions = ['php', 'html', 'txt', 'js', 'css', 'xml', 'json']
        
        return self.scan_files(target_url, 'common', extensions)
    
    def get_available_wordlists(self) -> Dict[str, str]:
        """Retourner les wordlists disponibles"""
        return self.wordlists
