import subprocess
import json
import uuid
import tempfile
import os
import re
from datetime import datetime
from typing import Dict, List, Optional

class NmapService:
    """Service pour l'intégration avec Nmap"""
    
    def __init__(self):
        self.nmap_path = self._find_nmap_path()
        self.temp_dir = tempfile.gettempdir()
    
    def _find_nmap_path(self) -> str:
        """Trouver le chemin vers Nmap"""
        possible_paths = [
            '/usr/bin/nmap',
            '/usr/local/bin/nmap',
            'nmap'
        ]
        
        for path in possible_paths:
            try:
                result = subprocess.run([path, '--version'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    return path
            except (subprocess.TimeoutExpired, FileNotFoundError):
                continue
        
        return 'nmap'  # Fallback
    
    def is_available(self) -> bool:
        """Vérifier si Nmap est disponible"""
        try:
            result = subprocess.run([self.nmap_path, '--version'], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    def scan_target(self, target: str, options: List[str] = None, ports: str = None, timeout: int = 600) -> Dict:
        """
        Lancer un scan Nmap sur une cible
        
        Args:
            target: IP ou hostname à scanner
            options: Options de scan Nmap
            ports: Ports à scanner (ex: "22,80,443" ou "1-1000")
            
        Returns:
            Dictionnaire avec les résultats du scan
        """
        scan_id = str(uuid.uuid4())
        output_file = os.path.join(self.temp_dir, f"nmap_scan_{scan_id}.xml")
        
        # Construire la commande Nmap (sans sudo pour éviter les problèmes de mot de passe)
        cmd = [self.nmap_path]

        # Ajouter les options personnalisées (modifier pour scan non-privilégié)
        if options:
            # Remplacer les scans privilégiés par des alternatives non-privilégiées
            modified_options = []
            for opt in options.split() if isinstance(options, str) else options:
                if opt == '-sS':  # SYN scan -> TCP connect scan
                    modified_options.append('-sT')
                elif opt == '-O':  # OS detection -> skip (requires root)
                    continue
                else:
                    modified_options.append(opt)
            cmd.extend(modified_options)
        else:
            # Scan TCP connect par défaut (ne nécessite pas de privilèges root)
            cmd.extend(['-sT'])
        
        # Ajouter les ports si spécifiés
        if ports:
            cmd.extend(['-p', ports])
        
        # Format de sortie XML pour parsing facile
        cmd.extend(['-oX', output_file])
        
        # Ajouter la cible
        cmd.append(target)
        
        try:
            # Exécuter Nmap
            start_time = datetime.utcnow()
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout)
            end_time = datetime.utcnow()
            
            # Parser les résultats
            if result.returncode == 0:
                ports_found = self._parse_nmap_ports(result.stdout)
                vulnerabilities = self._parse_nmap_vulnerabilities(result.stdout)
                
                # Nettoyer le fichier temporaire
                if os.path.exists(output_file):
                    os.remove(output_file)
                
                return {
                    'status': 'completed',
                    'scan_id': scan_id,
                    'target': target,
                    'start_time': start_time.isoformat(),
                    'end_time': end_time.isoformat(),
                    'scan_time': (end_time - start_time).total_seconds(),
                    'command': ' '.join(cmd),
                    'ports': ports_found,
                    'vulnerabilities': vulnerabilities,
                    'raw_output': result.stdout
                }
            else:
                return {
                    'status': 'failed',
                    'scan_id': scan_id,
                    'target': target,
                    'error': result.stderr,
                    'command': ' '.join(cmd)
                }
                
        except subprocess.TimeoutExpired:
            return {
                'status': 'timeout',
                'scan_id': scan_id,
                'target': target,
                'error': 'Nmap scan timed out after 10 minutes',
                'command': ' '.join(cmd)
            }
        except Exception as e:
            return {
                'status': 'error',
                'scan_id': scan_id,
                'target': target,
                'error': str(e),
                'command': ' '.join(cmd)
            }
    
    def _parse_nmap_ports(self, nmap_output: str) -> List[Dict]:
        """Parser les ports ouverts depuis la sortie nmap"""
        ports = []
        lines = nmap_output.split('\n')
        
        for line in lines:
            # Chercher les lignes de ports (format: 22/tcp open ssh)
            if '/tcp' in line or '/udp' in line:
                parts = line.split()
                if len(parts) >= 3:
                    port_info = parts[0].split('/')
                    if len(port_info) >= 2:
                        port = {
                            "port": int(port_info[0]) if port_info[0].isdigit() else port_info[0],
                            "protocol": port_info[1],
                            "state": parts[1] if len(parts) > 1 else "unknown",
                            "service": parts[2] if len(parts) > 2 else "unknown"
                        }
                        
                        # Ajouter la version si disponible
                        if len(parts) > 3:
                            port["version"] = " ".join(parts[3:])
                        
                        ports.append(port)
        
        return ports
    
    def _parse_nmap_vulnerabilities(self, nmap_output: str) -> List[Dict]:
        """Parser les vulnérabilités depuis la sortie nmap"""
        vulnerabilities = []
        lines = nmap_output.split('\n')
        
        for i, line in enumerate(lines):
            if 'VULNERABLE' in line.upper() or 'CVE-' in line:
                vuln = {
                    "name": line.strip(),
                    "severity": "medium",  # Par défaut
                    "description": line.strip(),
                    "tool": "nmap",
                    "cve": None
                }
                
                # Extraire CVE si présent
                if 'CVE-' in line:
                    cve_match = re.search(r'CVE-\d{4}-\d+', line)
                    if cve_match:
                        vuln["cve"] = cve_match.group()
                        vuln["id"] = cve_match.group()
                
                # Déterminer la sévérité basée sur des mots-clés
                if any(keyword in line.upper() for keyword in ['CRITICAL', 'HIGH', 'SEVERE']):
                    vuln["severity"] = "high"
                elif any(keyword in line.upper() for keyword in ['LOW', 'INFO']):
                    vuln["severity"] = "low"
                
                vulnerabilities.append(vuln)
        
        return vulnerabilities
    
    def quick_scan(self, target: str) -> Dict:
        """Scan rapide des ports les plus communs"""
        return self.scan_target(target, ['-T4', '-F'])
    
    def stealth_scan(self, target: str) -> Dict:
        """Scan furtif (SYN scan)"""
        return self.scan_target(target, ['-sS', '-T1'])
    
    def aggressive_scan(self, target: str) -> Dict:
        """Scan agressif avec détection OS et services"""
        return self.scan_target(target, ['-A', '-T4'])
    
    def vulnerability_scan(self, target: str) -> Dict:
        """Scan de vulnérabilités avec scripts NSE"""
        return self.scan_target(target, ['--script=vuln', '-sV'])
