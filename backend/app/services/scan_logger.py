import logging
import os
from datetime import datetime
from typing import Dict, List
from app.extensions import mongo

class ScanLogger:
    """Service pour logger les détails d'exécution des scans"""
    
    def __init__(self, scan_id: str):
        self.scan_id = scan_id
        self.logger = self._setup_logger()
    
    def _setup_logger(self):
        """Configurer le logger pour ce scan"""
        logger = logging.getLogger(f'scan_{self.scan_id}')
        logger.setLevel(logging.DEBUG)
        
        # Éviter les doublons de handlers
        if logger.handlers:
            return logger
        
        # Créer le répertoire de logs s'il n'existe pas
        log_dir = 'logs/scans'
        os.makedirs(log_dir, exist_ok=True)
        
        # Handler pour fichier
        file_handler = logging.FileHandler(f'{log_dir}/scan_{self.scan_id}.log')
        file_handler.setLevel(logging.DEBUG)
        
        # Handler pour console
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Format des logs
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def log_scan_start(self, category: str, scan_type: str, target: str, tools: List[str]):
        """Logger le début du scan"""
        message = f"🚀 SCAN STARTED - Category: {category}, Type: {scan_type}, Target: {target}, Tools: {', '.join(tools)}"
        self.logger.info(message)
        self._save_log_to_db('info', 'scan_start', message)
    
    def log_tool_start(self, tool: str, command: str = None):
        """Logger le début d'un outil"""
        message = f"🔧 TOOL START - {tool.upper()}"
        if command:
            message += f" - Command: {command}"
        self.logger.info(message)
        self._save_log_to_db('info', 'tool_start', message, {'tool': tool, 'command': command})
    
    def log_tool_progress(self, tool: str, progress: int, details: str = None):
        """Logger la progression d'un outil"""
        message = f"📊 TOOL PROGRESS - {tool.upper()}: {progress}%"
        if details:
            message += f" - {details}"
        self.logger.info(message)
        self._save_log_to_db('info', 'tool_progress', message, {'tool': tool, 'progress': progress})
    
    def log_tool_output(self, tool: str, output: str, output_type: str = 'stdout'):
        """Logger la sortie d'un outil"""
        # Limiter la taille des logs pour éviter la surcharge
        truncated_output = output[:1000] + "..." if len(output) > 1000 else output
        message = f"📝 TOOL OUTPUT - {tool.upper()} ({output_type}): {truncated_output}"
        self.logger.debug(message)
        self._save_log_to_db('debug', 'tool_output', message, {
            'tool': tool, 
            'output_type': output_type, 
            'output': truncated_output,
            'full_output_length': len(output)
        })
    
    def log_tool_result(self, tool: str, status: str, results: Dict = None):
        """Logger le résultat d'un outil"""
        message = f"✅ TOOL RESULT - {tool.upper()}: {status}"
        if results:
            if 'ports' in results:
                message += f" - Found {len(results['ports'])} ports"
            if 'vulnerabilities' in results:
                message += f" - Found {len(results['vulnerabilities'])} vulnerabilities"
            if 'directories' in results:
                message += f" - Found {len(results['directories'])} directories"
        
        self.logger.info(message)
        self._save_log_to_db('info', 'tool_result', message, {'tool': tool, 'status': status, 'results_summary': results})
    
    def log_tool_error(self, tool: str, error: str, error_type: str = 'execution'):
        """Logger une erreur d'outil"""
        message = f"❌ TOOL ERROR - {tool.upper()}: {error}"
        self.logger.error(message)
        self._save_log_to_db('error', 'tool_error', message, {'tool': tool, 'error': error, 'error_type': error_type})
    
    def log_scan_complete(self, status: str, duration: float, summary: Dict = None):
        """Logger la fin du scan"""
        message = f"🏁 SCAN COMPLETE - Status: {status}, Duration: {duration:.1f}s"
        if summary:
            message += f" - Summary: {summary}"
        self.logger.info(message)
        self._save_log_to_db('info', 'scan_complete', message, {'status': status, 'duration': duration, 'summary': summary})
    
    def log_custom(self, level: str, event_type: str, message: str, data: Dict = None):
        """Logger un message personnalisé"""
        log_method = getattr(self.logger, level.lower(), self.logger.info)
        log_method(message)
        self._save_log_to_db(level, event_type, message, data)
    
    def _save_log_to_db(self, level: str, event_type: str, message: str, data: Dict = None):
        """Sauvegarder le log dans MongoDB"""
        try:
            log_entry = {
                'scan_id': self.scan_id,
                'timestamp': datetime.utcnow().isoformat(),
                'level': level,
                'event_type': event_type,
                'message': message,
                'data': data or {}
            }

            # Ajouter à la collection des logs
            result = mongo.db.scan_logs.insert_one(log_entry.copy())
            print(f"📋 Log saved to DB: {log_entry['message'][:50]}... (ID: {result.inserted_id})")

            # Créer une copie sans ObjectId pour le scan document
            clean_log_entry = {
                'scan_id': self.scan_id,
                'timestamp': log_entry['timestamp'],
                'level': level,
                'event_type': event_type,
                'message': message,
                'data': data or {}
            }

            # Aussi mettre à jour le scan avec le dernier log
            update_result = mongo.db.scans.update_one(
                {'scan_id': self.scan_id},
                {
                    '$set': {'last_log': clean_log_entry},
                    '$push': {
                        'recent_logs': {
                            '$each': [clean_log_entry],
                            '$slice': -50  # Garder seulement les 50 derniers logs
                        }
                    }
                }
            )

            if update_result.modified_count > 0:
                print(f"📋 Scan document updated with log")
            else:
                print(f"⚠️ Scan document not found or not updated: {self.scan_id}")

        except Exception as e:
            print(f"❌ Failed to save log to database: {e}")
            self.logger.error(f"Failed to save log to database: {e}")
    
    @staticmethod
    def get_scan_logs(scan_id: str, limit: int = 100, level: str = None) -> List[Dict]:
        """Récupérer les logs d'un scan"""
        try:
            query = {'scan_id': scan_id}
            if level:
                query['level'] = level

            logs = list(mongo.db.scan_logs.find(
                query,
                {'_id': 0}  # Exclure l'ID MongoDB
            ).sort('timestamp', -1).limit(limit))

            # Nettoyer les logs pour éviter les problèmes de sérialisation
            cleaned_logs = []
            for log in logs:
                cleaned_log = {
                    'scan_id': log.get('scan_id'),
                    'timestamp': log.get('timestamp'),
                    'level': log.get('level'),
                    'event_type': log.get('event_type'),
                    'message': log.get('message'),
                    'data': log.get('data', {})
                }
                cleaned_logs.append(cleaned_log)

            return cleaned_logs
        except Exception as e:
            print(f"Error retrieving scan logs: {e}")
            return []
    
    @staticmethod
    def get_tool_logs(scan_id: str, tool: str, limit: int = 50) -> List[Dict]:
        """Récupérer les logs d'un outil spécifique"""
        try:
            logs = list(mongo.db.scan_logs.find(
                {
                    'scan_id': scan_id,
                    'data.tool': tool
                },
                {'_id': 0}
            ).sort('timestamp', -1).limit(limit))
            
            return logs
        except Exception as e:
            print(f"Error retrieving tool logs: {e}")
            return []
    
    @staticmethod
    def cleanup_old_logs(days: int = 7):
        """Nettoyer les anciens logs"""
        try:
            from datetime import timedelta
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            result = mongo.db.scan_logs.delete_many({
                'timestamp': {'$lt': cutoff_date.isoformat()}
            })
            
            print(f"Cleaned up {result.deleted_count} old log entries")
            return result.deleted_count
        except Exception as e:
            print(f"Error cleaning up logs: {e}")
            return 0
