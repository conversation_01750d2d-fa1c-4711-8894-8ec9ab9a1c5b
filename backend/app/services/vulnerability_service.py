"""
Service pour la gestion des vulnérabilités détectées lors des scans
"""

from typing import List, Dict, Optional
from app.models.vulnerability_model import (
    save_vulnerability_to_db,
    get_vulnerabilities_from_db,
    get_vulnerability_by_id,
    update_vulnerability_status,
    get_vulnerability_statistics,
    delete_vulnerability,
    get_vulnerabilities_by_scan,
    normalize_vulnerability_data
)


class VulnerabilityService:
    """Service pour gérer les vulnérabilités"""
    
    @staticmethod
    def process_scan_vulnerabilities(scan_id: str, scan_results: Dict, target: str, user_id: str = None) -> List[str]:
        """
        Traiter et sauvegarder toutes les vulnérabilités d'un scan
        
        Args:
            scan_id (str): ID du scan
            scan_results (dict): Résultats du scan contenant les vulnérabilités
            target (str): Cible du scan (IP ou URL)
            user_id (str): ID de l'utilisateur
        
        Returns:
            List[str]: Liste des IDs des vulnérabilités sauvegardées
        """
        saved_vulnerability_ids = []
        
        try:
            # Traiter les vulnérabilités de chaque outil
            tools_with_vulns = ['nmap', 'openvas', 'metasploit', 'nikto', 'sqlmap', 'dirb', 'gobuster', 'owasp_zap']
            
            for tool in tools_with_vulns:
                if tool in scan_results and isinstance(scan_results[tool], dict):
                    tool_results = scan_results[tool]
                    
                    # Récupérer les vulnérabilités de cet outil
                    vulnerabilities = tool_results.get('vulnerabilities', [])
                    
                    if isinstance(vulnerabilities, list):
                        for vuln in vulnerabilities:
                            if isinstance(vuln, dict):
                                # Normaliser les données de la vulnérabilité
                                normalized_vuln = normalize_vulnerability_data(vuln, tool, target)
                                
                                # Sauvegarder la vulnérabilité
                                vuln_id = save_vulnerability_to_db(
                                    normalized_vuln, 
                                    scan_id=scan_id, 
                                    user_id=user_id
                                )
                                
                                if vuln_id:
                                    saved_vulnerability_ids.append(vuln_id)
                                    print(f"✅ Saved vulnerability from {tool}: {normalized_vuln.get('name', 'Unknown')}")
            
            # Traiter aussi les vulnérabilités dans le résumé global
            if 'vulnerabilities' in scan_results:
                global_vulns = scan_results['vulnerabilities']
                if isinstance(global_vulns, list):
                    for vuln in global_vulns:
                        if isinstance(vuln, dict):
                            # Déterminer l'outil source
                            tool_source = vuln.get('tool', 'unknown')
                            
                            # Normaliser et sauvegarder
                            normalized_vuln = normalize_vulnerability_data(vuln, tool_source, target)
                            vuln_id = save_vulnerability_to_db(
                                normalized_vuln, 
                                scan_id=scan_id, 
                                user_id=user_id
                            )
                            
                            if vuln_id:
                                saved_vulnerability_ids.append(vuln_id)
                                print(f"✅ Saved global vulnerability: {normalized_vuln.get('name', 'Unknown')}")
            
            print(f"✅ Processed {len(saved_vulnerability_ids)} vulnerabilities for scan {scan_id}")
            return saved_vulnerability_ids
            
        except Exception as e:
            print(f"❌ Error processing vulnerabilities for scan {scan_id}: {str(e)}")
            return saved_vulnerability_ids
    
    @staticmethod
    def get_vulnerabilities(limit: int = 50, user_id: str = None, filters: Dict = None) -> List[Dict]:
        """
        Récupérer les vulnérabilités avec filtres
        
        Args:
            limit (int): Nombre maximum de vulnérabilités
            user_id (str): ID utilisateur pour filtrer (None pour admin)
            filters (dict): Filtres à appliquer
        
        Returns:
            List[Dict]: Liste des vulnérabilités
        """
        return get_vulnerabilities_from_db(limit=limit, user_id=user_id, filters=filters)
    
    @staticmethod
    def get_vulnerability(vulnerability_id: str, user_id: str = None) -> Optional[Dict]:
        """
        Récupérer une vulnérabilité spécifique
        
        Args:
            vulnerability_id (str): ID de la vulnérabilité
            user_id (str): ID utilisateur pour filtrer (None pour admin)
        
        Returns:
            Dict: Vulnérabilité ou None si non trouvée
        """
        return get_vulnerability_by_id(vulnerability_id, user_id=user_id)
    
    @staticmethod
    def update_status(vulnerability_id: str, status: str, notes: str = None, user_id: str = None) -> bool:
        """
        Mettre à jour le statut d'une vulnérabilité
        
        Args:
            vulnerability_id (str): ID de la vulnérabilité
            status (str): Nouveau statut
            notes (str): Notes optionnelles
            user_id (str): ID utilisateur pour filtrer (None pour admin)
        
        Returns:
            bool: True si mise à jour réussie
        """
        valid_statuses = ['detected', 'verified', 'false_positive', 'fixed']
        if status not in valid_statuses:
            print(f"❌ Invalid status: {status}. Valid statuses: {valid_statuses}")
            return False
        
        return update_vulnerability_status(vulnerability_id, status, notes, user_id)
    
    @staticmethod
    def get_statistics(user_id: str = None) -> Dict:
        """
        Obtenir les statistiques des vulnérabilités
        
        Args:
            user_id (str): ID utilisateur pour filtrer (None pour admin)
        
        Returns:
            Dict: Statistiques des vulnérabilités
        """
        return get_vulnerability_statistics(user_id=user_id)
    
    @staticmethod
    def delete_vulnerability(vulnerability_id: str, user_id: str = None) -> bool:
        """
        Supprimer une vulnérabilité
        
        Args:
            vulnerability_id (str): ID de la vulnérabilité
            user_id (str): ID utilisateur pour filtrer (None pour admin)
        
        Returns:
            bool: True si suppression réussie
        """
        return delete_vulnerability(vulnerability_id, user_id)
    
    @staticmethod
    def get_scan_vulnerabilities(scan_id: str, user_id: str = None) -> List[Dict]:
        """
        Récupérer toutes les vulnérabilités d'un scan
        
        Args:
            scan_id (str): ID du scan
            user_id (str): ID utilisateur pour filtrer (None pour admin)
        
        Returns:
            List[Dict]: Liste des vulnérabilités du scan
        """
        return get_vulnerabilities_by_scan(scan_id, user_id)
    
    @staticmethod
    def get_vulnerability_summary(user_id: str = None) -> Dict:
        """
        Obtenir un résumé des vulnérabilités avec des métriques utiles
        
        Args:
            user_id (str): ID utilisateur pour filtrer (None pour admin)
        
        Returns:
            Dict: Résumé des vulnérabilités
        """
        stats = get_vulnerability_statistics(user_id)
        
        # Calculer des métriques additionnelles
        total = stats.get('total', 0)
        critical_high = stats.get('critical', 0) + stats.get('high', 0)
        
        summary = {
            'total_vulnerabilities': total,
            'critical_high_count': critical_high,
            'severity_distribution': {
                'critical': stats.get('critical', 0),
                'high': stats.get('high', 0),
                'medium': stats.get('medium', 0),
                'low': stats.get('low', 0),
                'info': stats.get('info', 0)
            },
            'status_distribution': {
                'detected': stats.get('detected', 0),
                'verified': stats.get('verified', 0),
                'false_positive': stats.get('false_positive', 0),
                'fixed': stats.get('fixed', 0)
            },
            'risk_metrics': {
                'critical_high_percentage': round((critical_high / total * 100) if total > 0 else 0, 1),
                'unresolved_count': stats.get('detected', 0) + stats.get('verified', 0),
                'resolution_rate': round(((stats.get('fixed', 0) + stats.get('false_positive', 0)) / total * 100) if total > 0 else 0, 1)
            }
        }
        
        return summary
    
    @staticmethod
    def search_vulnerabilities(query: str, user_id: str = None, limit: int = 50) -> List[Dict]:
        """
        Rechercher des vulnérabilités par nom, description ou CVE
        
        Args:
            query (str): Terme de recherche
            user_id (str): ID utilisateur pour filtrer (None pour admin)
            limit (int): Nombre maximum de résultats
        
        Returns:
            List[Dict]: Liste des vulnérabilités correspondantes
        """
        # Créer des filtres de recherche
        search_filters = {}
        
        # Si la requête ressemble à un CVE
        if query.upper().startswith('CVE-'):
            search_filters['cve'] = query.upper()
        else:
            # Recherche dans le nom et la description (sera implémentée avec regex dans MongoDB)
            # Pour l'instant, on utilise le filtre de base
            pass
        
        return get_vulnerabilities_from_db(limit=limit, user_id=user_id, filters=search_filters)
    
    @staticmethod
    def get_top_vulnerabilities(user_id: str = None, limit: int = 10) -> List[Dict]:
        """
        Obtenir les vulnérabilités les plus critiques
        
        Args:
            user_id (str): ID utilisateur pour filtrer (None pour admin)
            limit (int): Nombre maximum de vulnérabilités
        
        Returns:
            List[Dict]: Liste des vulnérabilités les plus critiques
        """
        # Filtrer par sévérité critique et haute
        filters = {
            'severity': ['critical', 'high'],
            'status': ['detected', 'verified']  # Seulement les non résolues
        }
        
        return get_vulnerabilities_from_db(limit=limit, user_id=user_id, filters=filters)
