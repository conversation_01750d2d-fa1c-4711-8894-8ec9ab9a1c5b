import subprocess
import json
import uuid
import tempfile
import os
from datetime import datetime
from typing import Dict, List, Optional

class DirbService:
    """Service pour l'intégration avec Dirb"""
    
    def __init__(self):
        self.dirb_path = self._find_dirb_path()
        self.temp_dir = tempfile.gettempdir()
        self.wordlists = self._find_wordlists()
    
    def _find_dirb_path(self) -> str:
        """Trouver le chemin vers Dirb"""
        possible_paths = [
            '/usr/bin/dirb',
            '/usr/local/bin/dirb',
            'dirb'
        ]
        
        for path in possible_paths:
            try:
                result = subprocess.run([path], capture_output=True, text=True, timeout=5)
                # Dirb retourne un code d'erreur quand lancé sans arguments, mais affiche l'aide
                if 'DIRB' in result.stderr or 'DIRB' in result.stdout:
                    return path
            except (subprocess.TimeoutExpired, FileNotFoundError):
                continue
        
        return 'dirb'  # Fallback
    
    def _find_wordlists(self) -> Dict[str, str]:
        """Trouver les wordlists disponibles"""
        wordlists = {}
        possible_locations = [
            '/usr/share/dirb/wordlists/',
            '/usr/share/wordlists/dirb/',
            '/opt/dirb/wordlists/'
        ]
        
        common_wordlists = {
            'common': 'common.txt',
            'big': 'big.txt',
            'small': 'small.txt',
            'extensions_common': 'extensions_common.txt',
            'vulns': 'vulns/apache.txt'
        }
        
        for location in possible_locations:
            if os.path.exists(location):
                for name, filename in common_wordlists.items():
                    full_path = os.path.join(location, filename)
                    if os.path.exists(full_path):
                        wordlists[name] = full_path
                break
        
        # Fallbacks si aucune wordlist trouvée
        if not wordlists:
            wordlists = {
                'common': '/usr/share/dirb/wordlists/common.txt',
                'big': '/usr/share/dirb/wordlists/big.txt',
                'small': '/usr/share/dirb/wordlists/small.txt'
            }
        
        return wordlists
    
    def is_available(self) -> bool:
        """Vérifier si Dirb est disponible"""
        try:
            result = subprocess.run([self.dirb_path], capture_output=True, text=True, timeout=5)
            return 'DIRB' in result.stderr or 'DIRB' in result.stdout
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    def scan_target(self, target_url: str, wordlist: str = None, options: List[str] = None) -> Dict:
        """
        Lancer un scan Dirb sur une cible
        
        Args:
            target_url: URL à scanner
            wordlist: Chemin vers la wordlist ou nom de wordlist prédéfinie
            options: Options Dirb personnalisées
            
        Returns:
            Dictionnaire avec les résultats du scan
        """
        scan_id = str(uuid.uuid4())
        output_file = os.path.join(self.temp_dir, f"dirb_scan_{scan_id}.txt")
        
        # Déterminer la wordlist à utiliser
        if not wordlist:
            wordlist = self.wordlists.get('common', '/usr/share/dirb/wordlists/common.txt')
        elif wordlist in self.wordlists:
            wordlist = self.wordlists[wordlist]
        
        # Construire la commande Dirb
        cmd = [self.dirb_path, target_url, wordlist, '-o', output_file]
        
        # Ajouter les options par défaut
        cmd.extend(['-w'])  # Ne pas arrêter sur les warnings
        
        # Ajouter les options personnalisées
        if options:
            cmd.extend(options)
        
        try:
            # Exécuter Dirb
            start_time = datetime.utcnow()
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30 minutes max
            end_time = datetime.utcnow()
            
            # Lire le fichier de sortie
            output_content = ""
            if os.path.exists(output_file):
                with open(output_file, 'r') as f:
                    output_content = f.read()
                os.remove(output_file)
            
            # Parser les résultats
            directories = self._parse_dirb_output(output_content or result.stdout)
            
            if result.returncode == 0 or directories:  # Dirb peut retourner un code d'erreur même en cas de succès
                return {
                    'status': 'completed',
                    'scan_id': scan_id,
                    'target': target_url,
                    'wordlist': wordlist,
                    'start_time': start_time.isoformat(),
                    'end_time': end_time.isoformat(),
                    'scan_time': (end_time - start_time).total_seconds(),
                    'command': ' '.join(cmd),
                    'directories': directories,
                    'raw_output': output_content or result.stdout
                }
            else:
                return {
                    'status': 'failed',
                    'scan_id': scan_id,
                    'target': target_url,
                    'error': result.stderr,
                    'command': ' '.join(cmd)
                }
                
        except subprocess.TimeoutExpired:
            return {
                'status': 'timeout',
                'scan_id': scan_id,
                'target': target_url,
                'error': 'Dirb scan timed out after 30 minutes',
                'command': ' '.join(cmd)
            }
        except Exception as e:
            return {
                'status': 'error',
                'scan_id': scan_id,
                'target': target_url,
                'error': str(e),
                'command': ' '.join(cmd)
            }
    
    def _parse_dirb_output(self, dirb_output: str) -> List[Dict]:
        """Parser les répertoires trouvés depuis la sortie Dirb"""
        directories = []
        lines = dirb_output.split('\n')
        
        for line in lines:
            line = line.strip()
            
            # Chercher les lignes avec des répertoires trouvés
            # Format typique: ==> DIRECTORY: http://example.com/admin/
            # Format typique: + http://example.com/admin (CODE:200|SIZE:1234)
            if '==>' in line and 'DIRECTORY:' in line:
                url = line.split('DIRECTORY:')[1].strip()
                directories.append({
                    'path': url.replace(url.split('/')[0] + '//' + url.split('/')[2], ''),
                    'url': url,
                    'type': 'directory',
                    'status': 200,  # Par défaut pour les répertoires
                    'tool': 'dirb'
                })
            
            elif line.startswith('+') and '(CODE:' in line:
                # Parser les fichiers trouvés
                parts = line.split('(CODE:')
                if len(parts) >= 2:
                    url = parts[0].replace('+', '').strip()
                    status_part = parts[1].split('|')[0].strip()
                    status_code = int(status_part.replace(')', '')) if status_part.replace(')', '').isdigit() else 200
                    
                    # Extraire la taille si disponible
                    size = None
                    if 'SIZE:' in parts[1]:
                        size_part = parts[1].split('SIZE:')[1].split(')')[0].strip()
                        if size_part.isdigit():
                            size = int(size_part)
                    
                    directories.append({
                        'path': url.replace(url.split('/')[0] + '//' + url.split('/')[2], '') if '://' in url else url,
                        'url': url,
                        'type': 'file',
                        'status': status_code,
                        'size': size,
                        'tool': 'dirb'
                    })
        
        return directories
    
    def quick_scan(self, target_url: str) -> Dict:
        """Scan rapide avec la wordlist commune"""
        return self.scan_target(target_url, 'small', ['-S'])  # Silent mode
    
    def comprehensive_scan(self, target_url: str) -> Dict:
        """Scan complet avec la grande wordlist"""
        return self.scan_target(target_url, 'big', ['-r'])  # Récursif
    
    def stealth_scan(self, target_url: str) -> Dict:
        """Scan furtif avec délais"""
        return self.scan_target(target_url, 'common', ['-z', '1000'])  # Délai de 1 seconde
    
    def get_available_wordlists(self) -> Dict[str, str]:
        """Retourner les wordlists disponibles"""
        return self.wordlists
