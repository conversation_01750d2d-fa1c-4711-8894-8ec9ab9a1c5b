"""
Service pour générer des rapports PDF des scans de pentesting
"""
import os
import json
from datetime import datetime
from io import BytesIO
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.colors import HexColor, black, white, red, orange, yellow, green
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.platypus.tableofcontents import TableOfContents
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from reportlab.pdfgen import canvas
from reportlab.lib import colors


class PDFReportService:
    """Service pour générer des rapports PDF des scans"""
    
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
    
    def _setup_custom_styles(self):
        """Configuration des styles personnalisés avec design moderne"""

        # Style pour le titre principal - Plus grand et moderne
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Title'],
            fontSize=32,
            spaceAfter=40,
            spaceBefore=20,
            textColor=HexColor('#6366f1'),
            alignment=TA_CENTER,
            fontName='Helvetica-Bold'
        ))

        # Style pour le sous-titre
        self.styles.add(ParagraphStyle(
            name='CustomSubtitle',
            parent=self.styles['Normal'],
            fontSize=16,
            spaceAfter=30,
            textColor=HexColor('#64748b'),
            alignment=TA_CENTER,
            fontName='Helvetica'
        ))

        # Style pour les titres de section - Avec fond coloré
        self.styles.add(ParagraphStyle(
            name='CustomHeading1',
            parent=self.styles['Heading1'],
            fontSize=20,
            spaceAfter=15,
            spaceBefore=25,
            textColor=white,
            backColor=HexColor('#4f46e5'),
            borderWidth=0,
            borderPadding=12,
            fontName='Helvetica-Bold',
            leftIndent=0,
            rightIndent=0
        ))

        # Style pour les sous-sections
        self.styles.add(ParagraphStyle(
            name='CustomHeading2',
            parent=self.styles['Heading2'],
            fontSize=16,
            spaceAfter=10,
            spaceBefore=15,
            textColor=HexColor('#1e293b'),
            fontName='Helvetica-Bold',
            borderWidth=0,
            borderColor=HexColor('#e2e8f0'),
            borderPadding=8,
            backColor=HexColor('#f8fafc')
        ))

        # Style pour les sous-sous-sections
        self.styles.add(ParagraphStyle(
            name='CustomHeading3',
            parent=self.styles['Normal'],
            fontSize=14,
            spaceAfter=8,
            spaceBefore=10,
            textColor=HexColor('#475569'),
            fontName='Helvetica-Bold'
        ))

        # Style pour le contenu normal - Plus lisible
        self.styles.add(ParagraphStyle(
            name='CustomNormal',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=8,
            textColor=HexColor('#334155'),
            fontName='Helvetica',
            leading=14
        ))

        # Style pour les informations importantes
        self.styles.add(ParagraphStyle(
            name='InfoBox',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=10,
            textColor=HexColor('#1e40af'),
            backColor=HexColor('#eff6ff'),
            borderWidth=1,
            borderColor=HexColor('#3b82f6'),
            borderPadding=10,
            fontName='Helvetica'
        ))

        # Styles pour les alertes - Plus visuels
        self.styles.add(ParagraphStyle(
            name='AlertCritical',
            parent=self.styles['Normal'],
            fontSize=12,
            spaceAfter=12,
            textColor=HexColor('#991b1b'),
            backColor=HexColor('#fef2f2'),
            borderWidth=2,
            borderColor=HexColor('#dc2626'),
            borderPadding=12,
            fontName='Helvetica-Bold'
        ))

        self.styles.add(ParagraphStyle(
            name='AlertHigh',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=10,
            textColor=HexColor('#dc2626'),
            backColor=HexColor('#fef2f2'),
            borderWidth=1,
            borderColor=HexColor('#fca5a5'),
            borderPadding=10,
            fontName='Helvetica'
        ))

        self.styles.add(ParagraphStyle(
            name='AlertMedium',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=10,
            textColor=HexColor('#d97706'),
            backColor=HexColor('#fffbeb'),
            borderWidth=1,
            borderColor=HexColor('#fcd34d'),
            borderPadding=10,
            fontName='Helvetica'
        ))

        self.styles.add(ParagraphStyle(
            name='AlertLow',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=10,
            textColor=HexColor('#059669'),
            backColor=HexColor('#f0fdf4'),
            borderWidth=1,
            borderColor=HexColor('#86efac'),
            borderPadding=10,
            fontName='Helvetica'
        ))

        # Style pour les codes et sorties techniques
        self.styles.add(ParagraphStyle(
            name='CodeBlock',
            parent=self.styles['Normal'],
            fontSize=9,
            spaceAfter=8,
            textColor=HexColor('#374151'),
            backColor=HexColor('#f9fafb'),
            borderWidth=1,
            borderColor=HexColor('#d1d5db'),
            borderPadding=8,
            fontName='Courier',
            leading=11
        ))

        # Style pour les statistiques
        self.styles.add(ParagraphStyle(
            name='StatNumber',
            parent=self.styles['Normal'],
            fontSize=24,
            spaceAfter=5,
            textColor=HexColor('#6366f1'),
            fontName='Helvetica-Bold',
            alignment=TA_CENTER
        ))

        self.styles.add(ParagraphStyle(
            name='StatLabel',
            parent=self.styles['Normal'],
            fontSize=10,
            spaceAfter=10,
            textColor=HexColor('#64748b'),
            fontName='Helvetica',
            alignment=TA_CENTER
        ))

    def generate_scan_report(self, scan_data):
        """
        Génère un rapport PDF pour un scan
        
        Args:
            scan_data: Données du scan depuis MongoDB
            
        Returns:
            BytesIO: Buffer contenant le PDF généré
        """
        buffer = BytesIO()
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        
        # Construire le contenu du rapport
        story = []
        
        # Page de titre
        story.extend(self._build_title_page(scan_data))
        story.append(PageBreak())
        
        # Résumé exécutif
        story.extend(self._build_executive_summary(scan_data))
        story.append(PageBreak())
        
        # Détails techniques
        story.extend(self._build_technical_details(scan_data))
        
        # Résultats par outil
        if scan_data.get('results'):
            story.append(PageBreak())
            story.extend(self._build_tool_results(scan_data))
        
        # Vulnérabilités détaillées
        if scan_data.get('results', {}).get('vulnerabilities'):
            story.append(PageBreak())
            story.extend(self._build_vulnerabilities_section(scan_data))
        
        # Ports ouverts
        if scan_data.get('results', {}).get('ports'):
            story.append(PageBreak())
            story.extend(self._build_ports_section(scan_data))
        
        # Recommandations
        story.append(PageBreak())
        story.extend(self._build_recommendations(scan_data))
        
        # Construire le PDF
        doc.build(story)
        buffer.seek(0)
        return buffer

    def _build_title_page(self, scan_data):
        """Construit une page de titre moderne et professionnelle"""
        story = []

        # Espacement initial
        story.append(Spacer(1, 80))

        # Logo et titre principal avec design moderne
        story.append(Paragraph("🛡️ PICA", self.styles['CustomTitle']))
        story.append(Paragraph("Plateforme de Cybersécurité Automatisée", self.styles['CustomSubtitle']))
        story.append(Spacer(1, 60))

        # Titre du rapport avec icône
        scan_type = scan_data.get('scan_type', 'Unknown').title()
        category = scan_data.get('category', 'Unknown').title()

        # Icônes par catégorie
        category_icons = {
            'network': '🌐',
            'web': '🔍',
            'vulnerability': '🛡️',
            'deep': '⚡'
        }

        icon = category_icons.get(category.lower(), '📊')
        story.append(Paragraph(f"{icon} Rapport de Sécurité", self.styles['CustomHeading1']))
        story.append(Paragraph(f"{category} Scan - {scan_type}", self.styles['CustomHeading2']))
        story.append(Spacer(1, 40))
        
        # Informations du scan dans un design moderne
        story.append(Paragraph("📋 Informations du Scan", self.styles['CustomHeading2']))
        story.append(Spacer(1, 15))

        # Statut avec couleur
        status = scan_data.get('status', 'N/A').upper()
        status_color = {
            'COMPLETED': HexColor('#059669'),
            'FAILED': HexColor('#dc2626'),
            'STOPPED': HexColor('#d97706'),
            'RUNNING': HexColor('#2563eb')
        }.get(status, HexColor('#6b7280'))

        scan_info = [
            ['🎯 Cible', scan_data.get('target', 'N/A')],
            ['📊 Type de scan', f"{category} - {scan_type}"],
            ['⚡ Statut', status],
            ['🕐 Date de début', self._format_datetime(scan_data.get('start_time'))],
            ['🏁 Date de fin', self._format_datetime(scan_data.get('end_time'))],
            ['⏱️ Durée', self._calculate_duration(scan_data)],
            ['👤 Utilisateur', scan_data.get('user_id', 'N/A')],
            ['🔑 ID du scan', scan_data.get('scan_id', 'N/A')]
        ]

        table = Table(scan_info, colWidths=[2.2*inch, 3.8*inch])
        table.setStyle(TableStyle([
            # En-tête avec fond coloré
            ('BACKGROUND', (0, 0), (0, -1), HexColor('#6366f1')),
            ('TEXTCOLOR', (0, 0), (0, -1), white),
            ('BACKGROUND', (1, 0), (1, -1), HexColor('#f8fafc')),
            ('TEXTCOLOR', (1, 0), (1, -1), HexColor('#1e293b')),

            # Alignement et police
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),

            # Bordures modernes
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#e2e8f0')),
            ('LINEWIDTH', (0, 0), (-1, -1), 0.5),

            # Espacement
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 12),
            ('RIGHTPADDING', (0, 0), (-1, -1), 12),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),

            # Couleur spéciale pour le statut
            ('TEXTCOLOR', (1, 2), (1, 2), status_color),
            ('FONTNAME', (1, 2), (1, 2), 'Helvetica-Bold'),
        ]))

        story.append(table)
        story.append(Spacer(1, 40))
        
        # Note de confidentialité
        story.append(Paragraph(
            "<b>CONFIDENTIEL</b><br/>Ce rapport contient des informations sensibles sur la sécurité. "
            "Il doit être traité avec la plus grande confidentialité.",
            self.styles['AlertHigh']
        ))
        
        return story

    def _build_executive_summary(self, scan_data):
        """Construit un résumé exécutif moderne avec statistiques visuelles"""
        story = []

        story.append(Paragraph("📊 Résumé Exécutif", self.styles['CustomHeading1']))
        story.append(Spacer(1, 20))

        # Statistiques générales
        results = scan_data.get('results', {})

        # Compter les vulnérabilités par sévérité
        vulnerabilities = results.get('vulnerabilities', [])
        vuln_stats = {'high': 0, 'medium': 0, 'low': 0, 'info': 0}

        for vuln in vulnerabilities:
            severity = vuln.get('severity', 'info').lower()
            if severity in vuln_stats:
                vuln_stats[severity] += 1
            else:
                vuln_stats['info'] += 1

        # Section des statistiques principales avec design moderne
        story.append(Paragraph("🎯 Vue d'ensemble", self.styles['CustomHeading2']))
        story.append(Spacer(1, 10))

        # Créer des cartes de statistiques visuelles
        stats_cards = [
            ['📊 Métrique', '🔢 Valeur', '📈 Statut', '💡 Impact'],
            [
                '🔓 Ports ouverts',
                str(len(results.get('ports', []))),
                self._get_visual_status(len(results.get('ports', [])) > 0),
                'Surface d\'attaque'
            ],
            [
                '🚨 Vulnérabilités critiques',
                str(vuln_stats['high']),
                self._get_severity_visual('high', vuln_stats['high']),
                'Action immédiate requise' if vuln_stats['high'] > 0 else 'Aucune menace critique'
            ],
            [
                '⚠️ Vulnérabilités moyennes',
                str(vuln_stats['medium']),
                self._get_severity_visual('medium', vuln_stats['medium']),
                'Correction recommandée' if vuln_stats['medium'] > 0 else 'Niveau acceptable'
            ],
            [
                '💡 Vulnérabilités faibles',
                str(vuln_stats['low']),
                self._get_severity_visual('low', vuln_stats['low']),
                'Surveillance continue' if vuln_stats['low'] > 0 else 'Bon niveau'
            ],
            [
                'ℹ️ Informations',
                str(vuln_stats['info']),
                self._get_severity_visual('info', vuln_stats['info']),
                'Données contextuelles'
            ]
        ]

        # Créer le tableau avec un style moderne
        summary_table = Table(stats_cards, colWidths=[2.2*inch, 0.8*inch, 1.2*inch, 2.3*inch])
        summary_table.setStyle(TableStyle([
            # En-tête avec dégradé
            ('BACKGROUND', (0, 0), (-1, 0), HexColor('#4f46e5')),
            ('TEXTCOLOR', (0, 0), (-1, 0), white),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),

            # Corps du tableau
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('ALIGN', (1, 1), (1, -1), 'CENTER'),  # Centrer les valeurs
            ('ALIGN', (2, 1), (2, -1), 'CENTER'),  # Centrer les statuts

            # Bordures et espacement
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#e2e8f0')),
            ('LINEWIDTH', (0, 0), (-1, -1), 0.5),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 10),
            ('RIGHTPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),

            # Alternance de couleurs pour les lignes
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [HexColor('#f8fafc'), white]),

            # Mise en évidence des valeurs critiques
            ('FONTNAME', (1, 2), (1, 2), 'Helvetica-Bold'),  # Vulnérabilités critiques
            ('TEXTCOLOR', (1, 2), (1, 2), HexColor('#dc2626')),
        ]))

        story.append(summary_table)
        story.append(Spacer(1, 25))

        # Évaluation du risque global avec design amélioré
        risk_level = self._calculate_risk_level(vuln_stats)
        story.append(Paragraph("🎯 Évaluation du Risque Global", self.styles['CustomHeading2']))
        story.append(Spacer(1, 10))

        # Utiliser le style approprié pour le niveau de risque
        risk_style_map = {
            'AlertCritical': 'AlertCritical',
            'AlertHigh': 'AlertHigh',
            'AlertMedium': 'AlertMedium',
            'AlertLow': 'AlertLow'
        }

        risk_style = self.styles.get(risk_style_map.get(risk_level['style'], 'AlertLow'), self.styles['CustomNormal'])

        # Score de sécurité
        security_score = self._calculate_security_score(vuln_stats, len(results.get('ports', [])))
        score_color = self._get_score_color(security_score)

        story.append(Paragraph(
            f"<b>🛡️ Niveau de risque: {risk_level['level']}</b><br/>"
            f"📊 Score de sécurité: <font color='{score_color}'><b>{security_score}/100</b></font><br/><br/>"
            f"{risk_level['description']}",
            risk_style
        ))

        return story

    def _get_visual_status(self, has_issues):
        """Retourne un statut visuel avec emoji"""
        return "🔴 Attention" if has_issues else "✅ OK"

    def _get_severity_visual(self, severity, count):
        """Retourne un statut visuel basé sur la sévérité et le nombre"""
        if count == 0:
            return "✅ Aucune"
        elif severity == 'high':
            return f"🔴 {count} critique{'s' if count > 1 else ''}"
        elif severity == 'medium':
            return f"🟡 {count} moyenne{'s' if count > 1 else ''}"
        elif severity == 'low':
            return f"🔵 {count} faible{'s' if count > 1 else ''}"
        else:
            return f"ℹ️ {count} info{'s' if count > 1 else ''}"

    def _get_score_color(self, score):
        """Retourne la couleur basée sur le score de sécurité"""
        if score >= 80:
            return '#059669'  # Vert
        elif score >= 60:
            return '#d97706'  # Orange
        elif score >= 40:
            return '#dc2626'  # Rouge
        else:
            return '#991b1b'  # Rouge foncé

    def _calculate_security_score(self, vuln_stats, port_count):
        """Calcule un score de sécurité sur 100"""
        base_score = 100

        # Déductions pour les vulnérabilités
        base_score -= vuln_stats['high'] * 25  # -25 points par vulnérabilité critique
        base_score -= vuln_stats['medium'] * 10  # -10 points par vulnérabilité moyenne
        base_score -= vuln_stats['low'] * 3  # -3 points par vulnérabilité faible
        base_score -= vuln_stats['info'] * 1  # -1 point par information

        # Déduction pour les ports ouverts (surface d'attaque)
        if port_count > 20:
            base_score -= 15
        elif port_count > 10:
            base_score -= 10
        elif port_count > 5:
            base_score -= 5

        # S'assurer que le score reste entre 0 et 100
        return max(0, min(100, base_score))

    def _get_cvss_color(self, cvss_score):
        """Retourne la couleur basée sur le score CVSS"""
        if cvss_score >= 9.0:
            return '#991b1b'  # Rouge très foncé - Critique
        elif cvss_score >= 7.0:
            return '#dc2626'  # Rouge - Élevé
        elif cvss_score >= 4.0:
            return '#d97706'  # Orange - Moyen
        elif cvss_score > 0.0:
            return '#059669'  # Vert - Faible
        else:
            return '#6b7280'  # Gris - Inconnu

    def _build_technical_details(self, scan_data):
        """Construit la section des détails techniques"""
        story = []
        
        story.append(Paragraph("Détails Techniques", self.styles['CustomHeading1']))
        story.append(Spacer(1, 12))
        
        # Configuration du scan
        story.append(Paragraph("Configuration du Scan", self.styles['CustomHeading2']))
        
        config_data = [
            ['Paramètre', 'Valeur'],
            ['Cible', scan_data.get('target', 'N/A')],
            ['Catégorie', scan_data.get('category', 'N/A').title()],
            ['Type', scan_data.get('scan_type', 'N/A').title()],
            ['Outils utilisés', ', '.join(scan_data.get('tools', []))],
        ]
        
        if scan_data.get('ports'):
            config_data.append(['Ports spécifiés', scan_data.get('ports')])
        
        config_table = Table(config_data, colWidths=[2*inch, 4*inch])
        config_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor('#f3f4f6')),
            ('TEXTCOLOR', (0, 0), (-1, 0), HexColor('#374151')),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#e5e7eb')),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))
        
        story.append(config_table)
        story.append(Spacer(1, 20))
        
        # Métadonnées du scan
        story.append(Paragraph("Métadonnées", self.styles['CustomHeading2']))
        
        metadata = [
            ['Propriété', 'Valeur'],
            ['ID du scan', scan_data.get('scan_id', 'N/A')],
            ['Utilisateur', scan_data.get('user_id', 'N/A')],
            ['Heure de début', self._format_datetime(scan_data.get('start_time'))],
            ['Heure de fin', self._format_datetime(scan_data.get('end_time'))],
            ['Durée totale', self._calculate_duration(scan_data)],
            ['Statut final', scan_data.get('status', 'N/A').upper()],
        ]
        
        if scan_data.get('isRobust'):
            metadata.append(['Mode robuste', 'Activé'])
        
        metadata_table = Table(metadata, colWidths=[2*inch, 4*inch])
        metadata_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor('#f3f4f6')),
            ('TEXTCOLOR', (0, 0), (-1, 0), HexColor('#374151')),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#e5e7eb')),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))
        
        story.append(metadata_table)
        
        return story

    def _format_datetime(self, dt_str):
        """Formate une chaîne datetime"""
        if not dt_str:
            return 'N/A'
        try:
            if isinstance(dt_str, str):
                dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
            else:
                dt = dt_str
            return dt.strftime('%d/%m/%Y %H:%M:%S')
        except:
            return str(dt_str)

    def _calculate_duration(self, scan_data):
        """Calcule la durée du scan"""
        start_time = scan_data.get('start_time')
        end_time = scan_data.get('end_time')
        
        if not start_time or not end_time:
            return 'N/A'
        
        try:
            if isinstance(start_time, str):
                start = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            else:
                start = start_time
                
            if isinstance(end_time, str):
                end = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
            else:
                end = end_time
                
            duration = end - start
            
            hours, remainder = divmod(duration.total_seconds(), 3600)
            minutes, seconds = divmod(remainder, 60)
            
            if hours > 0:
                return f"{int(hours)}h {int(minutes)}m {int(seconds)}s"
            elif minutes > 0:
                return f"{int(minutes)}m {int(seconds)}s"
            else:
                return f"{int(seconds)}s"
        except:
            return 'N/A'

    def _get_status_icon(self, has_issues):
        """Retourne une icône de statut"""
        return "⚠️" if has_issues else "✅"

    def _get_severity_status(self, severity, count):
        """Retourne le statut basé sur la sévérité et le nombre"""
        if count == 0:
            return "✅"
        elif severity == 'high':
            return "🔴"
        elif severity == 'medium':
            return "🟡"
        else:
            return "🔵"

    def _calculate_risk_level(self, vuln_stats):
        """Calcule le niveau de risque global"""
        if vuln_stats['high'] > 0:
            return {
                'level': 'CRITIQUE',
                'description': 'Des vulnérabilités critiques ont été détectées. Une action immédiate est requise.',
                'style': 'AlertHigh'
            }
        elif vuln_stats['medium'] > 3:
            return {
                'level': 'ÉLEVÉ',
                'description': 'Plusieurs vulnérabilités moyennes détectées. Une attention particulière est recommandée.',
                'style': 'AlertMedium'
            }
        elif vuln_stats['medium'] > 0 or vuln_stats['low'] > 5:
            return {
                'level': 'MODÉRÉ',
                'description': 'Quelques vulnérabilités détectées. Un suivi est recommandé.',
                'style': 'AlertMedium'
            }
        else:
            return {
                'level': 'FAIBLE',
                'description': 'Peu ou pas de vulnérabilités critiques détectées.',
                'style': 'AlertLow'
            }

    def _build_tool_results(self, scan_data):
        """Construit la section des résultats par outil"""
        story = []

        story.append(Paragraph("Résultats par Outil", self.styles['CustomHeading1']))
        story.append(Spacer(1, 12))

        results = scan_data.get('results', {})

        # Pour les scans de vulnérabilité, traiter les outils spécialement
        if scan_data.get('category') == 'vulnerability' and results.get('tools'):
            tools_data = results.get('tools', {})

            for tool_name, tool_result in tools_data.items():
                story.append(Paragraph(f"Résultats {tool_name.upper()}", self.styles['CustomHeading2']))

                # Informations de base de l'outil
                tool_info = [
                    ['Propriété', 'Valeur'],
                    ['Statut', tool_result.get('status', 'N/A')],
                    ['Temps d\'exécution', tool_result.get('scan_time', 'N/A')],
                ]

                if tool_result.get('vulnerabilities_found'):
                    tool_info.append(['Vulnérabilités trouvées', str(tool_result.get('vulnerabilities_found'))])

                tool_table = Table(tool_info, colWidths=[2*inch, 4*inch])
                tool_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), HexColor('#f3f4f6')),
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, -1), 9),
                    ('GRID', (0, 0), (-1, -1), 1, HexColor('#e5e7eb')),
                ]))

                story.append(tool_table)
                story.append(Spacer(1, 10))

        # Pour les autres types de scans, traiter les résultats normalement
        else:
            for tool, result in results.items():
                if tool in ['vulnerabilities', 'ports', 'summary', 'tools']:
                    continue

                story.append(Paragraph(f"Résultats {tool.upper()}", self.styles['CustomHeading2']))

                if isinstance(result, dict):
                    # Afficher les informations structurées
                    if result.get('status'):
                        story.append(Paragraph(f"<b>Statut:</b> {result.get('status')}", self.styles['CustomNormal']))

                    if result.get('scan_time'):
                        story.append(Paragraph(f"<b>Temps d'exécution:</b> {result.get('scan_time')}", self.styles['CustomNormal']))

                    if result.get('output'):
                        story.append(Paragraph("<b>Sortie:</b>", self.styles['CustomNormal']))
                        # Limiter la sortie pour éviter des pages trop longues
                        output = str(result.get('output'))[:1000]
                        if len(str(result.get('output'))) > 1000:
                            output += "... (tronqué)"
                        story.append(Paragraph(f"<font name='Courier' size='8'>{output}</font>", self.styles['CustomNormal']))

                elif isinstance(result, str):
                    # Afficher la chaîne directement (limitée)
                    output = result[:1000]
                    if len(result) > 1000:
                        output += "... (tronqué)"
                    story.append(Paragraph(f"<font name='Courier' size='8'>{output}</font>", self.styles['CustomNormal']))

                story.append(Spacer(1, 15))

        return story

    def _build_vulnerabilities_section(self, scan_data):
        """Construit une section des vulnérabilités moderne et détaillée"""
        story = []

        story.append(Paragraph("🚨 Vulnérabilités Détaillées", self.styles['CustomHeading1']))
        story.append(Spacer(1, 20))

        vulnerabilities = scan_data.get('results', {}).get('vulnerabilities', [])

        if not vulnerabilities:
            story.append(Paragraph(
                "✅ <b>Excellente nouvelle !</b><br/>"
                "Aucune vulnérabilité n'a été détectée lors de ce scan. "
                "Votre système présente un bon niveau de sécurité pour les tests effectués.",
                self.styles['AlertLow']
            ))
            return story

        # Grouper par sévérité
        vuln_by_severity = {'high': [], 'medium': [], 'low': [], 'info': []}

        for vuln in vulnerabilities:
            severity = vuln.get('severity', 'info').lower()
            if severity in vuln_by_severity:
                vuln_by_severity[severity].append(vuln)
            else:
                vuln_by_severity['info'].append(vuln)

        # Afficher par ordre de sévérité avec design moderne
        severity_order = [
            ('high', '🔴 Vulnérabilités Critiques', 'AlertCritical', '⚠️'),
            ('medium', '🟡 Vulnérabilités Moyennes', 'AlertMedium', '⚠️'),
            ('low', '🔵 Vulnérabilités Faibles', 'AlertLow', 'ℹ️'),
            ('info', 'ℹ️ Informations', 'InfoBox', 'ℹ️')
        ]

        for severity, title, style, icon in severity_order:
            vulns = vuln_by_severity[severity]
            if not vulns:
                continue

            story.append(Paragraph(f"{title} ({len(vulns)})", self.styles['CustomHeading2']))
            story.append(Spacer(1, 12))

            for i, vuln in enumerate(vulns, 1):
                # Titre de la vulnérabilité avec design moderne
                vuln_title = vuln.get('name', vuln.get('title', f'Vulnérabilité #{i}'))

                # Créer un en-tête de vulnérabilité avec couleur de sévérité
                severity_colors = {
                    'high': HexColor('#dc2626'),
                    'medium': HexColor('#d97706'),
                    'low': HexColor('#059669'),
                    'info': HexColor('#2563eb')
                }

                severity_bg = {
                    'high': HexColor('#fef2f2'),
                    'medium': HexColor('#fffbeb'),
                    'low': HexColor('#f0fdf4'),
                    'info': HexColor('#eff6ff')
                }

                # En-tête de la vulnérabilité
                vuln_header = Table(
                    [[f"🔍 {i}. {vuln_title}"]],
                    colWidths=[6*inch]
                )
                vuln_header.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, -1), severity_bg.get(severity, HexColor('#f9fafb'))),
                    ('TEXTCOLOR', (0, 0), (-1, -1), severity_colors.get(severity, HexColor('#374151'))),
                    ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, -1), 12),
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('LEFTPADDING', (0, 0), (-1, -1), 12),
                    ('RIGHTPADDING', (0, 0), (-1, -1), 12),
                    ('TOPPADDING', (0, 0), (-1, -1), 8),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
                    ('LINEWIDTH', (0, 0), (-1, -1), 1),
                    ('LINECOLOR', (0, 0), (-1, -1), severity_colors.get(severity, HexColor('#e5e7eb'))),
                ]))

                story.append(vuln_header)
                story.append(Spacer(1, 5))

                # Détails de la vulnérabilité avec icônes
                vuln_details = []

                if vuln.get('description'):
                    vuln_details.append(['📝 Description', vuln.get('description')])

                if vuln.get('port'):
                    vuln_details.append(['🔌 Port', str(vuln.get('port'))])

                if vuln.get('service'):
                    vuln_details.append(['⚙️ Service', vuln.get('service')])

                if vuln.get('cvss'):
                    cvss_score = str(vuln.get('cvss'))
                    cvss_color = self._get_cvss_color(float(cvss_score) if cvss_score.replace('.', '').isdigit() else 0)
                    vuln_details.append(['📊 Score CVSS', f'<font color="{cvss_color}"><b>{cvss_score}</b></font>'])

                if vuln.get('cve'):
                    vuln_details.append(['🆔 CVE', vuln.get('cve')])

                if vuln.get('solution'):
                    vuln_details.append(['💡 Solution', vuln.get('solution')])

                if vuln_details:
                    vuln_table = Table(vuln_details, colWidths=[1.8*inch, 4.2*inch])
                    vuln_table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (0, -1), HexColor('#f8fafc')),
                        ('BACKGROUND', (1, 0), (1, -1), white),
                        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                        ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                        ('FONTSIZE', (0, 0), (-1, -1), 10),
                        ('GRID', (0, 0), (-1, -1), 1, HexColor('#e2e8f0')),
                        ('LINEWIDTH', (0, 0), (-1, -1), 0.5),
                        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                        ('LEFTPADDING', (0, 0), (-1, -1), 10),
                        ('RIGHTPADDING', (0, 0), (-1, -1), 10),
                        ('TOPPADDING', (0, 0), (-1, -1), 6),
                        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                    ]))

                    story.append(vuln_table)

                story.append(Spacer(1, 15))

        return story

    def _build_ports_section(self, scan_data):
        """Construit la section des ports ouverts"""
        story = []

        story.append(Paragraph("Ports Ouverts", self.styles['CustomHeading1']))
        story.append(Spacer(1, 12))

        ports = scan_data.get('results', {}).get('ports', [])

        if not ports:
            story.append(Paragraph("Aucun port ouvert détecté.", self.styles['CustomNormal']))
            return story

        # Tableau des ports
        port_data = [['Port', 'Protocole', 'Service', 'Version', 'État']]

        for port in ports:
            port_data.append([
                str(port.get('port', 'N/A')),
                port.get('protocol', 'N/A'),
                port.get('service', 'N/A'),
                port.get('version', 'N/A'),
                port.get('state', 'N/A')
            ])

        ports_table = Table(port_data, colWidths=[0.8*inch, 1*inch, 1.5*inch, 2*inch, 0.7*inch])
        ports_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor('#4f46e5')),
            ('TEXTCOLOR', (0, 0), (-1, 0), white),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#e5e7eb')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [HexColor('#f9fafb'), white]),
        ]))

        story.append(ports_table)
        story.append(Spacer(1, 15))

        # Statistiques des ports
        story.append(Paragraph("Statistiques des Ports", self.styles['CustomHeading2']))

        # Compter par service
        services = {}
        for port in ports:
            service = port.get('service', 'Unknown')
            services[service] = services.get(service, 0) + 1

        stats_data = [['Service', 'Nombre de ports']]
        for service, count in sorted(services.items(), key=lambda x: x[1], reverse=True):
            stats_data.append([service, str(count)])

        stats_table = Table(stats_data, colWidths=[3*inch, 2*inch])
        stats_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor('#f3f4f6')),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#e5e7eb')),
        ]))

        story.append(stats_table)

        return story

    def _build_recommendations(self, scan_data):
        """Construit la section des recommandations"""
        story = []

        story.append(Paragraph("Recommandations", self.styles['CustomHeading1']))
        story.append(Spacer(1, 12))

        # Analyser les résultats pour générer des recommandations
        results = scan_data.get('results', {})
        vulnerabilities = results.get('vulnerabilities', [])
        ports = results.get('ports', [])

        recommendations = []

        # Recommandations basées sur les vulnérabilités
        high_vulns = [v for v in vulnerabilities if v.get('severity', '').lower() == 'high']
        medium_vulns = [v for v in vulnerabilities if v.get('severity', '').lower() == 'medium']

        if high_vulns:
            recommendations.append({
                'priority': 'CRITIQUE',
                'title': 'Correction des vulnérabilités critiques',
                'description': f'{len(high_vulns)} vulnérabilité(s) critique(s) détectée(s). '
                             'Ces vulnérabilités doivent être corrigées immédiatement car elles '
                             'présentent un risque élevé pour la sécurité du système.',
                'actions': [
                    'Appliquer les correctifs de sécurité disponibles',
                    'Mettre à jour les services vulnérables',
                    'Configurer des contrôles d\'accès appropriés',
                    'Surveiller les tentatives d\'exploitation'
                ]
            })

        if medium_vulns:
            recommendations.append({
                'priority': 'ÉLEVÉE',
                'title': 'Correction des vulnérabilités moyennes',
                'description': f'{len(medium_vulns)} vulnérabilité(s) moyenne(s) détectée(s). '
                             'Ces vulnérabilités doivent être traitées dans les plus brefs délais.',
                'actions': [
                    'Planifier la correction dans les 30 jours',
                    'Évaluer l\'impact des correctifs',
                    'Tester les correctifs en environnement de test'
                ]
            })

        # Recommandations basées sur les ports ouverts
        if ports:
            open_ports_count = len(ports)
            if open_ports_count > 10:
                recommendations.append({
                    'priority': 'MOYENNE',
                    'title': 'Réduction de la surface d\'attaque',
                    'description': f'{open_ports_count} ports ouverts détectés. '
                                 'Réduire le nombre de services exposés diminue la surface d\'attaque.',
                    'actions': [
                        'Fermer les ports non nécessaires',
                        'Configurer un pare-feu restrictif',
                        'Utiliser des VPN pour les accès administratifs',
                        'Implémenter une segmentation réseau'
                    ]
                })

        # Recommandations générales
        recommendations.append({
            'priority': 'CONTINUE',
            'title': 'Amélioration continue de la sécurité',
            'description': 'Maintenir un niveau de sécurité élevé nécessite une approche continue.',
            'actions': [
                'Effectuer des scans de sécurité réguliers',
                'Maintenir un inventaire des actifs à jour',
                'Former le personnel aux bonnes pratiques de sécurité',
                'Mettre en place une surveillance continue',
                'Développer un plan de réponse aux incidents'
            ]
        })

        # Afficher les recommandations
        for i, rec in enumerate(recommendations, 1):
            # Titre de la recommandation avec priorité
            priority_style = {
                'CRITIQUE': 'AlertHigh',
                'ÉLEVÉE': 'AlertMedium',
                'MOYENNE': 'AlertMedium',
                'CONTINUE': 'AlertLow'
            }.get(rec['priority'], 'CustomNormal')

            rec_style = self.styles.get(priority_style, self.styles['CustomNormal'])
            story.append(Paragraph(
                f"<b>{i}. {rec['title']} [Priorité: {rec['priority']}]</b>",
                rec_style
            ))

            # Description
            story.append(Paragraph(rec['description'], self.styles['CustomNormal']))
            story.append(Spacer(1, 8))

            # Actions recommandées
            story.append(Paragraph("<b>Actions recommandées:</b>", self.styles['CustomNormal']))
            for action in rec['actions']:
                story.append(Paragraph(f"• {action}", self.styles['CustomNormal']))

            story.append(Spacer(1, 15))

        return story
