"""
PICA - Complete Phishing Detection Service
==========================================

This module provides a comprehensive phishing detection system using a structured,
weighted scoring approach with priority-based indicators.

DETECTION PROCESS OVERVIEW:
==========================

1. INPUT URL → Parse components (domain, TLD, subdomains, protocol, parameters)
2. RUN CHECKS → Execute all 14 detection indicators
3. CALCULATE SCORE → Sum weights of failed checks (max 100%)
4. CLASSIFY RISK → Apply thresholds to determine phishing status

INDICATOR PRIORITIES & WEIGHTS:
==============================

HIGH PRIORITY (Critical Indicators):
- IP in URL: 25% - Raw IP addresses instead of domain names
- Random Domain: 20% - Algorithmically generated domain names
- Domain Age: 20% - Very new domains (< 30 days)
- Suspicious TLD: 20% - High-risk TLDs (.tk, .ml, .xyz, etc.)
- SSL Certificate Error: 20% - Invalid, expired, or missing certificates

MEDIUM PRIORITY (Important Indicators):
- Temporary Domain: 12% - Disposable/temporary domain services
- Excessive Subdomain Count: 10% - Too many subdomains
- URL Length: 10% - Abnormally long URLs (> 75 chars)
- Special Characters: 10% - Excessive special characters
- Redirects: 8% - Immediate redirections to other domains

LOW PRIORITY (Supporting Indicators):
- Numeric Domain: 5% - Domain names mostly composed of numbers
- HTTPS Missing: 5% - Using HTTP instead of HTTPS
- Suspicious URL Parameters: 5% - Malicious query parameters
- Bad External Reputation: 5% - Known malicious domains

RISK CLASSIFICATION THRESHOLDS:
==============================
- 0-30%: Low Risk → NOT PHISHING
- 31-50%: Medium Risk → PHISHING (Suspicious - Review)
- 51-100%: High Risk → PHISHING (Block/Alert)

DECISION RULE: Score > 30% = PHISHING URL

EXAMPLE ANALYSIS:
================
URL: http://***********/login.php?id=abc123
- IP in URL: +25%
- HTTPS Missing: +5%
- Special Characters: +10%
Total: 40% → PHISHING (Medium Risk)

URL: https://secure-login.bankaccount.tk/reset?uid=********
- Suspicious TLD (.tk): +20%
- Domain Age (new): +20%
- Excessive Subdomains: +10%
Total: 50% → PHISHING (Medium Risk)
"""

# ============================================================================
# IMPORTS AND CONFIGURATION
# ============================================================================

import re
import socket
import ssl
import requests
import string
import urllib3
import time
from urllib.parse import urlparse
from bs4 import BeautifulSoup
from datetime import datetime, timedelta

# Disable SSL warnings for self-signed certificates
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# ============================================================================
# DYNAMIC IMPORTS AND OPTIONAL DEPENDENCIES
# ============================================================================

# Dynamic import of advanced detection module
try:
    from app.utils import advanced_detection
except ImportError:
    advanced_detection = None

# Dynamic import of API integrations module
try:
    from app.utils import api_integrations
except ImportError:
    api_integrations = None

# Optional whois import
try:
    import whois
except ImportError:
    whois = None
    print("python-whois not installed. Domain age checks will be disabled.")

# ============================================================================
# EXTERNAL API CONFIGURATION
# ============================================================================

# API keys for reputation services
URLSCAN_API_KEY = "************************************"
VIRUSTOTAL_API_KEY = "****************************************************************"

# External API URLs
URLSCAN_SUBMIT_URL = "https://urlscan.io/api/v1/scan/"
URLSCAN_RESULT_URL = "https://urlscan.io/api/v1/result/{uuid}/"
VIRUSTOTAL_URL_SCAN = "https://www.virustotal.com/api/v3/urls"
VIRUSTOTAL_URL_REPORT = "https://www.virustotal.com/api/v3/analyses/{id}"

# ============================================================================
# CONSTANTS AND REFERENCE LISTS
# ============================================================================

# Popular brands often targeted in phishing attacks
POPULAR_BRANDS = [
    "amazon", "apple", "google", "microsoft", "paypal", "facebook",
    "instagram", "twitter", "netflix", "linkedin", "chase", "wellsfargo",
    "bankofamerica", "citibank", "dropbox", "gmail", "yahoo", "outlook",
    "americanexpress", "ebay", "coinbase", "binance", "blockchain", "bitcoin"
]

# ============================================================================
# CUSTOM EXCEPTION CLASSES
# ============================================================================

class APIError(Exception):
    """Custom exception for external API related errors."""
    pass

# ============================================================================
# HELPER FUNCTIONS
# ============================================================================

def get_check_priority(check_name):
    """
    Determine the priority level of a check based on its name.

    Args:
        check_name (str): Name of the check

    Returns:
        str: Priority level ('High', 'Medium', 'Low')
    """
    high_priority_checks = [
        'IP in URL', 'Random Domain', 'Domain Age',
        'Suspicious TLD', 'SSL Certificate'
    ]

    medium_priority_checks = [
        'Temporary Domain', 'Subdomain Count', 'URL Length',
        'Special Characters', 'Redirects'
    ]

    # Check for exact matches or partial matches
    for high_check in high_priority_checks:
        if high_check in check_name:
            return 'High'

    for medium_check in medium_priority_checks:
        if medium_check in check_name:
            return 'Medium'

    return 'Low'  # Default for remaining checks

# ============================================================================
# MAIN ANALYSIS FUNCTION
# ============================================================================

def analyze_url(url):
    """
    Analyze a URL to detect phishing indicators using comprehensive weighted scoring.

    This function performs a comprehensive analysis of a URL using
    various detection techniques including domain analysis,
    HTML content analysis, and external reputation checks.

    The scoring system uses specific weights for each indicator:
    - IP in URL: 25%
    - Random Domain: 20%
    - Domain Age (very new): 20%
    - Suspicious TLD: 20%
    - SSL Certificate Error: 20%
    - Temporary Domain: 12%
    - Subdomain Count (excessive): 10%
    - URL Length (long): 10%
    - Special Characters: 10%
    - Redirects: 8%
    - Numeric Domain: 5%
    - HTTPS Missing: 5%
    - Suspicious URL Parameters: 5%
    - Bad External Reputation: 5%

    Risk Levels:
    - 0-30%: Low Risk (Not Phishing)
    - 31-50%: Medium Risk (Suspicious)
    - 51-100%: High Risk (Phishing)

    Phishing Classification: >30% = Phishing

    Args:
        url (str): The URL to analyze

    Returns:
        dict: Dictionary containing analysis results with:
            - url: The analyzed URL
            - domain: The extracted domain
            - checks: List of performed checks
            - risk_score: Risk score (0-100)
            - likelihood: Risk level (Low/Medium/High Risk)
            - is_phishing: Boolean indicating if URL is suspicious (>30%)
    """
    # URL normalization
    if not url.startswith('http://') and not url.startswith('https://'):
        url = 'http://' + url

    # URL validation and parsing
    try:
        parsed_url = urlparse(url)
        domain = parsed_url.netloc
    except Exception:
        return {
            'error': 'Invalid URL format',
            'checks': [],
            'risk_score': 0
        }

    # HTML content initialization
    html_content = None

    # === PHASE 1: PRIORITY-BASED URL CHECKS ===
    checks = []

    # HIGH PRIORITY CHECKS (Critical Indicators - 25%, 20%, 20%, 20%, 20%)
    high_priority_checks = [
        check_ip_in_url(url),                    # 25% - IP in URL
        check_random_domain(domain),             # 20% - Random Domain
        check_domain_age(domain),                # 20% - Domain Age (very new)
        check_suspicious_tld(domain),            # 20% - Suspicious TLD
        check_ssl_certificate(domain)            # 20% - SSL Certificate Error
    ]

    # MEDIUM PRIORITY CHECKS (Important Indicators - 12%, 10%, 10%, 10%, 8%)
    medium_priority_checks = [
        check_temporary_domain(domain),          # 12% - Temporary Domain
        check_subdomain_count(domain),           # 10% - Excessive Subdomain Count
        check_url_length(url),                   # 10% - URL Length (long)
        check_special_chars(url),                # 10% - Special Characters
        check_redirects(url)                     # 8%  - Redirects
    ]

    # LOW PRIORITY CHECKS (Supporting Indicators - 5%, 5%, 5%, 5%)
    low_priority_checks = [
        check_numeric_domain(domain),            # 5% - Numeric Domain
        check_https(parsed_url),                 # 5% - HTTPS Missing
        check_url_parameters(parsed_url.query)   # 5% - Suspicious URL Parameters
        # Bad External Reputation (5%) - added in Phase 2
    ]

    # Combine all checks in priority order
    checks.extend(high_priority_checks)
    checks.extend(medium_priority_checks)
    checks.extend(low_priority_checks)

    # === PHASE 2: EXTERNAL REPUTATION CHECKS ===
    if api_integrations:
        reputation_check = api_integrations.check_reputation(url)
        if reputation_check:
            checks.append(reputation_check)

    # === PHASE 3: HTML CONTENT ANALYSIS ===
    try:
        html_content = fetch_url_content(url)
        if html_content:
            # Basic content checks
            checks.extend([
                check_login_form(html_content),
                check_favicon_source(html_content, domain),
                check_external_links(html_content, domain)
            ])

            # Advanced detections if module is available
            if advanced_detection:
                checks.extend([
                    advanced_detection.detect_brand_impersonation(url, html_content),
                    advanced_detection.detect_homoglyph_attack(url),
                    advanced_detection.detect_obfuscation_techniques(html_content),
                    advanced_detection.detect_data_uri_scheme(url, html_content),
                    advanced_detection.detect_suspicious_redirects(html_content),
                    advanced_detection.detect_cloaking_techniques(html_content)
                ])
    except Exception as e:
        # Add warning if content analysis fails
        checks.append({
            'name': 'Content Analysis',
            'result': 'Warning',
            'description': f'Could not analyze page content: {str(e)}',
            'weight': 1
        })

    # === PHASE 4: COMPREHENSIVE WEIGHTED RISK SCORE CALCULATION ===
    valid_checks = [check for check in checks if check is not None]

    # Initialize detailed scoring breakdown
    suspicion_score = 0
    triggered_indicators = []
    high_priority_triggered = []
    medium_priority_triggered = []
    low_priority_triggered = []

    # Calculate suspicion score with detailed tracking
    for check in valid_checks:
        check_weight = check.get('weight', 1)
        check_name = check.get('name', 'Unknown Check')
        check_priority = get_check_priority(check_name)

        if check['result'] == 'Failed':
            suspicion_score += check_weight
            indicator_info = {
                'name': check_name,
                'weight': check_weight,
                'priority': check_priority,
                'description': check.get('description', ''),
                'contribution': check_weight
            }
            triggered_indicators.append(indicator_info)

            # Categorize by priority
            if check_priority == 'High':
                high_priority_triggered.append(indicator_info)
            elif check_priority == 'Medium':
                medium_priority_triggered.append(indicator_info)
            else:
                low_priority_triggered.append(indicator_info)

        elif check['result'] == 'Warning':
            # Warnings contribute half weight
            warning_contribution = check_weight * 0.5
            suspicion_score += warning_contribution
            indicator_info = {
                'name': check_name,
                'weight': check_weight,
                'priority': check_priority,
                'description': check.get('description', ''),
                'contribution': warning_contribution,
                'warning': True
            }
            triggered_indicators.append(indicator_info)

    # Cap the score at 100%
    if suspicion_score > 100:
        suspicion_score = 100

    risk_score = int(suspicion_score)

    # New risk level determination based on specified thresholds
    if risk_score <= 30:
        likelihood = 'Low Risk'
        risk_category = 'NOT_PHISHING'
    elif risk_score <= 50:
        likelihood = 'Medium Risk'
        risk_category = 'PHISHING_SUSPICIOUS'
    else:
        likelihood = 'High Risk'
        risk_category = 'PHISHING_HIGH_RISK'

    # New phishing classification: >30% = phishing
    is_phishing = risk_score > 30

    # === PHASE 5: COMPREHENSIVE RESULT COMPILATION ===
    result = {
        'url': url,
        'domain': domain,
        'risk_score': risk_score,
        'likelihood': likelihood,
        'risk_category': risk_category,
        'is_phishing': is_phishing,
        'checks': valid_checks,
        'analysis_timestamp': datetime.now().isoformat(),

        # Detailed scoring breakdown
        'scoring_details': {
            'total_suspicion_score': suspicion_score,
            'triggered_indicators_count': len(triggered_indicators),
            'triggered_indicators': triggered_indicators,
            'high_priority_triggered': high_priority_triggered,
            'medium_priority_triggered': medium_priority_triggered,
            'low_priority_triggered': low_priority_triggered
        },

        # Check statistics
        'check_statistics': {
            'total_checks': len(valid_checks),
            'failed_checks': len([c for c in valid_checks if c['result'] == 'Failed']),
            'warning_checks': len([c for c in valid_checks if c['result'] == 'Warning']),
            'passed_checks': len([c for c in valid_checks if c['result'] == 'Passed']),
            'high_priority_failed': len(high_priority_triggered),
            'medium_priority_failed': len(medium_priority_triggered),
            'low_priority_failed': len(low_priority_triggered)
        },

        # Classification summary
        'classification_summary': {
            'threshold_used': 30,
            'decision_rule': 'Score > 30% = Phishing',
            'risk_levels': {
                'low': '0-30% (Not Phishing)',
                'medium': '31-50% (Phishing - Suspicious)',
                'high': '51-100% (Phishing - High Risk)'
            }
        }
    }

    return result

# ============================================================================
# BASIC URL VERIFICATION FUNCTIONS
# ============================================================================

def check_url_length(url):
    """
    Check if the URL is abnormally long.

    Phishing URLs are often very long to hide
    the real domain or include suspicious parameters.

    Weight: 10% (URL Length indicator)

    Args:
        url (str): The URL to check

    Returns:
        dict: Verification result
    """
    if len(url) > 75:
        return {
            'name': 'URL Length',
            'result': 'Failed',
            'description': f'URL length ({len(url)}) is suspiciously long',
            'weight': 10
        }
    return {
        'name': 'URL Length',
        'result': 'Passed',
        'description': f'URL length ({len(url)}) is normal',
        'weight': 10
    }

def check_ip_in_url(url):
    """
    Check if the URL contains an IP address instead of a domain name.

    Phishing sites often use IP addresses to avoid
    DNS resolution and hide their identity.

    Weight: 25% (IP in URL indicator - highest weight)

    Args:
        url (str): The URL to check

    Returns:
        dict: Verification result
    """
    ip_pattern = re.compile(r'(^|\D)((\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3}))($|\D)')
    if ip_pattern.search(url):
        return {
            'name': 'IP in URL',
            'result': 'Failed',
            'description': 'URL contains an IP address instead of a domain name',
            'weight': 25,
            'high_risk': True
        }
    return {
        'name': 'IP in URL',
        'result': 'Passed',
        'description': 'No IP address in URL',
        'weight': 25
    }

def check_special_chars(url):
    """
    Check for excessive presence of special characters in the URL.

    A high number of special characters may indicate an attempt
    at obfuscation or URL manipulation.

    Weight: 10% (Special Characters indicator)

    Args:
        url (str): The URL to check

    Returns:
        dict: Verification result
    """
    special_chars = ['@', '?', '-', '=', '.', '#', '%', '+', '$', '!', '*', "'", ',']
    count = sum(url.count(char) for char in special_chars)

    if count > 10:
        return {
            'name': 'Special Characters',
            'result': 'Failed',
            'description': f'URL contains too many special characters ({count})',
            'weight': 10
        }
    return {
        'name': 'Special Characters',
        'result': 'Passed',
        'description': 'Normal number of special characters',
        'weight': 10
    }

def check_suspicious_tld(domain):
    """
    Vérifie si le domaine utilise un TLD (Top Level Domain) suspect.

    Certains TLD sont fréquemment utilisés pour des activités malveillantes
    car ils sont gratuits ou peu coûteux.

    Weight: 20% (Suspicious TLD indicator)

    Args:
        domain (str): Le domaine à vérifier

    Returns:
        dict: Résultat de la vérification
    """
    suspicious_tlds = [
        '.tk', '.ml', '.ga', '.cf', '.gq', '.xyz', '.top', '.club',
        '.work', '.date', '.bid', '.download', '.link', '.stream',
        '.gdn', '.fit', '.review', '.kim'
    ]

    for tld in suspicious_tlds:
        if domain.endswith(tld):
            return {
                'name': 'Suspicious TLD',
                'result': 'Failed',
                'description': f'Domain uses suspicious TLD ({tld})',
                'weight': 20,
                'high_risk': True
            }
    return {
        'name': 'Suspicious TLD',
        'result': 'Passed',
        'description': 'Domain uses common TLD',
        'weight': 20
    }

def check_subdomain_count(domain):
    """
    Vérifie le nombre de sous-domaines.

    Un nombre excessif de sous-domaines peut indiquer une tentative
    de masquer le véritable domaine ou de créer de la confusion.

    Weight: 10% (Subdomain Count indicator)

    Args:
        domain (str): Le domaine à vérifier

    Returns:
        dict: Résultat de la vérification
    """
    subdomain_count = domain.count('.')
    if subdomain_count > 3:
        return {
            'name': 'Subdomain Count',
            'result': 'Failed',
            'description': f'Domain has excessive subdomains ({subdomain_count})',
            'weight': 10
        }
    return {
        'name': 'Subdomain Count',
        'result': 'Passed',
        'description': f'Normal subdomain structure ({subdomain_count})',
        'weight': 10
    }

def check_https(parsed_url):
    """
    Checks if the URL uses the secure HTTPS protocol.

    The absence of HTTPS may indicate an unsecured site,
    although this is not always an indicator of phishing.

    Weight: 5% (HTTPS Missing indicator)

    Args:
        parsed_url: URL parsée avec urlparse

    Returns:
        dict: Résultat de la vérification
    """
    if parsed_url.scheme != 'https':
        return {
            'name': 'HTTPS Missing',
            'result': 'Failed',
            'description': 'URL does not use secure HTTPS protocol',
            'weight': 5
        }
    return {
        'name': 'HTTPS Missing',
        'result': 'Passed',
        'description': 'URL uses secure HTTPS protocol',
        'weight': 5
    }

# ============================================================================
# FONCTIONS DE VÉRIFICATION AVANCÉES DU DOMAINE
# ============================================================================

def check_domain_age(domain):
    """
    Vérifie l'âge du domaine.

    Les domaines très récents sont souvent utilisés pour le phishing
    car ils sont abandonnés rapidement après une campagne.

    Weight: 20% (Domain Age indicator)

    Args:
        domain (str): Le domaine à vérifier

    Returns:
        dict: Résultat de la vérification ou None si whois n'est pas disponible
    """
    if whois is None:
        return None

    try:
        domain_info = whois.whois(domain)
        if domain_info.creation_date is None:
            return {
                'name': 'Domain Age',
                'result': 'Warning',
                'description': 'Could not determine domain creation date',
                'weight': 20
            }

        # Gérer les cas où creation_date est une liste
        creation_date = domain_info.creation_date
        if isinstance(creation_date, list):
            creation_date = creation_date[0]

        # Calculer l'âge du domaine en jours
        import datetime
        domain_age_days = (datetime.datetime.now() - creation_date).days

        if domain_age_days < 30:
            return {
                'name': 'Domain Age',
                'result': 'Failed',
                'description': f'Domain is very new ({domain_age_days} days old)',
                'weight': 20,
                'high_risk': True
            }
        return {
            'name': 'Domain Age',
            'result': 'Passed',
            'description': f'Domain is {domain_age_days} days old',
            'weight': 20
        }
    except Exception:
        return {
            'name': 'Domain Age',
            'result': 'Warning',
            'description': 'Could not check domain age',
            'weight': 20
        }

def check_redirects(url):
    """
    Checks if the URL immediately redirects to another location.

    Immediate redirections may indicate an attempt to hide
    the true destination or bypass security filters.

    Weight: 8% (Redirects indicator)

    Args:
        url (str): The URL to check

    Returns:
        dict: Verification result
    """
    try:
        response = requests.get(url, allow_redirects=False, timeout=5)
        if response.status_code in (301, 302, 303, 307, 308):
            return {
                'name': 'Redirects',
                'result': 'Failed',
                'description': 'URL immediately redirects to another location',
                'weight': 8
            }
        return {
            'name': 'Redirects',
            'result': 'Passed',
            'description': 'No immediate redirects',
            'weight': 8
        }
    except Exception:
        return {
            'name': 'Redirects',
            'result': 'Warning',
            'description': 'Could not check redirects',
            'weight': 8
        }

def check_ssl_certificate(domain):
    """
    Vérifie la validité du certificat SSL.

    Un certificat SSL invalide ou manquant peut indiquer un site
    non sécurisé ou potentiellement malveillant.

    Weight: 20% (SSL Certificate Error indicator)

    Args:
        domain (str): Le domaine à vérifier

    Returns:
        dict: Résultat de la vérification
    """
    try:
        ctx = ssl.create_default_context()
        with ctx.wrap_socket(socket.socket(), server_hostname=domain) as s:
            s.connect((domain, 443))
            cert = s.getpeercert()

        # Vérifier si le certificat existe
        if not cert:
            return {
                'name': 'SSL Certificate',
                'result': 'Failed',
                'description': 'Invalid SSL certificate',
                'weight': 20,
                'high_risk': True
            }

        return {
            'name': 'SSL Certificate',
            'result': 'Passed',
            'description': 'Valid SSL certificate',
            'weight': 20
        }
    except Exception:
        return {
            'name': 'SSL Certificate',
            'result': 'Failed',
            'description': 'SSL certificate error',
            'weight': 20,
            'high_risk': True
        }

# ============================================================================
# FONCTIONS D'ANALYSE DU CONTENU HTML
# ============================================================================

def fetch_url_content(url):
    """
    Récupère le contenu HTML d'une URL pour analyse.

    Utilise un User-Agent standard pour éviter la détection et
    désactive la vérification SSL pour les certificats auto-signés.

    Args:
        url (str): The URL to retrieve content from

    Returns:
        str: The HTML content of the page or None in case of error
    """
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        # Désactiver les avertissements SSL pour les certificats auto-signés
        requests.packages.urllib3.disable_warnings()
        response = requests.get(url, headers=headers, timeout=10, verify=False)

        content = response.text
        if content:
            return content
        return None
    except Exception:
        return None

def check_login_form(html_content):
    """
    Vérifie si la page contient un formulaire de connexion.

    La présence d'un formulaire de connexion peut indiquer une tentative
    de vol d'identifiants, surtout sur des sites suspects.

    Args:
        html_content (str): Le contenu HTML à analyser

    Returns:
        dict: Résultat de la vérification
    """
    soup = BeautifulSoup(html_content, 'lxml')

    # Rechercher les champs de mot de passe
    password_inputs = soup.find_all('input', {'type': 'password'})

    if password_inputs:
        return {
            'name': 'Login Form',
            'result': 'Warning',
            'description': 'Page contains login form - be cautious with credentials'
        }
    return {
        'name': 'Login Form',
        'result': 'Passed',
        'description': 'No login form detected'
    }

def check_favicon_source(html_content, domain):
    """
    Vérifie si le favicon est chargé depuis un domaine différent.

    Un favicon chargé depuis un autre domaine peut indiquer une tentative
    d'usurpation d'identité ou de vol de ressources.

    Args:
        html_content (str): Le contenu HTML à analyser
        domain (str): Le domaine de la page

    Returns:
        dict: Résultat de la vérification
    """
    soup = BeautifulSoup(html_content, 'lxml')

    favicon_link = soup.find('link', rel=lambda r: r and ('icon' in r.lower() or 'shortcut' in r.lower()))

    if favicon_link and 'href' in favicon_link.attrs:
        favicon_url = favicon_link['href']
        if favicon_url.startswith('http'):
            parsed_favicon = urlparse(favicon_url)
            if parsed_favicon.netloc and parsed_favicon.netloc != domain:
                return {
                    'name': 'Favicon Source',
                    'result': 'Failed',
                    'description': 'Favicon loaded from different domain'
                }

    return {
        'name': 'Favicon Source',
        'result': 'Passed',
        'description': 'Favicon loaded from same domain or not specified'
    }

def check_external_links(html_content, domain):
    """
    Vérifie la proportion de liens externes sur la page.

    Une proportion élevée de liens externes peut indiquer un site
    de redirection ou une tentative de masquer la véritable destination.

    Args:
        html_content (str): Le contenu HTML à analyser
        domain (str): Le domaine de la page

    Returns:
        dict: Résultat de la vérification
    """
    soup = BeautifulSoup(html_content, 'lxml')

    links = soup.find_all('a', href=True)
    total_links = len(links)
    external_count = 0

    for link in links:
        href = link['href']
        if href.startswith('http'):
            parsed_link = urlparse(href)
            if parsed_link.netloc and parsed_link.netloc != domain:
                external_count += 1

    if total_links > 0:
        external_ratio = external_count / total_links
        if external_ratio > 0.7:  # Si plus de 70% des liens sont externes
            return {
                'name': 'External Links',
                'result': 'Failed',
                'description': f'High proportion of external links ({external_count}/{total_links})',
                'weight': 2
            }

    return {
        'name': 'External Links',
        'result': 'Passed',
        'description': 'Normal proportion of external links',
        'weight': 2
    }


# ============================================================================
# FONCTIONS DE DÉTECTION DE DOMAINES SUSPECTS
# ============================================================================

def check_numeric_domain(domain):
    """
    Vérifie si le domaine contient une proportion élevée de chiffres.

    Les domaines avec beaucoup de chiffres sont souvent générés
    automatiquement et utilisés pour des activités malveillantes.

    Args:
        domain (str): Le domaine à vérifier

    Returns:
        dict: Résultat de la vérification
    """
    # Supprimer le préfixe www. s'il existe
    if domain.startswith('www.'):
        domain = domain[4:]

    # Obtenir la partie principale du domaine (avant le TLD)
    domain_parts = domain.split('.')
    main_domain = domain_parts[0] if len(domain_parts) > 0 else domain

    digit_count = sum(c.isdigit() for c in main_domain)
    total_len = len(main_domain)

    if total_len > 0:
        digit_ratio = digit_count / total_len
        if digit_ratio > 0.5 and digit_count >= 6:  # Plus de 50% de chiffres et au moins 6 chiffres
            return {
                'name': 'Numeric Domain',
                'result': 'Failed',
                'description': f'Domain contains a high proportion of numbers ({digit_count}/{total_len})',
                'weight': 5,
                'high_risk': True
            }

    return {
        'name': 'Numeric Domain',
        'result': 'Passed',
        'description': 'Domain contains a normal number of digits',
        'weight': 5
    }

def check_random_domain(domain):
    """
    Vérifie si le domaine semble être généré aléatoirement.

    Les domaines aléatoires sont souvent utilisés pour des campagnes
    de phishing temporaires pour éviter la détection.

    Args:
        domain (str): Le domaine à vérifier

    Returns:
        dict: Résultat de la vérification
    """
    # Supprimer le préfixe www. s'il existe
    if domain.startswith('www.'):
        domain = domain[4:]

    # Obtenir la partie principale du domaine (avant le TLD)
    domain_parts = domain.split('.')
    main_domain = domain_parts[0] if len(domain_parts) > 0 else domain

    # Vérifier l'entropie du domaine (caractère aléatoire)
    has_digits = any(c.isdigit() for c in main_domain)
    has_letters = any(c.isalpha() for c in main_domain)
    has_both = has_digits and has_letters

    # Vérifier les séquences alphanumériques longues
    if has_both and len(main_domain) >= 10:
        # Compter les transitions entre types de caractères
        transitions = 0
        for i in range(1, len(main_domain)):
            if (main_domain[i-1].isdigit() and main_domain[i].isalpha()) or \
               (main_domain[i-1].isalpha() and main_domain[i].isdigit()):
                transitions += 1

        # Vérifier les motifs hexadécimaux (communs dans les domaines aléatoires)
        hex_chars = set(string.hexdigits.lower())
        hex_ratio = sum(1 for c in main_domain.lower() if c in hex_chars) / len(main_domain)

        if transitions >= 2 or hex_ratio > 0.8:
            return {
                'name': 'Random Domain',
                'result': 'Failed',
                'description': 'Domain appears to be randomly generated',
                'weight': 20,
                'high_risk': True
            }

    return {
        'name': 'Random Domain',
        'result': 'Passed',
        'description': 'Domain does not appear randomly generated',
        'weight': 20
    }

def check_temporary_domain(domain):
    """
    Vérifie si le domaine utilise des motifs de domaines temporaires/jetables.

    Les services de raccourcissement d'URL et domaines temporaires
    sont souvent utilisés pour masquer la véritable destination.

    Args:
        domain (str): Le domaine à vérifier

    Returns:
        dict: Résultat de la vérification
    """
    temporary_patterns = [
        'temporary', 'temp', 'disposable', 'shorturl', 'tinyurl', 'bit.ly',
        'goo.gl', 't.co', 'is.gd', 'shorte.st', 'ow.ly', 'buff.ly', 'adf.ly',
        'tiny.cc', 'lnkd.in', 'db.tt', 'qr.ae', 'link.zip', 'cutt.ly', 'link',
        'shortlink', 'short'
    ]

    for pattern in temporary_patterns:
        if pattern in domain.lower():
            return {
                'name': 'Temporary Domain',
                'result': 'Failed',
                'description': f'Domain uses temporary domain pattern ({pattern})',
                'weight': 12,
                'high_risk': True
            }

    return {
        'name': 'Temporary Domain',
        'result': 'Passed',
        'description': 'Domain does not appear to be a temporary link',
        'weight': 12
    }

def check_url_parameters(query_string):
    """
    Vérifie la présence de paramètres d'URL suspects.

    Certains paramètres peuvent indiquer des tentatives de phishing,
    notamment ceux liés à l'authentification ou aux services bancaires.

    Args:
        query_string (str): La chaîne de requête de l'URL

    Returns:
        dict: Résultat de la vérification
    """
    suspicious_params = [
        'login', 'password', 'user', 'account', 'auth', 'token', 'session',
        'verify', 'secure', 'banking', 'update', 'recover', 'unlock', 'ebay',
        'paypal', 'apple', 'microsoft', 'amazon', 'google', 'facebook', 'signin',
        'security', 'confirm'
    ]

    # Vérifier les paramètres suspects
    has_suspicious = False
    params = query_string.lower().split('&')

    for param in params:
        for suspicious in suspicious_params:
            if suspicious in param:
                has_suspicious = True
                break

    # Vérifier les paramètres chiffrés ou encodés
    encoded_pattern = re.compile(r'=[A-Za-z0-9+/]{20,}={0,2}$')  # Motif Base64
    has_encoded = any(encoded_pattern.search(param) for param in params)

    # Paramètres d'apparence aléatoire (communs dans le phishing)
    random_param_pattern = re.compile(r'=[A-Za-z0-9]{10,}$')  # Motif de chaîne aléatoire
    has_random_params = any(random_param_pattern.search(param) for param in params)

    if has_suspicious or has_encoded or has_random_params:
        return {
            'name': 'URL Parameters',
            'result': 'Failed',
            'description': 'URL contains suspicious query parameters',
            'weight': 5,
            'high_risk': has_encoded  # Les paramètres encodés sont à haut risque
        }

    return {
        'name': 'URL Parameters',
        'result': 'Passed',
        'description': 'URL parameters appear normal',
        'weight': 5
    }

# ============================================================================
# INTÉGRATIONS API EXTERNES POUR VÉRIFICATION DE RÉPUTATION
# ============================================================================

def query_urlscan(url, wait_for_scan=False):
    """
    Interroge URLscan.io pour obtenir des informations sur une URL.

    URLscan.io est un service qui analyse les URLs et fournit des informations
    détaillées sur leur contenu, leur sécurité et leur réputation.

    Args:
        url (str): URL à scanner
        wait_for_scan (bool): Si True, attend que le scan soit terminé (max 30 secondes)

    Returns:
        dict: Dictionary with scan results or None in case of failure
    """
    try:
        # Prepare scan request
        headers = {
            'API-Key': URLSCAN_API_KEY,
            'Content-Type': 'application/json'
        }
        data = {
            'url': url,
            'visibility': 'public'  # public, unlisted, ou private
        }

        # Submit scan request
        response = requests.post(URLSCAN_SUBMIT_URL, headers=headers, json=data)

        # Handle response errors
        if response.status_code != 200:
            if response.status_code == 429:
                # Rate limit exceeded
                return {
                    'success': False,
                    'error': 'Rate limit exceeded for URLscan.io API',
                    'status': 'rate_limited'
                }
            elif response.status_code == 400:
                # Mauvaise requête ou URL déjà scannée récemment
                return {
                    'success': False,
                    'error': response.json().get('message', 'Bad request or URL already scanned'),
                    'status': 'error'
                }
            else:
                return {
                    'success': False,
                    'error': f"URLscan.io API error: {response.status_code}",
                    'status': 'error'
                }

        # Obtenir l'UUID du scan
        scan_data = response.json()
        scan_uuid = scan_data.get('uuid')

        if not scan_uuid:
            return {
                'success': False,
                'error': 'No scan UUID received',
                'status': 'error'
            }

        # Si l'utilisateur ne veut pas attendre les résultats
        if not wait_for_scan:
            return {
                'success': True,
                'message': 'Scan submitted successfully',
                'scan_uuid': scan_uuid,
                'scan_url': scan_data.get('result'),
                'status': 'submitted'
            }

        # Attendre que le scan soit terminé et obtenir les résultats (max 30 secondes)
        max_retries = 6
        for i in range(max_retries):
            time.sleep(5)  # Attendre 5 secondes entre les vérifications
            result_response = requests.get(
                URLSCAN_RESULT_URL.format(uuid=scan_uuid),
                headers={'API-Key': URLSCAN_API_KEY}
            )

            if result_response.status_code == 200:
                result_data = result_response.json()

                # Vérifier si le scan est encore en attente
                if result_data.get('status', 0) == 404:
                    if i == max_retries - 1:
                        # Timeout en attendant les résultats
                        return {
                            'success': True,
                            'message': 'Scan still processing',
                            'scan_uuid': scan_uuid,
                            'scan_url': scan_data.get('result'),
                            'status': 'processing'
                        }
                    continue

                # Extraire les données de sécurité pertinentes
                security_data = {}

                # Obtenir les verdicts malveillants
                verdicts = result_data.get('verdicts', {})
                overall = verdicts.get('overall', {})
                security_data['malicious'] = overall.get('malicious', False)
                security_data['score'] = overall.get('score', 0)

                # Obtenir les catégories et menaces
                page_data = result_data.get('page', {})
                security_data['categories'] = page_data.get('categories', [])
                security_data['brands'] = page_data.get('brands', [])
                security_data['ip'] = page_data.get('ip', '')
                security_data['asn'] = page_data.get('asn', '')
                security_data['asnname'] = page_data.get('asnname', '')
                security_data['countries'] = page_data.get('countries', [])

                return {
                    'success': True,
                    'scan_uuid': scan_uuid,
                    'scan_url': scan_data.get('result'),
                    'security_data': security_data,
                    'full_report_url': scan_data.get('result'),
                    'status': 'completed'
                }

            elif result_response.status_code == 404:
                # Scan still being processed
                continue
            else:
                # Error retrieving results
                return {
                    'success': False,
                    'error': f"Failed to retrieve scan results: {result_response.status_code}",
                    'scan_uuid': scan_uuid,
                    'scan_url': scan_data.get('result'),
                    'status': 'error'
                }

        # If we get here, the scan is still being processed
        return {
            'success': True,
            'message': 'Scan submitted, but results not ready yet',
            'scan_uuid': scan_uuid,
            'scan_url': scan_data.get('result'),
            'status': 'pending'
        }

    except Exception as e:
        return {
            'success': False,
            'error': f"Error querying URLscan.io: {str(e)}",
            'status': 'error'
        }

def query_virustotal(url, wait_for_scan=False):
    """
    Query VirusTotal for information about a URL.
    
    Args:
        url: URL to scan
        wait_for_scan: If True, wait for scan to complete (max 30 seconds)
    
    Returns:
        Dictionary with scan results or None on failure
    """
    try:
        headers = {
            'x-apikey': VIRUSTOTAL_API_KEY,
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        # Submit URL for scanning
        data = {'url': url}
        response = requests.post(VIRUSTOTAL_URL_SCAN, headers=headers, data=data)
        
        if response.status_code != 200:
            if response.status_code == 429:
                return {
                    'success': False,
                    'error': 'Rate limit exceeded for VirusTotal API',
                    'status': 'rate_limited'
                }
            else:
                return {
                    'success': False,
                    'error': f"VirusTotal API error: {response.status_code}",
                    'status': 'error'
                }
        
        # Get the analysis ID
        result = response.json()
        analysis_id = result.get('data', {}).get('id')
        
        if not analysis_id:
            return {
                'success': False,
                'error': 'No analysis ID received',
                'status': 'error'
            }
        
        # If user doesn't want to wait for results
        if not wait_for_scan:
            return {
                'success': True,
                'message': 'Scan submitted successfully',
                'analysis_id': analysis_id,
                'status': 'submitted'
            }
        
        # Wait for scan to complete and get results
        max_retries = 6
        for i in range(max_retries):
            time.sleep(5)  # Wait 5 seconds between checks
            result_url = VIRUSTOTAL_URL_REPORT.format(id=analysis_id)
            result_response = requests.get(result_url, headers=headers)
            
            if result_response.status_code != 200:
                return {
                    'success': False,
                    'error': f"Failed to retrieve analysis results: {result_response.status_code}",
                    'analysis_id': analysis_id,
                    'status': 'error'
                }
            
            result_data = result_response.json()
            analysis_status = result_data.get('data', {}).get('attributes', {}).get('status')
            
            if analysis_status == "completed":
                attributes = result_data.get('data', {}).get('attributes', {})
                stats = attributes.get('stats', {})
                
                security_data = {
                    'malicious': stats.get('malicious', 0),
                    'suspicious': stats.get('suspicious', 0),
                    'harmless': stats.get('harmless', 0),
                    'undetected': stats.get('undetected', 0),
                    'timeout': stats.get('timeout', 0),
                    'total_engines': sum(stats.values())
                }
                
                # Calculate malicious ratio
                if security_data['total_engines'] > 0:
                    security_data['malicious_ratio'] = round(
                        (security_data['malicious'] + security_data['suspicious']) / security_data['total_engines'], 2
                    )
                else:
                    security_data['malicious_ratio'] = 0
                
                return {
                    'success': True,
                    'analysis_id': analysis_id,
                    'security_data': security_data,
                    'full_report_url': f"https://www.virustotal.com/gui/url/{analysis_id}/detection",
                    'status': 'completed'
                }
            
            elif i == max_retries - 1:
                # Timeout waiting for results
                return {
                    'success': True,
                    'message': 'Analysis still processing',
                    'analysis_id': analysis_id,
                    'status': 'processing'
                }
    
    except Exception as e:
        return {
            'success': False,
            'error': f"Error querying VirusTotal: {str(e)}",
            'status': 'error'
        }

def get_detailed_domain_report(domain):
    """
    Get detailed reputation information for a domain using both VT and URLscan.io
    
    Args:
        domain: Domain name to check
        
    Returns:
        Dictionary with combined reputation data
    """
    domain = domain.lower().strip()
    if domain.startswith('www.'):
        domain = domain[4:]
        
    # Prepare results dictionary
    result = {
        'domain': domain,
        'reputation': {
            'is_malicious': False,
            'confidence': 0,
            'sources': []
        },
        'details': {}
    }
    
    # Query VirusTotal
    vt_url = f"https://{domain}"
    vt_result = query_virustotal(vt_url, wait_for_scan=True)
    
    if vt_result and vt_result.get('success') and vt_result.get('status') == 'completed':
        security_data = vt_result.get('security_data', {})
        result['details']['virustotal'] = security_data
        
        # Add to sources
        if security_data.get('malicious', 0) > 0:
            malicious_ratio = security_data.get('malicious_ratio', 0)
            result['reputation']['sources'].append({
                'name': 'VirusTotal',
                'is_malicious': malicious_ratio >= 0.02,  # Flag as malicious if 2% or more engines detect it
                'confidence': min(malicious_ratio * 100, 100),  # Convert ratio to percentage, max 100
                'report_url': vt_result.get('full_report_url', '')
            })
            
    # Query URLscan.io
    us_url = f"https://{domain}"
    us_result = query_urlscan(us_url, wait_for_scan=True)
    
    if us_result and us_result.get('success') and us_result.get('status') == 'completed':
        security_data = us_result.get('security_data', {})
        result['details']['urlscan'] = security_data
        
        # Add to sources
        if security_data.get('malicious', False):
            result['reputation']['sources'].append({
                'name': 'URLscan.io',
                'is_malicious': True,
                'confidence': security_data.get('score', 0) * 100,  # Convert score to percentage
                'report_url': us_result.get('scan_url', '')
            })
    
    # Determine overall verdict
    if result['reputation']['sources']:
        # Calculate average confidence across sources that flag as malicious
        malicious_sources = [s for s in result['reputation']['sources'] if s.get('is_malicious')]
        if malicious_sources:
            result['reputation']['is_malicious'] = True
            confidence_sum = sum(s.get('confidence', 0) for s in malicious_sources)
            result['reputation']['confidence'] = round(confidence_sum / len(malicious_sources), 1)
    
    return result

def format_api_results_as_check(results):
    """
    Format API results as a check object compatible with our phishing detection system
    
    Args:
        results: Results from get_detailed_domain_report
        
    Returns:
        Check object dictionary
    """
    if not results or 'reputation' not in results:
        return None
    
    rep = results['reputation']
    sources_text = []
    
    for source in rep.get('sources', []):
        sources_text.append(
            f"{source['name']}: {'Flagged as malicious' if source['is_malicious'] else 'Clean'} "
            f"({source['confidence']}% confidence)"
        )
    
    if not sources_text:
        sources_text = ["No reputation data available from external services"]
    
    if rep.get('is_malicious'):
        return {
            'name': 'Reputation Check',
            'result': 'Failed',
            'description': f"Domain has been flagged as malicious by security services (Confidence: {rep['confidence']}%). " + 
                          " | ".join(sources_text),
            'weight': 5,
            'high_risk': rep['confidence'] > 50
        }
    else:
        return {
            'name': 'Reputation Check',
            'result': 'Passed',
            'description': "Domain has not been flagged as malicious by security services. " +
                          " | ".join(sources_text),
            'weight': 5
        }

def check_reputation(url):
    """
    Check URL reputation using VirusTotal and URLscan.io
    
    Args:
        url: URL to check
        
    Returns:
        Check object dictionary
    """
    try:
        parsed_url = urlparse(url)
        domain = parsed_url.netloc
        if not domain:
            return None
            
        # Get reputation data
        reputation_results = get_detailed_domain_report(domain)
        
        # Format as check
        return format_api_results_as_check(reputation_results)
        
    except Exception as e:
        return {
            'name': 'Reputation Check',
            'result': 'Warning',
            'description': f"Could not perform reputation check: {str(e)}",
            'weight': 1
        }


# ============================================================================
# FONCTIONS DE DÉTECTION AVANCÉE
# ============================================================================

def detect_brand_impersonation(url, html_content=None):
    """
    Détecte si un site web usurpe l'identité d'une marque populaire.

    Cette fonction vérifie si le domaine contient des noms de marques
    connues sans être le site officiel, ce qui est un indicateur
    fort de tentative de phishing.

    Args:
        url (str): L'URL à analyser
        html_content (str): Contenu HTML optionnel de la page

    Returns:
        dict: Dictionnaire avec le résultat de la détection
    """
    parsed_url = urlparse(url)
    domain = parsed_url.netloc.lower()

    # Supprimer le préfixe www. s'il est présent
    if domain.startswith('www.'):
        domain = domain[4:]

    # Diviser le domaine par points pour obtenir le nom de domaine principal
    domain_parts = domain.split('.')
    main_domain = domain_parts[0] if domain_parts else ""

    # Rechercher les marques détectées dans le domaine
    detected_brands = []
    for brand in POPULAR_BRANDS:
        # Vérifier la présence de la marque dans le domaine mais pas une correspondance exacte
        if brand in main_domain and brand != main_domain:
            detected_brands.append(brand)

    # Si nous avons du contenu HTML, vérifier les mentions dans le contenu
    brand_mentions_in_content = []
    if html_content:
        soup = BeautifulSoup(html_content, 'lxml')
        text_content = soup.get_text().lower()

        for brand in POPULAR_BRANDS:
            if brand in text_content:
                brand_mentions_in_content.append(brand)

    # Rechercher une discordance de marque (marque mentionnée dans le contenu mais pas dans le domaine)
    brand_mismatch = any(brand for brand in brand_mentions_in_content if brand not in main_domain)

    if detected_brands:
        return {
            'name': 'Brand Impersonation',
            'result': 'Failed',
            'description': f'Domain appears to be impersonating: {", ".join(detected_brands)}',
            'weight': 5,
            'high_risk': True
        }
    elif brand_mismatch:
        return {
            'name': 'Brand Mismatch',
            'result': 'Failed',
            'description': f'Page mentions brands not reflected in the domain',
            'weight': 4,
            'high_risk': False
        }

    return {
        'name': 'Brand Impersonation',
        'result': 'Passed',
        'description': 'No brand impersonation detected',
        'weight': 5
    }

def detect_homoglyph_attack(url):
    """
    Détecte les attaques par homoglyphes où des caractères visuellement similaires sont utilisés.

    Les attaques par homoglyphes utilisent des caractères qui se ressemblent
    visuellement pour tromper les utilisateurs (ex: 'o' et '0', 'l' et '1').

    Args:
        url (str): L'URL à analyser

    Returns:
        dict: Dictionnaire avec le résultat de la détection
    """
    parsed_url = urlparse(url)
    domain = parsed_url.netloc.lower()

    # Caractères homoglyphes communs utilisés dans le phishing
    homoglyphs = {
        '0': 'o', 'o': '0',
        '1': 'l', 'l': '1', 'i': '1', '1': 'i',
        'rn': 'm', 'm': 'rn',
        '5': 's', 's': '5',
        'g': 'q', 'q': 'g',
        'cl': 'd', 'd': 'cl',
        'vv': 'w', 'w': 'vv'
    }

    # Vérifier les substitutions de caractères suspects
    substitutions = []
    for original, substitute in homoglyphs.items():
        if original in domain:
            # Créer un domaine avec la substitution pour comparer avec les marques populaires
            substituted_domain = domain.replace(original, substitute)
            for brand in POPULAR_BRANDS:
                if brand in substituted_domain and brand not in domain:
                    substitutions.append(f"{original} → {substitute} (possible {brand} impersonation)")

    # Vérifier les attaques homographes Punycode/IDN (noms de domaine internationalisés)
    has_punycode = "xn--" in domain

    if substitutions or has_punycode:
        description = "Possible homoglyph attack detected"
        if substitutions:
            description += f": {', '.join(substitutions)}"
        if has_punycode:
            description += " (Punycode domain)"

        return {
            'name': 'Homoglyph Attack',
            'result': 'Failed',
            'description': description,
            'weight': 5,
            'high_risk': True
        }

    return {
        'name': 'Homoglyph Attack',
        'result': 'Passed',
        'description': 'No homoglyph attack patterns detected',
        'weight': 5
    }

def detect_obfuscation_techniques(html_content):
    """
    Detect obfuscation techniques in HTML content.
    
    Args:
        html_content: The HTML content to analyze
    
    Returns:
        A dictionary with the detection result
    """
    if not html_content:
        return None
        
    soup = BeautifulSoup(html_content, 'lxml')
    
    # Check for invisible elements (common in phishing)
    invisible_elements = 0
    suspicious_styles = ['display:none', 'visibility:hidden', 'opacity:0']
    
    for tag in soup.find_all(style=True):
        style = tag.get('style', '').lower()
        if any(s in style for s in suspicious_styles):
            invisible_elements += 1
    
    # Check for obfuscated JavaScript
    scripts = soup.find_all('script')
    obfuscated_js = 0
    
    for script in scripts:
        script_content = script.string if script.string else ""
        # Look for common JS obfuscation patterns
        if script_content and (
            'eval(' in script_content or 
            'escape(' in script_content or 
            'unescape(' in script_content or
            'String.fromCharCode(' in script_content or
            re.search(r'\\x[0-9a-f]{2}', script_content) or
            re.search(r'\\u[0-9a-f]{4}', script_content)
        ):
            obfuscated_js += 1
    
    # Check for hidden iframes
    hidden_iframes = 0
    iframes = soup.find_all('iframe')
    
    for iframe in iframes:
        style = iframe.get('style', '').lower()
        if any(s in style for s in suspicious_styles) or iframe.get('width', '') == '0' or iframe.get('height', '') == '0':
            hidden_iframes += 1
    
    # Check if form action submits to different domain
    suspicious_forms = 0
    for form in soup.find_all('form'):
        action = form.get('action', '')
        if action.startswith('http'):
            form_domain = urlparse(action).netloc
            page_domain = urlparse(soup.get('url', '')).netloc if soup.get('url') else ''
            if form_domain and page_domain and form_domain != page_domain:
                suspicious_forms += 1
    
    if invisible_elements > 0 or obfuscated_js > 0 or hidden_iframes > 0 or suspicious_forms > 0:
        issues = []
        if invisible_elements > 0:
            issues.append(f"{invisible_elements} hidden elements")
        if obfuscated_js > 0:
            issues.append(f"{obfuscated_js} obfuscated scripts")
        if hidden_iframes > 0:
            issues.append(f"{hidden_iframes} hidden iframes")
        if suspicious_forms > 0:
            issues.append(f"{suspicious_forms} suspicious forms")
            
        return {
            'name': 'Content Obfuscation',
            'result': 'Failed',
            'description': f"Obfuscation techniques detected: {', '.join(issues)}",
            'weight': 5,
            'high_risk': obfuscated_js > 0 or hidden_iframes > 0  # These are particularly suspicious
        }
    
    return {
        'name': 'Content Obfuscation',
        'result': 'Passed',
        'description': 'No obfuscation techniques detected in page content',
        'weight': 5
    }

def detect_data_uri_scheme(url, html_content=None):
    """
    Detect data URI scheme usage which can be used to hide malicious code.
    
    Args:
        url: The URL to analyze
        html_content: Optional HTML content of the page
    
    Returns:
        A dictionary with the detection result
    """
    # Check if the URL itself uses a data URI scheme
    if url.startswith('data:'):
        return {
            'name': 'Data URI Scheme',
            'result': 'Failed',
            'description': 'URL uses data URI scheme which can hide malicious code',
            'weight': 5,
            'high_risk': True
        }
    
    # If we have HTML, check for data URIs in resources
    data_uri_count = 0
    if html_content:
        soup = BeautifulSoup(html_content, 'lxml')
        
        # Check all attributes that could contain URLs
        for tag in soup.find_all(['img', 'script', 'iframe', 'embed', 'object', 'a']):
            for attr in ['src', 'href', 'data']:
                if attr in tag.attrs and tag[attr].startswith('data:'):
                    data_uri_count += 1
    
    if data_uri_count > 0:
        return {
            'name': 'Data URI Resources',
            'result': 'Failed',
            'description': f'Page contains {data_uri_count} data URI resources that can hide malicious code',
            'weight': 4,
            'high_risk': data_uri_count > 3  # Multiple data URIs is more suspicious
        }
    
    return {
        'name': 'Data URI Scheme',
        'result': 'Passed',
        'description': 'No suspicious data URI schemes detected',
        'weight': 4
    }

def detect_suspicious_redirects(html_content):
    """
    Detect suspicious client-side redirects in the page.
    
    Args:
        html_content: The HTML content to analyze
    
    Returns:
        A dictionary with the detection result
    """
    if not html_content:
        return None
        
    soup = BeautifulSoup(html_content, 'lxml')
    
    # Check for meta refresh redirects
    meta_redirects = soup.find_all('meta', attrs={'http-equiv': lambda x: x and x.lower() == 'refresh'})
    meta_redirect_count = len(meta_redirects)
    
    # Check for JavaScript redirects
    js_redirects = 0
    scripts = soup.find_all('script')
    redirect_patterns = [
        'window.location', 'document.location', 
        'location.href', 'location.replace', 
        'location=', 'location ='
    ]
    
    for script in scripts:
        script_content = script.string if script.string else ""
        if script_content and any(pattern in script_content for pattern in redirect_patterns):
            js_redirects += 1
    
    # Check for immediate redirects in body onload
    body_tag = soup.find('body')
    body_onload_redirect = False
    if body_tag and body_tag.has_attr('onload'):
        onload_content = body_tag['onload'].lower()
        if any(pattern.lower() in onload_content for pattern in redirect_patterns):
            body_onload_redirect = True
    
    if meta_redirect_count > 0 or js_redirects > 0 or body_onload_redirect:
        redirect_types = []
        if meta_redirect_count > 0:
            redirect_types.append(f"{meta_redirect_count} meta refresh redirects")
        if js_redirects > 0:
            redirect_types.append(f"{js_redirects} JavaScript redirects")
        if body_onload_redirect:
            redirect_types.append("body onload redirect")
            
        return {
            'name': 'Suspicious Redirects',
            'result': 'Failed',
            'description': f"Page contains client-side redirects: {', '.join(redirect_types)}",
            'weight': 4,
            'high_risk': True
        }
    
    return {
        'name': 'Suspicious Redirects',
        'result': 'Passed',
        'description': 'No suspicious client-side redirects detected',
        'weight': 4
    }

def detect_cloaking_techniques(html_content):
    """
    Détecte si un site web utilise des techniques de masquage (cloaking).

    Le cloaking consiste à présenter un contenu différent selon le visiteur
    (utilisateur normal vs robot d'indexation), souvent utilisé pour tromper
    les moteurs de recherche et les systèmes de sécurité.

    Args:
        html_content (str): Contenu HTML de la page

    Returns:
        dict: Dictionnaire avec le résultat de la détection
    """
    if not html_content:
        return None

    soup = BeautifulSoup(html_content, 'lxml')

    # Vérifier les scripts de détection d'user-agent
    ua_detection = False
    scripts = soup.find_all('script')
    ua_patterns = [
        'navigator.userAgent', 'navigator.appName', 'navigator.appVersion',
        'navigator.vendor', 'navigator.platform'
    ]

    for script in scripts:
        script_content = script.string if script.string else ""
        if script_content and any(pattern in script_content for pattern in ua_patterns):
            ua_detection = True
            break

    # Vérifier les scripts de détection de robots d'indexation
    bot_detection = False
    bot_patterns = ['bot', 'crawler', 'spider', 'googlebot', 'bingbot', 'slurp']

    for script in scripts:
        script_content = script.string if script.string else ""
        if script_content and any(pattern.lower() in script_content.lower() for pattern in bot_patterns):
            bot_detection = True
            break

    # Vérifier les vérifications de cookies (peut indiquer un contenu différent basé sur les cookies)
    cookie_checks = False
    cookie_patterns = ['document.cookie', 'navigator.cookieEnabled']

    for script in scripts:
        script_content = script.string if script.string else ""
        if script_content and any(pattern in script_content for pattern in cookie_patterns):
            cookie_checks = True
            break

    if ua_detection or bot_detection or cookie_checks:
        cloaking_methods = []
        if ua_detection:
            cloaking_methods.append("user-agent detection")
        if bot_detection:
            cloaking_methods.append("bot detection")
        if cookie_checks:
            cloaking_methods.append("cookie checks")

        return {
            'name': 'Cloaking Techniques',
            'result': 'Warning',
            'description': f"Page may use cloaking techniques: {', '.join(cloaking_methods)}",
            'weight': 3
        }

    return {
        'name': 'Cloaking Techniques',
        'result': 'Passed',
        'description': 'No evidence of cloaking techniques',
        'weight': 3
    }

# ============================================================================
# RÉSUMÉ DU MODULE
# ============================================================================

"""
Ce module fournit un système complet de détection de phishing qui inclut :

1. VÉRIFICATIONS BASIQUES :
   - Longueur d'URL
   - Présence d'adresses IP
   - Caractères spéciaux excessifs
   - TLD suspects
   - Nombre de sous-domaines
   - Protocole HTTPS

2. VÉRIFICATIONS AVANCÉES DU DOMAINE :
   - Âge du domaine
   - Redirections
   - Certificats SSL
   - Domaines numériques
   - Domaines aléatoires
   - Domaines temporaires
   - Paramètres d'URL suspects

3. ANALYSE DU CONTENU HTML :
   - Formulaires de connexion
   - Source du favicon
   - Proportion de liens externes

4. DÉTECTIONS AVANCÉES :
   - Usurpation de marques
   - Attaques par homoglyphes
   - Techniques d'obfuscation
   - Schémas URI de données
   - Redirections suspectes
   - Techniques de masquage

5. INTÉGRATIONS API EXTERNES :
   - URLscan.io pour l'analyse de réputation
   - VirusTotal pour la vérification de sécurité

Le système utilise un scoring pondéré pour évaluer le risque global
et fournit des résultats détaillés pour chaque vérification effectuée.
"""
