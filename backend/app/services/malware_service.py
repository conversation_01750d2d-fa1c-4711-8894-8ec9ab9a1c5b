import os
import hashlib
import re
import json
import warnings
import uuid
import math
import tempfile
import time
import logging
from datetime import datetime, timezone
from typing import Dict, List, Tuple, Any, Optional
from pathlib import Path

# Import of technical detector
from .malwareDetector import MalwareDetector

# Core imports with error handling
try:
    import pefile
    PEFILE_AVAILABLE = True
except ImportError:
    PEFILE_AVAILABLE = False
    print("Warning: pefile module not found. PE analysis will be limited.")

try:
    import yara
    YARA_AVAILABLE = True
except ImportError:
    YARA_AVAILABLE = False
    print("Warning: yara-python module not found. YARA rule matching will be disabled.")

try:
    import ssdeep
    SSDEEP_AVAILABLE = True
except ImportError:
    SSDEEP_AVAILABLE = False
    print("Warning: ssdeep module not found. Fuzzy hashing will be disabled.")

try:
    import magic
    MAGIC_AVAILABLE = True
except ImportError:
    MAGIC_AVAILABLE = False
    print("Warning: python-magic module not found. File type detection will be limited.")

# Suppress warnings
warnings.filterwarnings("ignore")

class MalwareService:
    """
    Malware detection and analysis service for PICA.
    Analyzes files to detect indicators of compromise (IoCs) and suspicious patterns.
    """

    def __init__(self, yara_rules_path: str = None):
        """
        Initialize the malware detection service.

        Args:
            yara_rules_path: Path to YARA rules (optional)
        """
        self.yara_rules = None
        self.logger = logging.getLogger(__name__)

        # Load YARA rules if available
        if YARA_AVAILABLE and yara_rules_path and os.path.exists(yara_rules_path):
            try:
                self.yara_rules = yara.compile(filepath=yara_rules_path)
                self.logger.info(f"YARA rules loaded from {yara_rules_path}")
            except Exception as e:
                self.logger.warning(f"Failed to load YARA rules: {e}")

        # Configure suspicious patterns
        self._init_suspicious_patterns()

        # Configure suspicious APIs with threat scores
        self._init_suspicious_apis()

        # Configure suspicious file extensions
        self._init_suspicious_extensions()

        # Initialize integrated malware detector
        self._init_malware_detector()

        # Initialize advanced technical detector
        self.detector = MalwareDetector(yara_rules=self.yara_rules)

    def _init_suspicious_patterns(self):
        """Initialize suspicious patterns to search for in files."""
        self.suspicious_strings = [
            b'powershell', b'cmd.exe', b'regsvr32', b'rundll32',
            b'http://', b'https://', b'ftp://', b'Start-Process',
            b'Invoke-Expression', b'DownloadString', b'WebClient',
            b'ShellExecute', b'CreateObject', b'WScript.Shell',
            b'CreateRemoteThread', b'VirtualAlloc', b'WriteProcessMemory',
            b'URLDownloadToFile', b'InternetOpenUrl', b'InternetReadFile',
            b'RegCreateKey', b'RegSetValue', b'RegOpenKey', b'RegDeleteKey',
            b'GetProcAddress', b'LoadLibrary', b'GetModuleHandle',
            b'CreateService', b'StartService', b'OpenService'
        ]

        # Regex patterns for IOCs
        self.ip_pattern = re.compile(
            r'\b(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b'
        )
        self.domain_pattern = re.compile(
            r'\b(?!-)(?:(?!\d+\.\d+\.\d+\.\d+)(?:[a-zA-Z0-9][a-zA-Z0-9\-]{0,61}[a-zA-Z0-9]?\.)+[a-zA-Z]{2,})\b'
        )
        self.url_pattern = re.compile(
            r'https?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        )
        self.email_pattern = re.compile(
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        )

    def _init_suspicious_apis(self):
        """Initialize the list of suspicious APIs with their threat scores."""
        self.suspicious_apis = {
            # Memory manipulation
            'VirtualAlloc': 3, 'VirtualProtect': 4, 'VirtualAllocEx': 3,
            'VirtualProtectEx': 4, 'WriteProcessMemory': 5, 'ReadProcessMemory': 3,

            # Manipulation de threads
            'CreateRemoteThread': 7, 'CreateThread': 5, 'QueueUserAPC': 4,
            'SetWindowsHookEx': 3,

            # Manipulation de processus
            'OpenProcess': 3, 'DebugActiveProcess': 4, 'TerminateProcess': 3,
            'CreateProcessInternalW': 3, 'CreateProcessAsUserW': 4,

            # Chargement DLL/Fonctions
            'LoadLibraryA': 2, 'LoadLibraryW': 2, 'GetProcAddress': 2,
            'GetModuleHandleA': 2, 'GetModuleHandleW': 2,

            # Anti-debugging
            'IsDebuggerPresent': 3, 'CheckRemoteDebuggerPresent': 3,
            'OutputDebugStringA': 2, 'OutputDebugStringW': 2,

            # Cryptographie
            'CryptAcquireContext': 2, 'CryptCreateHash': 2, 'CryptDecrypt': 3,
            'CryptEncrypt': 3, 'CryptGenKey': 2, 'CryptGenRandom': 2
        }

        # Séquences d'APIs indiquant un comportement de déballage
        self.unpacking_sequences = [
            ['VirtualAlloc', 'WriteProcessMemory', 'CreateThread'],
            ['VirtualAlloc', 'VirtualProtect', 'CreateThread'],
            ['LoadLibrary', 'GetProcAddress', 'VirtualAlloc', 'VirtualProtect']
        ]

    def _init_suspicious_extensions(self):
        """Initialise la liste des extensions de fichiers suspectes."""
        self.suspicious_extensions = [
            '.exe', '.dll', '.sys', '.bat', '.cmd', '.vbs', '.vbe', '.js', '.jse',
            '.wsf', '.wsh', '.ps1', '.ps1xml', '.ps2', '.ps2xml', '.psc1', '.psc2',
            '.msc', '.msh', '.msh1', '.msh2', '.scf', '.lnk', '.inf', '.reg',
            '.pif', '.scr', '.cpl', '.msi', '.msp', '.com', '.ocx', '.jar',
            '.class', '.swf', '.application', '.gadget', '.hta'
        ]

    def _init_malware_detector(self):
        """Initialise le détecteur de malware intégré avec toutes les fonctionnalités avancées."""
        # Initialiser les patterns de détection avancés
        self.whitelisted_publishers = [
            'Microsoft Corporation', 'Google Inc.', 'Adobe Systems Incorporated',
            'Oracle Corporation', 'Mozilla Corporation', 'Apple Inc.',
            'Intel Corporation', 'NVIDIA Corporation', 'Symantec Corporation'
        ]

        self.known_legitimate_packers = [
            'UPX', 'ASPack', 'PECompact', 'Themida', 'VMProtect'
        ]

        self.legitimate_api_patterns = [
            ['CreateFile', 'ReadFile', 'WriteFile', 'CloseHandle'],
            ['RegOpenKey', 'RegQueryValue', 'RegCloseKey'],
            ['LoadLibrary', 'GetProcAddress', 'FreeLibrary']
        ]

        # Patterns de registre suspects
        self.suspicious_registry = [
            r'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run',
            r'HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run',
            r'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunOnce',
            r'HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunOnce',
            r'HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services',
            r'HKEY_LOCAL_MACHINE\\SOFTWARE\\Classes\\exefile\\shell\\open\\command',
            r'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Winlogon\\Shell'
        ]

        # Imports suspects
        self.suspicious_imports = [
            'CreateRemoteThread', 'WriteProcessMemory', 'VirtualAllocEx',
            'SetWindowsHookEx', 'GetAsyncKeyState', 'FindWindow',
            'URLDownloadToFile', 'InternetOpenUrl', 'InternetReadFile',
            'CryptAcquireContext', 'CryptCreateHash', 'CryptHashData'
        ]

        # Utiliser les bases de données de packers du détecteur
        self.packer_map = {}
        self.known_sections = []

        print("🔧 Advanced malware detector initialized")

    def is_available(self) -> bool:
        """Vérifie si le service de détection de malware est disponible."""
        try:
            print(f"🔍 Checking Malware Detection Service availability...")

            # Vérifier les dépendances critiques
            if not PEFILE_AVAILABLE:
                print("⚠️ PE file analysis not available - pefile module missing")
            else:
                print("✅ PE file analysis available")

            if not YARA_AVAILABLE:
                print("⚠️ YARA rule matching not available - yara-python module missing")
            else:
                print("✅ YARA rule matching available")

            if not SSDEEP_AVAILABLE:
                print("⚠️ Fuzzy hashing not available - ssdeep module missing")
            else:
                print("✅ Fuzzy hashing available")

            if not MAGIC_AVAILABLE:
                print("⚠️ File type detection limited - python-magic module missing")
            else:
                print("✅ Advanced file type detection available")

            # Le service est considéré comme disponible même avec des fonctionnalités limitées
            print("✅ Malware Detection Service is available")
            return True

        except Exception as e:
            print(f"❌ Malware service availability check failed: {e}")
            return False

    def analyze_file(self, file_path: str, file_name: str = None) -> Dict[str, Any]:
        """
        Analyse un fichier pour détecter les indicateurs de malware.

        Args:
            file_path: Chemin vers le fichier à analyser
            file_name: Nom du fichier (optionnel)

        Returns:
            Dict contenant les résultats de l'analyse
        """
        analysis_id = str(uuid.uuid4())
        start_time = datetime.now(timezone.utc)

        if not file_name:
            file_name = os.path.basename(file_path)

        try:
            print(f"🔍 Starting malware analysis for {file_name}")

            # Vérifier que le fichier existe
            if not os.path.exists(file_path):
                return {
                    'analysis_id': analysis_id,
                    'status': 'error',
                    'message': 'File not found',
                    'file_name': file_name
                }

            # Informations de base du fichier
            file_info = self._get_file_info(file_path)

            # Calcul des hashes avec le détecteur
            hashes = self.detector.calculate_hashes(file_path) or self._calculate_hashes(file_path)

            # Détection du type de fichier avec le détecteur
            file_type = self._detect_file_type(file_path)
            file_type['mime_type'] = self.detector.get_file_type(file_path)

            # Analyse des strings suspectes
            suspicious_strings = self._analyze_strings(file_path)

            # Extraction des IOCs avec le détecteur
            iocs = self.detector.extract_iocs(file_path)

            # Analyse PE si applicable avec le détecteur avancé
            pe_analysis = None
            if file_type.get('is_pe', False):
                pe_analysis = self.detector.analyze_pe_advanced(file_path)
                if 'error' in pe_analysis and PEFILE_AVAILABLE:
                    # Fallback vers l'analyse PE basique
                    pe_analysis = self._analyze_pe_file(file_path)

            # Analyse YARA si disponible avec le détecteur
            yara_matches = self.detector.analyze_with_yara(file_path)

            # Analyse avancée avec le détecteur intégré
            advanced_analysis = self._perform_advanced_analysis(file_path, file_type)

            # Calcul du score de menace
            threat_score = self._calculate_threat_score(
                file_info, suspicious_strings, iocs, pe_analysis, yara_matches, advanced_analysis
            )

            end_time = datetime.now(timezone.utc)
            duration = (end_time - start_time).total_seconds()

            result = {
                'analysis_id': analysis_id,
                'status': 'completed',
                'file_name': file_name,
                'file_path': file_path,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration': duration,
                'file_info': file_info,
                'hashes': hashes,
                'file_type': file_type,
                'threat_score': threat_score,
                'is_malware': threat_score >= 50,  # Seuil de détection
                'suspicious_strings': suspicious_strings,
                'iocs': iocs,
                'pe_analysis': pe_analysis,
                'yara_matches': yara_matches,
                'advanced_analysis': advanced_analysis,
                'recommendations': self._generate_recommendations(threat_score, file_type)
            }

            print(f"✅ Analysis completed for {file_name} - Threat score: {threat_score}")
            return result

        except Exception as e:
            print(f"❌ Error analyzing file {file_name}: {e}")
            return {
                'analysis_id': analysis_id,
                'status': 'error',
                'message': str(e),
                'file_name': file_name,
                'file_path': file_path
            }

    def _get_file_info(self, file_path: str) -> Dict[str, Any]:
        """Récupère les informations de base du fichier."""
        try:
            stat = os.stat(file_path)
            return {
                'size': stat.st_size,
                'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'accessed': datetime.fromtimestamp(stat.st_atime).isoformat(),
                'permissions': oct(stat.st_mode)[-3:]
            }
        except Exception as e:
            self.logger.warning(f"Failed to get file info: {e}")
            return {}

    def _calculate_hashes(self, file_path: str) -> Dict[str, str]:
        """Calcule les hashes du fichier."""
        hashes = {}
        try:
            with open(file_path, 'rb') as f:
                content = f.read()

            hashes['md5'] = hashlib.md5(content).hexdigest()
            hashes['sha1'] = hashlib.sha1(content).hexdigest()
            hashes['sha256'] = hashlib.sha256(content).hexdigest()

            # Fuzzy hash si disponible
            if SSDEEP_AVAILABLE:
                try:
                    hashes['ssdeep'] = ssdeep.hash(content)
                except Exception as e:
                    self.logger.warning(f"Failed to calculate ssdeep hash: {e}")

        except Exception as e:
            self.logger.warning(f"Failed to calculate hashes: {e}")

        return hashes

    def _detect_file_type(self, file_path: str) -> Dict[str, Any]:
        """Détecte le type de fichier."""
        file_type = {
            'extension': Path(file_path).suffix.lower(),
            'is_pe': False,
            'is_suspicious_extension': False,
            'mime_type': None,
            'description': None
        }

        try:
            # Vérifier l'extension
            if file_type['extension'] in self.suspicious_extensions:
                file_type['is_suspicious_extension'] = True

            # Utiliser python-magic si disponible
            if MAGIC_AVAILABLE:
                try:
                    file_type['mime_type'] = magic.from_file(file_path, mime=True)
                    file_type['description'] = magic.from_file(file_path)
                except Exception as e:
                    self.logger.warning(f"Magic detection failed: {e}")

            # Vérifier si c'est un fichier PE
            if PEFILE_AVAILABLE:
                try:
                    pe = pefile.PE(file_path)
                    file_type['is_pe'] = True
                    pe.close()
                except:
                    pass

        except Exception as e:
            self.logger.warning(f"File type detection failed: {e}")

        return file_type

    def scan_directory(self, directory_path: str, recursive: bool = True) -> Dict[str, Any]:
        """
        Analyse tous les fichiers d'un répertoire.

        Args:
            directory_path: Chemin du répertoire à analyser
            recursive: Analyser récursivement les sous-répertoires

        Returns:
            Dict contenant les résultats de l'analyse du répertoire
        """
        scan_id = str(uuid.uuid4())
        start_time = datetime.now(timezone.utc)

        try:
            print(f"🔍 Starting directory malware scan: {directory_path}")

            if not os.path.exists(directory_path):
                return {
                    'scan_id': scan_id,
                    'status': 'error',
                    'message': 'Directory not found'
                }

            files_analyzed = []
            malware_detected = []
            total_files = 0

            # Parcourir le répertoire
            if recursive:
                for root, _, files in os.walk(directory_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        total_files += 1

                        # Analyser le fichier
                        result = self.analyze_file(file_path, file)
                        files_analyzed.append(result)

                        if result.get('is_malware', False):
                            
                            malware_detected.append(result)
            else:
                for file in os.listdir(directory_path):
                    file_path = os.path.join(directory_path, file)
                    if os.path.isfile(file_path):
                        total_files += 1

                        # Analyser le fichier
                        result = self.analyze_file(file_path, file)
                        files_analyzed.append(result)

                        if result.get('is_malware', False):
                            malware_detected.append(result)

            end_time = datetime.now(timezone.utc)
            duration = (end_time - start_time).total_seconds()
            print("is malware", malware_detected)
            print(f"✅ Directory scan completed - {len(malware_detected)} malware detected out of {total_files} files")

            return {
                'scan_id': scan_id,
                'status': 'completed',
                'directory_path': directory_path,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration': duration,
                'total_files': total_files,
                'files_analyzed': len(files_analyzed),
                'malware_detected': len(malware_detected),
                'results': files_analyzed,
                'malware_files': malware_detected
            }

        except Exception as e:
            print(f"❌ Directory scan failed: {e}")
            return {
                'scan_id': scan_id,
                'status': 'error',
                'message': str(e),
                'directory_path': directory_path
            }

    def get_analysis_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Récupère l'historique des analyses (à implémenter avec une base de données)."""
        # Cette méthode sera implémentée quand la base de données sera configurée
        # Le paramètre limit sera utilisé pour limiter le nombre de résultats
        _ = limit  # Éviter le warning unused variable
        return []

    def get_service_status(self) -> Dict[str, Any]:
        """Retourne le statut détaillé du service."""
        return {
            'service_name': 'MalwareService',
            'status': 'available' if self.is_available() else 'unavailable',
            'capabilities': {
                'pe_analysis': PEFILE_AVAILABLE,
                'yara_rules': YARA_AVAILABLE and self.yara_rules is not None,
                'fuzzy_hashing': SSDEEP_AVAILABLE,
                'file_type_detection': MAGIC_AVAILABLE
            },
            'supported_extensions': self.suspicious_extensions,
            'threat_threshold': 70
        }

    def _get_file_info(self, file_path: str) -> Dict[str, Any]:
        """Récupère les informations de base du fichier."""
        try:
            stat = os.stat(file_path)
            return {
                'size': stat.st_size,
                'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'accessed': datetime.fromtimestamp(stat.st_atime).isoformat(),
                'permissions': oct(stat.st_mode)[-3:]
            }
        except Exception as e:
            self.logger.warning(f"Failed to get file info: {e}")
            return {}

    def _calculate_hashes(self, file_path: str) -> Dict[str, str]:
        """Calcule les hashes du fichier."""
        hashes = {}
        try:
            with open(file_path, 'rb') as f:
                content = f.read()

            hashes['md5'] = hashlib.md5(content).hexdigest()
            hashes['sha1'] = hashlib.sha1(content).hexdigest()
            hashes['sha256'] = hashlib.sha256(content).hexdigest()

            # Fuzzy hash si disponible
            if SSDEEP_AVAILABLE:
                try:
                    hashes['ssdeep'] = ssdeep.hash(content)
                except Exception as e:
                    self.logger.warning(f"Failed to calculate ssdeep hash: {e}")

        except Exception as e:
            self.logger.warning(f"Failed to calculate hashes: {e}")

        return hashes

    def _detect_file_type(self, file_path: str) -> Dict[str, Any]:
        """Détecte le type de fichier."""
        file_type = {
            'extension': Path(file_path).suffix.lower(),
            'is_pe': False,
            'is_suspicious_extension': False,
            'mime_type': None,
            'description': None
        }

        try:
            # Vérifier l'extension
            if file_type['extension'] in self.suspicious_extensions:
                file_type['is_suspicious_extension'] = True

            # Utiliser python-magic si disponible
            if MAGIC_AVAILABLE:
                try:
                    file_type['mime_type'] = magic.from_file(file_path, mime=True)
                    file_type['description'] = magic.from_file(file_path)
                except Exception as e:
                    self.logger.warning(f"Magic detection failed: {e}")

            # Vérifier si c'est un fichier PE
            if PEFILE_AVAILABLE:
                try:
                    pe = pefile.PE(file_path)
                    file_type['is_pe'] = True
                    pe.close()
                except:
                    pass

        except Exception as e:
            self.logger.warning(f"File type detection failed: {e}")

        return file_type

    def _analyze_strings(self, file_path: str) -> Dict[str, Any]:
        """Analyse les strings suspectes dans le fichier."""
        suspicious_found = []
        total_strings = 0

        try:
            with open(file_path, 'rb') as f:
                content = f.read()

            # Rechercher les strings suspectes
            for suspicious_string in self.suspicious_strings:
                if suspicious_string in content:
                    suspicious_found.append({
                        'string': suspicious_string.decode('utf-8', errors='ignore'),
                        'type': 'suspicious_pattern'
                    })

            # Extraire les strings ASCII imprimables
            ascii_strings = re.findall(b'[ -~]{4,}', content)
            total_strings = len(ascii_strings)

            # Analyser les strings pour des patterns suspects
            for string in ascii_strings[:100]:  # Limiter à 100 strings
                string_decoded = string.decode('ascii', errors='ignore')

                # Vérifier les patterns d'obfuscation
                if self._is_obfuscated_string(string_decoded):
                    suspicious_found.append({
                        'string': string_decoded,
                        'type': 'obfuscated'
                    })

                # Vérifier les commandes système
                if any(cmd in string_decoded.lower() for cmd in ['cmd', 'powershell', 'bash', 'sh']):
                    suspicious_found.append({
                        'string': string_decoded,
                        'type': 'system_command'
                    })

        except Exception as e:
            self.logger.warning(f"String analysis failed: {e}")

        return {
            'total_strings': total_strings,
            'suspicious_count': len(suspicious_found),
            'suspicious_strings': suspicious_found[:20]  # Limiter l'output
        }

    def _is_obfuscated_string(self, string: str) -> bool:
        """Vérifie si une string semble obfusquée."""
        if len(string) < 10:
            return False

        # Calculer l'entropie
        entropy = self._calculate_entropy(string)

        # Vérifier les patterns d'obfuscation
        has_high_entropy = entropy > 4.5
        has_mixed_case = any(c.isupper() for c in string) and any(c.islower() for c in string)
        has_numbers = any(c.isdigit() for c in string)
        has_special_chars = any(not c.isalnum() for c in string)

        # Score d'obfuscation
        obfuscation_score = sum([has_high_entropy, has_mixed_case, has_numbers, has_special_chars])

        return obfuscation_score >= 3

    def _calculate_entropy(self, string: str) -> float:
        """Calcule l'entropie d'une string."""
        if not string:
            return 0

        # Compter les fréquences des caractères
        char_counts = {}
        for char in string:
            char_counts[char] = char_counts.get(char, 0) + 1

        # Calculer l'entropie
        entropy = 0
        string_length = len(string)

        for count in char_counts.values():
            probability = count / string_length
            if probability > 0:
                entropy -= probability * math.log2(probability)

        return entropy

    def _extract_iocs(self, file_path: str) -> Dict[str, List[str]]:
        """Extrait les indicateurs de compromission (IOCs) du fichier."""
        iocs = {
            'ip_addresses': [],
            'domains': [],
            'urls': [],
            'email_addresses': []
        }

        try:
            with open(file_path, 'rb') as f:
                content = f.read()

            # Convertir en texte pour l'analyse regex
            text_content = content.decode('utf-8', errors='ignore')

            # Extraire les adresses IP
            ip_matches = self.ip_pattern.findall(text_content)
            iocs['ip_addresses'] = list(set(ip_matches))  # Supprimer les doublons

            # Extraire les domaines
            domain_matches = self.domain_pattern.findall(text_content)
            # Filtrer les faux positifs
            filtered_domains = []
            for domain in domain_matches:
                if self._is_valid_domain(domain):
                    filtered_domains.append(domain)
            iocs['domains'] = list(set(filtered_domains))

            # Extraire les URLs
            url_matches = self.url_pattern.findall(text_content)
            iocs['urls'] = list(set(url_matches))

            # Extraire les adresses email
            email_matches = self.email_pattern.findall(text_content)
            iocs['email_addresses'] = list(set(email_matches))

        except Exception as e:
            self.logger.warning(f"IOC extraction failed: {e}")

        return iocs

    def _is_valid_domain(self, domain: str) -> bool:
        """Vérifie si un domaine semble valide et non un faux positif."""
        # Filtrer les domaines trop courts ou avec des patterns suspects
        if len(domain) < 4:
            return False

        # Exclure les extensions de fichiers communes
        file_extensions = ['.exe', '.dll', '.txt', '.log', '.tmp', '.dat']
        if any(domain.lower().endswith(ext) for ext in file_extensions):
            return False

        # Exclure les domaines avec trop de chiffres
        digit_ratio = sum(c.isdigit() for c in domain) / len(domain)
        if digit_ratio > 0.5:
            return False

        return True

    def _analyze_pe_file(self, file_path: str) -> Dict[str, Any]:
        """Analyse un fichier PE (Portable Executable)."""
        if not PEFILE_AVAILABLE:
            return None

        pe_info = {
            'is_valid': False,
            'architecture': None,
            'subsystem': None,
            'compilation_timestamp': None,
            'entry_point': None,
            'sections': [],
            'imports': [],
            'exports': [],
            'suspicious_apis': [],
            'packer_detected': False,
            'digital_signature': None
        }

        try:
            pe = pefile.PE(file_path)
            pe_info['is_valid'] = True

            # Informations de base
            pe_info['architecture'] = 'x64' if pe.PE_TYPE == 0x20b else 'x86'
            pe_info['subsystem'] = pe.OPTIONAL_HEADER.Subsystem
            pe_info['compilation_timestamp'] = datetime.fromtimestamp(
                pe.FILE_HEADER.TimeDateStamp
            ).isoformat()
            pe_info['entry_point'] = hex(pe.OPTIONAL_HEADER.AddressOfEntryPoint)

            # Analyse des sections
            for section in pe.sections:
                section_info = {
                    'name': section.Name.decode('utf-8', errors='ignore').strip('\x00'),
                    'virtual_address': hex(section.VirtualAddress),
                    'virtual_size': section.Misc_VirtualSize,
                    'raw_size': section.SizeOfRawData,
                    'entropy': section.get_entropy(),
                    'characteristics': section.Characteristics
                }
                pe_info['sections'].append(section_info)

            # Analyse des imports
            if hasattr(pe, 'DIRECTORY_ENTRY_IMPORT'):
                for entry in pe.DIRECTORY_ENTRY_IMPORT:
                    dll_name = entry.dll.decode('utf-8', errors='ignore')
                    functions = []

                    for imp in entry.imports:
                        if imp.name:
                            func_name = imp.name.decode('utf-8', errors='ignore')
                            functions.append(func_name)

                            # Vérifier si c'est une API suspecte
                            if func_name in self.suspicious_apis:
                                pe_info['suspicious_apis'].append({
                                    'dll': dll_name,
                                    'function': func_name,
                                    'threat_score': self.suspicious_apis[func_name]
                                })

                    pe_info['imports'].append({
                        'dll': dll_name,
                        'functions': functions[:10]  # Limiter l'output
                    })

            # Analyse des exports
            if hasattr(pe, 'DIRECTORY_ENTRY_EXPORT'):
                for exp in pe.DIRECTORY_ENTRY_EXPORT.symbols:
                    if exp.name:
                        pe_info['exports'].append(
                            exp.name.decode('utf-8', errors='ignore')
                        )

            # Détection de packer basique
            pe_info['packer_detected'] = self._detect_packer(pe)

            pe.close()

        except Exception as e:
            self.logger.warning(f"PE analysis failed: {e}")

        return pe_info

    def _detect_packer(self, pe) -> bool:
        """Détection basique de packer dans un fichier PE."""
        try:
            # Vérifier l'entropie des sections
            high_entropy_sections = 0
            for section in pe.sections:
                if section.get_entropy() > 7.0:
                    high_entropy_sections += 1

            # Vérifier le ratio entre taille virtuelle et taille raw
            suspicious_ratios = 0
            for section in pe.sections:
                if section.SizeOfRawData > 0:
                    ratio = section.Misc_VirtualSize / section.SizeOfRawData
                    if ratio > 2.0 or ratio < 0.5:
                        suspicious_ratios += 1

            # Vérifier les noms de sections suspects
            suspicious_section_names = [
                '.upx', '.aspack', '.rlpack', '.petite', '.mew', '.upack'
            ]

            for section in pe.sections:
                section_name = section.Name.decode('utf-8', errors='ignore').lower()
                if any(sus_name in section_name for sus_name in suspicious_section_names):
                    return True

            # Score de détection de packer
            packer_score = high_entropy_sections + suspicious_ratios
            return packer_score >= 2

        except Exception:
            return False

    def _analyze_with_yara(self, file_path: str) -> List[Dict[str, Any]]:
        """Analyse le fichier avec les règles YARA."""
        if not self.yara_rules:
            return []

        matches = []
        try:
            yara_matches = self.yara_rules.match(file_path)

            for match in yara_matches:
                match_info = {
                    'rule': match.rule,
                    'namespace': match.namespace,
                    'tags': match.tags,
                    'meta': match.meta,
                    'strings': []
                }

                # Ajouter les strings matchées
                for string in match.strings:
                    match_info['strings'].append({
                        'identifier': string.identifier,
                        'instances': len(string.instances)
                    })

                matches.append(match_info)

        except Exception as e:
            self.logger.warning(f"YARA analysis failed: {e}")

        return matches

    def _perform_advanced_analysis(self, file_path: str, file_type: Dict) -> Dict[str, Any]:
        """Effectue une analyse avancée du fichier avec le détecteur intégré."""
        advanced_results = {
            'packer_detected': False,
            'suspicious_imports': [],
            'registry_modifications': [],
            'network_indicators': [],
            'behavioral_indicators': [],
            'entropy_analysis': {},
            'signature_verification': None
        }

        try:
            # Analyse d'entropie
            advanced_results['entropy_analysis'] = self._analyze_file_entropy(file_path)

            # Détection de packer avancée
            if file_type.get('is_pe', False):
                advanced_results['packer_detected'] = self._detect_advanced_packer(file_path)
                advanced_results['suspicious_imports'] = self._analyze_suspicious_imports(file_path)

            # Analyse comportementale
            advanced_results['behavioral_indicators'] = self._analyze_behavioral_patterns(file_path)

            # Analyse des indicateurs réseau
            advanced_results['network_indicators'] = self._analyze_network_patterns(file_path)

        except Exception as e:
            self.logger.warning(f"Advanced analysis failed: {e}")

        return advanced_results

    def _analyze_file_entropy(self, file_path: str) -> Dict[str, float]:
        """Analyse l'entropie du fichier par sections."""
        entropy_results = {'overall': 0.0, 'sections': []}

        try:
            with open(file_path, 'rb') as f:
                content = f.read()

            # Entropie globale
            entropy_results['overall'] = self._calculate_entropy(content.decode('latin-1', errors='ignore'))

            # Analyse par chunks de 1024 bytes
            chunk_size = 1024
            for i in range(0, len(content), chunk_size):
                chunk = content[i:i+chunk_size]
                if len(chunk) > 0:
                    chunk_entropy = self._calculate_entropy(chunk.decode('latin-1', errors='ignore'))
                    entropy_results['sections'].append({
                        'offset': i,
                        'size': len(chunk),
                        'entropy': chunk_entropy
                    })

        except Exception as e:
            self.logger.warning(f"Entropy analysis failed: {e}")

        return entropy_results

    def _detect_advanced_packer(self, file_path: str) -> bool:
        """Détection avancée de packer."""
        try:
            if not PEFILE_AVAILABLE:
                return False

            pe = pefile.PE(file_path)

            # Vérifier les signatures de packers connus
            for section in pe.sections:
                section_name = section.Name.decode('utf-8', errors='ignore').strip('\x00')
                if section_name.lower() in [name.lower() for name in self.known_sections]:
                    pe.close()
                    return True

            # Vérifier l'entropie des sections
            high_entropy_count = 0
            for section in pe.sections:
                if section.get_entropy() > 7.5:
                    high_entropy_count += 1

            pe.close()
            return high_entropy_count >= 2

        except Exception:
            return False

    def _analyze_suspicious_imports(self, file_path: str) -> List[Dict[str, Any]]:
        """Analyse les imports suspects dans un fichier PE."""
        suspicious_found = []

        try:
            if not PEFILE_AVAILABLE:
                return suspicious_found

            pe = pefile.PE(file_path)

            if hasattr(pe, 'DIRECTORY_ENTRY_IMPORT'):
                for entry in pe.DIRECTORY_ENTRY_IMPORT:
                    dll_name = entry.dll.decode('utf-8', errors='ignore')

                    for imp in entry.imports:
                        if imp.name:
                            func_name = imp.name.decode('utf-8', errors='ignore')
                            if func_name in self.suspicious_imports:
                                suspicious_found.append({
                                    'dll': dll_name,
                                    'function': func_name,
                                    'risk_level': 'high' if func_name in ['CreateRemoteThread', 'WriteProcessMemory'] else 'medium'
                                })

            pe.close()

        except Exception as e:
            self.logger.warning(f"Import analysis failed: {e}")

        return suspicious_found

    def _analyze_behavioral_patterns(self, file_path: str) -> List[Dict[str, Any]]:
        """Analyse les patterns comportementaux suspects."""
        behavioral_indicators = []

        try:
            with open(file_path, 'rb') as f:
                content = f.read()

            # Rechercher des patterns de persistance
            persistence_patterns = [
                b'SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run',
                b'SYSTEM\\CurrentControlSet\\Services',
                b'Winlogon\\Shell',
                b'schtasks', b'at.exe'
            ]

            for pattern in persistence_patterns:
                if pattern in content:
                    behavioral_indicators.append({
                        'type': 'persistence',
                        'pattern': pattern.decode('utf-8', errors='ignore'),
                        'description': 'Potential persistence mechanism detected'
                    })

            # Rechercher des patterns d'évasion
            evasion_patterns = [
                b'IsDebuggerPresent',
                b'CheckRemoteDebuggerPresent',
                b'GetTickCount',
                b'Sleep',
                b'VirtualProtect'
            ]

            for pattern in evasion_patterns:
                if pattern in content:
                    behavioral_indicators.append({
                        'type': 'evasion',
                        'pattern': pattern.decode('utf-8', errors='ignore'),
                        'description': 'Potential evasion technique detected'
                    })

        except Exception as e:
            self.logger.warning(f"Behavioral analysis failed: {e}")

        return behavioral_indicators

    def _analyze_network_patterns(self, file_path: str) -> List[Dict[str, Any]]:
        """Analyse les patterns réseau suspects."""
        network_indicators = []

        try:
            with open(file_path, 'rb') as f:
                content = f.read()

            # Rechercher des patterns de communication réseau
            network_patterns = [
                b'InternetOpen',
                b'InternetConnect',
                b'HttpOpenRequest',
                b'InternetReadFile',
                b'URLDownloadToFile',
                b'WinHttpOpen',
                b'socket',
                b'connect'
            ]

            for pattern in network_patterns:
                if pattern in content:
                    network_indicators.append({
                        'type': 'network_api',
                        'pattern': pattern.decode('utf-8', errors='ignore'),
                        'description': 'Network communication capability detected'
                    })

        except Exception as e:
            self.logger.warning(f"Network analysis failed: {e}")

        return network_indicators

    def _calculate_threat_score(self, file_info: Dict, suspicious_strings: Dict, iocs: Dict,
                               pe_analysis: Dict = None, yara_matches: List = None,
                               advanced_analysis: Dict = None) -> int:
        """Calcule le score de menace global du fichier."""
        score = 0

        try:
            # Score basé sur la taille du fichier
            file_size = file_info.get('size', 0)
            if file_size < 1024:  # Fichiers très petits suspects
                score += 10
            elif file_size > 50 * 1024 * 1024:  # Fichiers très gros suspects
                score += 5

            # Score basé sur les strings suspectes
            suspicious_count = suspicious_strings.get('suspicious_count', 0)
            score += min(suspicious_count * 5, 30)  # Max 30 points

            # Score basé sur les IOCs
            total_iocs = (len(iocs.get('ip_addresses', [])) +
                         len(iocs.get('domains', [])) +
                         len(iocs.get('urls', [])))
            score += min(total_iocs * 3, 20)  # Max 20 points

            # Score basé sur l'analyse PE
            if pe_analysis and pe_analysis.get('is_valid'):
                # APIs suspectes
                suspicious_apis_count = len(pe_analysis.get('suspicious_apis', []))
                score += min(suspicious_apis_count * 2, 25)  # Max 25 points

                # Packer détecté
                if pe_analysis.get('packer_detected'):
                    score += 15

            # Score basé sur les matches YARA
            if yara_matches:
                score += len(yara_matches) * 10  # 10 points par règle matchée

            # Score basé sur l'analyse avancée
            if advanced_analysis:
                # Packer détecté
                if advanced_analysis.get('packer_detected'):
                    score += 20

                # Imports suspects
                suspicious_imports = len(advanced_analysis.get('suspicious_imports', []))
                score += min(suspicious_imports * 3, 15)  # Max 15 points

                # Indicateurs comportementaux
                behavioral_indicators = len(advanced_analysis.get('behavioral_indicators', []))
                score += min(behavioral_indicators * 2, 10)  # Max 10 points

                # Indicateurs réseau
                network_indicators = len(advanced_analysis.get('network_indicators', []))
                score += min(network_indicators * 2, 10)  # Max 10 points

            # Limiter le score à 100
            score = min(score, 100)

        except Exception as e:
            self.logger.warning(f"Threat score calculation failed: {e}")
            score = 0

        return score

    def _generate_recommendations(self, threat_score: int, file_type: Dict) -> List[str]:
        """Génère des recommandations basées sur l'analyse."""
        recommendations = []

        if threat_score >= 70:
            recommendations.append("🚨 FICHIER HAUTEMENT SUSPECT - Isoler immédiatement")
            recommendations.append("🔍 Effectuer une analyse approfondie en environnement isolé")
            recommendations.append("🚫 Ne pas exécuter ce fichier")

        elif threat_score >= 40:
            recommendations.append("⚠️ Fichier potentiellement malveillant")
            recommendations.append("🔍 Analyser avec des outils supplémentaires")
            recommendations.append("🛡️ Exécuter uniquement en environnement contrôlé")

        elif threat_score >= 20:
            recommendations.append("⚡ Fichier suspect - Prudence recommandée")
            recommendations.append("🔍 Vérifier la source et l'intégrité")

        else:
            recommendations.append("✅ Fichier semble légitime")
            recommendations.append("🔍 Surveillance continue recommandée")

        # Recommandations spécifiques au type de fichier
        if file_type.get('is_suspicious_extension'):
            recommendations.append("⚠️ Extension de fichier potentiellement dangereuse")

        return recommendations
