"""
Service pour la gestion des incidents et des tickets d'enquête
"""
from ..models.incident_model import (
    create_ticket, get_all_tickets, get_tickets_by_user, get_ticket_by_number, update_ticket, delete_ticket,
    create_incident, get_all_incidents, get_incident_by_incident_id, update_incident,
    close_incident as close_incident_db, reopen_incident as reopen_incident_db, delete_incident as delete_incident_db,
    get_notification_settings_by_user_id, create_or_update_notification_settings,
    get_all_notification_settings, add_timeline_entry, add_attachments_to_ticket_db,
    remove_attachment_from_ticket_db, get_user_email_by_id
)
from ..utils.websocket import (
    notify_new_incident, notify_new_ticket, notify_critical_alert
)
from ..extensions import socketio
from datetime import datetime
import uuid
import requests
from ..utils.email_utils import send_async_email
from .email_template_service import EmailTemplateService

class IncidentService:
    def __init__(self, notify_func=None):
        self.notify_func = notify_func

    def notify_admins(self, event_name, data):
        # Emit websocket to all connected clients
        try:
            socketio.emit('incident_notification', {
                'event': event_name,
                'data': data,
                'timestamp': datetime.now().isoformat()
            })
            print(f"✅ Socket.IO notification sent: {event_name}")
        except Exception as e:
            print(f"❌ Socket.IO notification failed: {e}")

        # Legacy websocket support
        if self.notify_func:
            self.notify_func(event_name, data)

        # Charger les admins
        print(f"🔔 notify_admins called - Event: {event_name}")
        admins = get_all_notification_settings()
        print(f"🔔 Found {len(admins) if admins else 0} admin notification settings")

        if not admins:
            print("⚠️ No admin notification settings found")
            return

        for admin in admins:
            print(f"🔔 Processing admin: {admin}")

            # Skip file-related notifications for Telegram
            if event_name in ['ticket_attachments_added', 'ticket_attachment_removed']:
                print(f"📱 Skipping Telegram notification for file event: {event_name}")
                continue

            # Format message for Telegram with better formatting
            telegram_message = self.format_telegram_message(event_name, data)

            # Send email if enabled
            if admin.get('email_enabled') and admin.get('email_address'):
                print(f"📧 Sending email to {admin['email_address']}")
                self.send_email(admin['email_address'], f"PICA - {event_name}", data)

            # Send telegram if enabled (using fixed configuration)
            if admin.get('telegram_enabled'):
                print(f"📱 Sending Telegram notification")
                self.send_telegram_message(admin.get('telegram_chat_id'), telegram_message)
            else:
                print(f"📱 Telegram not enabled for admin: telegram_enabled={admin.get('telegram_enabled')}")

    def send_email(self, to_email, subject, body):
        """Send email notification using beautiful templates"""
        print(f"📧 Sending EMAIL to {to_email}: Subject={subject}")

        try:
            # Determine notification type and action from subject
            if "ticket_created" in subject:
                notification_type = "New Ticket Created"
                item_type = "ticket"
                action = "created"
            elif "ticket_updated" in subject:
                notification_type = "Ticket Updated"
                item_type = "ticket"
                action = "updated"
            elif "ticket_assigned" in subject:
                notification_type = "Ticket Assigned"
                item_type = "ticket"
                action = "assigned"
            elif "ticket_closed" in subject:
                notification_type = "Ticket Closed"
                item_type = "ticket"
                action = "closed"
            elif "ticket_reopened" in subject:
                notification_type = "Ticket Reopened"
                item_type = "ticket"
                action = "reopened"
            elif "ticket_deleted" in subject:
                notification_type = "Ticket Deleted"
                item_type = "ticket"
                action = "deleted"
            elif "ticket_converted" in subject:
                notification_type = "Ticket Converted to Incident"
                item_type = "ticket"
                action = "converted"
            elif "incident_created" in subject:
                notification_type = "New Security Incident Created"
                item_type = "incident"
                action = "created"
            elif "incident_closed" in subject:
                notification_type = "Security Incident Closed"
                item_type = "incident"
                action = "closed"
            elif "incident_reopened" in subject:
                notification_type = "Security Incident Reopened"
                item_type = "incident"
                action = "reopened"
            elif "incident_deleted" in subject:
                notification_type = "Security Incident Deleted"
                item_type = "incident"
                action = "deleted"
            elif "New Ticket Created" in subject:
                notification_type = "New Ticket Created"
                item_type = "ticket"
                action = "created"
            elif "Ticket Updated" in subject:
                notification_type = "Ticket Updated"
                item_type = "ticket"
                action = "updated"
            elif "New Incident" in subject:
                notification_type = "New Incident Created"
                item_type = "incident"
                action = "created"
            else:
                notification_type = subject.replace("PICA - ", "")
                item_type = "item"
                action = "updated"

            # Send beautiful email using template
            EmailTemplateService.send_incident_notification_email(
                email=to_email,
                notification_type=notification_type,
                item_type=item_type,
                action=action,
                data=body if isinstance(body, dict) else {}
            )
            print(f"✅ Templated email sent successfully to {to_email}")

        except Exception as e:
            print(f"❌ Failed to send email to {to_email}: {str(e)}")

    def format_email_body(self, subject, data):
        """Format email body for incident notifications"""

        # Convert data to dict if it's a string representation
        if isinstance(data, str):
            try:
                import ast
                data = ast.literal_eval(data)
            except:
                # If parsing fails, use the string as is
                return f"""
Hello,

You have received a new PICA notification:

{subject}

Details: {data}

Best regards,
PICA Team
"""

        # Format based on the type of notification
        if 'ticket_number' in data:
            # Calculate criticality score (same logic as Telegram)
            impact = data.get('impact', 'Medium')
            urgency = data.get('urgency', 'Medium')
            priority = data.get('priority', 3)

            criticality_score = 50
            if impact == 'High':
                criticality_score += 25
            elif impact == 'Medium':
                criticality_score += 15

            if urgency == 'High':
                criticality_score += 25
            elif urgency == 'Medium':
                criticality_score += 15

            if priority == 1:
                criticality_score += 10
            elif priority == 2:
                criticality_score += 5

            priority_labels = {1: 'Critical', 2: 'High', 3: 'Medium', 4: 'Low'}
            priority_label = priority_labels.get(priority, 'Medium')

            from datetime import datetime
            created_date = datetime.now().strftime('%Y-%m-%d %H:%M')

            return f"""Hello,

PICA - New Ticket Created

📝 Number: {data.get('ticket_number', 'N/A')}
🖊 Description: {data.get('short_description', 'N/A')}
🗂 Category: {data.get('category', 'N/A')}
🚩 Type: {data.get('subcategory', 'Generic Incident')}
⚡ Impact: {impact}
🔥 Urgency: {urgency}
🚨 Priority: {priority_label}
🎯 Criticality Score: {criticality_score}/100
💻 Hostname: {data.get('configuration_item', 'N/A')}
🌐 IP: {data.get('location', 'N/A')}
👤 Created by: {data.get('user_id', 'N/A')}
🟩 Status: {data.get('status', 'New')}

🔗 View ticket: http://localhost:5173/incidents
🕒 Created on: {created_date}

Best regards,
PICA Team
"""
        else:
            # Format générique
            return f"""
Bonjour,

Vous avez reçu une nouvelle notification PICA :

{subject}

Détails : {str(data)}

Cordialement,
L'équipe PICA
"""

    def send_telegram_message(self, chat_id, message):
        print(f"🤖 send_telegram_message called - Chat ID: {chat_id}")
        print(f"🤖 Message: {message}")

        # Configuration Telegram (utilise le Chat ID fixe de la configuration)
        conf_via_telegram = 1
        conf_token = "**********************************************"
        conf_chat_id = "5694506830"  # Chat ID fixe de la configuration

        if conf_via_telegram != 1:
            print("📱 Telegram notifications disabled")
            return

        try:
            import urllib.parse

            # Encoder le message pour l'URL (comme urlencode en PHP)
            encoded_message = urllib.parse.quote(message)

            # Construire l'URL comme dans le code PHP
            url = f"https://api.telegram.org/bot{conf_token}/sendMessage?chat_id={conf_chat_id}&text={encoded_message}&parse_mode=HTML"

            print(f"🤖 Sending to Telegram API: {url[:100]}...")  # Afficher seulement le début de l'URL

            # Utiliser requests.get au lieu de POST (comme curl en PHP)
            response = requests.get(url, timeout=10, verify=False)  # verify=False équivaut à CURLOPT_SSL_VERIFYPEER false

            print(f"🤖 Telegram response: {response.status_code} - {response.text}")

            if response.status_code == 200:
                result = response.json()
                if result.get('ok'):
                    print("✅ Telegram message sent successfully!")
                else:
                    print(f"❌ Telegram API error: {result}")
            else:
                print(f"❌ HTTP error: {response.status_code} - {response.text}")

        except Exception as e:
            print(f"❌ Telegram error: {str(e)}")

    def format_telegram_message(self, event_name, data):
        """Format notification message for Telegram with HTML formatting"""

        # Emoji mapping for different events
        event_emojis = {
            'ticket_created': '🎫',
            'ticket_updated': '📝',
            'ticket_assigned': '👤',
            'ticket_closed': '🔒',
            'ticket_reopened': '🔓',
            'ticket_deleted': '🗑️',
            'ticket_converted': '🔄',
            'incident_created': '🚨',
            'incident_updated': '📋',
            'incident_closed': '✅',
            'incident_reopened': '🔄',
            'incident_deleted': '🗑️',
            'critical_alert': '⚠️'
        }

        emoji = event_emojis.get(event_name, '📢')

        # Function to escape HTML characters for Telegram
        def escape_html(text):
            if not text:
                return 'N/A'
            return str(text).replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')

        if event_name == 'ticket_created':
            # Calculate criticality score based on impact and urgency
            impact = data.get('impact', 'Medium')
            urgency = data.get('urgency', 'Medium')
            priority = data.get('priority', 3)

            # Calculate criticality score
            criticality_score = 50  # Base score
            if impact == 'High':
                criticality_score += 25
            elif impact == 'Medium':
                criticality_score += 15

            if urgency == 'High':
                criticality_score += 25
            elif urgency == 'Medium':
                criticality_score += 15

            if priority == 1:
                criticality_score += 10
            elif priority == 2:
                criticality_score += 5

            # Priority label
            priority_labels = {1: 'Critical', 2: 'High', 3: 'Medium', 4: 'Low'}
            priority_label = priority_labels.get(priority, 'Medium')

            # Format creation date
            from datetime import datetime
            created_date = datetime.now().strftime('%Y-%m-%d %H:%M')

            message = f"""<b>PICA - New Ticket Created</b>

📝 <b>Number:</b> {escape_html(data.get('ticket_number', 'N/A'))}
🖊 <b>Description:</b> {escape_html(data.get('short_description', 'N/A'))}
🗂 <b>Category:</b> {escape_html(data.get('category', 'N/A'))}
🚩 <b>Type:</b> {escape_html(data.get('subcategory', 'Generic Incident'))}
⚡ <b>Impact:</b> {escape_html(impact)}
🔥 <b>Urgency:</b> {escape_html(urgency)}
🚨 <b>Priority:</b> {escape_html(priority_label)}
🎯 <b>Criticality Score:</b> {criticality_score}/100
💻 <b>Hostname:</b> {escape_html(data.get('configuration_item', 'N/A'))}
🌐 <b>IP:</b> {escape_html(data.get('location', 'N/A'))}
👤 <b>Created by:</b> {escape_html(data.get('user_id', 'N/A'))}
🟩 <b>Status:</b> {escape_html(data.get('status', 'New'))}

🔗 <b>View ticket:</b> http://localhost:5173/incidents
🕒 <b>Created on:</b> {created_date}
"""
        elif event_name == 'ticket_updated':
            message = f"""
{emoji} <b>PICA - Ticket Updated</b>

📋 <b>Number:</b> {escape_html(data.get('ticket_number', 'N/A'))}
📝 <b>Description:</b> {escape_html(data.get('short_description', 'N/A'))}
📊 <b>Status:</b> {escape_html(data.get('status', 'N/A'))}
👤 <b>Modified by:</b> {escape_html(data.get('updated_by', 'N/A'))}

🕒 <i>Updated just now</i>
"""
        elif event_name == 'ticket_assigned':
            message = f"""
{emoji} <b>PICA - Ticket Assigned</b>

📋 <b>Number:</b> {escape_html(data.get('ticket_number', 'N/A'))}
📝 <b>Description:</b> {escape_html(data.get('short_description', 'N/A'))}
👤 <b>Assigned to:</b> {escape_html(data.get('assigned_to', 'N/A'))}
🔄 <b>Assigned by:</b> {escape_html(data.get('assigned_by', 'N/A'))}

🕒 <i>Assigned just now</i>
"""
        elif event_name == 'ticket_assigned':
            message = f"""
{emoji} <b>PICA - Ticket Assigned</b>

🎫 <b>Ticket:</b> {escape_html(data.get('ticket_number', 'N/A'))}
📝 <b>Description:</b> {escape_html(data.get('short_description', 'N/A'))}
🗂 <b>Category:</b> {escape_html(data.get('category', 'N/A'))}

👤 <b>Assigned to:</b> {escape_html(data.get('assigned_to', 'N/A'))}
🔄 <b>Assigned by:</b> {escape_html(data.get('assigned_by', 'N/A'))}
🚩 <b>Priority:</b> {escape_html(str(data.get('priority', 'N/A')))}
📊 <b>Status:</b> {escape_html(data.get('status', 'N/A'))}

👤 <b>Created by:</b> {escape_html(data.get('created_by', 'N/A'))}

🕒 <i>Assigned just now</i>
"""
        elif event_name == 'ticket_closed':
            message = f"""
{emoji} <b>PICA - Ticket Closed</b>

🎫 <b>Ticket:</b> {escape_html(data.get('ticket_number', 'N/A'))}
📝 <b>Description:</b> {escape_html(data.get('short_description', 'N/A'))}
🗂 <b>Category:</b> {escape_html(data.get('category', 'N/A'))}

🚩 <b>Priority:</b> {escape_html(str(data.get('priority', 'N/A')))}
📊 <b>Status:</b> {escape_html(data.get('status', 'N/A'))}

👤 <b>Closed by:</b> {escape_html(data.get('closed_by', 'N/A'))}
👤 <b>Created by:</b> {escape_html(data.get('created_by', 'N/A'))}

🕒 <i>Ticket closed just now</i>
"""
        elif event_name == 'ticket_reopened':
            message = f"""
{emoji} <b>PICA - Ticket Reopened</b>

🎫 <b>Ticket:</b> {escape_html(data.get('ticket_number', 'N/A'))}
📝 <b>Description:</b> {escape_html(data.get('short_description', 'N/A'))}
🗂 <b>Category:</b> {escape_html(data.get('category', 'N/A'))}

🚩 <b>Priority:</b> {escape_html(str(data.get('priority', 'N/A')))}
📊 <b>Status:</b> {escape_html(data.get('status', 'N/A'))}

👤 <b>Reopened by:</b> {escape_html(data.get('reopened_by', 'N/A'))}
👤 <b>Created by:</b> {escape_html(data.get('created_by', 'N/A'))}

🕒 <i>Ticket reopened just now</i>
"""
        elif event_name == 'ticket_converted':
            message = f"""
{emoji} <b>PICA - Ticket Converted to Incident</b>

🎫 <b>Ticket:</b> {escape_html(data.get('ticket_number', 'N/A'))}
📝 <b>Description:</b> {escape_html(data.get('short_description', 'N/A'))}
🗂 <b>Category:</b> {escape_html(data.get('category', 'N/A'))}

🚨 <b>Incident ID:</b> {escape_html(data.get('incident_id', 'N/A'))}
📋 <b>Incident Title:</b> {escape_html(data.get('incident_title', 'N/A'))}
🔥 <b>Severity:</b> {escape_html(data.get('incident_severity', 'N/A'))}

👤 <b>Converted by:</b> {escape_html(data.get('converted_by', 'N/A'))}
👤 <b>Original Creator:</b> {escape_html(data.get('created_by', 'N/A'))}

🕒 <i>Converted just now</i>
"""
        elif event_name == 'incident_created':
            message = f"""
{emoji} <b>PICA - New Security Incident Created</b>

🚨 <b>Incident ID:</b> {escape_html(data.get('incident_id', 'N/A'))}
📝 <b>Title:</b> {escape_html(data.get('title', 'N/A'))}
📋 <b>Description:</b> {escape_html(data.get('description', 'N/A'))}

🔥 <b>Severity:</b> {escape_html(data.get('severity', 'N/A'))}
📊 <b>Status:</b> {escape_html(data.get('status', 'N/A'))}
💼 <b>Business Impact:</b> {escape_html(data.get('business_impact', 'N/A'))}

👤 <b>Created by:</b> {escape_html(data.get('created_by', 'N/A'))}
🎫 <b>Original Ticket:</b> {escape_html(data.get('original_ticket_number', 'N/A'))}
👥 <b>Assigned Team:</b> {escape_html(data.get('assigned_team', 'N/A'))}

🕒 <i>Incident created just now</i>
"""
        elif event_name == 'incident_closed':
            message = f"""
{emoji} <b>PICA - Security Incident Closed</b>

🚨 <b>Incident ID:</b> {escape_html(data.get('incident_id', 'N/A'))}
📝 <b>Title:</b> {escape_html(data.get('title', 'N/A'))}
🔥 <b>Severity:</b> {escape_html(data.get('severity', 'N/A'))}

👤 <b>Closed by:</b> {escape_html(data.get('closed_by', 'N/A'))}
📊 <b>Previous Status:</b> {escape_html(data.get('original_status', 'N/A'))}

🕒 <i>Incident closed just now</i>
"""
        elif event_name == 'incident_reopened':
            message = f"""
{emoji} <b>PICA - Security Incident Reopened</b>

🚨 <b>Incident ID:</b> {escape_html(data.get('incident_id', 'N/A'))}
📝 <b>Title:</b> {escape_html(data.get('title', 'N/A'))}
🔥 <b>Severity:</b> {escape_html(data.get('severity', 'N/A'))}

👤 <b>Reopened by:</b> {escape_html(data.get('reopened_by', 'N/A'))}
📊 <b>Previous Status:</b> {escape_html(data.get('previous_status', 'N/A'))}

🕒 <i>Incident reopened just now</i>
"""
        elif event_name == 'incident_deleted':
            message = f"""
{emoji} <b>PICA - Security Incident Deleted</b>

🚨 <b>Incident ID:</b> {escape_html(data.get('incident_id', 'N/A'))}
📝 <b>Title:</b> {escape_html(data.get('title', 'N/A'))}
🔥 <b>Severity:</b> {escape_html(data.get('severity', 'N/A'))}

👤 <b>Deleted by:</b> {escape_html(data.get('deleted_by', 'N/A'))}
📊 <b>Previous Status:</b> {escape_html(data.get('original_status', 'N/A'))}

🕒 <i>Incident deleted just now</i>
"""
        elif event_name == 'ticket_deleted':
            message = f"""
{emoji} <b>PICA - Ticket Deleted</b>

🎫 <b>Ticket:</b> {escape_html(data.get('ticket_number', 'N/A'))}
📝 <b>Description:</b> {escape_html(data.get('short_description', 'N/A'))}
📊 <b>Status:</b> {escape_html(data.get('original_status', 'N/A'))}

👤 <b>Deleted by:</b> {escape_html(data.get('deleted_by', 'N/A'))}
👤 <b>Original Creator:</b> {escape_html(data.get('original_creator', 'N/A'))}

🕒 <i>Ticket deleted just now</i>
"""
        elif event_name == 'test_notification':
            message = f"""
🧪 <b>PICA - Test de Notification</b>

✅ <b>Statut:</b> Configuration Telegram fonctionnelle
🤖 <b>Bot:</b> Connecté et opérationnel
📱 <b>Chat ID:</b> Configuré correctement

🕒 <i>Message de test envoyé à l'instant</i>

<i>Si vous recevez ce message, les notifications Telegram sont correctement configurées !</i>
"""
        else:
            # Format générique pour autres événements
            message = f"""
{emoji} <b>PICA - {event_name.replace('_', ' ').title()}</b>

📋 <b>Détails:</b>
{str(data)}

🕒 <i>Notification sent just now</i>
"""

        return message.strip()

    def create_ticket(self, **ticket_data):
        """Create a new ITSM-compliant ticket"""
        ticket_number = f"TCKT-{uuid.uuid4().hex[:8]}"

        # Ensure required fields
        ticket_data['ticket_number'] = ticket_number
        if 'status' not in ticket_data:
            ticket_data['status'] = 'New'

        # Handle backward compatibility
        if 'title' in ticket_data and 'short_description' not in ticket_data:
            ticket_data['short_description'] = ticket_data['title']

        ticket = create_ticket(ticket_data)
        if ticket:
            # Legacy notification system
            self.notify_admins('ticket_created', {
                'ticket_number': ticket['ticket_number'],
                'short_description': ticket.get('short_description', ''),
                'description': ticket.get('description', ''),
                'category': ticket.get('category', ''),
                'subcategory': ticket.get('subcategory', ''),
                'impact': ticket.get('impact', ''),
                'urgency': ticket.get('urgency', ''),
                'priority': ticket.get('priority', ''),
                'status': ticket.get('status', ''),
                'user_email': ticket.get('user_email', ''),
                'created_by': ticket.get('user_email', ''),  # For template compatibility
                'assigned_to': ticket.get('assigned_to', ''),
                'configuration_item': ticket.get('configuration_item', ''),
                'location': ticket.get('location', ''),
                'created_at': ticket.get('created_at', ''),
                'contact_type': ticket.get('contact_type', 'Portal')
            })

            # WebSocket notifications
            try:
                notify_new_ticket(ticket)
            except Exception as e:
                print(f"⚠️ Error sending WebSocket notification for new ticket: {str(e)}")

        return ticket



    def update_ticket(self, ticket_number, user_id=None, **kwargs):
        """Update ticket with timeline tracking"""
        ticket = update_ticket(ticket_number, kwargs, user_id)
        if ticket:
            self.notify_admins('ticket_updated', {
                'ticket_number': ticket['ticket_number'],
                'short_description': ticket.get('short_description', ''),
                'description': ticket.get('description', ''),
                'category': ticket.get('category', ''),
                'subcategory': ticket.get('subcategory', ''),
                'impact': ticket.get('impact', ''),
                'urgency': ticket.get('urgency', ''),
                'priority': ticket.get('priority', ''),
                'status': ticket.get('status', ''),
                'created_by': ticket.get('user_email', ''),
                'updated_by': user_id,
                'assigned_to': ticket.get('assigned_to', ''),
                'configuration_item': ticket.get('configuration_item', ''),
                'location': ticket.get('location', '')
            })
        return ticket

    def assign_ticket(self, ticket_number, assigned_to_user_id, assigning_user_id=None):
        """Assign ticket to a user with timeline tracking"""
        update_data = {
            'assigned_to': assigned_to_user_id,
            'status': 'In Progress'  # Auto-update status when assigned
        }
        ticket = update_ticket(ticket_number, update_data, assigning_user_id)

        if ticket:
            # Get email of assigning user
            assigning_user_email = get_user_email_by_id(assigning_user_id) if assigning_user_id else 'System'

            self.notify_admins('ticket_assigned', {
                'ticket_number': ticket['ticket_number'],
                'short_description': ticket.get('short_description', ''),
                'description': ticket.get('description', ''),
                'category': ticket.get('category', ''),
                'subcategory': ticket.get('subcategory', ''),
                'impact': ticket.get('impact', ''),
                'urgency': ticket.get('urgency', ''),
                'priority': ticket.get('priority', ''),
                'status': ticket.get('status', ''),
                'created_by': ticket.get('user_email', ''),
                'assigned_to': ticket['assigned_to'],
                'assigned_by': assigning_user_email,
                'configuration_item': ticket.get('configuration_item', ''),
                'location': ticket.get('location', '')
            })

        return ticket

    def close_ticket(self, ticket_number, closing_user_id=None):
        """Close a ticket with timeline tracking"""
        update_data = {
            'status': 'Closed',
            'closed_at': datetime.utcnow().isoformat(),
            'closed_by': closing_user_id
        }
        ticket = update_ticket(ticket_number, update_data, closing_user_id)

        if ticket:
            # Get email of closing user
            closing_user_email = get_user_email_by_id(closing_user_id) if closing_user_id else 'System'

            self.notify_admins('ticket_closed', {
                'ticket_number': ticket['ticket_number'],
                'short_description': ticket.get('short_description', ''),
                'description': ticket.get('description', ''),
                'category': ticket.get('category', ''),
                'subcategory': ticket.get('subcategory', ''),
                'impact': ticket.get('impact', ''),
                'urgency': ticket.get('urgency', ''),
                'priority': ticket.get('priority', ''),
                'status': ticket.get('status', ''),
                'created_by': ticket.get('user_email', ''),
                'assigned_to': ticket.get('assigned_to', ''),
                'closed_by': closing_user_email,
                'closed_at': update_data['closed_at'],
                'configuration_item': ticket.get('configuration_item', ''),
                'location': ticket.get('location', '')
            })

        return ticket

    def reopen_ticket(self, ticket_number, reopening_user_id=None):
        """Reopen a closed ticket with timeline tracking"""
        update_data = {
            'status': 'New',  # Reset to New status when reopened
            'reopened_at': datetime.utcnow().isoformat(),
            'reopened_by': reopening_user_id,
            'closed_at': None,  # Clear closed timestamp
            'closed_by': None   # Clear closed by user
        }
        ticket = update_ticket(ticket_number, update_data, reopening_user_id)

        if ticket:
            # Get email of reopening user
            reopening_user_email = get_user_email_by_id(reopening_user_id) if reopening_user_id else 'System'

            self.notify_admins('ticket_reopened', {
                'ticket_number': ticket['ticket_number'],
                'short_description': ticket.get('short_description', ''),
                'description': ticket.get('description', ''),
                'category': ticket.get('category', ''),
                'subcategory': ticket.get('subcategory', ''),
                'impact': ticket.get('impact', ''),
                'urgency': ticket.get('urgency', ''),
                'priority': ticket.get('priority', ''),
                'status': ticket.get('status', ''),
                'created_by': ticket.get('user_email', ''),
                'assigned_to': ticket.get('assigned_to', ''),
                'reopened_by': reopening_user_email,
                'reopened_at': update_data['reopened_at'],
                'configuration_item': ticket.get('configuration_item', ''),
                'location': ticket.get('location', '')
            })

        return ticket

    def delete_ticket(self, ticket_number, deleting_user_id=None):
        """Delete a ticket permanently with notification"""
        try:
            # Get ticket info before deletion for notification
            ticket = get_ticket_by_number(ticket_number)
            if not ticket:
                print(f"❌ Ticket {ticket_number} not found")
                return False

            # Delete the ticket
            success = delete_ticket(ticket_number, deleting_user_id)

            if success:
                # Get email of deleting user
                deleting_user_email = get_user_email_by_id(deleting_user_id) if deleting_user_id else 'System'

                # Send notification about deletion
                self.notify_admins('ticket_deleted', {
                    'ticket_number': ticket_number,
                    'short_description': ticket.get('short_description', ''),
                    'deleted_by': deleting_user_email,
                    'original_status': ticket.get('status'),
                    'original_creator': ticket.get('user_email', ticket.get('user_id'))
                })

                print(f"✅ Ticket {ticket_number} deleted successfully by user {deleting_user_id}")
                return True
            else:
                print(f"❌ Failed to delete ticket {ticket_number}")
                return False

        except Exception as e:
            print(f"❌ Error deleting ticket {ticket_number}: {str(e)}")
            return False

    def convert_ticket_to_incident(self, ticket_number, converting_user_id=None):
        """Convert a ticket to an incident with complete data transfer"""
        try:
            ticket = get_ticket_by_number(ticket_number)
            if not ticket:
                print(f"❌ Ticket {ticket_number} not found")
                return None

            incident_id = f"INC-{uuid.uuid4().hex[:8]}"

            # Map ticket fields to incident fields
            title = ticket.get('short_description', 'Converted from ticket')
            description = ticket.get('description', '')

            # Map impact to severity
            impact_to_severity = {
                'High': 'high',
                'Medium': 'medium',
                'Low': 'low'
            }
            severity = impact_to_severity.get(ticket.get('impact', 'Medium'), 'medium')

            # Copy and adapt timeline from ticket
            incident_timeline = []
            if ticket.get('timeline'):
                for entry in ticket['timeline']:
                    # Convert ticket timeline entries to incident format
                    incident_timeline.append({
                        'timestamp': entry.get('timestamp'),
                        'action': f"ticket_{entry.get('action', 'unknown')}",
                        'user_id': entry.get('user_id'),
                        'details': {
                            **entry.get('details', {}),
                            'source': 'converted_from_ticket',
                            'original_ticket': ticket_number
                        }
                    })

            # Add conversion entry to timeline
            conversion_timestamp = datetime.now()
            incident_timeline.append({
                'timestamp': conversion_timestamp,
                'action': 'incident_created_from_ticket',
                'user_id': converting_user_id,
                'details': {
                    'original_ticket': ticket_number,
                    'original_impact': ticket.get('impact'),
                    'original_urgency': ticket.get('urgency'),
                    'original_priority': ticket.get('priority'),
                    'original_category': ticket.get('category'),
                    'converted_by': converting_user_id
                }
            })

            incident_data = {
                'title': title,
                'description': description,
                'severity': severity,
                'incident_id': incident_id,
                'status': 'open',
                'evidence': [],
                'timeline': incident_timeline,
                'recommendations': [],
                'tags': [f"converted-from-{ticket_number}"],
                'false_positive': False,
                'escalated': False,
                # Set the original ticket creator as the incident creator
                'user_id': ticket.get('user_id'),
                'user_email': ticket.get('user_email'),
                # Keep reference to original ticket
                'original_ticket_number': ticket_number,
                'original_ticket_id': ticket.get('id'),
                # Preserve original creator information
                'original_creator_id': ticket.get('user_id'),
                'original_creator_email': ticket.get('user_email'),
                # Copy attachments if any
                'attachments': ticket.get('attachments', [])
            }

            print(f"🔄 Converting ticket {ticket_number} to incident {incident_id}")
            incident = create_incident(incident_data)

            if incident:
                # Update ticket status and add conversion timeline entry
                update_data = {
                    'status': 'converted_to_incident',
                    'converted_incident_id': incident_id
                }
                update_ticket(ticket_number, update_data, converting_user_id)

                # Add timeline entry to ticket about conversion
                add_timeline_entry(ticket_number, 'converted_to_incident', converting_user_id, {
                    'incident_id': incident_id,
                    'converted_at': conversion_timestamp.isoformat(),
                    'new_status': 'converted_to_incident'
                })

                print(f"✅ Ticket {ticket_number} converted to incident {incident_id}")

                # Send incident created notification
                self.notify_admins('incident_created', {
                    'incident_id': incident_id,
                    'title': incident_data.get('title', ''),
                    'description': incident_data.get('description', ''),
                    'severity': incident_data.get('severity', ''),
                    'status': incident_data.get('status', ''),
                    'created_by': incident_data.get('user_email', ''),
                    'original_ticket_number': ticket_number,
                    'original_creator': ticket.get('user_email', ''),
                    'impact_assessment': incident_data.get('impact_assessment', ''),
                    'business_impact': incident_data.get('business_impact', ''),
                    'assigned_to': incident_data.get('assigned_to', ''),
                    'assigned_team': incident_data.get('assigned_team', ''),
                    'created_at': incident_data.get('created_at', ''),
                    'category': ticket.get('category', ''),
                    'subcategory': ticket.get('subcategory', '')
                })

                # Get email of converting user
                converting_user_email = get_user_email_by_id(converting_user_id) if converting_user_id else 'System'

                # Send ticket conversion notification
                self.notify_admins('ticket_converted', {
                    'ticket_number': ticket['ticket_number'],
                    'short_description': ticket.get('short_description', ''),
                    'description': ticket.get('description', ''),
                    'category': ticket.get('category', ''),
                    'subcategory': ticket.get('subcategory', ''),
                    'impact': ticket.get('impact', ''),
                    'urgency': ticket.get('urgency', ''),
                    'priority': ticket.get('priority', ''),
                    'created_by': ticket.get('user_email', ''),
                    'incident_id': incident_id,
                    'incident_title': incident_data.get('title', ''),
                    'incident_severity': incident_data.get('severity', ''),
                    'converted_by': converting_user_email,
                    'converted_at': conversion_timestamp.isoformat(),
                    'original_creator': ticket.get('user_email', ticket.get('user_id')),
                    'configuration_item': ticket.get('configuration_item', ''),
                    'location': ticket.get('location', '')
                })

                return incident
            else:
                print(f"❌ Failed to create incident for ticket {ticket_number}")
                return None

        except Exception as e:
            print(f"❌ Error converting ticket {ticket_number} to incident: {str(e)}")
            return None

    def get_ticket(self, ticket_number):
        return get_ticket_by_number(ticket_number)

    def list_tickets(self):
        return get_all_tickets()

    def list_tickets_by_user(self, user_id):
        """Get tickets created by a specific user"""
        return get_tickets_by_user(user_id)

    def list_incidents(self):
        return get_all_incidents()

    def get_incident(self, incident_id):
        return get_incident_by_incident_id(incident_id)

    def update_incident(self, incident_id, **kwargs):
        incident = update_incident(incident_id, kwargs)
        if incident:
            self.notify_admins('incident_updated', incident)
        return incident

    def get_notification_settings(self, user_id):
        return get_notification_settings_by_user_id(user_id)

    def create_or_update_notification_settings(self, user_id, settings_data):
        return create_or_update_notification_settings(user_id, settings_data)

    def add_attachments_to_ticket(self, ticket_number, attachments, user_id=None):
        """Add attachments to a ticket with timeline tracking"""
        try:
            success = add_attachments_to_ticket_db(ticket_number, attachments)
            if success:
                # Add timeline entry
                add_timeline_entry(ticket_number, 'attachments_added', user_id, {
                    'files_count': len(attachments),
                    'files': [att['original_name'] for att in attachments]
                })

                # Notify admins
                self.notify_admins('ticket_attachments_added', {
                    'ticket_number': ticket_number,
                    'files_count': len(attachments),
                    'added_by': user_id
                })
            return success
        except Exception as e:
            print(f"❌ Error adding attachments to ticket {ticket_number}: {str(e)}")
            return False

    def remove_attachment_from_ticket(self, ticket_number, attachment_id, user_id=None):
        """Remove an attachment from a ticket with timeline tracking"""
        try:
            # Get attachment info before removal
            ticket = self.get_ticket(ticket_number)
            if not ticket or 'attachments' not in ticket:
                return False

            attachment_to_remove = None
            for att in ticket['attachments']:
                if att.get('id') == attachment_id or att.get('stored_name') == attachment_id:
                    attachment_to_remove = att
                    break

            if not attachment_to_remove:
                return False

            success = remove_attachment_from_ticket_db(ticket_number, attachment_id)
            if success:
                # Add timeline entry
                add_timeline_entry(ticket_number, 'attachment_removed', user_id, {
                    'file_name': attachment_to_remove.get('original_name', 'Unknown'),
                    'file_id': attachment_id
                })

                # Notify admins
                self.notify_admins('ticket_attachment_removed', {
                    'ticket_number': ticket_number,
                    'file_name': attachment_to_remove.get('original_name', 'Unknown'),
                    'removed_by': user_id
                })
            return success
        except Exception as e:
            print(f"❌ Error removing attachment from ticket {ticket_number}: {str(e)}")
            return False

    def close_incident(self, incident_id, closing_user_id=None):
        """Close an incident with notification"""
        try:
            # Get incident info before closing for notification
            incident = get_incident_by_incident_id(incident_id)
            if not incident:
                print(f"❌ Incident {incident_id} not found")
                return None

            # Close the incident
            closed_incident = close_incident_db(incident_id, closing_user_id)

            if closed_incident:
                # Get email of closing user
                closing_user_email = get_user_email_by_id(closing_user_id) if closing_user_id else 'System'

                # Send notification about closure
                self.notify_admins('incident_closed', {
                    'incident_id': incident_id,
                    'title': incident.get('title', ''),
                    'closed_by': closing_user_email,
                    'original_status': incident.get('status'),
                    'severity': incident.get('severity')
                })

                print(f"✅ Incident {incident_id} closed successfully by user {closing_user_id}")
                return closed_incident
            else:
                print(f"❌ Failed to close incident {incident_id}")
                return None

        except Exception as e:
            print(f"❌ Error closing incident {incident_id}: {str(e)}")
            return None

    def reopen_incident(self, incident_id, reopening_user_id=None):
        """Reopen a closed incident with notification"""
        try:
            # Get incident info before reopening for notification
            incident = get_incident_by_incident_id(incident_id)
            if not incident:
                print(f"❌ Incident {incident_id} not found")
                return None

            # Reopen the incident
            reopened_incident = reopen_incident_db(incident_id, reopening_user_id)

            if reopened_incident:
                # Get email of reopening user
                reopening_user_email = get_user_email_by_id(reopening_user_id) if reopening_user_id else 'System'

                # Send notification about reopening
                self.notify_admins('incident_reopened', {
                    'incident_id': incident_id,
                    'title': incident.get('title', ''),
                    'reopened_by': reopening_user_email,
                    'previous_status': incident.get('status'),
                    'severity': incident.get('severity')
                })

                print(f"✅ Incident {incident_id} reopened successfully by user {reopening_user_id}")
                return reopened_incident
            else:
                print(f"❌ Failed to reopen incident {incident_id}")
                return None

        except Exception as e:
            print(f"❌ Error reopening incident {incident_id}: {str(e)}")
            return None

    def delete_incident(self, incident_id, deleting_user_id=None):
        """Delete an incident permanently with notification"""
        try:
            # Get incident info before deletion for notification
            incident = get_incident_by_incident_id(incident_id)
            if not incident:
                print(f"❌ Incident {incident_id} not found")
                return False

            # Delete the incident
            success = delete_incident_db(incident_id, deleting_user_id)

            if success:
                # Get email of deleting user
                deleting_user_email = get_user_email_by_id(deleting_user_id) if deleting_user_id else 'System'

                # Send notification about deletion
                self.notify_admins('incident_deleted', {
                    'incident_id': incident_id,
                    'title': incident.get('title', ''),
                    'deleted_by': deleting_user_email,
                    'original_status': incident.get('status'),
                    'severity': incident.get('severity')
                })

                print(f"✅ Incident {incident_id} deleted successfully by user {deleting_user_id}")
                return True
            else:
                print(f"❌ Failed to delete incident {incident_id}")
                return False

        except Exception as e:
            print(f"❌ Error deleting incident {incident_id}: {str(e)}")
            return False
