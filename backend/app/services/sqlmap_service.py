import subprocess
import json
import uuid
import tempfile
import os
from datetime import datetime
from typing import Dict, List, Optional

class SQLMapService:
    """Service pour l'intégration avec SQLMap"""
    
    def __init__(self):
        self.sqlmap_path = self._find_sqlmap_path()
        self.temp_dir = tempfile.gettempdir()
    
    def _find_sqlmap_path(self) -> str:
        """Trouver le chemin vers SQLMap"""
        possible_paths = [
            '/usr/bin/sqlmap',
            '/usr/local/bin/sqlmap',
            '/opt/sqlmap/sqlmap.py',
            'sqlmap',
            'python3 /usr/share/sqlmap/sqlmap.py'
        ]
        
        for path in possible_paths:
            try:
                cmd = path.split() + ['--version']
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    return path
            except (subprocess.TimeoutExpired, FileNotFoundError):
                continue
        
        return 'sqlmap'  # Fallback
    
    def is_available(self) -> bool:
        """Vérifier si SQLMap est disponible"""
        try:
            cmd = self.sqlmap_path.split() + ['--version']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    def scan_target(self, target_url: str, options: List[str] = None, data: str = None) -> Dict:
        """
        Lancer un scan SQLMap sur une cible
        
        Args:
            target_url: URL à tester pour les injections SQL
            options: Options SQLMap personnalisées
            data: Données POST à tester
            
        Returns:
            Dictionnaire avec les résultats du scan
        """
        scan_id = str(uuid.uuid4())
        output_dir = os.path.join(self.temp_dir, f"sqlmap_scan_{scan_id}")
        os.makedirs(output_dir, exist_ok=True)
        
        # Construire la commande SQLMap
        cmd = self.sqlmap_path.split()
        cmd.extend(['-u', target_url])
        
        # Ajouter les options par défaut
        cmd.extend([
            '--batch',  # Mode non-interactif
            '--output-dir', output_dir,
            '--flush-session',  # Nouvelle session
            '--fresh-queries',  # Nouvelles requêtes
            '--threads', '5'  # Parallélisation
        ])
        
        # Ajouter les données POST si spécifiées
        if data:
            cmd.extend(['--data', data])
        
        # Ajouter les options personnalisées
        if options:
            cmd.extend(options)
        
        try:
            # Exécuter SQLMap
            start_time = datetime.utcnow()
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30 minutes max
            end_time = datetime.utcnow()
            
            # Parser les résultats
            vulnerabilities = self._parse_sqlmap_output(result.stdout)
            
            # Nettoyer le répertoire temporaire
            import shutil
            if os.path.exists(output_dir):
                shutil.rmtree(output_dir)
            
            if result.returncode == 0:
                return {
                    'status': 'completed',
                    'scan_id': scan_id,
                    'target': target_url,
                    'start_time': start_time.isoformat(),
                    'end_time': end_time.isoformat(),
                    'scan_time': (end_time - start_time).total_seconds(),
                    'command': ' '.join(cmd),
                    'vulnerabilities': vulnerabilities,
                    'raw_output': result.stdout
                }
            else:
                return {
                    'status': 'failed',
                    'scan_id': scan_id,
                    'target': target_url,
                    'error': result.stderr,
                    'command': ' '.join(cmd)
                }
                
        except subprocess.TimeoutExpired:
            return {
                'status': 'timeout',
                'scan_id': scan_id,
                'target': target_url,
                'error': 'SQLMap scan timed out after 30 minutes',
                'command': ' '.join(cmd)
            }
        except Exception as e:
            return {
                'status': 'error',
                'scan_id': scan_id,
                'target': target_url,
                'error': str(e),
                'command': ' '.join(cmd)
            }
    
    def _parse_sqlmap_output(self, sqlmap_output: str) -> List[Dict]:
        """Parser les vulnérabilités depuis la sortie SQLMap"""
        vulnerabilities = []
        lines = sqlmap_output.split('\n')
        
        current_vuln = None
        for line in lines:
            line = line.strip()
            
            # Détecter les injections trouvées
            if 'Parameter:' in line and 'is vulnerable' in sqlmap_output:
                param_name = line.split('Parameter:')[1].strip() if 'Parameter:' in line else 'unknown'
                current_vuln = {
                    'id': f'SQLMAP-{uuid.uuid4().hex[:8]}',
                    'name': f'SQL Injection in parameter: {param_name}',
                    'severity': 'high',
                    'description': f'SQL injection vulnerability found in parameter {param_name}',
                    'tool': 'sqlmap',
                    'parameter': param_name
                }
            
            # Détecter le type d'injection
            if current_vuln and 'Type:' in line:
                injection_type = line.split('Type:')[1].strip()
                current_vuln['injection_type'] = injection_type
                current_vuln['description'] += f' (Type: {injection_type})'
            
            # Détecter le payload
            if current_vuln and 'Payload:' in line:
                payload = line.split('Payload:')[1].strip()
                current_vuln['payload'] = payload
                vulnerabilities.append(current_vuln)
                current_vuln = None
            
            # Détecter les bases de données trouvées
            if 'available databases' in line.lower():
                if current_vuln:
                    current_vuln['severity'] = 'critical'  # Élever la sévérité si DB accessible
            
            # Détecter les erreurs SQL
            if any(keyword in line.lower() for keyword in ['sql syntax', 'mysql error', 'oracle error', 'postgresql error']):
                if not current_vuln:
                    current_vuln = {
                        'id': f'SQLMAP-ERROR-{uuid.uuid4().hex[:8]}',
                        'name': 'SQL Error Disclosure',
                        'severity': 'medium',
                        'description': f'SQL error message disclosed: {line}',
                        'tool': 'sqlmap'
                    }
                    vulnerabilities.append(current_vuln)
                    current_vuln = None
        
        return vulnerabilities
    
    def test_url(self, url: str, level: int = 1, risk: int = 1) -> Dict:
        """Test rapide d'une URL pour les injections SQL"""
        options = [
            '--level', str(level),
            '--risk', str(risk),
            '--timeout', '30'
        ]
        return self.scan_target(url, options)
    
    def test_form(self, url: str, data: str, level: int = 3, risk: int = 2) -> Dict:
        """Test d'un formulaire pour les injections SQL"""
        options = [
            '--level', str(level),
            '--risk', str(risk),
            '--timeout', '60'
        ]
        return self.scan_target(url, options, data)
    
    def deep_scan(self, url: str) -> Dict:
        """Scan approfondi avec tous les tests"""
        options = [
            '--level', '5',
            '--risk', '3',
            '--timeout', '120',
            '--tamper', 'space2comment',
            '--technique', 'BEUSTQ'  # Tous les types d'injection
        ]
        return self.scan_target(url, options)
