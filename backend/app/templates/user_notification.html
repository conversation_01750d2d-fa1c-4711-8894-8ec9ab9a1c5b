{% extends "email_base.html" %}

{% block content %}
<h2 style="color:#8b5cf6; margin-top:0; font-size:24px; font-weight:bold; text-align:center;">
  {% if notification_type == 'security_alerts' %}
    🛡️ Security Alert
  {% elif notification_type == 'login_alerts' %}
    🔑 Login Alert
  {% elif notification_type == 'profile_changes' %}
    👤 Profile Updated
  {% elif notification_type == 'analysis_completed' %}
    📊 Analysis Complete
  {% elif notification_type == 'system_updates' %}
    🔄 System Update
  {% else %}
    🔔 Notification
  {% endif %}
</h2>

<p style="margin-bottom:20px; font-size:16px; color:#000000;">Hello <strong style="color: #8b5cf6;">{{ user_name or 'User' }}</strong>,</p>

<div style="background-color: #f8fafc; border-left: 4px solid 
  {% if notification_type == 'security_alerts' %}#ef4444{% elif notification_type == 'login_alerts' %}#f59e0b{% elif notification_type == 'profile_changes' %}#8b5cf6{% elif notification_type == 'analysis_completed' %}#10b981{% elif notification_type == 'system_updates' %}#06b6d4{% else %}#6b7280{% endif %}; 
  padding: 20px; margin: 20px 0; border-radius: 0 8px 8px 0;">
  
  <h3 style="margin-top: 0; color: 
    {% if notification_type == 'security_alerts' %}#dc2626{% elif notification_type == 'login_alerts' %}#d97706{% elif notification_type == 'profile_changes' %}#7c3aed{% elif notification_type == 'analysis_completed' %}#059669{% elif notification_type == 'system_updates' %}#0284c7{% else %}#374151{% endif %}; 
    font-size: 18px; font-weight: bold;">
    {{ title }}
  </h3>
  
  <p style="margin: 10px 0; color: #374151; font-size: 16px; line-height: 1.5;">
    {{ message }}
  </p>
  
  {% if data %}
    {% if data.ip_address %}
      <p style="margin: 5px 0; color: #6b7280; font-size: 14px;">
        <strong>IP Address:</strong> {{ data.ip_address }}
      </p>
    {% endif %}
    
    {% if data.user_agent %}
      <p style="margin: 5px 0; color: #6b7280; font-size: 14px;">
        <strong>Device:</strong> {{ data.user_agent[:100] }}{% if data.user_agent|length > 100 %}...{% endif %}
      </p>
    {% endif %}
    
    {% if data.timestamp %}
      <p style="margin: 5px 0; color: #6b7280; font-size: 14px;">
        <strong>Time:</strong> {{ data.timestamp }}
      </p>
    {% endif %}
  {% endif %}
</div>

{% if notification_type == 'security_alerts' %}
  <div style="background-color: #fef2f2; border: 1px solid #fecaca; padding: 15px; border-radius: 8px; margin: 20px 0;">
    <p style="margin: 0; color: #dc2626; font-size: 14px; font-weight: 500;">
      ⚠️ <strong>Security Recommendation:</strong> If this wasn't you, please change your password immediately and review your account security settings.
    </p>
  </div>
{% endif %}

{% if notification_type == 'login_alerts' %}
  <div style="background-color: #fffbeb; border: 1px solid #fed7aa; padding: 15px; border-radius: 8px; margin: 20px 0;">
    <p style="margin: 0; color: #d97706; font-size: 14px; font-weight: 500;">
      🔐 <strong>Security Note:</strong> If this login wasn't authorized by you, please secure your account immediately.
    </p>
  </div>
{% endif %}

<div style="text-align: center; margin: 30px 0;">
  <a href="{{ platform_url }}" style="display: inline-block; background: linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%); color: #ffffff; text-decoration: none; padding: 12px 30px; border-radius: 25px; font-weight: bold; font-size: 16px; box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3); transition: all 0.3s ease;">
    🚀 Access PICA Platform
  </a>
</div>

<div style="background-color: #f1f5f9; padding: 20px; border-radius: 8px; margin: 20px 0;">
  <h4 style="margin-top: 0; color: #475569; font-size: 16px;">📧 Email Preferences</h4>
  <p style="margin: 10px 0 0 0; color: #64748b; font-size: 14px;">
    You're receiving this email because you have email notifications enabled for {{ notification_type.replace('_', ' ').title() }} in your PICA account settings. 
    You can manage your notification preferences in your <a href="{{ platform_url }}/settings?tab=notifications" style="color: #8b5cf6; text-decoration: none;">account settings</a>.
  </p>
</div>

<p style="margin-top: 30px; color: #6b7280; font-size: 14px;">
  Best regards,<br>
  <strong style="color: #8b5cf6;">The PICA Team</strong><br>
  <span style="color: #06b6d4;">Plateforme de Cybersécurité Automatisée</span>
</p>
{% endblock %}
