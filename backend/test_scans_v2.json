{"info": {"name": "PICA Pentesting Tests v2.0 - Structure Organisée", "description": "Tests pour la nouvelle structure organisée des scans selon spécifications", "version": "2.0.0", "date": "2025-01-23"}, "base_url": "http://localhost:5000/scan/pentesting", "auth": {"type": "Bearer", "token": "YOUR_JWT_TOKEN_HERE"}, "test_targets": {"network": "scanme.nmap.org", "web": "http://testphp.vulnweb.com", "vulnerability": "***********", "deep": "scanme.nmap.org"}, "categories": {"network": {"description": "🟦 Network Scan - Nmap + OpenVAS + Metasploit (parallèle)", "endpoint": "/scan/network", "tools": ["nmap", "openvas", "metasploit"], "execution": "parallel", "tests": {"basic": {"method": "POST", "url": "{{base_url}}/scan/network", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{token}}"}, "body": {"target": "{{test_targets.network}}", "scan_type": "basic", "ports": "22,80,443"}, "expected": {"status": 200, "response_fields": ["scan_id", "scan_type", "target", "config", "message"], "scan_type": "basic", "category": "network"}}, "aggressive": {"method": "POST", "url": "{{base_url}}/scan/network", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{token}}"}, "body": {"target": "{{test_targets.network}}", "scan_type": "aggressive", "ports": "1-1000"}, "expected": {"status": 200, "response_fields": ["scan_id", "scan_type", "target", "config"], "scan_type": "aggressive"}}, "stealth": {"method": "POST", "url": "{{base_url}}/scan/network", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{token}}"}, "body": {"target": "{{test_targets.network}}", "scan_type": "stealth", "ports": "80,443"}, "expected": {"status": 200, "scan_type": "stealth"}}, "comprehensive": {"method": "POST", "url": "{{base_url}}/scan/network", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{token}}"}, "body": {"target": "{{test_targets.network}}", "scan_type": "comprehensive", "ports": "1-65535"}, "expected": {"status": 200, "scan_type": "comprehensive"}}}}, "web": {"description": "🟩 Web Scan - Nikto + SQLMap + Dirb + GoBuster + Zap (parallèle)", "endpoint": "/scan/web", "tools": ["nikto", "sqlmap", "dirb", "gobuster", "zap"], "execution": "parallel", "tests": {"basic": {"method": "POST", "url": "{{base_url}}/scan/web", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{token}}"}, "body": {"target": "{{test_targets.web}}", "scan_type": "basic"}, "expected": {"status": 200, "response_fields": ["scan_id", "scan_type", "target", "config"], "scan_type": "basic", "category": "web"}}, "aggressive": {"method": "POST", "url": "{{base_url}}/scan/web", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{token}}"}, "body": {"target": "{{test_targets.web}}", "scan_type": "aggressive"}, "expected": {"status": 200, "scan_type": "aggressive"}}, "stealth": {"method": "POST", "url": "{{base_url}}/scan/web", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{token}}"}, "body": {"target": "{{test_targets.web}}", "scan_type": "stealth"}, "expected": {"status": 200, "scan_type": "stealth"}}, "comprehensive": {"method": "POST", "url": "{{base_url}}/scan/web", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{token}}"}, "body": {"target": "{{test_targets.web}}", "scan_type": "comprehensive"}, "expected": {"status": 200, "scan_type": "comprehensive"}}}}, "vulnerability": {"description": "🟪 Vulnerability Scan - OpenVAS + Metasploit (parallèle)", "endpoint": "/scan/vulnerability", "tools": ["openvas", "metasploit"], "execution": "parallel", "tests": {"basic": {"method": "POST", "url": "{{base_url}}/scan/vulnerability", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{token}}"}, "body": {"target": "{{test_targets.vulnerability}}", "scan_type": "basic", "ports": "22,80,443"}, "expected": {"status": 200, "response_fields": ["scan_id", "scan_type", "target", "config"], "scan_type": "basic", "category": "vulnerability"}}, "aggressive": {"method": "POST", "url": "{{base_url}}/scan/vulnerability", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{token}}"}, "body": {"target": "{{test_targets.vulnerability}}", "scan_type": "aggressive", "ports": "1-1000"}, "expected": {"status": 200, "scan_type": "aggressive"}}, "stealth": {"method": "POST", "url": "{{base_url}}/scan/vulnerability", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{token}}"}, "body": {"target": "{{test_targets.vulnerability}}", "scan_type": "stealth", "ports": "80,443"}, "expected": {"status": 200, "scan_type": "stealth"}}, "comprehensive": {"method": "POST", "url": "{{base_url}}/scan/vulnerability", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{token}}"}, "body": {"target": "{{test_targets.vulnerability}}", "scan_type": "comprehensive", "ports": "1-65535"}, "expected": {"status": 200, "scan_type": "comprehensive"}}}}, "deep": {"description": "🟥 Deep Scan - TOUS les outils (séquentiel avec progression)", "endpoint": "/scan/deep", "tools": ["nmap", "nikto", "sqlmap", "dirb", "gobuster", "openvas", "metasploit", "zap"], "execution": "sequential_with_progress", "tests": {"basic": {"method": "POST", "url": "{{base_url}}/scan/deep", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{token}}"}, "body": {"target": "{{test_targets.deep}}", "scan_type": "basic", "ports": "22,80,443"}, "expected": {"status": 200, "response_fields": ["scan_id", "scan_type", "target", "config", "progress_tracking"], "scan_type": "basic", "category": "deep", "progress_tracking": true}}, "aggressive": {"method": "POST", "url": "{{base_url}}/scan/deep", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{token}}"}, "body": {"target": "{{test_targets.deep}}", "scan_type": "aggressive", "ports": "1-1000"}, "expected": {"status": 200, "scan_type": "aggressive", "progress_tracking": true}}, "stealth": {"method": "POST", "url": "{{base_url}}/scan/deep", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{token}}"}, "body": {"target": "{{test_targets.deep}}", "scan_type": "stealth", "ports": "80,443"}, "expected": {"status": 200, "scan_type": "stealth", "progress_tracking": true}}, "comprehensive": {"method": "POST", "url": "{{base_url}}/scan/deep", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{token}}"}, "body": {"target": "{{test_targets.deep}}", "scan_type": "comprehensive", "ports": "1-65535"}, "expected": {"status": 200, "scan_type": "comprehensive", "progress_tracking": true}}}}}, "utility_tests": {"status": {"description": "Obt<PERSON><PERSON> le statut du module pentesting", "method": "GET", "url": "{{base_url}}/", "headers": {"Authorization": "Bearer {{token}}"}, "expected": {"status": 200, "response_fields": ["status", "module", "description", "version", "categories", "scan_types"], "module": "pentesting", "version": "2.0.0"}}, "scan_history": {"description": "Obtenir l'historique des scans", "method": "GET", "url": "{{base_url}}/scans", "headers": {"Authorization": "Bearer {{token}}"}, "expected": {"status": 200, "response_fields": ["scans", "total", "message"], "scans_is_array": true}}, "scan_result": {"description": "Obt<PERSON>r le résultat d'un scan spécifique", "method": "GET", "url": "{{base_url}}/scan/{scan_id}", "headers": {"Authorization": "Bearer {{token}}"}, "note": "Remplacer {scan_id} par un ID de scan réel", "expected": {"status": 200, "response_fields": ["scan_id", "category", "scan_type", "target", "status"]}}}, "test_scenarios": {"quick_test": {"description": "Test rapide de toutes les catégories avec type basic", "steps": ["1. Tester Network Scan Basic", "2. Tester <PERSON>", "3. Tester Vulnerability Scan Basic", "4. Tester <PERSON>", "5. Vérifier l'historique des scans"]}, "comprehensive_test": {"description": "Test complet de tous les types pour chaque catégorie", "steps": ["1. Tester tous les types Network (basic, aggressive, stealth, comprehensive)", "2. Tester tous les types Web (basic, aggressive, stealth, comprehensive)", "3. Tester tous les types Vulnerability (basic, aggressive, stealth, comprehensive)", "4. Tester tous les types Deep (basic, aggressive, stealth, comprehensive)", "5. Vérifier l'historique et les résultats"]}}, "validation_rules": {"all_scans": {"required_fields": ["scan_id", "scan_type", "target", "config", "message"], "scan_id_format": "UUID v4", "status_code": 200, "execution_mode": "asynchronous"}, "deep_scan_specific": {"additional_fields": ["progress_tracking"], "progress_tracking": true, "tools_count": "variable selon scan_type"}, "error_handling": {"missing_target": {"status": 400, "error_message": "Target is required"}, "invalid_scan_type": {"status": 400, "error_message": "Invalid scan_type. Must be one of: [basic, aggressive, stealth, comprehensive]"}, "unauthorized": {"status": 401, "error_message": "Missing or invalid JWT token"}}}}