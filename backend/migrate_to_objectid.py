#!/usr/bin/env python3
"""
Script de migration pour convertir les user_id de username vers ObjectId
dans les collections analyses_scan et scans.
"""

from app.extensions import mongo
from app import create_app
from bson import ObjectId

def migrate_analyses_to_objectid():
    """Migre les analyses pour utiliser ObjectId au lieu de username"""
    print("🔄 Migration des analyses vers ObjectId...")
    
    app = create_app()
    with app.app_context():
        # Récupérer toutes les analyses
        analyses = list(mongo.db.analyses_scan.find({}))
        
        migrated_count = 0
        error_count = 0
        already_objectid_count = 0
        
        for analysis in analyses:
            user_id = analysis.get('user_id')
            
            # Si c'est déjà un ObjectId, passer
            if isinstance(user_id, ObjectId):
                already_objectid_count += 1
                continue
                
            # Si c'est un string, essayer de le convertir
            if isinstance(user_id, str):
                # D'abord essayer de voir si c'est déjà un ObjectId valide en string
                if len(user_id) == 24:
                    try:
                        object_id = ObjectId(user_id)
                        # Vérifier que l'utilisateur existe
                        user_exists = mongo.db.users.find_one({'_id': object_id})
                        if user_exists:
                            # Mettre à jour avec l'ObjectId
                            result = mongo.db.analyses_scan.update_one(
                                {'_id': analysis['_id']},
                                {'$set': {'user_id': object_id}}
                            )
                            if result.modified_count > 0:
                                migrated_count += 1
                                print(f"  ✅ Analyse {analysis['analysis_id']}: ObjectId string -> ObjectId")
                            continue
                    except:
                        pass
                
                # Sinon, chercher l'utilisateur par username
                user_doc = mongo.db.users.find_one({'username': user_id})
                if user_doc:
                    # Mettre à jour avec l'ObjectId
                    result = mongo.db.analyses_scan.update_one(
                        {'_id': analysis['_id']},
                        {'$set': {'user_id': user_doc['_id']}}
                    )
                    if result.modified_count > 0:
                        migrated_count += 1
                        print(f"  ✅ Analyse {analysis['analysis_id']}: '{user_id}' -> {user_doc['_id']}")
                    else:
                        error_count += 1
                        print(f"  ❌ Échec migration analyse {analysis['analysis_id']}")
                else:
                    error_count += 1
                    print(f"  ⚠️ Utilisateur non trouvé pour analyse {analysis['analysis_id']}: '{user_id}'")
        
        print(f"\n📊 Résultats migration analyses:")
        print(f"  - Analyses migrées: {migrated_count}")
        print(f"  - Déjà en ObjectId: {already_objectid_count}")
        print(f"  - Erreurs: {error_count}")
        print(f"  - Total analyses: {len(analyses)}")

def migrate_scans_to_objectid():
    """Migre les scans pour utiliser ObjectId au lieu de username (optionnel)"""
    print("\n🔄 Migration des scans vers ObjectId...")
    
    app = create_app()
    with app.app_context():
        # Récupérer tous les scans avec user_id non-null
        scans = list(mongo.db.scans.find({'user_id': {'$ne': None}}))
        
        migrated_count = 0
        error_count = 0
        already_objectid_count = 0
        
        for scan in scans:
            user_id = scan.get('user_id')
            
            # Si c'est déjà un ObjectId, passer
            if isinstance(user_id, ObjectId):
                already_objectid_count += 1
                continue
                
            # Si c'est un string, essayer de le convertir
            if isinstance(user_id, str):
                # D'abord essayer de voir si c'est déjà un ObjectId valide en string
                if len(user_id) == 24:
                    try:
                        object_id = ObjectId(user_id)
                        # Vérifier que l'utilisateur existe
                        user_exists = mongo.db.users.find_one({'_id': object_id})
                        if user_exists:
                            # Mettre à jour avec l'ObjectId
                            result = mongo.db.scans.update_one(
                                {'_id': scan['_id']},
                                {'$set': {'user_id': object_id}}
                            )
                            if result.modified_count > 0:
                                migrated_count += 1
                            continue
                    except:
                        pass
                
                # Sinon, chercher l'utilisateur par username
                user_doc = mongo.db.users.find_one({'username': user_id})
                if user_doc:
                    # Mettre à jour avec l'ObjectId
                    result = mongo.db.scans.update_one(
                        {'_id': scan['_id']},
                        {'$set': {'user_id': user_doc['_id']}}
                    )
                    if result.modified_count > 0:
                        migrated_count += 1
                        if migrated_count <= 5:  # Afficher seulement les 5 premiers
                            print(f"  ✅ Scan {scan.get('scan_id', 'unknown')}: '{user_id}' -> {user_doc['_id']}")
                        elif migrated_count == 6:
                            print(f"  ... (migration en cours)")
                    else:
                        error_count += 1
                else:
                    error_count += 1
                    if error_count <= 3:  # Afficher seulement les 3 premières erreurs
                        print(f"  ⚠️ Utilisateur non trouvé pour scan {scan.get('scan_id', 'unknown')}: '{user_id}'")
        
        print(f"\n📊 Résultats migration scans:")
        print(f"  - Scans migrés: {migrated_count}")
        print(f"  - Déjà en ObjectId: {already_objectid_count}")
        print(f"  - Erreurs: {error_count}")
        print(f"  - Total scans: {len(scans)}")

def verify_migration():
    """Vérifie que la migration s'est bien passée"""
    print("\n🔍 Vérification de la migration...")
    
    app = create_app()
    with app.app_context():
        # Vérifier analyses
        analyses_with_string_ids = list(mongo.db.analyses_scan.find({
            'user_id': {'$type': 'string'}
        }))
        
        analyses_with_objectid = list(mongo.db.analyses_scan.find({
            'user_id': {'$type': 'objectId'}
        }))
        
        print(f"  - Analyses avec user_id ObjectId: {len(analyses_with_objectid)}")
        print(f"  - Analyses avec user_id string restants: {len(analyses_with_string_ids)}")
        
        if len(analyses_with_string_ids) == 0:
            print("  ✅ Migration des analyses réussie !")
        else:
            print("  ⚠️ Il reste des analyses avec user_id string")
            for analysis in analyses_with_string_ids[:3]:
                print(f"    - {analysis['analysis_id']}: user_id = '{analysis['user_id']}'")

def test_jwt_compatibility():
    """Teste que le nouveau système JWT fonctionne"""
    print("\n🧪 Test de compatibilité JWT...")
    
    app = create_app()
    with app.app_context():
        from flask_jwt_extended import create_access_token
        
        # Tester avec un utilisateur existant
        user = mongo.db.users.find_one({'username': 'zeinebcha'})
        if user:
            # Créer un token avec le nouveau système (ObjectId comme identity)
            token = create_access_token(
                identity=str(user['_id']),
                additional_claims={
                    'username': user['username'],
                    'email': user['email']
                }
            )
            
            print(f"  ✅ Token JWT créé avec ObjectId: {str(user['_id'])[:8]}...")
            print(f"  ✅ Username dans claims: {user['username']}")
        else:
            print("  ❌ Aucun utilisateur trouvé pour le test")

if __name__ == "__main__":
    print("🚀 Migration vers ObjectId comme identifiant unique")
    
    # Migrer les analyses
    migrate_analyses_to_objectid()
    
    # Migrer les scans (optionnel)
    migrate_scans_to_objectid()
    
    # Vérifier la migration
    verify_migration()
    
    # Tester la compatibilité JWT
    test_jwt_compatibility()
    
    print("\n✅ Migration terminée !")
    print("\n⚠️ IMPORTANT: Redémarrez le backend et reconnectez-vous pour utiliser le nouveau système JWT.")
