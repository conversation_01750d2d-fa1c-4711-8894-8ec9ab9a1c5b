2025-06-30 19:40:54,734 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 🚀 SCAN STARTED - Category: deep, Type: comprehensive, Target: ************, Tools: nmap, openvas, metasploit, nikto, sqlmap, dirb, gobuster, zap
2025-06-30 19:40:54,741 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 🚀 SCAN STARTED - Category: deep, Type: comprehensive, Target: ************, Tools: nmap, openvas, metasploit, nikto, sqlmap, dirb, gobuster, zap
2025-06-30 19:40:54,746 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 🔧 TOOL START - DEEP_SCAN - Command: 🚀 Starting Deep Scan - All Tools Running in Parallel
2025-06-30 19:40:54,758 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Launching all tools in parallel...
2025-06-30 19:40:54,760 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started nmap in background
2025-06-30 19:40:54,760 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on ************
2025-06-30 19:40:54,762 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started openvas in background
2025-06-30 19:40:54,763 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on ************
2025-06-30 19:40:54,763 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 19:40:54,764 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started metasploit in background
2025-06-30 19:40:54,765 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on ************
2025-06-30 19:40:54,767 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 19:40:54,767 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 19:40:54,769 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started nikto in background
2025-06-30 19:40:54,769 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 🔧 TOOL START - NIKTO - Command: nikto scan on ************
2025-06-30 19:40:54,770 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap port scan...
2025-06-30 19:40:54,770 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 19:40:54,771 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 19:40:54,774 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started sqlmap in background
2025-06-30 19:40:54,774 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 🔧 TOOL START - SQLMAP - Command: sqlmap scan on ************
2025-06-30 19:40:54,775 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS vulnerability scan...
2025-06-30 19:40:54,775 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 19:40:54,776 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 19:40:54,778 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started dirb in background
2025-06-30 19:40:54,778 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 🔧 TOOL START - DIRB - Command: dirb scan on ************
2025-06-30 19:40:54,780 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 19:40:54,780 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 19:40:54,783 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit exploitation scan...
2025-06-30 19:40:54,783 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started gobuster in background
2025-06-30 19:40:54,784 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 19:40:54,784 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 🔧 TOOL START - GOBUSTER - Command: gobuster scan on ************
2025-06-30 19:40:54,784 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 19:40:54,784 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Formatted target for web tool: ************ -> http://************
2025-06-30 19:40:54,788 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Formatted target for web tool: ************ -> http://************
2025-06-30 19:40:54,788 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started zap in background
2025-06-30 19:40:54,788 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 🔧 TOOL START - ZAP - Command: zap scan on ************
2025-06-30 19:40:54,790 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 19:40:54,790 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 19:40:54,791 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Starting Nikto web vulnerability scan...
2025-06-30 19:40:54,792 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 15% - All 8 tools are now running in parallel
2025-06-30 19:40:54,793 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 19:40:54,793 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 10% - Starting SQLMap scan (maximum intensity)...
2025-06-30 19:40:54,794 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 19:40:54,795 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Formatted target for web tool: ************ -> http://************
2025-06-30 19:40:54,797 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Formatted target for web tool: ************ -> http://************
2025-06-30 19:40:54,797 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 19:40:54,798 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Command: sqlmap -u http://************ --batch --level=5 --risk=3 --threads=5 --tamper=space2comment
2025-06-30 19:40:54,798 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 10% - Starting Dirb directory scan (maximum intensity)...
2025-06-30 19:40:54,799 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Formatted target for web tool: ************ -> http://************
2025-06-30 19:40:54,800 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 10% - Starting GoBuster directory scan (maximum intensity)...
2025-06-30 19:40:54,802 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Command: dirb http://************ /usr/share/dirb/wordlists/big.txt -w
2025-06-30 19:40:54,803 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - ZAP: 10% - Starting ZAP security scan...
2025-06-30 19:40:54,804 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Command: gobuster dir -u http://************ -w /usr/share/wordlists/dirb/big.txt -t 50 -q
2025-06-30 19:40:54,804 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 25% - Testing for SQL injection... (0s elapsed)
2025-06-30 19:40:54,807 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 25% - Scanning directories... (0s elapsed)
2025-06-30 19:40:54,808 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 25% - Brute forcing directories... (0s elapsed)
2025-06-30 19:40:56,775 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Scanning ports... 10% complete
2025-06-30 19:40:56,800 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 15% - 0/8 tools completed
2025-06-30 19:40:57,787 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Metasploit scanning... 10% complete
2025-06-30 19:40:57,797 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Scanning web vulnerabilities... 10% complete
2025-06-30 19:40:58,782 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NMAP: 20% - Scanning ports... 20% complete
2025-06-30 19:40:58,807 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - ZAP: 10% - ZAP security scanning... 10% complete
2025-06-30 19:40:58,808 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 15% - 0/8 tools completed
2025-06-30 19:40:59,783 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - OpenVAS vulnerability scanning... 10% complete
2025-06-30 19:40:59,809 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 27% - Testing for SQL injection... (5s elapsed)
2025-06-30 19:40:59,813 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 27% - Scanning directories... (5s elapsed)
2025-06-30 19:40:59,813 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 28% - Brute forcing directories... (5s elapsed)
2025-06-30 19:41:00,789 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Scanning ports... 30% complete
2025-06-30 19:41:00,794 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 25% - Metasploit scanning... 25% complete
2025-06-30 19:41:00,804 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Scanning web vulnerabilities... 25% complete
2025-06-30 19:41:00,817 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 15% - 0/8 tools completed
2025-06-30 19:41:02,798 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NMAP: 40% - Scanning ports... 40% complete
2025-06-30 19:41:02,816 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - ZAP: 30% - ZAP security scanning... 30% complete
2025-06-30 19:41:02,824 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 15% - 0/8 tools completed
2025-06-30 19:41:03,800 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 40% - Metasploit scanning... 40% complete
2025-06-30 19:41:03,810 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NIKTO: 40% - Scanning web vulnerabilities... 40% complete
2025-06-30 19:41:04,790 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - OPENVAS: 20% - OpenVAS vulnerability scanning... 20% complete
2025-06-30 19:41:04,806 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NMAP: 50% - Scanning ports... 50% complete
2025-06-30 19:41:04,818 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 30% - Testing for SQL injection... (10s elapsed)
2025-06-30 19:41:04,821 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 30% - Scanning directories... (10s elapsed)
2025-06-30 19:41:04,823 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 32% - Brute forcing directories... (10s elapsed)
2025-06-30 19:41:04,831 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 15% - 0/8 tools completed
2025-06-30 19:41:06,807 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 55% - Metasploit scanning... 55% complete
2025-06-30 19:41:06,813 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NMAP: 60% - Scanning ports... 60% complete
2025-06-30 19:41:06,817 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NIKTO: 55% - Scanning web vulnerabilities... 55% complete
2025-06-30 19:41:06,824 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - ZAP: 50% - ZAP security scanning... 50% complete
2025-06-30 19:41:06,837 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 15% - 0/8 tools completed
2025-06-30 19:41:08,819 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NMAP: 70% - Scanning ports... 70% complete
2025-06-30 19:41:08,844 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 15% - 0/8 tools completed
2025-06-30 19:41:09,797 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS vulnerability scanning... 30% complete
2025-06-30 19:41:09,814 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 70% - Metasploit scanning... 70% complete
2025-06-30 19:41:09,824 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NIKTO: 70% - Scanning web vulnerabilities... 70% complete
2025-06-30 19:41:09,825 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 33% - Testing for SQL injection... (15s elapsed)
2025-06-30 19:41:09,828 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 33% - Scanning directories... (15s elapsed)
2025-06-30 19:41:09,831 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 90% - Parsing results...
2025-06-30 19:41:09,836 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - ✅ TOOL RESULT - GOBUSTER: completed - Found 0 directories
2025-06-30 19:41:10,822 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NMAP: 80% - Scanning ports... 80% complete
2025-06-30 19:41:10,827 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan (maximum intensity)...
2025-06-30 19:41:10,829 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - ZAP: 70% - ZAP security scanning... 70% complete
2025-06-30 19:41:10,833 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - ZAP: 10% - Starting security scan...
2025-06-30 19:41:10,837 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - ZAP: 25% - Performing HTTP security checks...
2025-06-30 19:41:10,851 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 25% - 1/8 tools completed
2025-06-30 19:41:10,857 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-06-30 19:41:10,863 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using scan config: Complete audit with all available tools and options
2025-06-30 19:41:10,867 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Nmap options: -T4 -A --script=vuln,safe,discovery
2025-06-30 19:41:10,871 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Port range: 1-65535
2025-06-30 19:41:12,820 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 85% - Metasploit scanning... 85% complete
2025-06-30 19:41:12,823 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-30 19:41:12,825 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-30 19:41:12,827 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-30 19:41:12,829 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NIKTO: 85% - Scanning web vulnerabilities... 85% complete
2025-06-30 19:41:12,831 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Starting Nikto scan (maximum intensity)...
2025-06-30 19:41:12,834 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Executing Nikto command...
2025-06-30 19:41:12,838 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Command: nikto -h http://************ -Format txt -output /tmp/nikto_1751305272.txt -maxtime 2400 -Tuning x
2025-06-30 19:41:12,840 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Scanning... (0s elapsed)
2025-06-30 19:41:12,859 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 25% - 1/8 tools completed
2025-06-30 19:41:14,803 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - OPENVAS: 40% - OpenVAS vulnerability scanning... 40% complete
2025-06-30 19:41:14,832 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 35% - Testing for SQL injection... (20s elapsed)
2025-06-30 19:41:14,833 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 35% - Scanning directories... (20s elapsed)
2025-06-30 19:41:14,863 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 25% - 1/8 tools completed
2025-06-30 19:41:16,869 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 25% - 1/8 tools completed
2025-06-30 19:41:18,873 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 25% - 1/8 tools completed
2025-06-30 19:41:19,807 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - OPENVAS: 50% - OpenVAS vulnerability scanning... 50% complete
2025-06-30 19:41:19,838 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 38% - Testing for SQL injection... (25s elapsed)
2025-06-30 19:41:19,838 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 38% - Scanning directories... (25s elapsed)
2025-06-30 19:41:20,861 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): HTTP request failed: HTTPConnectionPool(host='************', port=80): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x7aab736faf00>, 'Connection to ************ timed out. (connect timeout=10)'))
2025-06-30 19:41:20,864 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - ZAP: 100% - Security scan completed
2025-06-30 19:41:20,865 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Found 1 security alerts
2025-06-30 19:41:20,868 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - ✅ TOOL RESULT - ZAP: completed
2025-06-30 19:41:20,877 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - 2/8 tools completed
2025-06-30 19:41:22,842 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NIKTO: 28% - Scanning... (10s elapsed)
2025-06-30 19:41:22,880 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - 2/8 tools completed
2025-06-30 19:41:24,810 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - OPENVAS: 60% - OpenVAS vulnerability scanning... 60% complete
2025-06-30 19:41:24,842 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 41% - Testing for SQL injection... (30s elapsed)
2025-06-30 19:41:24,842 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 41% - Scanning directories... (30s elapsed)
2025-06-30 19:41:24,884 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - 2/8 tools completed
2025-06-30 19:41:26,887 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - 2/8 tools completed
2025-06-30 19:41:28,891 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - 2/8 tools completed
2025-06-30 19:41:29,815 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS vulnerability scanning... 70% complete
2025-06-30 19:41:29,846 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 43% - Testing for SQL injection... (35s elapsed)
2025-06-30 19:41:29,846 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 43% - Scanning directories... (35s elapsed)
2025-06-30 19:41:30,894 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - 2/8 tools completed
2025-06-30 19:41:32,845 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NIKTO: 32% - Scanning... (20s elapsed)
2025-06-30 19:41:32,898 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - 2/8 tools completed
2025-06-30 19:41:34,818 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - OPENVAS: 80% - OpenVAS vulnerability scanning... 80% complete
2025-06-30 19:41:34,822 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-30 19:41:34,851 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 46% - Testing for SQL injection... (40s elapsed)
2025-06-30 19:41:34,852 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 46% - Scanning directories... (40s elapsed)
2025-06-30 19:41:34,883 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-30 19:41:34,903 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - 2/8 tools completed
2025-06-30 19:41:36,910 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - 2/8 tools completed
2025-06-30 19:41:38,917 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - 2/8 tools completed
2025-06-30 19:41:39,858 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 49% - Testing for SQL injection... (45s elapsed)
2025-06-30 19:41:39,860 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 49% - Scanning directories... (45s elapsed)
2025-06-30 19:41:40,924 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - 2/8 tools completed
2025-06-30 19:41:42,847 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NIKTO: 35% - Scanning... (30s elapsed)
2025-06-30 19:41:42,931 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - 2/8 tools completed
2025-06-30 19:41:44,867 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 52% - Testing for SQL injection... (50s elapsed)
2025-06-30 19:41:44,869 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 52% - Scanning directories... (50s elapsed)
2025-06-30 19:41:44,935 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - 2/8 tools completed
2025-06-30 19:41:46,939 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - 2/8 tools completed
2025-06-30 19:41:48,941 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - 2/8 tools completed
2025-06-30 19:41:49,876 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 54% - Testing for SQL injection... (55s elapsed)
2025-06-30 19:41:49,877 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 54% - Scanning directories... (55s elapsed)
2025-06-30 19:41:50,944 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - 2/8 tools completed
2025-06-30 19:41:52,853 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NIKTO: 39% - Scanning... (40s elapsed)
2025-06-30 19:41:52,947 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - 2/8 tools completed
2025-06-30 19:41:53,116 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-06-30 19:41:53,119 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Scan completed: /usr/bin/nmap -T4 -A --script=vuln,safe,discovery -p 1-65535 -oX /tmp/nmap_scan_9ca2367a-2205-47ee-8b15-cb2ecdd302e4.xml ************
2025-06-30 19:41:53,120 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Found 0 ports
2025-06-30 19:41:53,123 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - ✅ TOOL RESULT - NMAP: completed - Found 0 ports - Found 2 vulnerabilities
2025-06-30 19:41:54,882 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 57% - Testing for SQL injection... (60s elapsed)
2025-06-30 19:41:54,885 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 57% - Scanning directories... (60s elapsed)
2025-06-30 19:41:54,951 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:41:56,918 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-30 19:41:56,921 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_7858ec37-b85e-49dd-b32b-2429791ca506
2025-06-30 19:41:56,925 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 1 vulnerabilities
2025-06-30 19:41:56,953 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:41:58,956 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:41:59,889 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 60% - Testing for SQL injection... (65s elapsed)
2025-06-30 19:41:59,891 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 60% - Scanning directories... (65s elapsed)
2025-06-30 19:42:00,959 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:42:02,858 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NIKTO: 43% - Scanning... (50s elapsed)
2025-06-30 19:42:02,962 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:42:04,896 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 62% - Testing for SQL injection... (70s elapsed)
2025-06-30 19:42:04,897 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 62% - Scanning directories... (70s elapsed)
2025-06-30 19:42:04,965 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:42:06,968 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:42:08,971 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:42:09,904 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 65% - Testing for SQL injection... (75s elapsed)
2025-06-30 19:42:09,906 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 65% - Scanning directories... (75s elapsed)
2025-06-30 19:42:10,974 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:42:12,864 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NIKTO: 46% - Scanning... (60s elapsed)
2025-06-30 19:42:12,976 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:42:14,911 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 68% - Testing for SQL injection... (80s elapsed)
2025-06-30 19:42:14,912 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 68% - Scanning directories... (80s elapsed)
2025-06-30 19:42:14,979 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:42:16,982 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:42:18,985 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:42:19,916 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 71% - Testing for SQL injection... (85s elapsed)
2025-06-30 19:42:19,919 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 71% - Scanning directories... (85s elapsed)
2025-06-30 19:42:20,988 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:42:22,869 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NIKTO: 50% - Scanning... (70s elapsed)
2025-06-30 19:42:22,991 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:42:24,925 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 73% - Testing for SQL injection... (90s elapsed)
2025-06-30 19:42:24,926 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 73% - Scanning directories... (90s elapsed)
2025-06-30 19:42:24,994 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:42:26,997 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:42:29,000 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:42:29,930 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 76% - Testing for SQL injection... (95s elapsed)
2025-06-30 19:42:29,930 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 76% - Scanning directories... (95s elapsed)
2025-06-30 19:42:31,003 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:42:32,874 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NIKTO: 53% - Scanning... (80s elapsed)
2025-06-30 19:42:33,006 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:42:34,933 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 79% - Testing for SQL injection... (100s elapsed)
2025-06-30 19:42:34,934 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 79% - Scanning directories... (100s elapsed)
2025-06-30 19:42:35,009 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:42:37,013 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:42:39,016 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:42:39,936 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 81% - Testing for SQL injection... (105s elapsed)
2025-06-30 19:42:39,937 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 81% - Scanning directories... (105s elapsed)
2025-06-30 19:42:41,019 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:42:42,880 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NIKTO: 90% - Parsing results...
2025-06-30 19:42:42,884 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - NIKTO: 100% - Nikto scan completed
2025-06-30 19:42:42,887 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Scan completed - Found 0 potential issues
2025-06-30 19:42:42,891 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - ✅ TOOL RESULT - NIKTO: completed - Found 0 vulnerabilities
2025-06-30 19:42:43,022 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 19:42:44,939 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 84% - Testing for SQL injection... (110s elapsed)
2025-06-30 19:42:44,940 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 84% - Scanning directories... (110s elapsed)
2025-06-30 19:42:45,024 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 19:42:47,027 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 19:42:49,030 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 19:42:49,942 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 87% - Testing for SQL injection... (115s elapsed)
2025-06-30 19:42:49,943 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 87% - Scanning directories... (115s elapsed)
2025-06-30 19:42:51,033 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 19:42:53,036 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 19:42:55,038 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 19:42:57,041 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 19:42:59,044 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 19:42:59,945 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Scan timeout after 2 minutes - completing with current results
2025-06-30 19:42:59,946 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - SQLMAP: 90% - Parsing results...
2025-06-30 19:42:59,949 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DIRB: 90% - Parsing results...
2025-06-30 19:42:59,951 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - ✅ TOOL RESULT - SQLMAP: completed - Found 0 vulnerabilities
2025-06-30 19:42:59,953 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - ✅ TOOL RESULT - DIRB: completed - Found 0 directories
2025-06-30 19:43:01,048 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:03,052 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:05,056 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:07,058 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:09,062 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:11,065 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:12,871 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - ERROR - ❌ TOOL ERROR - METASPLOIT: Timeout running TCP Port Scanner
2025-06-30 19:43:12,876 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-30 19:43:12,880 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-30 19:43:13,069 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:15,072 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:17,075 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:19,078 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:21,082 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:23,086 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:25,089 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:27,091 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:29,094 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:31,100 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:31,595 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-30 19:43:31,598 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-30 19:43:33,104 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:35,108 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:37,111 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:39,115 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:41,119 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:43,122 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:45,124 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:47,128 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:49,133 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:51,142 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:53,149 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:55,158 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:57,164 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:43:59,172 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:44:01,179 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:44:03,188 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:44:05,196 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:44:07,204 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:44:09,212 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:44:11,220 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:44:13,228 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:44:15,233 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:44:17,236 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:44:19,185 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-30 19:44:19,188 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-30 19:44:19,240 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:44:21,245 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:44:23,249 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:44:25,252 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:44:27,255 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:44:29,258 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:44:31,262 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:44:33,265 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:44:35,269 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:44:35,876 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-30 19:44:35,879 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 3 modules - Found 0 potential vulnerabilities
2025-06-30 19:44:35,883 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-30 19:44:37,274 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - ✅ TOOL RESULT - DEEP_SCAN: all_tools_complete
2025-06-30 19:44:37,280 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 🔧 TOOL START - DEEP_SCAN - Command: 📊 Results Analysis and Consolidation
2025-06-30 19:44:37,288 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 96% - Consolidating port scan results...
2025-06-30 19:44:37,293 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 96% - Found 0 ports from nmap
2025-06-30 19:44:37,297 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 97% - Found 0 ports from metasploit
2025-06-30 19:44:37,304 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 97% - Consolidating vulnerability results...
2025-06-30 19:44:37,309 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 84% - Added 2 vulnerabilities from nmap
2025-06-30 19:44:37,314 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 84% - Added 1 vulnerabilities from openvas
2025-06-30 19:44:37,319 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 98% - Added 1 vulnerabilities from zap
2025-06-30 19:44:37,325 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 98% - Calculating final summary...
2025-06-30 19:44:37,329 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 99% - Summary: 0 ports, 4 vulnerabilities
2025-06-30 19:44:37,336 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 99% - Saving vulnerabilities to database...
2025-06-30 19:44:37,350 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 98% - Saved 3 vulnerabilities to database
2025-06-30 19:44:37,354 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 99% - Creating frontend-compatible results...
2025-06-30 19:44:37,363 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 99% - Finalizing scan results...
2025-06-30 19:44:37,371 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - ✅ TOOL RESULT - DEEP_SCAN: completed
2025-06-30 19:44:37,375 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'network': {'nmap': {'status': 'completed', 'scan_id': '9ca2367a-2205-47ee-8b15-cb2ecdd302e4', 'target': '************', 'start_time': '2025-06-30T17:41:10.874478', 'end_time': '2025-06-30T17:41:53.116302', 'scan_time': 42.241824, 'command': '/usr/bin/nmap -T4 -A --script=vuln,safe,discovery -p 1-65535 -oX /tmp/nmap_scan_9ca2367a-2205-47ee-8b15-cb2ecdd302e4.xml ************', 'ports': [], 'vulnerabilities': [{'name': '|   After NULL UDP avahi packet DoS (CVE-2011-1002).', 'severity': 'medium', 'description': '|   After NULL UDP avahi packet DoS (CVE-2011-1002).', 'tool': 'nmap', 'cve': 'CVE-2011-1002', 'id': 'CVE-2011-1002', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|_  Hosts are all up (not vulnerable).', 'severity': 'medium', 'description': '|_  Hosts are all up (not vulnerable).', 'tool': 'nmap', 'cve': None, 'source_tool': 'nmap', 'category': 'network'}], 'raw_output': "Starting Nmap 7.94SVN ( https://nmap.org ) at 2025-06-30 19:41 CEST\nPre-scan script results:\n| targets-asn: \n|_  targets-asn.asn is a mandatory parameter\n|_http-robtex-shared-ns: *TEMPORARILY DISABLED* due to changes in Robtex's API. See https://www.robtex.com/api/\n| broadcast-dns-service-discovery: \n|   ***********\n|     80/tcp http\n|       path=/phpmyadmin/\n|       Address=************ fe80::a07a:1b52:f619:5817\n|     445/tcp smb\n|       Address=************ fe80::a07a:1b52:f619:5817\n|     Device Information\n|       model=MacSamba\n|_      Address=************ fe80::a07a:1b52:f619:5817\n|_hostmap-robtex: *TEMPORARILY DISABLED* due to changes in Robtex's API. See https://www.robtex.com/api/\n| broadcast-avahi-dos: \n|   Discovered hosts:\n|     ***********\n|   After NULL UDP avahi packet DoS (CVE-2011-1002).\n|_  Hosts are all up (not vulnerable).\nNote: Host seems down. If it is really up, but blocking our ping probes, try -Pn\nNmap done: 1 IP address (0 hosts up) scanned in 42.23 seconds\n"}, 'openvas': {'status': 'completed', 'scan_id': '7858ec37-b85e-49dd-b32b-2429791ca506', 'task_id': 'greenbone_task_7858ec37-b85e-49dd-b32b-2429791ca506', 'target_id': 'greenbone_target_7858ec37-b85e-49dd-b32b-2429791ca506', 'vulnerabilities': [{'name': 'SMB Service Detected', 'severity': 'high', 'description': 'SMB service - check for SMB vulnerabilities', 'port': 445, 'solution': 'Review SMB service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}], 'message': 'Greenbone scan completed for ************', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 3, 'raw_output': "=== TCP Port Scanner ===\nTimeout after 120 seconds\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ************:445      - SMB Detected (versions:2, 3) (preferred dialect:SMB 3.1.1) (compression capabilities:LZNT1, Pattern_V1) (encryption capabilities:AES-256-GCM) (signatures:optional) (guid:{36f404c1-64fd-4c8e-8298-7d4ea363cb4a}) (authentication domain:0XBIOS)\n\x1b[1m\x1b[32m[+]\x1b[0m ************:445      -   Host is running Version 10.0.22621 (likely Windows 11 version 22H2)\n\x1b[1m\x1b[34m[*]\x1b[0m ************          - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => ************\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m ************:445      - An SMB Login Error occurred while connecting to the IPC$ tree.\n\x1b[1m\x1b[34m[*]\x1b[0m ************:445      - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'web': {'nikto': {'status': 'completed', 'vulnerabilities': [], 'scan_time': '90 seconds', 'total_tests': 2, 'raw_output': '- Nikto v2.1.5/2.1.5\n'}, 'sqlmap': {'status': 'completed', 'injections': [], 'raw_output': "        ___\n       __H__\n ___ ___[']_____ ___ ___  {1.8.4#stable}\n|_ -| . [,]     | .'| . |\n|___|_  [(]_|_|_|__,|  _|\n      |_|V...       |_|   https://sqlmap.org\n\n[!] legal disclaimer: Usage of sqlmap for attacking targets without prior mutual consent is illegal. It is the end user's responsibility to obey all applicable local, state and federal laws. Developers assume no liability and are not responsible for any misuse or damage caused by this program\n\n[*] starting @ 19:40:55 /2025-06-30/\n\n[19:40:55] [INFO] loading tamper module 'space2comment'\n[19:40:55] [INFO] testing connection to the target URL\n[19:41:25] [CRITICAL] connection timed out to the target URL. sqlmap is going to retry the request(s)\n[19:41:25] [WARNING] if the problem persists please check that the provided target URL is reachable. In case that it is, you can try to rerun with switch '--random-agent' and/or proxy switches ('--proxy', '--proxy-file'...)\n[19:42:55] [CRITICAL] connection timed out to the target URL\n[19:42:55] [WARNING] your sqlmap version is outdated\n\n[*] ending @ 19:42:55 /2025-06-30/\n\n", 'vulnerabilities': []}, 'dirb': {'status': 'completed', 'directories': [], 'raw_output': '\n-----------------\nDIRB v2.22    \nBy The Dark Raver\n-----------------\n\nSTART_TIME: Mon Jun 30 19:40:54 2025\nURL_BASE: http://************/\nWORDLIST_FILES: /usr/share/dirb/wordlists/big.txt\nOPTION: Not Stopping on warning messages\n\n-----------------\n\n*** Generating Wordlist...\n                                                                               \nGENERATED WORDS: 20458\n\n---- Scanning URL: http://************/ ----\n*** Calculating NOT_FOUND code...\n', 'found_paths': 0}, 'gobuster': {'status': 'completed', 'directories': [], 'raw_output': '', 'found_paths': 0}, 'zap': {'status': 'completed', 'alerts': [{'risk': 'Medium', 'name': 'Connection Issue', 'count': 1, 'description': "Could not analyze target: HTTPConnectionPool(host='************', port=80): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x7aab736faf00>, 'Connection to ************ timed out. (connect timeout=10)'))"}], 'scan_time': '10 seconds', 'raw_output': 'Basic security scan completed. Found 1 potential issues.'}}, 'vulnerabilities': [{'name': '|   After NULL UDP avahi packet DoS (CVE-2011-1002).', 'severity': 'medium', 'description': '|   After NULL UDP avahi packet DoS (CVE-2011-1002).', 'tool': 'nmap', 'cve': 'CVE-2011-1002', 'id': 'CVE-2011-1002', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|_  Hosts are all up (not vulnerable).', 'severity': 'medium', 'description': '|_  Hosts are all up (not vulnerable).', 'tool': 'nmap', 'cve': None, 'source_tool': 'nmap', 'category': 'network'}, {'name': 'SMB Service Detected', 'severity': 'high', 'description': 'SMB service - check for SMB vulnerabilities', 'port': 445, 'solution': 'Review SMB service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'type': 'Connection Issue', 'severity': 'medium', 'description': "Could not analyze target: HTTPConnectionPool(host='************', port=80): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x7aab736faf00>, 'Connection to ************ timed out. (connect timeout=10)'))", 'source_tool': 'zap', 'category': 'web'}], 'ports': [], 'summary': {'total_ports': 0, 'open_ports': 0, 'total_vulnerabilities': 4, 'high_severity': 1, 'medium_severity': 3, 'low_severity': 0, 'scan_phases': 1, 'tools_executed': 8}}
2025-06-30 19:44:37,380 - scan_5b495b0d-c940-45a0-b627-68df9e4a9956 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 222.6s
