2025-06-26 18:46:20,993 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 🚀 SCAN STARTED - Category: deep, Type: comprehensive, Target: ***********, Tools: nmap, openvas, metasploit, nikto, sqlmap, dirb, gobuster, zap
2025-06-26 18:46:20,996 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 🚀 SCAN STARTED - Category: deep, Type: comprehensive, Target: ***********, Tools: nmap, openvas, metasploit, nikto, sqlmap, dirb, gobuster, zap
2025-06-26 18:46:20,997 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 🔧 TOOL START - DEEP_SCAN - Command: 🔍 Phase 1: Network Discovery and Port Scanning
2025-06-26 18:46:21,000 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 5% - Phase 1: Running nmap scan...
2025-06-26 18:46:21,002 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on ***********
2025-06-26 18:46:21,005 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan...
2025-06-26 18:46:21,021 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-06-26 18:47:08,896 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-06-26 18:47:08,898 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Scan completed: /usr/bin/nmap -sT -sV --version-intensity 5 -p 1-1000 -oX /tmp/nmap_scan_154224dd-5b43-4814-992a-6bd67369ae2e.xml ***********
2025-06-26 18:47:08,900 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Found 5 ports
2025-06-26 18:47:08,904 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - ✅ TOOL RESULT - NMAP: completed - Found 5 ports - Found 0 vulnerabilities
2025-06-26 18:47:08,908 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 14% - Phase 1: Running openvas scan...
2025-06-26 18:47:08,911 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on ***********
2025-06-26 18:47:08,915 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-26 18:47:08,980 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-26 18:47:09,052 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-26 18:47:09,056 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_474db1da-fe26-4cf1-b0d0-f107ba00feb0
2025-06-26 18:47:09,061 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 3 vulnerabilities
2025-06-26 18:47:09,066 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 23% - Phase 1: Running metasploit scan...
2025-06-26 18:47:09,069 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on ***********
2025-06-26 18:47:09,073 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-26 18:47:09,076 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-26 18:47:09,078 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-26 18:47:30,534 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-26 18:47:30,539 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-26 18:47:58,429 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-26 18:47:58,433 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-26 18:48:28,478 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-26 18:48:28,482 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-26 18:48:58,476 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-26 18:48:58,480 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 4 modules - Found 0 potential vulnerabilities
2025-06-26 18:48:58,483 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-26 18:48:58,486 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - ✅ TOOL RESULT - DEEP_SCAN: phase_1_complete
2025-06-26 18:48:58,488 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 🔧 TOOL START - DEEP_SCAN - Command: 🌐 Phase 2: Web Application Security Testing
2025-06-26 18:48:58,496 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - Phase 2: Running nikto scan...
2025-06-26 18:48:58,498 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 🔧 TOOL START - NIKTO - Command: nikto scan on ***********
2025-06-26 18:48:58,502 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Starting Nikto scan...
2025-06-26 18:48:58,503 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Executing Nikto command...
2025-06-26 18:48:58,505 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Command: nikto -h *********** -Format txt -output /tmp/nikto_1750956538.txt -maxtime 180 -Tuning 1,2,3,4,5,6,7,8,9,0,a,b,c
2025-06-26 18:48:58,512 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Scanning... (0s elapsed)
2025-06-26 18:49:08,516 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - NIKTO: 90% - Parsing results...
2025-06-26 18:49:08,518 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - NIKTO: 100% - Nikto scan completed
2025-06-26 18:49:08,521 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Scan completed - Found 5 potential issues
2025-06-26 18:49:08,526 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - ✅ TOOL RESULT - NIKTO: completed - Found 5 vulnerabilities
2025-06-26 18:49:08,536 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 41% - Phase 2: Running sqlmap scan...
2025-06-26 18:49:08,539 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 🔧 TOOL START - SQLMAP - Command: sqlmap scan on ***********
2025-06-26 18:49:08,547 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - SQLMAP: 10% - Starting SQLMap scan...
2025-06-26 18:49:08,552 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - SQLMAP: 25% - Executing SQLMap command...
2025-06-26 18:49:08,555 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Command: sqlmap -u *********** --batch --level=1 --risk=1 --timeout=15 --retries=1 --threads=1 --technique=B --no-cast --disable-coloring --flush-session --fresh-queries --crawl=1
2025-06-26 18:49:08,562 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - SQLMAP: 25% - Testing for SQL injection... (0s elapsed)
2025-06-26 18:49:18,568 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - SQLMAP: 90% - Parsing results...
2025-06-26 18:49:18,574 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - SQLMAP: 100% - SQLMap scan completed
2025-06-26 18:49:18,578 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): No SQL injection vulnerabilities detected
2025-06-26 18:49:18,586 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - ✅ TOOL RESULT - SQLMAP: completed
2025-06-26 18:49:18,593 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 47% - Phase 2: Running dirb scan...
2025-06-26 18:49:18,597 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 🔧 TOOL START - DIRB - Command: dirb scan on ***********
2025-06-26 18:49:18,606 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - DIRB: 10% - Starting directory scan...
2025-06-26 18:49:18,628 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - DIRB: 25% - Executing Dirb command...
2025-06-26 18:49:18,631 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Command: dirb *********** /usr/share/dirb/wordlists/small.txt -w -r -S -z 50 -X .php,.html,.txt,.js
2025-06-26 18:49:18,634 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - DIRB: 25% - Scanning directories... (0s elapsed)
2025-06-26 18:49:23,637 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - DIRB: 90% - Parsing results...
2025-06-26 18:49:23,640 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - DIRB: 100% - Directory scan completed
2025-06-26 18:49:23,645 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Found 0 directories
2025-06-26 18:49:23,651 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - ✅ TOOL RESULT - DIRB: completed - Found 0 directories
2025-06-26 18:49:23,658 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 53% - Phase 2: Running gobuster scan...
2025-06-26 18:49:23,665 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 🔧 TOOL START - GOBUSTER - Command: gobuster scan on ***********
2025-06-26 18:49:23,673 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - GOBUSTER: 10% - Starting GoBuster scan...
2025-06-26 18:49:23,704 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - GOBUSTER: 25% - Executing GoBuster command...
2025-06-26 18:49:23,707 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Command: gobuster dir -u *********** -w /usr/share/dirb/wordlists/common.txt -t 5 -q --no-error --timeout 5s --delay 100ms
2025-06-26 18:49:23,712 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - GOBUSTER: 25% - Brute forcing directories... (0s elapsed)
2025-06-26 18:49:28,717 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): GoBuster completed with no results: Error: the server returns a status code that matches the provided options for non existing urls. http://***********/4975d2c9-f230-440d-9d36-804d5d0a1525 => 200 (Length: 151). To continue please exclude the status code or the length

2025-06-26 18:49:28,721 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - ✅ TOOL RESULT - GOBUSTER: completed
2025-06-26 18:49:28,727 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 59% - Phase 2: Running zap scan...
2025-06-26 18:49:28,731 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 🔧 TOOL START - ZAP - Command: zap scan on ***********
2025-06-26 18:49:28,735 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - ZAP: 10% - Starting security scan...
2025-06-26 18:49:28,737 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - ZAP: 25% - Performing HTTP security checks...
2025-06-26 18:49:28,742 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): HTTP request failed: Invalid URL '***********': No scheme supplied. Perhaps you meant https://***********?
2025-06-26 18:49:28,746 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - ZAP: 100% - Security scan completed
2025-06-26 18:49:28,751 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Found 1 security alerts
2025-06-26 18:49:28,759 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - ✅ TOOL RESULT - ZAP: completed
2025-06-26 18:49:28,765 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - ✅ TOOL RESULT - DEEP_SCAN: phase_2_complete
2025-06-26 18:49:28,767 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 🔧 TOOL START - DEEP_SCAN - Command: 📊 Phase 3: Results Analysis and Consolidation
2025-06-26 18:49:28,770 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - Phase 3: Consolidating port scan results...
2025-06-26 18:49:28,775 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 78% - Found 5 ports from nmap
2025-06-26 18:49:28,780 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 82% - Phase 3: Consolidating vulnerability results...
2025-06-26 18:49:28,783 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 84% - Added 3 vulnerabilities from openvas
2025-06-26 18:49:28,786 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 86% - Added 5 vulnerabilities from nikto
2025-06-26 18:49:28,791 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 86% - Added 1 vulnerabilities from zap
2025-06-26 18:49:28,797 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 88% - Phase 3: Calculating final summary...
2025-06-26 18:49:28,800 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 92% - Summary: 5 ports, 9 vulnerabilities
2025-06-26 18:49:28,803 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 96% - Saving vulnerabilities to database...
2025-06-26 18:49:28,819 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 98% - Saved 8 vulnerabilities to database
2025-06-26 18:49:28,821 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 99% - Finalizing scan results...
2025-06-26 18:49:28,830 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - ✅ TOOL RESULT - DEEP_SCAN: completed
2025-06-26 18:49:28,834 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'network': {'nmap': {'status': 'completed', 'scan_id': '154224dd-5b43-4814-992a-6bd67369ae2e', 'target': '***********', 'start_time': '2025-06-26T16:46:21.024097', 'end_time': '2025-06-26T16:47:08.895788', 'scan_time': 47.871691, 'command': '/usr/bin/nmap -sT -sV --version-intensity 5 -p 1-1000 -oX /tmp/nmap_scan_154224dd-5b43-4814-992a-6bd67369ae2e.xml ***********', 'ports': [{'port': 21, 'protocol': 'tcp', 'state': 'open', 'service': 'ftp'}, {'port': 23, 'protocol': 'tcp', 'state': 'open', 'service': 'tcpwrapped'}, {'port': 53, 'protocol': 'tcp', 'state': 'open', 'service': 'domain', 'version': '(generic dns response: SERVFAIL)'}, {'port': 80, 'protocol': 'tcp', 'state': 'open', 'service': 'http?'}, {'port': 443, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/https?'}], 'vulnerabilities': [], 'raw_output': 'Starting Nmap 7.94SVN ( https://nmap.org ) at 2025-06-26 18:46 CEST\nNmap scan report for _gateway (***********)\nHost is up (0.0054s latency).\nNot shown: 995 closed tcp ports (conn-refused)\nPORT    STATE SERVICE    VERSION\n21/tcp  open  ftp\n23/tcp  open  tcpwrapped\n53/tcp  open  domain     (generic dns response: SERVFAIL)\n80/tcp  open  http?\n443/tcp open  ssl/https?\n1 service unrecognized despite returning data. If you know the service/version, please submit the following fingerprint at https://nmap.org/cgi-bin/submit.cgi?new-service :\nSF-Port80-TCP:V=7.94SVN%I=5%D=6/26%Time=685D7964%P=x86_64-pc-linux-gnu%r(G\nSF:etRequest,110,"HTTP/1\\.1\\x20200\\x20OK\\r\\nServer:\\x20RTK\\x20Web\\x200\\.9\\\nSF:r\\nSet-Cookie:\\x20SessionID=;\\x20path=/\\r\\nContent-Type:\\x20text/html\\r\nSF:\\nContent-Length:\\x20151\\x20\\x20\\x20\\r\\n\\r\\n<html><head><meta\\x20HTTP-E\nSF:QUIV=\\"Pragma\\"\\x20CONTENT=\\"no-cache\\"><script\\x20language=\'javascript\nSF:\'>parent\\.location=\\"/login\\.htm\\"</script></head><body></body></html>"\nSF:)%r(HTTPOptions,ED,"HTTP/1\\.1\\x20400\\x20Bad\\x20Request\\r\\nConnection:\\x\nSF:20close\\r\\nServer:\\x20RTK\\x20Web\\x200\\.9\\r\\nX-Frame-Options:\\x20SAMEORI\nSF:GIN\\r\\n\\r\\n<HTML><HEAD><TITLE>400\\x20Bad\\x20Request\\r\\n</TITLE></HEAD>\\\nSF:r\\n<BODY><H1>Error:\\x20400\\x20Bad\\x20Request\\r\\n</H1>\\x20<P>The\\x20requ\nSF:ested\\x20Url\\x20is\\x20\\"\\"\\.</P>\\x20</BODY></HTML>\\n")%r(RTSPRequest,ED\nSF:,"HTTP/1\\.1\\x20400\\x20Bad\\x20Request\\r\\nConnection:\\x20close\\r\\nServer:\nSF:\\x20RTK\\x20Web\\x200\\.9\\r\\nX-Frame-Options:\\x20SAMEORIGIN\\r\\n\\r\\n<HTML><\nSF:HEAD><TITLE>400\\x20Bad\\x20Request\\r\\n</TITLE></HEAD>\\r\\n<BODY><H1>Error\nSF::\\x20400\\x20Bad\\x20Request\\r\\n</H1>\\x20<P>The\\x20requested\\x20Url\\x20is\nSF:\\x20\\"\\"\\.</P>\\x20</BODY></HTML>\\n")%r(FourOhFourRequest,110,"HTTP/1\\.1\nSF:\\x20200\\x20OK\\r\\nServer:\\x20RTK\\x20Web\\x200\\.9\\r\\nSet-Cookie:\\x20Sessio\nSF:nID=;\\x20path=/\\r\\nContent-Type:\\x20text/html\\r\\nContent-Length:\\x20151\nSF:\\x20\\x20\\x20\\r\\n\\r\\n<html><head><meta\\x20HTTP-EQUIV=\\"Pragma\\"\\x20CONTE\nSF:NT=\\"no-cache\\"><script\\x20language=\'javascript\'>parent\\.location=\\"/lo\nSF:gin\\.htm\\"</script></head><body></body></html>")%r(GenericLines,ED,"HTT\nSF:P/1\\.1\\x20400\\x20Bad\\x20Request\\r\\nConnection:\\x20close\\r\\nServer:\\x20R\nSF:TK\\x20Web\\x200\\.9\\r\\nX-Frame-Options:\\x20SAMEORIGIN\\r\\n\\r\\n<HTML><HEAD>\nSF:<TITLE>400\\x20Bad\\x20Request\\r\\n</TITLE></HEAD>\\r\\n<BODY><H1>Error:\\x20\nSF:400\\x20Bad\\x20Request\\r\\n</H1>\\x20<P>The\\x20requested\\x20Url\\x20is\\x20\\\nSF:"\\"\\.</P>\\x20</BODY></HTML>\\n")%r(SIPOptions,110,"HTTP/1\\.1\\x20200\\x20O\nSF:K\\r\\nServer:\\x20RTK\\x20Web\\x200\\.9\\r\\nSet-Cookie:\\x20SessionID=;\\x20pat\nSF:h=/\\r\\nContent-Type:\\x20text/html\\r\\nContent-Length:\\x20151\\x20\\x20\\x20\nSF:\\r\\n\\r\\n<html><head><meta\\x20HTTP-EQUIV=\\"Pragma\\"\\x20CONTENT=\\"no-cach\nSF:e\\"><script\\x20language=\'javascript\'>parent\\.location=\\"/login\\.htm\\"</\nSF:script></head><body></body></html>");\n\nService detection performed. Please report any incorrect results at https://nmap.org/submit/ .\nNmap done: 1 IP address (1 host up) scanned in 47.85 seconds\n'}, 'openvas': {'status': 'completed', 'scan_id': '474db1da-fe26-4cf1-b0d0-f107ba00feb0', 'task_id': 'greenbone_task_474db1da-fe26-4cf1-b0d0-f107ba00feb0', 'target_id': 'greenbone_target_474db1da-fe26-4cf1-b0d0-f107ba00feb0', 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'Telnet Service Detected', 'severity': 'high', 'description': 'Telnet service - unencrypted protocol', 'port': 23, 'solution': 'Review Telnet service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}], 'message': 'Greenbone scan completed for ***********', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/portscan/tcp', 'auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 4, 'raw_output': "=== TCP Port Scanner ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0mPORTS => 1-1000\n\x1b[0m\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:21 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:23 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:53 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:80 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:443 - TCP OPEN\n\x1b[1m\x1b[34m[*]\x1b[0m ***********           - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ***********           - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m Error: ***********: Errno::ECONNREFUSED Connection refused - connect(2) for ***********:22\n\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m ***********:445       - Rex::ConnectionRefused: The connection was refused by the remote host (***********:445).\n\x1b[1m\x1b[34m[*]\x1b[0m ***********:445       - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'web': {'nikto': {'status': 'completed', 'vulnerabilities': [{'type': 'Cookie Security Issue', 'severity': 'medium', 'description': 'GET /: Cookie SessionID created without the httponly flag', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Missing X-Frame-Options Header', 'severity': 'medium', 'description': 'GET /: The anti-clickjacking X-Frame-Options header is not present.', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Security Finding', 'severity': 'medium', 'description': 'GET /clientaccesspolicy.xml: lines', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Security Finding', 'severity': 'medium', 'description': 'GET /crossdomain.xml: /crossdomain.xml contains 0 line which should be manually viewed for improper domains or wildcards.', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Security Finding', 'severity': 'medium', 'description': 'GET /robots.txt: "robots.txt" retrieved but it does not contain any \'disallow\' entries (which is odd).', 'source_tool': 'nikto', 'category': 'web'}], 'scan_time': '10 seconds', 'total_tests': 10, 'raw_output': '- Nikto v2.1.5/2.1.5\n+ Target Host: _gateway\n+ Target Port: 80\n+ GET /: Cookie SessionID created without the httponly flag\n+ GET /: The anti-clickjacking X-Frame-Options header is not present.\n+ GET /clientaccesspolicy.xml: lines\n+ GET /crossdomain.xml: /crossdomain.xml contains 0 line which should be manually viewed for improper domains or wildcards.\n+ GET /robots.txt: "robots.txt" retrieved but it does not contain any \'disallow\' entries (which is odd).\n+ DEBUG HASH(0x602b09864938): DEBUG HTTP verb may show server debugging information. See http://msdn.microsoft.com/en-us/library/e8z01xdh%28VS.80%29.aspx for details.\n'}, 'sqlmap': {'status': 'completed', 'injections': [], 'scan_time': '10 seconds', 'raw_output': '        ___\n       __H__\n ___ ___["]_____ ___ ___  {1.8.4#stable}\n|_ -| . [(]     | .\'| . |\n|___|_  [.]_|_|_|__,|  _|\n      |_|V...       |_|   https://sqlmap.org\n\n[!] legal disclaimer: Usage of sqlmap for attacking targets without prior mutual consent is illegal. It is the end user\'s responsibility to obey all applicable local, state and federal laws. Developers assume no liability and are not responsible for any misuse or damage caused by this program\n\n[*] starting @ 18:49:09 /2025-06-26/\n\ndo you want to check for the existence of site\'s sitemap(.xml) [y/N] N\n\n[18:49:09] [INFO] starting crawler for target URL \'http://***********\'\n\n[18:49:09] [INFO] searching for links with depth 1\n\n[18:49:09] [WARNING] no usable links found (with GET parameters)\n\n[18:49:09] [WARNING] your sqlmap version is outdated\n\n[*] ending @ 18:49:09 /2025-06-26/\n\n'}, 'dirb': {'status': 'completed', 'directories': [], 'scan_time': '5 seconds', 'raw_output': '\n-----------------\nDIRB v2.22    \nBy The Dark Raver\n-----------------\n\n\n(!) FATAL: Invalid URL format: ***********/\n    (Use: "http://host/" or "https://host/" for SSL)\n'}, 'gobuster': {'status': 'completed', 'found_paths': [], 'scan_time': '5 seconds', 'raw_output': ''}, 'zap': {'status': 'completed', 'alerts': [{'risk': 'Medium', 'name': 'Connection Issue', 'count': 1, 'description': "Could not analyze target: Invalid URL '***********': No scheme supplied. Perhaps you meant https://***********?"}], 'scan_time': '0 seconds', 'raw_output': 'Basic security scan completed. Found 1 potential issues.'}}, 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'Telnet Service Detected', 'severity': 'high', 'description': 'Telnet service - unencrypted protocol', 'port': 23, 'solution': 'Review Telnet service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'type': 'Cookie Security Issue', 'severity': 'medium', 'description': 'GET /: Cookie SessionID created without the httponly flag', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Missing X-Frame-Options Header', 'severity': 'medium', 'description': 'GET /: The anti-clickjacking X-Frame-Options header is not present.', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Security Finding', 'severity': 'medium', 'description': 'GET /clientaccesspolicy.xml: lines', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Security Finding', 'severity': 'medium', 'description': 'GET /crossdomain.xml: /crossdomain.xml contains 0 line which should be manually viewed for improper domains or wildcards.', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Security Finding', 'severity': 'medium', 'description': 'GET /robots.txt: "robots.txt" retrieved but it does not contain any \'disallow\' entries (which is odd).', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Connection Issue', 'severity': 'medium', 'description': "Could not analyze target: Invalid URL '***********': No scheme supplied. Perhaps you meant https://***********?", 'source_tool': 'zap', 'category': 'web'}], 'ports': [{'port': 21, 'protocol': 'tcp', 'state': 'open', 'service': 'ftp'}, {'port': 23, 'protocol': 'tcp', 'state': 'open', 'service': 'tcpwrapped'}, {'port': 53, 'protocol': 'tcp', 'state': 'open', 'service': 'domain', 'version': '(generic dns response: SERVFAIL)'}, {'port': 80, 'protocol': 'tcp', 'state': 'open', 'service': 'http?'}, {'port': 443, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/https?'}], 'summary': {'total_ports': 5, 'open_ports': 5, 'total_vulnerabilities': 9, 'high_severity': 1, 'medium_severity': 8, 'low_severity': 0, 'scan_phases': 3, 'tools_executed': 8}}
2025-06-26 18:49:28,840 - scan_48e6002b-c756-409a-8104-6bc1f08f237e - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 187.8s
