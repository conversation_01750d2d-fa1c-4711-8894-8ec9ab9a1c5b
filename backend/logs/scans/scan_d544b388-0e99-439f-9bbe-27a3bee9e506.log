2025-06-25 22:03:26,285 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: *************, Tools: nmap, openvas, metasploit
2025-06-25 22:03:26,289 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: *************, Tools: nmap, openvas, metasploit
2025-06-25 22:03:26,296 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on *************
2025-06-25 22:03:26,296 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on *************
2025-06-25 22:03:26,298 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on *************
2025-06-25 22:03:26,307 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan...
2025-06-25 22:03:26,312 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-25 22:03:26,314 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-25 22:03:26,317 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - ERROR - ❌ TOOL ERROR - OPENVAS: cannot import name 'OpenVASService' from 'app.services.openvas_service' (/home/<USER>/PICA_V1.0.01/backend/app/services/openvas_service.py)
2025-06-25 22:03:26,319 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-25 22:03:26,325 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-25 22:03:26,327 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - INFO - ✅ TOOL RESULT - OPENVAS: completed
2025-06-25 22:03:26,354 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-06-25 22:04:05,345 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-06-25 22:04:05,348 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Scan completed: /usr/bin/nmap -sT -sV --version-intensity 5 -p 1-1000 -oX /tmp/nmap_scan_bf7bd6b0-fb9e-49dc-af79-7b6a98699803.xml *************
2025-06-25 22:04:05,350 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Found 3 ports
2025-06-25 22:04:05,353 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - INFO - ✅ TOOL RESULT - NMAP: completed - Found 3 ports - Found 0 vulnerabilities
2025-06-25 22:04:17,183 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-25 22:04:17,196 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-25 22:04:51,180 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-25 22:04:51,185 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-25 22:05:16,922 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-25 22:05:16,927 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-25 22:05:25,060 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-25 22:05:25,063 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 4 modules - Found 0 potential vulnerabilities
2025-06-25 22:05:25,069 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-25 22:05:25,074 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'tools': {'openvas': {'status': 'failed', 'error': "cannot import name 'OpenVASService' from 'app.services.openvas_service' (/home/<USER>/PICA_V1.0.01/backend/app/services/openvas_service.py)"}, 'nmap': {'status': 'completed', 'scan_id': 'bf7bd6b0-fb9e-49dc-af79-7b6a98699803', 'target': '*************', 'start_time': '2025-06-25T20:03:26.361510', 'end_time': '2025-06-25T20:04:05.345604', 'scan_time': 38.984094, 'command': '/usr/bin/nmap -sT -sV --version-intensity 5 -p 1-1000 -oX /tmp/nmap_scan_bf7bd6b0-fb9e-49dc-af79-7b6a98699803.xml *************', 'ports': [{'port': 22, 'protocol': 'tcp', 'state': 'open', 'service': 'ssh', 'version': 'OpenSSH 9.6p1 Ubuntu 3ubuntu13.11 (Ubuntu Linux; protocol 2.0)'}, {'port': 80, 'protocol': 'tcp', 'state': 'open', 'service': 'http', 'version': 'Apache httpd 2.4.58 ((Ubuntu))'}, {'port': 443, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/http', 'version': 'Apache httpd 2.4.58 ((Ubuntu))'}], 'vulnerabilities': [], 'raw_output': 'Starting Nmap 7.94SVN ( https://nmap.org ) at 2025-06-25 22:03 CEST\nNmap scan report for vps-3811c29b.vps.ovh.ca (*************)\nHost is up (0.22s latency).\nNot shown: 997 closed tcp ports (conn-refused)\nPORT    STATE SERVICE  VERSION\n22/tcp  open  ssh      OpenSSH 9.6p1 Ubuntu 3ubuntu13.11 (Ubuntu Linux; protocol 2.0)\n80/tcp  open  http     Apache httpd 2.4.58 ((Ubuntu))\n443/tcp open  ssl/http Apache httpd 2.4.58 ((Ubuntu))\nService Info: OS: Linux; CPE: cpe:/o:linux:linux_kernel\n\nService detection performed. Please report any incorrect results at https://nmap.org/submit/ .\nNmap done: 1 IP address (1 host up) scanned in 38.95 seconds\n'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/portscan/tcp', 'auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 4, 'raw_output': "=== TCP Port Scanner ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0mPORTS => 1-1000\n\x1b[0m\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:22 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:80 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:443 - TCP OPEN\n\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ************* - Key Fingerprint: ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIJU7bXzWjVFCRr70wCRrw80liBHhsKANhaQHwAVEuPka\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - SSH server version: SSH-2.0-OpenSSH_9.6p1 Ubuntu-3ubuntu13.11\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - Server Information and Encryption\n=================================\n\n  Type                     Value                                 Note\n  ----                     -----                                 ----\n  encryption.compression   none\n  encryption.compression   <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.encryption    aes128-ctr\n  encryption.encryption    aes192-ctr\n  encryption.encryption    aes256-ctr\n  encryption.encryption    <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          hmac-sha2-256\n  encryption.hmac          hmac-sha2-512\n  encryption.hmac          hmac-sha1\n  encryption.host_key      rsa-sha2-512\n  encryption.host_key      rsa-sha2-256\n  encryption.host_key      ecdsa-sha2-nistp256                   Weak elliptic curve\n  encryption.host_key      ssh-ed25519\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  curve25519-sha256\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  ecdh-sha2-nistp256\n  encryption.key_exchange  ecdh-sha2-nistp384\n  encryption.key_exchange  ecdh-sha2-nistp521\n  encryption.key_exchange  diffie-hellman-group-exchange-sha256\n  encryption.key_exchange  diffie-hellman-group16-sha512\n  encryption.key_exchange  diffie-hellman-group18-sha512\n  encryption.key_exchange  diffie-hellman-group14-sha256\n  encryption.key_exchange  ext-info-s\n  encryption.key_exchange  <EMAIL>\n  fingerprint_db           ssh.banner\n  openssh.comment          Ubuntu-3ubuntu13.11\n  os.certainty             0.75\n  os.cpe23                 cpe:/o:canonical:ubuntu_linux:-\n  os.family                Linux\n  os.product               Linux\n  os.vendor                Ubuntu\n  service.cpe23            cpe:/a:openbsd:openssh:9.6p1\n  service.family           OpenSSH\n  service.product          OpenSSH\n  service.protocol         ssh\n  service.vendor           OpenBSD\n  service.version          9.6p1\n\n\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\nAborting...\nThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'ports': [{'port': 22, 'protocol': 'tcp', 'state': 'open', 'service': 'ssh', 'version': 'OpenSSH 9.6p1 Ubuntu 3ubuntu13.11 (Ubuntu Linux; protocol 2.0)'}, {'port': 80, 'protocol': 'tcp', 'state': 'open', 'service': 'http', 'version': 'Apache httpd 2.4.58 ((Ubuntu))'}, {'port': 443, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/http', 'version': 'Apache httpd 2.4.58 ((Ubuntu))'}], 'vulnerabilities': [], 'summary': {'total_ports': 3, 'open_ports': 3, 'total_vulnerabilities': 0}}
2025-06-25 22:05:25,078 - scan_d544b388-0e99-439f-9bbe-27a3bee9e506 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 118.8s
