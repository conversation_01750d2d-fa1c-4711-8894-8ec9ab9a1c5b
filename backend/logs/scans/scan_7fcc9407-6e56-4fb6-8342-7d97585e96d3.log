2025-06-30 20:31:09,948 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 🚀 SCAN STARTED - Category: deep, Type: comprehensive, Target: ************, Tools: nmap, openvas, metasploit, nikto, sqlmap, dirb, gobuster, zap
2025-06-30 20:31:09,952 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 🚀 SCAN STARTED - Category: deep, Type: comprehensive, Target: ************, Tools: nmap, openvas, metasploit, nikto, sqlmap, dirb, gobuster, zap
2025-06-30 20:31:09,953 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 🔧 TOOL START - DEEP_SCAN - Command: 🚀 Starting Deep Scan - All Tools Running in Parallel
2025-06-30 20:31:09,961 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Launching all tools in parallel...
2025-06-30 20:31:09,963 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started nmap in background
2025-06-30 20:31:09,963 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on ************
2025-06-30 20:31:09,964 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started openvas in background
2025-06-30 20:31:09,965 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on ************
2025-06-30 20:31:09,966 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 20:31:09,967 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 20:31:09,968 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started metasploit in background
2025-06-30 20:31:09,969 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on ************
2025-06-30 20:31:09,970 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 20:31:09,973 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap port scan...
2025-06-30 20:31:09,973 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started nikto in background
2025-06-30 20:31:09,974 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 20:31:09,974 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 🔧 TOOL START - NIKTO - Command: nikto scan on ************
2025-06-30 20:31:09,976 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 20:31:09,978 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started sqlmap in background
2025-06-30 20:31:09,979 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 🔧 TOOL START - SQLMAP - Command: sqlmap scan on ************
2025-06-30 20:31:09,979 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS vulnerability scan...
2025-06-30 20:31:09,980 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 20:31:09,980 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started dirb in background
2025-06-30 20:31:09,981 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 20:31:09,981 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 🔧 TOOL START - DIRB - Command: dirb scan on ************
2025-06-30 20:31:09,984 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 20:31:09,984 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit exploitation scan...
2025-06-30 20:31:09,986 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started gobuster in background
2025-06-30 20:31:09,986 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 🔧 TOOL START - GOBUSTER - Command: gobuster scan on ************
2025-06-30 20:31:09,988 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 20:31:09,989 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 20:31:09,990 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 20:31:09,993 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Formatted target for web tool: ************ -> http://************
2025-06-30 20:31:09,993 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started zap in background
2025-06-30 20:31:09,994 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 🔧 TOOL START - ZAP - Command: zap scan on ************
2025-06-30 20:31:09,994 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 20:31:09,996 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Formatted target for web tool: ************ -> http://************
2025-06-30 20:31:09,997 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 20:31:09,998 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 10% - Starting SQLMap scan (maximum intensity)...
2025-06-30 20:31:09,998 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 15% - All 8 tools are now running in parallel
2025-06-30 20:31:09,999 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 20:31:10,000 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 20:31:10,002 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Starting Nikto web vulnerability scan...
2025-06-30 20:31:10,003 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Formatted target for web tool: ************ -> http://************
2025-06-30 20:31:10,005 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Formatted target for web tool: ************ -> http://************
2025-06-30 20:31:10,006 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Command: sqlmap -u http://************ --batch --level=5 --risk=3 --threads=5 --tamper=space2comment
2025-06-30 20:31:10,006 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DIRB: 10% - Starting Dirb directory scan (maximum intensity)...
2025-06-30 20:31:10,006 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 20:31:10,008 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 10% - Starting GoBuster directory scan (maximum intensity)...
2025-06-30 20:31:10,008 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Formatted target for web tool: ************ -> http://************
2025-06-30 20:31:10,010 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Command: dirb http://************ /usr/share/dirb/wordlists/big.txt -w
2025-06-30 20:31:10,011 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 25% - Testing for SQL injection... (0s elapsed)
2025-06-30 20:31:10,011 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - ZAP: 10% - Starting ZAP security scan...
2025-06-30 20:31:10,012 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Command: gobuster dir -u http://************ -w /usr/share/wordlists/dirb/big.txt -t 50 -q
2025-06-30 20:31:10,015 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DIRB: 25% - Scanning directories... (0s elapsed)
2025-06-30 20:31:10,016 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 25% - Brute forcing directories... (0s elapsed)
2025-06-30 20:31:11,978 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Scanning ports... 10% complete
2025-06-30 20:31:12,005 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 15% - 0/8 tools completed
2025-06-30 20:31:12,991 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Metasploit scanning... 10% complete
2025-06-30 20:31:13,006 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Scanning web vulnerabilities... 10% complete
2025-06-30 20:31:13,983 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NMAP: 20% - Scanning ports... 20% complete
2025-06-30 20:31:14,009 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 15% - 0/8 tools completed
2025-06-30 20:31:14,013 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - ZAP: 10% - ZAP security scanning... 10% complete
2025-06-30 20:31:14,987 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - OpenVAS vulnerability scanning... 10% complete
2025-06-30 20:31:15,016 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 90% - Parsing results...
2025-06-30 20:31:15,019 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DIRB: 90% - Parsing results...
2025-06-30 20:31:15,020 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 90% - Parsing results...
2025-06-30 20:31:15,027 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - ✅ TOOL RESULT - SQLMAP: completed - Found 0 vulnerabilities
2025-06-30 20:31:15,029 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - ✅ TOOL RESULT - GOBUSTER: completed - Found 0 directories
2025-06-30 20:31:15,031 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - ✅ TOOL RESULT - DIRB: completed - Found 0 directories
2025-06-30 20:31:15,986 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Scanning ports... 30% complete
2025-06-30 20:31:15,998 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 25% - Metasploit scanning... 25% complete
2025-06-30 20:31:16,012 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Scanning web vulnerabilities... 25% complete
2025-06-30 20:31:16,014 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 20:31:17,994 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NMAP: 40% - Scanning ports... 40% complete
2025-06-30 20:31:18,016 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - ZAP: 30% - ZAP security scanning... 30% complete
2025-06-30 20:31:18,022 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 20:31:19,006 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 40% - Metasploit scanning... 40% complete
2025-06-30 20:31:19,020 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NIKTO: 40% - Scanning web vulnerabilities... 40% complete
2025-06-30 20:31:19,994 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 20% - OpenVAS vulnerability scanning... 20% complete
2025-06-30 20:31:20,001 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NMAP: 50% - Scanning ports... 50% complete
2025-06-30 20:31:20,029 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 20:31:22,007 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NMAP: 60% - Scanning ports... 60% complete
2025-06-30 20:31:22,009 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 55% - Metasploit scanning... 55% complete
2025-06-30 20:31:22,023 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NIKTO: 55% - Scanning web vulnerabilities... 55% complete
2025-06-30 20:31:22,026 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - ZAP: 50% - ZAP security scanning... 50% complete
2025-06-30 20:31:22,035 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 20:31:24,011 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NMAP: 70% - Scanning ports... 70% complete
2025-06-30 20:31:24,039 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 20:31:25,003 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS vulnerability scanning... 30% complete
2025-06-30 20:31:25,012 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 70% - Metasploit scanning... 70% complete
2025-06-30 20:31:25,028 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NIKTO: 70% - Scanning web vulnerabilities... 70% complete
2025-06-30 20:31:26,014 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NMAP: 80% - Scanning ports... 80% complete
2025-06-30 20:31:26,019 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan (maximum intensity)...
2025-06-30 20:31:26,029 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - ZAP: 70% - ZAP security scanning... 70% complete
2025-06-30 20:31:26,035 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - ZAP: 10% - Starting security scan...
2025-06-30 20:31:26,039 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - ZAP: 25% - Performing HTTP security checks...
2025-06-30 20:31:26,043 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 20:31:26,049 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-06-30 20:31:26,053 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using scan config: Complete audit with all available tools and options
2025-06-30 20:31:26,056 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Nmap options: -T4 -A --script=vuln,safe,discovery
2025-06-30 20:31:26,061 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Port range: 1-65535
2025-06-30 20:31:28,018 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 85% - Metasploit scanning... 85% complete
2025-06-30 20:31:28,023 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-30 20:31:28,028 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-30 20:31:28,032 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-30 20:31:28,035 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NIKTO: 85% - Scanning web vulnerabilities... 85% complete
2025-06-30 20:31:28,041 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Starting Nikto scan (maximum intensity)...
2025-06-30 20:31:28,045 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Executing Nikto command...
2025-06-30 20:31:28,050 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Command: nikto -h http://************ -Format txt -output /tmp/nikto_1751308288.txt -maxtime 2400 -Tuning x
2025-06-30 20:31:28,053 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 20:31:28,057 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Scanning... (0s elapsed)
2025-06-30 20:31:30,009 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 40% - OpenVAS vulnerability scanning... 40% complete
2025-06-30 20:31:30,060 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 20:31:32,063 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 20:31:34,066 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 20:31:35,012 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 50% - OpenVAS vulnerability scanning... 50% complete
2025-06-30 20:31:36,051 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): HTTP request failed: HTTPConnectionPool(host='************', port=80): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x7aab73647320>, 'Connection to ************ timed out. (connect timeout=10)'))
2025-06-30 20:31:36,053 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - ZAP: 100% - Security scan completed
2025-06-30 20:31:36,055 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Found 1 security alerts
2025-06-30 20:31:36,058 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - ✅ TOOL RESULT - ZAP: completed
2025-06-30 20:31:36,068 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 20:31:38,062 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NIKTO: 28% - Scanning... (10s elapsed)
2025-06-30 20:31:38,071 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 20:31:40,016 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 60% - OpenVAS vulnerability scanning... 60% complete
2025-06-30 20:31:40,075 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 20:31:42,078 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 20:31:44,081 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 20:31:45,018 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS vulnerability scanning... 70% complete
2025-06-30 20:31:46,085 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 20:31:47,373 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-30 20:31:47,375 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-30 20:31:48,065 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NIKTO: 90% - Parsing results...
2025-06-30 20:31:48,067 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NIKTO: 100% - Nikto scan completed
2025-06-30 20:31:48,069 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Scan completed - Found 0 potential issues
2025-06-30 20:31:48,072 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - ✅ TOOL RESULT - NIKTO: completed - Found 0 vulnerabilities
2025-06-30 20:31:48,091 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 20:31:50,026 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 80% - OpenVAS vulnerability scanning... 80% complete
2025-06-30 20:31:50,028 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-30 20:31:50,051 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-30 20:31:50,095 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 20:31:52,097 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 20:31:54,100 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 20:31:56,103 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 20:31:58,107 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 20:32:00,110 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 20:32:02,113 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 20:32:04,120 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 20:32:06,132 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 20:32:08,139 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 20:32:08,365 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-06-30 20:32:08,368 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Scan completed: /usr/bin/nmap -T4 -A --script=vuln,safe,discovery -p 1-65535 -oX /tmp/nmap_scan_4346f81f-2267-4175-acc7-b759454134ae.xml ************
2025-06-30 20:32:08,371 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Found 0 ports
2025-06-30 20:32:08,377 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - ✅ TOOL RESULT - NMAP: completed - Found 0 ports - Found 2 vulnerabilities
2025-06-30 20:32:10,147 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:32:12,154 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:32:13,033 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-30 20:32:13,035 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-30 20:32:13,112 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-30 20:32:13,117 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_870f2fb3-67f6-4261-92f6-11f87fcd5c69
2025-06-30 20:32:13,128 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 0 vulnerabilities
2025-06-30 20:32:14,160 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:32:16,163 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:32:18,166 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:32:20,169 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:32:22,173 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:32:24,177 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:32:26,181 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:32:28,185 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:32:30,189 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:32:32,196 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:32:34,204 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:32:36,212 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:32:38,220 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:32:40,227 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:32:42,234 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:32:44,242 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:32:46,251 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:32:48,257 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:32:50,262 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:32:52,268 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:32:54,274 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:32:56,278 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:32:58,281 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:33:00,053 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-30 20:33:00,056 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-30 20:33:00,285 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:33:02,288 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:33:04,291 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:33:06,294 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:33:08,297 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:33:10,299 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:33:12,303 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:33:14,305 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:33:16,308 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:33:18,313 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:33:20,320 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:33:22,327 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:33:24,335 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:33:26,340 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:33:26,798 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-30 20:33:26,801 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 4 modules - Found 0 potential vulnerabilities
2025-06-30 20:33:26,804 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-30 20:33:28,347 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - ✅ TOOL RESULT - DEEP_SCAN: all_tools_complete
2025-06-30 20:33:28,352 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 🔧 TOOL START - DEEP_SCAN - Command: 📊 Results Analysis and Consolidation
2025-06-30 20:33:28,358 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 96% - Consolidating port scan results...
2025-06-30 20:33:28,362 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 96% - Found 0 ports from nmap
2025-06-30 20:33:28,365 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 97% - Found 0 ports from metasploit
2025-06-30 20:33:28,370 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 97% - Consolidating vulnerability results...
2025-06-30 20:33:28,375 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 84% - Added 2 vulnerabilities from nmap
2025-06-30 20:33:28,380 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 98% - Added 1 vulnerabilities from zap
2025-06-30 20:33:28,385 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 98% - Calculating final summary...
2025-06-30 20:33:28,389 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 99% - Summary: 0 ports, 3 vulnerabilities
2025-06-30 20:33:28,396 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 99% - Saving vulnerabilities to database...
2025-06-30 20:33:28,410 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 98% - Saved 2 vulnerabilities to database
2025-06-30 20:33:28,414 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 99% - Creating frontend-compatible results...
2025-06-30 20:33:28,417 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 99% - Finalizing scan results...
2025-06-30 20:33:28,431 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - ✅ TOOL RESULT - DEEP_SCAN: completed
2025-06-30 20:33:28,437 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'network': {'nmap': {'status': 'completed', 'scan_id': '4346f81f-2267-4175-acc7-b759454134ae', 'target': '************', 'start_time': '2025-06-30T18:31:26.065243', 'end_time': '2025-06-30T18:32:08.365279', 'scan_time': 42.300036, 'command': '/usr/bin/nmap -T4 -A --script=vuln,safe,discovery -p 1-65535 -oX /tmp/nmap_scan_4346f81f-2267-4175-acc7-b759454134ae.xml ************', 'ports': [], 'vulnerabilities': [{'name': '|   After NULL UDP avahi packet DoS (CVE-2011-1002).', 'severity': 'medium', 'description': '|   After NULL UDP avahi packet DoS (CVE-2011-1002).', 'tool': 'nmap', 'cve': 'CVE-2011-1002', 'id': 'CVE-2011-1002', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|_  Hosts are all up (not vulnerable).', 'severity': 'medium', 'description': '|_  Hosts are all up (not vulnerable).', 'tool': 'nmap', 'cve': None, 'source_tool': 'nmap', 'category': 'network'}], 'raw_output': "Starting Nmap 7.94SVN ( https://nmap.org ) at 2025-06-30 20:31 CEST\nPre-scan script results:\n|_hostmap-robtex: *TEMPORARILY DISABLED* due to changes in Robtex's API. See https://www.robtex.com/api/\n| targets-asn: \n|_  targets-asn.asn is a mandatory parameter\n| broadcast-dns-service-discovery: \n|   ***********\n|     80/tcp http\n|       path=/phpmyadmin/\n|       Address=************ fe80::a07a:1b52:f619:5817\n|     445/tcp smb\n|       Address=************ fe80::a07a:1b52:f619:5817\n|     Device Information\n|       model=MacSamba\n|_      Address=************ fe80::a07a:1b52:f619:5817\n| broadcast-avahi-dos: \n|   Discovered hosts:\n|     ***********\n|   After NULL UDP avahi packet DoS (CVE-2011-1002).\n|_  Hosts are all up (not vulnerable).\n|_http-robtex-shared-ns: *TEMPORARILY DISABLED* due to changes in Robtex's API. See https://www.robtex.com/api/\nNote: Host seems down. If it is really up, but blocking our ping probes, try -Pn\nNmap done: 1 IP address (0 hosts up) scanned in 42.27 seconds\n"}, 'openvas': {'status': 'completed', 'scan_id': '870f2fb3-67f6-4261-92f6-11f87fcd5c69', 'task_id': 'greenbone_task_870f2fb3-67f6-4261-92f6-11f87fcd5c69', 'target_id': 'greenbone_target_870f2fb3-67f6-4261-92f6-11f87fcd5c69', 'vulnerabilities': [], 'message': 'Greenbone scan completed for ************', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/portscan/tcp', 'auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 4, 'raw_output': "=== TCP Port Scanner ===\n\x1b[0m\x1b[0mRHOSTS => ************\n\x1b[0mPORTS => 1-1000\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ************          - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ************          - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => ************\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m ************:445      - Rex::ConnectionTimeout: The connection with (************:445) timed out.\n\x1b[1m\x1b[34m[*]\x1b[0m ************:445      - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'web': {'nikto': {'status': 'completed', 'vulnerabilities': [], 'scan_time': '20 seconds', 'total_tests': 2, 'raw_output': '- Nikto v2.1.5/2.1.5\n'}, 'sqlmap': {'status': 'completed', 'injections': [], 'raw_output': "        ___\n       __H__\n ___ ___[.]_____ ___ ___  {1.8.4#stable}\n|_ -| . [(]     | .'| . |\n|___|_  [(]_|_|_|__,|  _|\n      |_|V...       |_|   https://sqlmap.org\n\n[!] legal disclaimer: Usage of sqlmap for attacking targets without prior mutual consent is illegal. It is the end user's responsibility to obey all applicable local, state and federal laws. Developers assume no liability and are not responsible for any misuse or damage caused by this program\n\n[*] starting @ 20:31:10 /2025-06-30/\n\n[20:31:10] [INFO] loading tamper module 'space2comment'\n[20:31:10] [INFO] testing connection to the target URL\n[20:31:10] [CRITICAL] unable to connect to the target URL ('Connection refused'). sqlmap is going to retry the request(s)\n[20:31:10] [WARNING] if the problem persists please check that the provided target URL is reachable. In case that it is, you can try to rerun with switch '--random-agent' and/or proxy switches ('--proxy', '--proxy-file'...)\n[20:31:10] [CRITICAL] unable to connect to the target URL ('Connection refused')\n[20:31:10] [WARNING] your sqlmap version is outdated\n\n[*] ending @ 20:31:10 /2025-06-30/\n\n", 'vulnerabilities': []}, 'dirb': {'status': 'completed', 'directories': [], 'raw_output': '\n-----------------\nDIRB v2.22    \nBy The Dark Raver\n-----------------\n\nSTART_TIME: Mon Jun 30 20:31:10 2025\nURL_BASE: http://************/\nWORDLIST_FILES: /usr/share/dirb/wordlists/big.txt\nOPTION: Not Stopping on warning messages\n\n-----------------\n\n*** Generating Wordlist...\n                                                                               \nGENERATED WORDS: 20458\n\n---- Scanning URL: http://************/ ----\n*** Calculating NOT_FOUND code...\n                                                                               \n(!) FATAL: Too many errors connecting to host\n    (Possible cause: COULDNT CONNECT)\n                                                                               \n-----------------\nEND_TIME: Mon Jun 30 20:31:11 2025\nDOWNLOADED: 0 - FOUND: 0\n', 'found_paths': 0}, 'gobuster': {'status': 'completed', 'directories': [], 'raw_output': '', 'found_paths': 0}, 'zap': {'status': 'completed', 'alerts': [{'risk': 'Medium', 'name': 'Connection Issue', 'count': 1, 'description': "Could not analyze target: HTTPConnectionPool(host='************', port=80): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x7aab73647320>, 'Connection to ************ timed out. (connect timeout=10)'))"}], 'scan_time': '10 seconds', 'raw_output': 'Basic security scan completed. Found 1 potential issues.'}}, 'vulnerabilities': [{'name': '|   After NULL UDP avahi packet DoS (CVE-2011-1002).', 'severity': 'medium', 'description': '|   After NULL UDP avahi packet DoS (CVE-2011-1002).', 'tool': 'nmap', 'cve': 'CVE-2011-1002', 'id': 'CVE-2011-1002', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|_  Hosts are all up (not vulnerable).', 'severity': 'medium', 'description': '|_  Hosts are all up (not vulnerable).', 'tool': 'nmap', 'cve': None, 'source_tool': 'nmap', 'category': 'network'}, {'type': 'Connection Issue', 'severity': 'medium', 'description': "Could not analyze target: HTTPConnectionPool(host='************', port=80): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x7aab73647320>, 'Connection to ************ timed out. (connect timeout=10)'))", 'source_tool': 'zap', 'category': 'web'}], 'ports': [], 'summary': {'total_ports': 0, 'open_ports': 0, 'total_vulnerabilities': 3, 'high_severity': 0, 'medium_severity': 3, 'low_severity': 0, 'scan_phases': 1, 'tools_executed': 8}}
2025-06-30 20:33:28,444 - scan_7fcc9407-6e56-4fb6-8342-7d97585e96d3 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 138.5s
