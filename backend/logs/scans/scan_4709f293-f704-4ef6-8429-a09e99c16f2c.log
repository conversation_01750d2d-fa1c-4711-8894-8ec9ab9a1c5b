2025-06-25 22:05:31,832 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: *************, Tools: nmap, openvas, metasploit
2025-06-25 22:05:31,836 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: *************, Tools: nmap, openvas, metasploit
2025-06-25 22:05:31,840 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on *************
2025-06-25 22:05:31,842 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on *************
2025-06-25 22:05:31,843 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on *************
2025-06-25 22:05:31,848 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan...
2025-06-25 22:05:31,855 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-25 22:05:31,859 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-25 22:05:31,869 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-25 22:05:31,872 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-25 22:05:31,879 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-06-25 22:05:32,052 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-25 22:05:34,845 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-25 22:05:34,848 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_0a5ab0d2-b26b-432b-9064-40522712fa85
2025-06-25 22:05:34,851 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 1 vulnerabilities
2025-06-25 22:06:13,394 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-06-25 22:06:13,397 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Scan completed: /usr/bin/nmap -sT -sV --version-intensity 5 -p 1-1000 -oX /tmp/nmap_scan_49ea14e5-f5c3-4289-a512-db53e748b138.xml *************
2025-06-25 22:06:13,403 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Found 3 ports
2025-06-25 22:06:13,410 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - INFO - ✅ TOOL RESULT - NMAP: completed - Found 3 ports - Found 0 vulnerabilities
2025-06-25 22:06:22,014 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-25 22:06:22,017 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-25 22:06:41,963 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-25 22:06:41,966 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-25 22:07:02,869 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-25 22:07:02,872 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-25 22:07:22,538 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-25 22:07:22,541 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 4 modules - Found 0 potential vulnerabilities
2025-06-25 22:07:22,544 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-25 22:07:22,548 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'tools': {'openvas': {'status': 'completed', 'scan_id': '0a5ab0d2-b26b-432b-9064-40522712fa85', 'task_id': 'greenbone_task_0a5ab0d2-b26b-432b-9064-40522712fa85', 'target_id': 'greenbone_target_0a5ab0d2-b26b-432b-9064-40522712fa85', 'vulnerabilities': [{'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings'}], 'message': 'Greenbone scan completed for *************', 'scan_type': 'gmp_vulnerability_scan'}, 'nmap': {'status': 'completed', 'scan_id': '49ea14e5-f5c3-4289-a512-db53e748b138', 'target': '*************', 'start_time': '2025-06-25T20:05:31.882367', 'end_time': '2025-06-25T20:06:13.393847', 'scan_time': 41.51148, 'command': '/usr/bin/nmap -sT -sV --version-intensity 5 -p 1-1000 -oX /tmp/nmap_scan_49ea14e5-f5c3-4289-a512-db53e748b138.xml *************', 'ports': [{'port': 22, 'protocol': 'tcp', 'state': 'open', 'service': 'ssh', 'version': 'OpenSSH 9.6p1 Ubuntu 3ubuntu13.11 (Ubuntu Linux; protocol 2.0)'}, {'port': 80, 'protocol': 'tcp', 'state': 'open', 'service': 'http', 'version': 'Apache httpd 2.4.58 ((Ubuntu))'}, {'port': 443, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/http', 'version': 'Apache httpd 2.4.58 ((Ubuntu))'}], 'vulnerabilities': [], 'raw_output': 'Starting Nmap 7.94SVN ( https://nmap.org ) at 2025-06-25 22:05 CEST\nNmap scan report for vps-3811c29b.vps.ovh.ca (*************)\nHost is up (0.22s latency).\nNot shown: 997 closed tcp ports (conn-refused)\nPORT    STATE SERVICE  VERSION\n22/tcp  open  ssh      OpenSSH 9.6p1 Ubuntu 3ubuntu13.11 (Ubuntu Linux; protocol 2.0)\n80/tcp  open  http     Apache httpd 2.4.58 ((Ubuntu))\n443/tcp open  ssl/http Apache httpd 2.4.58 ((Ubuntu))\nService Info: OS: Linux; CPE: cpe:/o:linux:linux_kernel\n\nService detection performed. Please report any incorrect results at https://nmap.org/submit/ .\nNmap done: 1 IP address (1 host up) scanned in 41.49 seconds\n'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/portscan/tcp', 'auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 4, 'raw_output': "=== TCP Port Scanner ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0mPORTS => 1-1000\n\x1b[0m\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:22 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:80 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:443 - TCP OPEN\n\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ************* - Key Fingerprint: ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIJU7bXzWjVFCRr70wCRrw80liBHhsKANhaQHwAVEuPka\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - SSH server version: SSH-2.0-OpenSSH_9.6p1 Ubuntu-3ubuntu13.11\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - Server Information and Encryption\n=================================\n\n  Type                     Value                                 Note\n  ----                     -----                                 ----\n  encryption.compression   none\n  encryption.compression   <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.encryption    aes128-ctr\n  encryption.encryption    aes192-ctr\n  encryption.encryption    aes256-ctr\n  encryption.encryption    <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          hmac-sha2-256\n  encryption.hmac          hmac-sha2-512\n  encryption.hmac          hmac-sha1\n  encryption.host_key      rsa-sha2-512\n  encryption.host_key      rsa-sha2-256\n  encryption.host_key      ecdsa-sha2-nistp256                   Weak elliptic curve\n  encryption.host_key      ssh-ed25519\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  curve25519-sha256\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  ecdh-sha2-nistp256\n  encryption.key_exchange  ecdh-sha2-nistp384\n  encryption.key_exchange  ecdh-sha2-nistp521\n  encryption.key_exchange  diffie-hellman-group-exchange-sha256\n  encryption.key_exchange  diffie-hellman-group16-sha512\n  encryption.key_exchange  diffie-hellman-group18-sha512\n  encryption.key_exchange  diffie-hellman-group14-sha256\n  encryption.key_exchange  ext-info-s\n  encryption.key_exchange  <EMAIL>\n  fingerprint_db           ssh.banner\n  openssh.comment          Ubuntu-3ubuntu13.11\n  os.certainty             0.75\n  os.cpe23                 cpe:/o:canonical:ubuntu_linux:-\n  os.family                Linux\n  os.product               Linux\n  os.vendor                Ubuntu\n  service.cpe23            cpe:/a:openbsd:openssh:9.6p1\n  service.family           OpenSSH\n  service.product          OpenSSH\n  service.protocol         ssh\n  service.vendor           OpenBSD\n  service.version          9.6p1\n\n\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m *************:445     - Rex::ConnectionRefused: The connection was refused by the remote host (*************:445).\n\x1b[1m\x1b[34m[*]\x1b[0m *************:445     - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'ports': [{'port': 22, 'protocol': 'tcp', 'state': 'open', 'service': 'ssh', 'version': 'OpenSSH 9.6p1 Ubuntu 3ubuntu13.11 (Ubuntu Linux; protocol 2.0)'}, {'port': 80, 'protocol': 'tcp', 'state': 'open', 'service': 'http', 'version': 'Apache httpd 2.4.58 ((Ubuntu))'}, {'port': 443, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/http', 'version': 'Apache httpd 2.4.58 ((Ubuntu))'}], 'vulnerabilities': [{'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings'}], 'summary': {'total_ports': 3, 'open_ports': 3, 'total_vulnerabilities': 1}}
2025-06-25 22:07:22,551 - scan_4709f293-f704-4ef6-8429-a09e99c16f2c - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 110.7s
