2025-06-30 15:42:56,949 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: ***********, Tools: nmap, openvas, metasploit
2025-06-30 15:42:56,954 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: ***********, Tools: nmap, openvas, metasploit
2025-06-30 15:42:56,959 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on ***********
2025-06-30 15:42:56,959 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on ***********
2025-06-30 15:42:56,959 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on ***********
2025-06-30 15:42:56,962 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-30 15:42:56,962 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-30 15:42:56,962 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-30 15:42:56,964 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Intensity: low, Timeout: 300s
2025-06-30 15:42:56,964 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Intensity: low, Timeout: 300s
2025-06-30 15:42:56,965 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Intensity: low, Timeout: 300s
2025-06-30 15:42:56,967 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap port scan...
2025-06-30 15:42:56,968 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS vulnerability scan...
2025-06-30 15:42:56,968 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit exploitation scan...
2025-06-30 15:42:58,970 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Scanning ports... 10% complete
2025-06-30 15:42:59,971 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Metasploit scanning... 10% complete
2025-06-30 15:43:00,977 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - NMAP: 20% - Scanning ports... 20% complete
2025-06-30 15:43:01,971 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - OpenVAS vulnerability scanning... 10% complete
2025-06-30 15:43:02,975 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 25% - Metasploit scanning... 25% complete
2025-06-30 15:43:02,980 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Scanning ports... 30% complete
2025-06-30 15:43:04,987 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - NMAP: 40% - Scanning ports... 40% complete
2025-06-30 15:43:05,982 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 40% - Metasploit scanning... 40% complete
2025-06-30 15:43:06,974 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - OPENVAS: 20% - OpenVAS vulnerability scanning... 20% complete
2025-06-30 15:43:06,994 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - NMAP: 50% - Scanning ports... 50% complete
2025-06-30 15:43:08,989 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 55% - Metasploit scanning... 55% complete
2025-06-30 15:43:08,999 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - NMAP: 60% - Scanning ports... 60% complete
2025-06-30 15:43:11,006 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - NMAP: 70% - Scanning ports... 70% complete
2025-06-30 15:43:11,982 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS vulnerability scanning... 30% complete
2025-06-30 15:43:11,996 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 70% - Metasploit scanning... 70% complete
2025-06-30 15:43:13,013 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - NMAP: 80% - Scanning ports... 80% complete
2025-06-30 15:43:13,018 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan (low intensity)...
2025-06-30 15:43:13,041 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-06-30 15:43:13,044 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using scan config: Quick scan with minimal intrusion
2025-06-30 15:43:13,047 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Nmap options: -T3 -F
2025-06-30 15:43:13,049 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Port range: 1-1000
2025-06-30 15:43:13,061 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-06-30 15:43:13,067 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - ERROR - ❌ TOOL ERROR - NMAP: You cannot use -F (fast scan) with -p (explicit port selection) but see --top-ports and --port-ratio to fast scan a range of ports
QUITTING!

2025-06-30 15:43:13,074 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - ✅ TOOL RESULT - NMAP: completed
2025-06-30 15:43:15,004 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 85% - Metasploit scanning... 85% complete
2025-06-30 15:43:15,009 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-30 15:43:15,014 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-30 15:43:15,020 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-30 15:43:16,988 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - OPENVAS: 40% - OpenVAS vulnerability scanning... 40% complete
2025-06-30 15:43:21,992 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - OPENVAS: 50% - OpenVAS vulnerability scanning... 50% complete
2025-06-30 15:43:26,995 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - OPENVAS: 60% - OpenVAS vulnerability scanning... 60% complete
2025-06-30 15:43:31,998 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS vulnerability scanning... 70% complete
2025-06-30 15:43:34,257 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-30 15:43:34,261 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-30 15:43:37,002 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - OPENVAS: 80% - OpenVAS vulnerability scanning... 80% complete
2025-06-30 15:43:37,004 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-30 15:43:37,028 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-30 15:43:37,242 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-30 15:43:37,245 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_e68c7c5a-23f1-48fa-9d54-f53100c828b8
2025-06-30 15:43:37,249 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 3 vulnerabilities
2025-06-30 15:43:50,770 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-30 15:43:50,772 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-30 15:44:07,183 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-30 15:44:07,186 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-30 15:44:23,304 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-30 15:44:23,306 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 4 modules - Found 0 potential vulnerabilities
2025-06-30 15:44:23,309 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-30 15:44:23,317 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'tools': {'nmap': {'status': 'failed', 'scan_id': '6ad0ab2a-15d2-4e19-8b80-51c28f5771c0', 'target': '***********', 'error': 'You cannot use -F (fast scan) with -p (explicit port selection) but see --top-ports and --port-ratio to fast scan a range of ports\nQUITTING!\n', 'command': '/usr/bin/nmap -T3 -F -p 1-1000 -oX /tmp/nmap_scan_6ad0ab2a-15d2-4e19-8b80-51c28f5771c0.xml ***********'}, 'openvas': {'status': 'completed', 'scan_id': 'e68c7c5a-23f1-48fa-9d54-f53100c828b8', 'task_id': 'greenbone_task_e68c7c5a-23f1-48fa-9d54-f53100c828b8', 'target_id': 'greenbone_target_e68c7c5a-23f1-48fa-9d54-f53100c828b8', 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings'}, {'name': 'Telnet Service Detected', 'severity': 'high', 'description': 'Telnet service - unencrypted protocol', 'port': 23, 'solution': 'Review Telnet service configuration and security settings'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings'}], 'message': 'Greenbone scan completed for ***********', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/portscan/tcp', 'auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 4, 'raw_output': "=== TCP Port Scanner ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0mPORTS => 1-1000\n\x1b[0m\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:21 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:23 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:53 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:80 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:443 - TCP OPEN\n\x1b[1m\x1b[34m[*]\x1b[0m ***********           - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ***********           - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m Error: ***********: Errno::ECONNREFUSED Connection refused - connect(2) for ***********:22\n\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m ***********:445       - Rex::ConnectionRefused: The connection was refused by the remote host (***********:445).\n\x1b[1m\x1b[34m[*]\x1b[0m ***********:445       - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'ports': [], 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings'}, {'name': 'Telnet Service Detected', 'severity': 'high', 'description': 'Telnet service - unencrypted protocol', 'port': 23, 'solution': 'Review Telnet service configuration and security settings'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings'}], 'summary': {'total_ports': 0, 'open_ports': 0, 'total_vulnerabilities': 3}}
2025-06-30 15:44:23,319 - scan_3eb6b9cd-7b95-4064-996f-9d2264433944 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 86.4s
