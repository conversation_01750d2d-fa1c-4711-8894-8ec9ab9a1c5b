2025-06-28 03:16:13,218 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 🚀 SCAN STARTED - Category: vulnerability, Type: basic, Target: *************, Tools: openvas, metasploit
2025-06-28 03:16:13,223 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 🚀 SCAN STARTED - Category: vulnerability, Type: basic, Target: *************, Tools: openvas, metasploit
2025-06-28 03:16:13,234 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on *************
2025-06-28 03:16:13,235 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on *************
2025-06-28 03:16:13,239 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-28 03:16:13,244 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Intensity: low, Timeout: 300s
2025-06-28 03:16:13,245 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-28 03:16:13,248 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Intensity: low, Timeout: 300s
2025-06-28 03:16:13,249 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS vulnerability scan...
2025-06-28 03:16:13,253 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit exploitation scan...
2025-06-28 03:16:16,258 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Metasploit scanning... 10% complete
2025-06-28 03:16:18,254 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - OpenVAS vulnerability scanning... 10% complete
2025-06-28 03:16:19,266 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 25% - Metasploit scanning... 25% complete
2025-06-28 03:16:22,274 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 40% - Metasploit scanning... 40% complete
2025-06-28 03:16:23,264 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - OPENVAS: 20% - OpenVAS vulnerability scanning... 20% complete
2025-06-28 03:16:25,282 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 55% - Metasploit scanning... 55% complete
2025-06-28 03:16:28,273 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS vulnerability scanning... 30% complete
2025-06-28 03:16:28,290 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 70% - Metasploit scanning... 70% complete
2025-06-28 03:16:31,299 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 85% - Metasploit scanning... 85% complete
2025-06-28 03:16:31,303 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-28 03:16:31,305 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-28 03:16:31,309 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-28 03:16:33,281 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - OPENVAS: 40% - OpenVAS vulnerability scanning... 40% complete
2025-06-28 03:16:38,287 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - OPENVAS: 50% - OpenVAS vulnerability scanning... 50% complete
2025-06-28 03:16:43,293 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - OPENVAS: 60% - OpenVAS vulnerability scanning... 60% complete
2025-06-28 03:16:48,301 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS vulnerability scanning... 70% complete
2025-06-28 03:16:53,307 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - OPENVAS: 80% - OpenVAS vulnerability scanning... 80% complete
2025-06-28 03:16:53,312 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-28 03:16:53,369 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-28 03:16:54,127 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-28 03:16:54,135 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_46853ec5-7878-4c7b-a1e9-41790f22a98c
2025-06-28 03:16:54,143 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 4 vulnerabilities
2025-06-28 03:18:08,032 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-28 03:18:08,039 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-28 03:19:26,750 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-28 03:19:26,757 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-28 03:20:34,387 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-28 03:20:34,396 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-28 03:20:52,068 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-28 03:20:52,074 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 4 modules - Found 0 potential vulnerabilities
2025-06-28 03:20:52,084 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-28 03:20:52,110 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'tools': {'openvas': {'status': 'completed', 'scan_id': '46853ec5-7878-4c7b-a1e9-41790f22a98c', 'task_id': 'greenbone_task_46853ec5-7878-4c7b-a1e9-41790f22a98c', 'target_id': 'greenbone_target_46853ec5-7878-4c7b-a1e9-41790f22a98c', 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'POP3 Service Detected', 'severity': 'medium', 'description': 'POP3 service - unencrypted email protocol', 'port': 110, 'solution': 'Review POP3 service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'IMAP Service Detected', 'severity': 'medium', 'description': 'IMAP service - check for security configuration', 'port': 143, 'solution': 'Review IMAP service configuration and security settings', 'source_tool': 'openvas'}], 'message': 'Greenbone scan completed for *************', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/portscan/tcp', 'auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 4, 'raw_output': "=== TCP Port Scanner ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0mPORTS => 1-1000\n\x1b[0m\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:21 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:22 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:53 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:80 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:106 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:110 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:143 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:443 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:465 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:993 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:995 - TCP OPEN\n\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ************* - Key Fingerprint: ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIOpTVWZ/k5DUxBH0Ce/hiLXvstsiteakMsWsfmVk2i/j\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - SSH server version: SSH-2.0-OpenSSH_9.6p1 Ubuntu-3ubuntu13.12\n\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\nAborting...\nThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'POP3 Service Detected', 'severity': 'medium', 'description': 'POP3 service - unencrypted email protocol', 'port': 110, 'solution': 'Review POP3 service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'IMAP Service Detected', 'severity': 'medium', 'description': 'IMAP service - check for security configuration', 'port': 143, 'solution': 'Review IMAP service configuration and security settings', 'source_tool': 'openvas'}], 'ports': [], 'summary': {'total_vulnerabilities': 4, 'critical_severity': 0, 'high_severity': 0, 'medium_severity': 4, 'low_severity': 0, 'total_ports_scanned': 0, 'tools_executed': 2}}
2025-06-28 03:20:52,119 - scan_3b08b488-e3c9-4054-b6b4-a8f8d8070994 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 278.9s
