2025-06-28 04:29:40,043 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 🚀 SCAN STARTED - Category: network, Type: aggressive, Target: ***********, Tools: nmap, openvas, metasploit
2025-06-28 04:29:40,048 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 🚀 SCAN STARTED - Category: network, Type: aggressive, Target: ***********, Tools: nmap, openvas, metasploit
2025-06-28 04:29:40,054 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on ***********
2025-06-28 04:29:40,056 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on ***********
2025-06-28 04:29:40,056 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on ***********
2025-06-28 04:29:40,063 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using aggressive scan configuration: Comprehensive scan with high intensity
2025-06-28 04:29:40,063 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Using aggressive scan configuration: Comprehensive scan with high intensity
2025-06-28 04:29:40,064 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Using aggressive scan configuration: Comprehensive scan with high intensity
2025-06-28 04:29:40,066 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Intensity: high, Timeout: 1800s
2025-06-28 04:29:40,067 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Intensity: high, Timeout: 1800s
2025-06-28 04:29:40,069 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Intensity: high, Timeout: 1800s
2025-06-28 04:29:40,071 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap port scan...
2025-06-28 04:29:40,072 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit exploitation scan...
2025-06-28 04:29:40,074 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS vulnerability scan...
2025-06-28 04:29:42,078 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Scanning ports... 10% complete
2025-06-28 04:29:43,077 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Metasploit scanning... 10% complete
2025-06-28 04:29:44,085 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - NMAP: 20% - Scanning ports... 20% complete
2025-06-28 04:29:45,077 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - OpenVAS vulnerability scanning... 10% complete
2025-06-28 04:29:46,085 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 25% - Metasploit scanning... 25% complete
2025-06-28 04:29:46,091 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Scanning ports... 30% complete
2025-06-28 04:29:48,095 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - NMAP: 40% - Scanning ports... 40% complete
2025-06-28 04:29:49,089 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 40% - Metasploit scanning... 40% complete
2025-06-28 04:29:50,087 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - OPENVAS: 20% - OpenVAS vulnerability scanning... 20% complete
2025-06-28 04:29:50,099 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - NMAP: 50% - Scanning ports... 50% complete
2025-06-28 04:29:52,093 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 55% - Metasploit scanning... 55% complete
2025-06-28 04:29:52,104 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - NMAP: 60% - Scanning ports... 60% complete
2025-06-28 04:29:54,108 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - NMAP: 70% - Scanning ports... 70% complete
2025-06-28 04:29:55,091 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS vulnerability scanning... 30% complete
2025-06-28 04:29:55,097 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 70% - Metasploit scanning... 70% complete
2025-06-28 04:29:56,112 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - NMAP: 80% - Scanning ports... 80% complete
2025-06-28 04:29:56,115 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan (high intensity)...
2025-06-28 04:29:56,131 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-06-28 04:29:56,133 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using scan config: Comprehensive scan with high intensity
2025-06-28 04:29:56,135 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Nmap options: -T4 -A
2025-06-28 04:29:56,137 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Port range: 1-65535
2025-06-28 04:29:58,101 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 85% - Metasploit scanning... 85% complete
2025-06-28 04:29:58,104 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-28 04:29:58,106 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-28 04:29:58,110 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-28 04:29:58,379 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-06-28 04:29:58,382 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Scan completed: /usr/bin/nmap -T4 -A -p 1-65535 -oX /tmp/nmap_scan_9c5354a4-9a04-4234-9050-348ebceafcd5.xml ***********
2025-06-28 04:29:58,384 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Found 0 ports
2025-06-28 04:29:58,390 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - ✅ TOOL RESULT - NMAP: completed - Found 0 ports - Found 0 vulnerabilities
2025-06-28 04:30:00,098 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - OPENVAS: 40% - OpenVAS vulnerability scanning... 40% complete
2025-06-28 04:30:05,103 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - OPENVAS: 50% - OpenVAS vulnerability scanning... 50% complete
2025-06-28 04:30:10,107 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - OPENVAS: 60% - OpenVAS vulnerability scanning... 60% complete
2025-06-28 04:30:15,112 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS vulnerability scanning... 70% complete
2025-06-28 04:30:20,116 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - OPENVAS: 80% - OpenVAS vulnerability scanning... 80% complete
2025-06-28 04:30:20,120 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-28 04:30:20,170 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-28 04:30:44,198 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-28 04:30:44,200 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_0d6a48a5-8349-48cf-b76b-220bb26b1e19
2025-06-28 04:30:44,204 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 0 vulnerabilities
2025-06-28 04:31:58,212 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - ERROR - ❌ TOOL ERROR - METASPLOIT: Timeout running TCP Port Scanner
2025-06-28 04:31:58,217 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-28 04:31:58,222 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-28 04:32:51,213 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-28 04:32:51,215 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-28 04:33:56,767 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-28 04:33:56,773 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-28 04:35:02,918 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-28 04:35:02,922 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 3 modules - Found 0 potential vulnerabilities
2025-06-28 04:35:02,929 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-28 04:35:02,935 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'tools': {'nmap': {'status': 'completed', 'scan_id': '9c5354a4-9a04-4234-9050-348ebceafcd5', 'target': '***********', 'start_time': '2025-06-28T02:29:56.139589', 'end_time': '2025-06-28T02:29:58.379355', 'scan_time': 2.239766, 'command': '/usr/bin/nmap -T4 -A -p 1-65535 -oX /tmp/nmap_scan_9c5354a4-9a04-4234-9050-348ebceafcd5.xml ***********', 'ports': [], 'vulnerabilities': [], 'raw_output': 'Starting Nmap 7.94SVN ( https://nmap.org ) at 2025-06-28 04:29 CEST\nNote: Host seems down. If it is really up, but blocking our ping probes, try -Pn\nNmap done: 1 IP address (0 hosts up) scanned in 2.23 seconds\n'}, 'openvas': {'status': 'completed', 'scan_id': '0d6a48a5-8349-48cf-b76b-220bb26b1e19', 'task_id': 'greenbone_task_0d6a48a5-8349-48cf-b76b-220bb26b1e19', 'target_id': 'greenbone_target_0d6a48a5-8349-48cf-b76b-220bb26b1e19', 'vulnerabilities': [], 'message': 'Greenbone scan completed for ***********', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 3, 'raw_output': "=== TCP Port Scanner ===\nTimeout after 120 seconds\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ***********           - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m ***********:445       - Rex::ConnectionTimeout: The connection with (***********:445) timed out.\n\x1b[1m\x1b[34m[*]\x1b[0m ***********:445       - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'ports': [], 'vulnerabilities': [], 'summary': {'total_ports': 0, 'open_ports': 0, 'total_vulnerabilities': 0}}
2025-06-28 04:35:02,939 - scan_769b31ab-2c18-43ea-8613-2d612e245770 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 322.9s
