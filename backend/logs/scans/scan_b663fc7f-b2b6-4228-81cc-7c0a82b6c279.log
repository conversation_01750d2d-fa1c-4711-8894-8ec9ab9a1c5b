2025-06-25 20:09:16,467 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: *************, Tools: nmap, openvas, metasploit
2025-06-25 20:09:16,472 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: *************, Tools: nmap, openvas, metasploit
2025-06-25 20:09:16,477 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on *************
2025-06-25 20:09:16,478 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on *************
2025-06-25 20:09:16,480 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on *************
2025-06-25 20:09:16,485 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan...
2025-06-25 20:09:16,496 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-25 20:09:16,500 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-25 20:09:16,504 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-25 20:09:16,509 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-25 20:09:16,535 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-06-25 20:09:46,533 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - ERROR - ❌ TOOL ERROR - OPENVAS: OpenVAS not available
2025-06-25 20:09:46,540 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - INFO - ✅ TOOL RESULT - OPENVAS: completed
2025-06-25 20:10:01,587 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-06-25 20:10:01,590 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Scan completed: /usr/bin/nmap -sT -sV --version-intensity 5 -p 1-1000 -oX /tmp/nmap_scan_72cc214d-b1b5-4d76-8ac9-b49910b3de7e.xml *************
2025-06-25 20:10:01,593 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Found 7 ports
2025-06-25 20:10:01,597 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - INFO - ✅ TOOL RESULT - NMAP: completed - Found 7 ports - Found 0 vulnerabilities
2025-06-25 20:10:23,835 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-25 20:10:23,838 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-25 20:11:01,173 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-25 20:11:01,176 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-25 20:11:35,165 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-25 20:11:35,170 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-25 20:12:09,107 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-25 20:12:09,110 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 4 modules - Found 0 potential vulnerabilities
2025-06-25 20:12:09,114 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-25 20:12:09,120 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'tools': {'openvas': {'status': 'unavailable', 'error': 'OpenVAS service not available'}, 'nmap': {'status': 'completed', 'scan_id': '72cc214d-b1b5-4d76-8ac9-b49910b3de7e', 'target': '*************', 'start_time': '2025-06-25T18:09:16.540002', 'end_time': '2025-06-25T18:10:01.586853', 'scan_time': 45.046851, 'command': '/usr/bin/nmap -sT -sV --version-intensity 5 -p 1-1000 -oX /tmp/nmap_scan_72cc214d-b1b5-4d76-8ac9-b49910b3de7e.xml *************', 'ports': [{'port': 22, 'protocol': 'tcp', 'state': 'open', 'service': 'ssh', 'version': 'OpenSSH 9.6p1 Ubuntu 3ubuntu13.11 (Ubuntu Linux; protocol 2.0)'}, {'port': 80, 'protocol': 'tcp', 'state': 'open', 'service': 'http', 'version': 'Apache httpd 2.4.58 ((Ubuntu))'}, {'port': 231, 'protocol': 'tcp', 'state': 'filtered', 'service': 'unknown'}, {'port': 266, 'protocol': 'tcp', 'state': 'filtered', 'service': 'sst'}, {'port': 443, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/http', 'version': 'Apache httpd 2.4.58 ((Ubuntu))'}, {'port': 551, 'protocol': 'tcp', 'state': 'filtered', 'service': 'cybercash'}, {'port': 709, 'protocol': 'tcp', 'state': 'filtered', 'service': 'entrustmanager'}], 'vulnerabilities': [], 'raw_output': 'Starting Nmap 7.94SVN ( https://nmap.org ) at 2025-06-25 20:09 CEST\nNmap scan report for vps-3811c29b.vps.ovh.ca (*************)\nHost is up (0.25s latency).\nNot shown: 993 closed tcp ports (conn-refused)\nPORT    STATE    SERVICE        VERSION\n22/tcp  open     ssh            OpenSSH 9.6p1 Ubuntu 3ubuntu13.11 (Ubuntu Linux; protocol 2.0)\n80/tcp  open     http           Apache httpd 2.4.58 ((Ubuntu))\n231/tcp filtered unknown\n266/tcp filtered sst\n443/tcp open     ssl/http       Apache httpd 2.4.58 ((Ubuntu))\n551/tcp filtered cybercash\n709/tcp filtered entrustmanager\nService Info: OS: Linux; CPE: cpe:/o:linux:linux_kernel\n\nService detection performed. Please report any incorrect results at https://nmap.org/submit/ .\nNmap done: 1 IP address (1 host up) scanned in 45.03 seconds\n'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/portscan/tcp', 'auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 4, 'raw_output': "=== TCP Port Scanner ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0mPORTS => 1-1000\n\x1b[0m\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:22 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:80 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:443 - TCP OPEN\n\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ************* - Key Fingerprint: ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIJU7bXzWjVFCRr70wCRrw80liBHhsKANhaQHwAVEuPka\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - SSH server version: SSH-2.0-OpenSSH_9.6p1 Ubuntu-3ubuntu13.11\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - Server Information and Encryption\n=================================\n\n  Type                     Value                                 Note\n  ----                     -----                                 ----\n  encryption.compression   none\n  encryption.compression   <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.encryption    aes128-ctr\n  encryption.encryption    aes192-ctr\n  encryption.encryption    aes256-ctr\n  encryption.encryption    <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          hmac-sha2-256\n  encryption.hmac          hmac-sha2-512\n  encryption.hmac          hmac-sha1\n  encryption.host_key      rsa-sha2-512\n  encryption.host_key      rsa-sha2-256\n  encryption.host_key      ecdsa-sha2-nistp256                   Weak elliptic curve\n  encryption.host_key      ssh-ed25519\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  curve25519-sha256\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  ecdh-sha2-nistp256\n  encryption.key_exchange  ecdh-sha2-nistp384\n  encryption.key_exchange  ecdh-sha2-nistp521\n  encryption.key_exchange  diffie-hellman-group-exchange-sha256\n  encryption.key_exchange  diffie-hellman-group16-sha512\n  encryption.key_exchange  diffie-hellman-group18-sha512\n  encryption.key_exchange  diffie-hellman-group14-sha256\n  encryption.key_exchange  ext-info-s\n  encryption.key_exchange  <EMAIL>\n  fingerprint_db           ssh.banner\n  openssh.comment          Ubuntu-3ubuntu13.11\n  os.certainty             0.75\n  os.cpe23                 cpe:/o:canonical:ubuntu_linux:-\n  os.family                Linux\n  os.product               Linux\n  os.vendor                Ubuntu\n  service.cpe23            cpe:/a:openbsd:openssh:9.6p1\n  service.family           OpenSSH\n  service.product          OpenSSH\n  service.protocol         ssh\n  service.vendor           OpenBSD\n  service.version          9.6p1\n\n\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m *************:445     - Rex::ConnectionRefused: The connection was refused by the remote host (*************:445).\n\x1b[1m\x1b[34m[*]\x1b[0m *************:445     - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'ports': [{'port': 22, 'protocol': 'tcp', 'state': 'open', 'service': 'ssh', 'version': 'OpenSSH 9.6p1 Ubuntu 3ubuntu13.11 (Ubuntu Linux; protocol 2.0)'}, {'port': 80, 'protocol': 'tcp', 'state': 'open', 'service': 'http', 'version': 'Apache httpd 2.4.58 ((Ubuntu))'}, {'port': 231, 'protocol': 'tcp', 'state': 'filtered', 'service': 'unknown'}, {'port': 266, 'protocol': 'tcp', 'state': 'filtered', 'service': 'sst'}, {'port': 443, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/http', 'version': 'Apache httpd 2.4.58 ((Ubuntu))'}, {'port': 551, 'protocol': 'tcp', 'state': 'filtered', 'service': 'cybercash'}, {'port': 709, 'protocol': 'tcp', 'state': 'filtered', 'service': 'entrustmanager'}], 'vulnerabilities': [], 'summary': {'total_ports': 7, 'open_ports': 3, 'total_vulnerabilities': 0}}
2025-06-25 20:12:09,124 - scan_b663fc7f-b2b6-4228-81cc-7c0a82b6c279 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 172.7s
