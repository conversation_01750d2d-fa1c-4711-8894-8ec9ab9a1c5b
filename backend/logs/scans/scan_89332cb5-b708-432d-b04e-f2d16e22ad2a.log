2025-06-28 03:48:47,126 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: *************, Tools: nmap, openvas, metasploit
2025-06-28 03:48:47,133 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: *************, Tools: nmap, openvas, metasploit
2025-06-28 03:48:47,141 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on *************
2025-06-28 03:48:47,142 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on *************
2025-06-28 03:48:47,148 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on *************
2025-06-28 03:48:47,150 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-28 03:48:47,151 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-28 03:48:47,152 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Intensity: low, Timeout: 300s
2025-06-28 03:48:47,155 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-28 03:48:47,156 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Intensity: low, Timeout: 300s
2025-06-28 03:48:47,159 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Intensity: low, Timeout: 300s
2025-06-28 03:48:47,160 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap port scan...
2025-06-28 03:48:47,166 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS vulnerability scan...
2025-06-28 03:48:47,167 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit exploitation scan...
2025-06-28 03:48:49,170 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Scanning ports... 10% complete
2025-06-28 03:48:50,174 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Metasploit scanning... 10% complete
2025-06-28 03:48:51,177 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - NMAP: 20% - Scanning ports... 20% complete
2025-06-28 03:48:52,174 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - OpenVAS vulnerability scanning... 10% complete
2025-06-28 03:48:53,181 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 25% - Metasploit scanning... 25% complete
2025-06-28 03:48:53,184 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Scanning ports... 30% complete
2025-06-28 03:48:55,189 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - NMAP: 40% - Scanning ports... 40% complete
2025-06-28 03:48:56,186 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 40% - Metasploit scanning... 40% complete
2025-06-28 03:48:57,182 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - OPENVAS: 20% - OpenVAS vulnerability scanning... 20% complete
2025-06-28 03:48:57,196 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - NMAP: 50% - Scanning ports... 50% complete
2025-06-28 03:48:59,191 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 55% - Metasploit scanning... 55% complete
2025-06-28 03:48:59,202 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - NMAP: 60% - Scanning ports... 60% complete
2025-06-28 03:49:01,211 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - NMAP: 70% - Scanning ports... 70% complete
2025-06-28 03:49:02,187 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS vulnerability scanning... 30% complete
2025-06-28 03:49:02,198 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 70% - Metasploit scanning... 70% complete
2025-06-28 03:49:03,218 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - NMAP: 80% - Scanning ports... 80% complete
2025-06-28 03:49:03,222 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan (low intensity)...
2025-06-28 03:49:03,246 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-06-28 03:49:03,249 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using scan config: Quick scan with minimal intrusion
2025-06-28 03:49:03,250 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Nmap options: -T3 -F
2025-06-28 03:49:03,253 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Port range: 1-1000
2025-06-28 03:49:03,266 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-06-28 03:49:03,269 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - ERROR - ❌ TOOL ERROR - NMAP: You cannot use -F (fast scan) with -p (explicit port selection) but see --top-ports and --port-ratio to fast scan a range of ports
QUITTING!

2025-06-28 03:49:03,276 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - ✅ TOOL RESULT - NMAP: completed
2025-06-28 03:49:05,206 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 85% - Metasploit scanning... 85% complete
2025-06-28 03:49:05,210 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-28 03:49:05,214 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-28 03:49:05,218 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-28 03:49:07,194 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - OPENVAS: 40% - OpenVAS vulnerability scanning... 40% complete
2025-06-28 03:49:12,199 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - OPENVAS: 50% - OpenVAS vulnerability scanning... 50% complete
2025-06-28 03:49:17,204 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - OPENVAS: 60% - OpenVAS vulnerability scanning... 60% complete
2025-06-28 03:49:22,212 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS vulnerability scanning... 70% complete
2025-06-28 03:49:27,218 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - OPENVAS: 80% - OpenVAS vulnerability scanning... 80% complete
2025-06-28 03:49:27,221 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-28 03:49:27,254 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-28 03:49:27,966 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-28 03:49:27,969 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_9d208171-56b8-4fe5-bcf6-1f18c37c3916
2025-06-28 03:49:27,973 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 4 vulnerabilities
2025-06-28 03:50:31,349 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-28 03:50:31,353 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-28 03:51:05,171 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-28 03:51:05,177 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-28 03:51:35,807 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-28 03:51:35,812 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-28 03:52:05,133 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-28 03:52:05,139 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 4 modules - Found 0 potential vulnerabilities
2025-06-28 03:52:05,151 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-28 03:52:05,165 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'tools': {'nmap': {'status': 'failed', 'scan_id': 'dce25419-f9af-41ea-b3ea-1607282dacaf', 'target': '*************', 'error': 'You cannot use -F (fast scan) with -p (explicit port selection) but see --top-ports and --port-ratio to fast scan a range of ports\nQUITTING!\n', 'command': '/usr/bin/nmap -T3 -F -p 1-1000 -oX /tmp/nmap_scan_dce25419-f9af-41ea-b3ea-1607282dacaf.xml *************'}, 'openvas': {'status': 'completed', 'scan_id': '9d208171-56b8-4fe5-bcf6-1f18c37c3916', 'task_id': 'greenbone_task_9d208171-56b8-4fe5-bcf6-1f18c37c3916', 'target_id': 'greenbone_target_9d208171-56b8-4fe5-bcf6-1f18c37c3916', 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings'}, {'name': 'POP3 Service Detected', 'severity': 'medium', 'description': 'POP3 service - unencrypted email protocol', 'port': 110, 'solution': 'Review POP3 service configuration and security settings'}, {'name': 'IMAP Service Detected', 'severity': 'medium', 'description': 'IMAP service - check for security configuration', 'port': 143, 'solution': 'Review IMAP service configuration and security settings'}], 'message': 'Greenbone scan completed for *************', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/portscan/tcp', 'auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 4, 'raw_output': "=== TCP Port Scanner ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0mPORTS => 1-1000\n\x1b[0m\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:21 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:22 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:53 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:80 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:106 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:110 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:143 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:443 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:465 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:995 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:993 - TCP OPEN\n\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ************* - Key Fingerprint: ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIOpTVWZ/k5DUxBH0Ce/hiLXvstsiteakMsWsfmVk2i/j\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - SSH server version: SSH-2.0-OpenSSH_9.6p1 Ubuntu-3ubuntu13.12\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - Server Information and Encryption\n=================================\n\n  Type                     Value                                 Note\n  ----                     -----                                 ----\n  encryption.compression   none\n  encryption.compression   <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.encryption    aes128-ctr\n  encryption.encryption    aes192-ctr\n  encryption.encryption    aes256-ctr\n  encryption.encryption    <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          hmac-sha2-256\n  encryption.hmac          hmac-sha2-512\n  encryption.hmac          hmac-sha1\n  encryption.host_key      rsa-sha2-512\n  encryption.host_key      rsa-sha2-256\n  encryption.host_key      ecdsa-sha2-nistp256                   Weak elliptic curve\n  encryption.host_key      ssh-ed25519\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  curve25519-sha256\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  ecdh-sha2-nistp256\n  encryption.key_exchange  ecdh-sha2-nistp384\n  encryption.key_exchange  ecdh-sha2-nistp521\n  encryption.key_exchange  diffie-hellman-group-exchange-sha256\n  encryption.key_exchange  diffie-hellman-group16-sha512\n  encryption.key_exchange  diffie-hellman-group18-sha512\n  encryption.key_exchange  diffie-hellman-group14-sha256\n  encryption.key_exchange  ext-info-s\n  encryption.key_exchange  <EMAIL>\n  fingerprint_db           ssh.banner\n  openssh.comment          Ubuntu-3ubuntu13.12\n  os.certainty             0.75\n  os.cpe23                 cpe:/o:canonical:ubuntu_linux:-\n  os.family                Linux\n  os.product               Linux\n  os.vendor                Ubuntu\n  service.cpe23            cpe:/a:openbsd:openssh:9.6p1\n  service.family           OpenSSH\n  service.product          OpenSSH\n  service.protocol         ssh\n  service.vendor           OpenBSD\n  service.version          9.6p1\n\n\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m *************:445     - Rex::ConnectionRefused: The connection was refused by the remote host (*************:445).\n\x1b[1m\x1b[34m[*]\x1b[0m *************:445     - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'ports': [], 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings'}, {'name': 'POP3 Service Detected', 'severity': 'medium', 'description': 'POP3 service - unencrypted email protocol', 'port': 110, 'solution': 'Review POP3 service configuration and security settings'}, {'name': 'IMAP Service Detected', 'severity': 'medium', 'description': 'IMAP service - check for security configuration', 'port': 143, 'solution': 'Review IMAP service configuration and security settings'}], 'summary': {'total_ports': 0, 'open_ports': 0, 'total_vulnerabilities': 4}}
2025-06-28 03:52:05,170 - scan_89332cb5-b708-432d-b04e-f2d16e22ad2a - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 198.0s
