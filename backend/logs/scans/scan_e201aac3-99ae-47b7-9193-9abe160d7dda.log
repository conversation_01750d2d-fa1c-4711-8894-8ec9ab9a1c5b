2025-06-26 18:52:07,045 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 🚀 SCAN STARTED - Category: deep, Type: comprehensive, Target: *************, Tools: nmap, openvas, metasploit, nikto, sqlmap, dirb, gobuster, zap
2025-06-26 18:52:07,047 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 🚀 SCAN STARTED - Category: deep, Type: comprehensive, Target: *************, Tools: nmap, openvas, metasploit, nikto, sqlmap, dirb, gobuster, zap
2025-06-26 18:52:07,049 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 🔧 TOOL START - DEEP_SCAN - Command: 🔍 Phase 1: Network Discovery and Port Scanning
2025-06-26 18:52:07,055 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 5% - Phase 1: Running nmap scan...
2025-06-26 18:52:07,059 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on *************
2025-06-26 18:52:07,063 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan...
2025-06-26 18:52:07,081 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-06-26 18:52:27,196 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-06-26 18:52:27,199 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Scan completed: /usr/bin/nmap -sT -sV --version-intensity 5 -p 1-1000 -oX /tmp/nmap_scan_f61f44f9-b58d-4eec-bb7b-19dd87f140e7.xml *************
2025-06-26 18:52:27,200 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Found 11 ports
2025-06-26 18:52:27,203 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - ✅ TOOL RESULT - NMAP: completed - Found 11 ports - Found 0 vulnerabilities
2025-06-26 18:52:27,206 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 14% - Phase 1: Running openvas scan...
2025-06-26 18:52:27,208 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on *************
2025-06-26 18:52:27,212 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-26 18:52:27,241 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-26 18:52:27,911 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-26 18:52:27,915 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_2fc417a2-3f94-4b68-94c7-96a62efd338e
2025-06-26 18:52:27,920 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 5 vulnerabilities
2025-06-26 18:52:27,926 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 23% - Phase 1: Running metasploit scan...
2025-06-26 18:52:27,928 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on *************
2025-06-26 18:52:27,933 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-26 18:52:27,935 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-26 18:52:27,938 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-26 18:53:05,308 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-26 18:53:05,312 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-26 18:53:40,552 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-26 18:53:40,557 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-26 18:54:14,309 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-26 18:54:14,312 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-26 18:54:43,739 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-26 18:54:43,742 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 4 modules - Found 0 potential vulnerabilities
2025-06-26 18:54:43,746 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-26 18:54:43,751 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - ✅ TOOL RESULT - DEEP_SCAN: phase_1_complete
2025-06-26 18:54:43,755 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 🔧 TOOL START - DEEP_SCAN - Command: 🌐 Phase 2: Web Application Security Testing
2025-06-26 18:54:43,760 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - Phase 2: Running nikto scan...
2025-06-26 18:54:43,763 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 🔧 TOOL START - NIKTO - Command: nikto scan on *************
2025-06-26 18:54:43,768 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Starting Nikto scan...
2025-06-26 18:54:43,773 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Executing Nikto command...
2025-06-26 18:54:43,775 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Command: nikto -h ************* -Format txt -output /tmp/nikto_1750956883.txt -maxtime 180 -Tuning 1,2,3,4,5,6,7,8,9,0,a,b,c
2025-06-26 18:54:43,781 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Scanning... (0s elapsed)
2025-06-26 18:54:53,787 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - NIKTO: 90% - Parsing results...
2025-06-26 18:54:53,791 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - NIKTO: 100% - Nikto scan completed
2025-06-26 18:54:53,793 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Scan completed - Found 4 potential issues
2025-06-26 18:54:53,798 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - ✅ TOOL RESULT - NIKTO: completed - Found 4 vulnerabilities
2025-06-26 18:54:53,806 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 41% - Phase 2: Running sqlmap scan...
2025-06-26 18:54:53,808 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 🔧 TOOL START - SQLMAP - Command: sqlmap scan on *************
2025-06-26 18:54:53,815 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - SQLMAP: 10% - Starting SQLMap scan...
2025-06-26 18:54:53,820 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - SQLMAP: 25% - Executing SQLMap command...
2025-06-26 18:54:53,824 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Command: sqlmap -u ************* --batch --level=1 --risk=1 --timeout=15 --retries=1 --threads=1 --technique=B --no-cast --disable-coloring --flush-session --fresh-queries --crawl=1
2025-06-26 18:54:53,827 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - SQLMAP: 25% - Testing for SQL injection... (0s elapsed)
2025-06-26 18:55:03,832 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - SQLMAP: 90% - Parsing results...
2025-06-26 18:55:03,836 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - SQLMAP: 100% - SQLMap scan completed
2025-06-26 18:55:03,839 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): No SQL injection vulnerabilities detected
2025-06-26 18:55:03,842 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - ✅ TOOL RESULT - SQLMAP: completed
2025-06-26 18:55:03,849 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 47% - Phase 2: Running dirb scan...
2025-06-26 18:55:03,853 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 🔧 TOOL START - DIRB - Command: dirb scan on *************
2025-06-26 18:55:03,859 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - DIRB: 10% - Starting directory scan...
2025-06-26 18:55:03,874 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - DIRB: 25% - Executing Dirb command...
2025-06-26 18:55:03,879 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Command: dirb ************* /usr/share/dirb/wordlists/small.txt -w -r -S -z 50 -X .php,.html,.txt,.js
2025-06-26 18:55:03,883 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - DIRB: 25% - Scanning directories... (0s elapsed)
2025-06-26 18:55:08,888 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - DIRB: 90% - Parsing results...
2025-06-26 18:55:08,890 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - DIRB: 100% - Directory scan completed
2025-06-26 18:55:08,892 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Found 0 directories
2025-06-26 18:55:08,895 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - ✅ TOOL RESULT - DIRB: completed - Found 0 directories
2025-06-26 18:55:08,902 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 53% - Phase 2: Running gobuster scan...
2025-06-26 18:55:08,905 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 🔧 TOOL START - GOBUSTER - Command: gobuster scan on *************
2025-06-26 18:55:08,909 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - GOBUSTER: 10% - Starting GoBuster scan...
2025-06-26 18:55:08,920 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - GOBUSTER: 25% - Executing GoBuster command...
2025-06-26 18:55:08,924 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Command: gobuster dir -u ************* -w /usr/share/dirb/wordlists/common.txt -t 5 -q --no-error --timeout 5s --delay 100ms
2025-06-26 18:55:08,927 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - GOBUSTER: 25% - Brute forcing directories... (0s elapsed)
2025-06-26 18:55:13,930 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - GOBUSTER: 28% - Brute forcing directories... (5s elapsed)
2025-06-26 18:55:18,934 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - GOBUSTER: 32% - Brute forcing directories... (10s elapsed)
2025-06-26 18:55:23,939 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - GOBUSTER: 35% - Brute forcing directories... (15s elapsed)
2025-06-26 18:55:28,942 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - GOBUSTER: 39% - Brute forcing directories... (20s elapsed)
2025-06-26 18:55:33,947 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - GOBUSTER: 43% - Brute forcing directories... (25s elapsed)
2025-06-26 18:55:38,951 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - GOBUSTER: 46% - Brute forcing directories... (30s elapsed)
2025-06-26 18:55:43,955 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - GOBUSTER: 50% - Brute forcing directories... (35s elapsed)
2025-06-26 18:55:48,959 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - GOBUSTER: 53% - Brute forcing directories... (40s elapsed)
2025-06-26 18:55:53,963 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - GOBUSTER: 57% - Brute forcing directories... (45s elapsed)
2025-06-26 18:55:58,969 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - GOBUSTER: 61% - Brute forcing directories... (50s elapsed)
2025-06-26 18:56:03,972 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - GOBUSTER: 64% - Brute forcing directories... (55s elapsed)
2025-06-26 18:56:08,976 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - GOBUSTER: 68% - Brute forcing directories... (60s elapsed)
2025-06-26 18:56:13,981 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - GOBUSTER: 71% - Brute forcing directories... (65s elapsed)
2025-06-26 18:56:18,987 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - GOBUSTER: 75% - Brute forcing directories... (70s elapsed)
2025-06-26 18:56:23,991 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - GOBUSTER: 79% - Brute forcing directories... (75s elapsed)
2025-06-26 18:56:29,001 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - GOBUSTER: 82% - Brute forcing directories... (80s elapsed)
2025-06-26 18:56:34,005 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - GOBUSTER: 86% - Brute forcing directories... (85s elapsed)
2025-06-26 18:57:14,011 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Scan timeout after 2 minutes - completing with current results
2025-06-26 18:57:14,015 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - GOBUSTER: 90% - Parsing results...
2025-06-26 18:57:14,020 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - GOBUSTER: 100% - GoBuster scan completed
2025-06-26 18:57:14,024 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Found 6 paths
2025-06-26 18:57:14,029 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - ✅ TOOL RESULT - GOBUSTER: completed
2025-06-26 18:57:14,038 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 59% - Phase 2: Running zap scan...
2025-06-26 18:57:14,042 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 🔧 TOOL START - ZAP - Command: zap scan on *************
2025-06-26 18:57:14,053 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - ZAP: 10% - Starting security scan...
2025-06-26 18:57:14,060 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - ZAP: 25% - Performing HTTP security checks...
2025-06-26 18:57:14,065 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): HTTP request failed: Invalid URL '*************': No scheme supplied. Perhaps you meant https://*************?
2025-06-26 18:57:14,076 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - ZAP: 100% - Security scan completed
2025-06-26 18:57:14,083 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Found 1 security alerts
2025-06-26 18:57:14,088 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - ✅ TOOL RESULT - ZAP: completed
2025-06-26 18:57:14,097 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - ✅ TOOL RESULT - DEEP_SCAN: phase_2_complete
2025-06-26 18:57:14,100 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 🔧 TOOL START - DEEP_SCAN - Command: 📊 Phase 3: Results Analysis and Consolidation
2025-06-26 18:57:14,108 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - Phase 3: Consolidating port scan results...
2025-06-26 18:57:14,115 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 78% - Found 11 ports from nmap
2025-06-26 18:57:14,127 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 82% - Phase 3: Consolidating vulnerability results...
2025-06-26 18:57:14,131 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 84% - Added 5 vulnerabilities from openvas
2025-06-26 18:57:14,136 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 86% - Added 4 vulnerabilities from nikto
2025-06-26 18:57:14,141 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 86% - Added 1 vulnerabilities from zap
2025-06-26 18:57:14,147 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 88% - Phase 3: Calculating final summary...
2025-06-26 18:57:14,150 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 92% - Summary: 11 ports, 10 vulnerabilities
2025-06-26 18:57:14,157 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 96% - Saving vulnerabilities to database...
2025-06-26 18:57:14,172 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 98% - Saved 9 vulnerabilities to database
2025-06-26 18:57:14,175 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 99% - Finalizing scan results...
2025-06-26 18:57:14,187 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - ✅ TOOL RESULT - DEEP_SCAN: completed
2025-06-26 18:57:14,192 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'network': {'nmap': {'status': 'completed', 'scan_id': 'f61f44f9-b58d-4eec-bb7b-19dd87f140e7', 'target': '*************', 'start_time': '2025-06-26T16:52:07.084069', 'end_time': '2025-06-26T16:52:27.196636', 'scan_time': 20.112567, 'command': '/usr/bin/nmap -sT -sV --version-intensity 5 -p 1-1000 -oX /tmp/nmap_scan_f61f44f9-b58d-4eec-bb7b-19dd87f140e7.xml *************', 'ports': [{'port': 21, 'protocol': 'tcp', 'state': 'open', 'service': 'ftp', 'version': 'ProFTPD'}, {'port': 22, 'protocol': 'tcp', 'state': 'open', 'service': 'ssh', 'version': 'OpenSSH 9.6p1 Ubuntu 3ubuntu13.12 (Ubuntu Linux; protocol 2.0)'}, {'port': 25, 'protocol': 'tcp', 'state': 'open', 'service': 'smtp', 'version': 'Postfix smtpd'}, {'port': 53, 'protocol': 'tcp', 'state': 'open', 'service': 'domain', 'version': '(unknown banner: none)'}, {'port': 80, 'protocol': 'tcp', 'state': 'open', 'service': 'http?'}, {'port': 106, 'protocol': 'tcp', 'state': 'open', 'service': 'tcpwrapped'}, {'port': 110, 'protocol': 'tcp', 'state': 'open', 'service': 'pop3', 'version': 'Dovecot pop3d'}, {'port': 143, 'protocol': 'tcp', 'state': 'open', 'service': 'imap', 'version': 'Dovecot imapd'}, {'port': 465, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/smtp', 'version': 'Postfix smtpd'}, {'port': 993, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/imap', 'version': 'Dovecot imapd'}, {'port': 995, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/pop3', 'version': 'Dovecot pop3d'}], 'vulnerabilities': [], 'raw_output': 'Starting Nmap 7.94SVN ( https://nmap.org ) at 2025-06-26 18:52 CEST\nNmap scan report for ip82-165-144-72.pbiaas.com (*************)\nHost is up (0.056s latency).\nNot shown: 989 closed tcp ports (conn-refused)\nPORT    STATE SERVICE    VERSION\n21/tcp  open  ftp        ProFTPD\n22/tcp  open  ssh        OpenSSH 9.6p1 Ubuntu 3ubuntu13.12 (Ubuntu Linux; protocol 2.0)\n25/tcp  open  smtp       Postfix smtpd\n53/tcp  open  domain     (unknown banner: none)\n80/tcp  open  http?\n106/tcp open  tcpwrapped\n110/tcp open  pop3       Dovecot pop3d\n143/tcp open  imap       Dovecot imapd\n465/tcp open  ssl/smtp   Postfix smtpd\n993/tcp open  ssl/imap   Dovecot imapd\n995/tcp open  ssl/pop3   Dovecot pop3d\n1 service unrecognized despite returning data. If you know the service/version, please submit the following fingerprint at https://nmap.org/cgi-bin/submit.cgi?new-service :\nSF-Port80-TCP:V=7.94SVN%I=5%D=6/26%Time=685D7AC1%P=x86_64-pc-linux-gnu%r(G\nSF:etRequest,802,"HTTP/1\\.1\\x20200\\x20OK\\r\\nCache-Control:\\x20no-store,\\x2\nSF:0must-revalidate\\r\\nX-Powered-By:\\x20Next\\.js\\r\\nETag:\\x20\\"ek295vgndd1\nSF:e2\\"\\r\\nContent-Type:\\x20text/html;\\x20charset=utf-8\\r\\nContent-Length:\nSF:\\x201802\\r\\nVary:\\x20Accept-Encoding\\r\\nDate:\\x20Thu,\\x2026\\x20Jun\\x202\nSF:025\\x2016:52:17\\x20GMT\\r\\nConnection:\\x20close\\r\\n\\r\\n<!DOCTYPE\\x20html\nSF:><html\\x20lang=\\"en\\"><head><style\\x20data-next-hide-fouc=\\"true\\">body\nSF:{display:none}</style><noscript\\x20data-next-hide-fouc=\\"true\\"><style>\nSF:body{display:block}</style></noscript><meta\\x20charSet=\\"utf-8\\"/><meta\nSF:\\x20name=\\"viewport\\"\\x20content=\\"width=device-width\\"/><meta\\x20name=\nSF:\\"next-head-count\\"\\x20content=\\"2\\"/><link\\x20data-next-font=\\"size-ad\nSF:just\\"\\x20rel=\\"preconnect\\"\\x20href=\\"/\\"\\x20crossorigin=\\"anonymous\\"\nSF:/><noscript\\x20data-n-css=\\"\\"></noscript><script\\x20defer=\\"\\"\\x20nomo\nSF:dule=\\"\\"\\x20src=\\"/_next/static/chunks/polyfills\\.js\\?ts=1750956737104\nSF:\\"></script><script\\x20src=\\"/_next/static/chunks/webpack\\.js\\?ts=17509\nSF:56737104\\"\\x20defer=\\"\\"></script><script\\x20src=\\"/_next/static/chunks\nSF:/main\\.js\\?ts=1750956737104\\"")%r(HTTPOptions,802,"HTTP/1\\.1\\x20200\\x20\nSF:OK\\r\\nCache-Control:\\x20no-store,\\x20must-revalidate\\r\\nX-Powered-By:\\x\nSF:20Next\\.js\\r\\nETag:\\x20\\"kp2vtpuhm01e2\\"\\r\\nContent-Type:\\x20text/html;\nSF:\\x20charset=utf-8\\r\\nContent-Length:\\x201802\\r\\nVary:\\x20Accept-Encodin\nSF:g\\r\\nDate:\\x20Thu,\\x2026\\x20Jun\\x202025\\x2016:52:17\\x20GMT\\r\\nConnectio\nSF:n:\\x20close\\r\\n\\r\\n<!DOCTYPE\\x20html><html\\x20lang=\\"en\\"><head><style\\\nSF:x20data-next-hide-fouc=\\"true\\">body{display:none}</style><noscript\\x20\nSF:data-next-hide-fouc=\\"true\\"><style>body{display:block}</style></noscri\nSF:pt><meta\\x20charSet=\\"utf-8\\"/><meta\\x20name=\\"viewport\\"\\x20content=\\"\nSF:width=device-width\\"/><meta\\x20name=\\"next-head-count\\"\\x20content=\\"2\\\nSF:"/><link\\x20data-next-font=\\"size-adjust\\"\\x20rel=\\"preconnect\\"\\x20hre\nSF:f=\\"/\\"\\x20crossorigin=\\"anonymous\\"/><noscript\\x20data-n-css=\\"\\"></no\nSF:script><script\\x20defer=\\"\\"\\x20nomodule=\\"\\"\\x20src=\\"/_next/static/ch\nSF:unks/polyfills\\.js\\?ts=1750956737243\\"></script><script\\x20src=\\"/_next\nSF:/static/chunks/webpack\\.js\\?ts=1750956737243\\"\\x20defer=\\"\\"></script><\nSF:script\\x20src=\\"/_next/static/chunks/main\\.js\\?ts=1750956737243\\"");\nService Info: Hosts:  dazzling-kilby.82-165-144-72.plesk.page, dazzling-kilby.82-165-144-72.plesk.page; OS: Linux; CPE: cpe:/o:linux:linux_kernel\n\nService detection performed. Please report any incorrect results at https://nmap.org/submit/ .\nNmap done: 1 IP address (1 host up) scanned in 20.09 seconds\n'}, 'openvas': {'status': 'completed', 'scan_id': '2fc417a2-3f94-4b68-94c7-96a62efd338e', 'task_id': 'greenbone_task_2fc417a2-3f94-4b68-94c7-96a62efd338e', 'target_id': 'greenbone_target_2fc417a2-3f94-4b68-94c7-96a62efd338e', 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'SMTP Service Detected', 'severity': 'medium', 'description': 'SMTP service - check for open relay', 'port': 25, 'solution': 'Review SMTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'POP3 Service Detected', 'severity': 'medium', 'description': 'POP3 service - unencrypted email protocol', 'port': 110, 'solution': 'Review POP3 service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'IMAP Service Detected', 'severity': 'medium', 'description': 'IMAP service - check for security configuration', 'port': 143, 'solution': 'Review IMAP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}], 'message': 'Greenbone scan completed for *************', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/portscan/tcp', 'auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 4, 'raw_output': "=== TCP Port Scanner ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0mPORTS => 1-1000\n\x1b[0m\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:21 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:25 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:22 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:53 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:80 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:110 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:106 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:143 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:465 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:993 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:995 - TCP OPEN\n\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ************* - Key Fingerprint: ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIOpTVWZ/k5DUxBH0Ce/hiLXvstsiteakMsWsfmVk2i/j\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - SSH server version: SSH-2.0-OpenSSH_9.6p1 Ubuntu-3ubuntu13.12\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - Server Information and Encryption\n=================================\n\n  Type                     Value                                 Note\n  ----                     -----                                 ----\n  encryption.compression   none\n  encryption.compression   <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.encryption    aes128-ctr\n  encryption.encryption    aes192-ctr\n  encryption.encryption    aes256-ctr\n  encryption.encryption    <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          hmac-sha2-256\n  encryption.hmac          hmac-sha2-512\n  encryption.hmac          hmac-sha1\n  encryption.host_key      rsa-sha2-512\n  encryption.host_key      rsa-sha2-256\n  encryption.host_key      ecdsa-sha2-nistp256                   Weak elliptic curve\n  encryption.host_key      ssh-ed25519\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  curve25519-sha256\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  ecdh-sha2-nistp256\n  encryption.key_exchange  ecdh-sha2-nistp384\n  encryption.key_exchange  ecdh-sha2-nistp521\n  encryption.key_exchange  diffie-hellman-group-exchange-sha256\n  encryption.key_exchange  diffie-hellman-group16-sha512\n  encryption.key_exchange  diffie-hellman-group18-sha512\n  encryption.key_exchange  diffie-hellman-group14-sha256\n  encryption.key_exchange  ext-info-s\n  encryption.key_exchange  <EMAIL>\n  fingerprint_db           ssh.banner\n  openssh.comment          Ubuntu-3ubuntu13.12\n  os.certainty             0.75\n  os.cpe23                 cpe:/o:canonical:ubuntu_linux:-\n  os.family                Linux\n  os.product               Linux\n  os.vendor                Ubuntu\n  service.cpe23            cpe:/a:openbsd:openssh:9.6p1\n  service.family           OpenSSH\n  service.product          OpenSSH\n  service.protocol         ssh\n  service.vendor           OpenBSD\n  service.version          9.6p1\n\n\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m *************:445     - Rex::ConnectionRefused: The connection was refused by the remote host (*************:445).\n\x1b[1m\x1b[34m[*]\x1b[0m *************:445     - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'web': {'nikto': {'status': 'completed', 'vulnerabilities': [{'type': 'Security Finding', 'severity': 'medium', 'description': 'GET /: Retrieved x-powered-by header: Next.js', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Information Disclosure via ETags', 'severity': 'medium', 'description': 'GET /: Server leaks inodes via ETags, header found with file /, fields: 0xfrzlzd18fh1e2', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Missing X-Frame-Options Header', 'severity': 'medium', 'description': 'GET /: The anti-clickjacking X-Frame-Options header is not present.', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Security Finding', 'severity': 'medium', 'description': "GET /jkF2Su6q/: Uncommon header 'refresh' found, with contents: 0;url=/jkF2Su6q", 'source_tool': 'nikto', 'category': 'web'}], 'scan_time': '10 seconds', 'total_tests': 8, 'raw_output': "- Nikto v2.1.5/2.1.5\n+ Target Host: ip82-165-144-72.pbiaas.com\n+ Target Port: 80\n+ GET /: Retrieved x-powered-by header: Next.js\n+ GET /: Server leaks inodes via ETags, header found with file /, fields: 0xfrzlzd18fh1e2 \n+ GET /: The anti-clickjacking X-Frame-Options header is not present.\n+ GET /jkF2Su6q/: Uncommon header 'refresh' found, with contents: 0;url=/jkF2Su6q\n"}, 'sqlmap': {'status': 'completed', 'injections': [], 'scan_time': '10 seconds', 'raw_output': "        ___\n       __H__\n ___ ___[.]_____ ___ ___  {1.8.4#stable}\n|_ -| . [(]     | .'| . |\n|___|_  [,]_|_|_|__,|  _|\n      |_|V...       |_|   https://sqlmap.org\n\n[!] legal disclaimer: Usage of sqlmap for attacking targets without prior mutual consent is illegal. It is the end user's responsibility to obey all applicable local, state and federal laws. Developers assume no liability and are not responsible for any misuse or damage caused by this program\n\n[*] starting @ 18:54:54 /2025-06-26/\n\ndo you want to check for the existence of site's sitemap(.xml) [y/N] N\n\n[18:54:54] [INFO] starting crawler for target URL 'http://*************'\n\n[18:54:54] [INFO] searching for links with depth 1\n\n[18:54:54] [WARNING] no usable links found (with GET parameters)\n\n[18:54:54] [WARNING] your sqlmap version is outdated\n\n[*] ending @ 18:54:54 /2025-06-26/\n\n"}, 'dirb': {'status': 'completed', 'directories': [], 'scan_time': '5 seconds', 'raw_output': '\n-----------------\nDIRB v2.22    \nBy The Dark Raver\n-----------------\n\n\n(!) FATAL: Invalid URL format: *************/\n    (Use: "http://host/" or "https://host/" for SSL)\n'}, 'gobuster': {'status': 'completed', 'found_paths': [{'path': '/about', 'status': 200, 'size': 1807}, {'path': '/admin', 'status': 200, 'size': 1807}, {'path': '/blog', 'status': 200, 'size': 1805}, {'path': '/contact', 'status': 200, 'size': 1811}, {'path': '/favicon.ico', 'status': 200, 'size': 25931}, {'path': '/profile', 'status': 200, 'size': 1811}], 'scan_time': '125 seconds', 'raw_output': '\n\x1b[2K/about                (Status: 200) [Size: 1807]\n\n\x1b[2K/admin                (Status: 200) [Size: 1807]\n\n\x1b[2K/blog                 (Status: 200) [Size: 1805]\n\n\x1b[2K/cgi-bin/             (Status: 308) [Size: 8] [--> /cgi-bin]\n\n\x1b[2K/contact              (Status: 200) [Size: 1811]\n\n\x1b[2K/favicon.ico          (Status: 200) [Size: 25931]\n\n\x1b[2K/profile              (Status: 200) [Size: 1811]\n'}, 'zap': {'status': 'completed', 'alerts': [{'risk': 'Medium', 'name': 'Connection Issue', 'count': 1, 'description': "Could not analyze target: Invalid URL '*************': No scheme supplied. Perhaps you meant https://*************?"}], 'scan_time': '0 seconds', 'raw_output': 'Basic security scan completed. Found 1 potential issues.'}}, 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'SMTP Service Detected', 'severity': 'medium', 'description': 'SMTP service - check for open relay', 'port': 25, 'solution': 'Review SMTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'POP3 Service Detected', 'severity': 'medium', 'description': 'POP3 service - unencrypted email protocol', 'port': 110, 'solution': 'Review POP3 service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'IMAP Service Detected', 'severity': 'medium', 'description': 'IMAP service - check for security configuration', 'port': 143, 'solution': 'Review IMAP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'type': 'Security Finding', 'severity': 'medium', 'description': 'GET /: Retrieved x-powered-by header: Next.js', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Information Disclosure via ETags', 'severity': 'medium', 'description': 'GET /: Server leaks inodes via ETags, header found with file /, fields: 0xfrzlzd18fh1e2', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Missing X-Frame-Options Header', 'severity': 'medium', 'description': 'GET /: The anti-clickjacking X-Frame-Options header is not present.', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Security Finding', 'severity': 'medium', 'description': "GET /jkF2Su6q/: Uncommon header 'refresh' found, with contents: 0;url=/jkF2Su6q", 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Connection Issue', 'severity': 'medium', 'description': "Could not analyze target: Invalid URL '*************': No scheme supplied. Perhaps you meant https://*************?", 'source_tool': 'zap', 'category': 'web'}], 'ports': [{'port': 21, 'protocol': 'tcp', 'state': 'open', 'service': 'ftp', 'version': 'ProFTPD'}, {'port': 22, 'protocol': 'tcp', 'state': 'open', 'service': 'ssh', 'version': 'OpenSSH 9.6p1 Ubuntu 3ubuntu13.12 (Ubuntu Linux; protocol 2.0)'}, {'port': 25, 'protocol': 'tcp', 'state': 'open', 'service': 'smtp', 'version': 'Postfix smtpd'}, {'port': 53, 'protocol': 'tcp', 'state': 'open', 'service': 'domain', 'version': '(unknown banner: none)'}, {'port': 80, 'protocol': 'tcp', 'state': 'open', 'service': 'http?'}, {'port': 106, 'protocol': 'tcp', 'state': 'open', 'service': 'tcpwrapped'}, {'port': 110, 'protocol': 'tcp', 'state': 'open', 'service': 'pop3', 'version': 'Dovecot pop3d'}, {'port': 143, 'protocol': 'tcp', 'state': 'open', 'service': 'imap', 'version': 'Dovecot imapd'}, {'port': 465, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/smtp', 'version': 'Postfix smtpd'}, {'port': 993, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/imap', 'version': 'Dovecot imapd'}, {'port': 995, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/pop3', 'version': 'Dovecot pop3d'}], 'summary': {'total_ports': 11, 'open_ports': 11, 'total_vulnerabilities': 10, 'high_severity': 0, 'medium_severity': 10, 'low_severity': 0, 'scan_phases': 3, 'tools_executed': 8}}
2025-06-26 18:57:14,200 - scan_e201aac3-99ae-47b7-9193-9abe160d7dda - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 307.2s
