2025-06-26 19:36:25,472 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 🚀 SCAN STARTED - Category: deep, Type: comprehensive, Target: *************, Tools: nmap, openvas, metasploit, nikto, sqlmap, dirb, gobuster, zap
2025-06-26 19:36:25,480 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 🚀 SCAN STARTED - Category: deep, Type: comprehensive, Target: *************, Tools: nmap, openvas, metasploit, nikto, sqlmap, dirb, gobuster, zap
2025-06-26 19:36:25,485 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 🔧 TOOL START - DEEP_SCAN - Command: 🔍 Phase 1: Network Discovery and Port Scanning
2025-06-26 19:36:25,492 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 5% - Phase 1: Running nmap scan...
2025-06-26 19:36:25,497 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on *************
2025-06-26 19:36:25,502 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-26 19:36:25,506 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Intensity: maximum, Timeout: 2400s
2025-06-26 19:36:25,511 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan (maximum intensity)...
2025-06-26 19:36:25,533 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-06-26 19:36:25,555 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-06-26 19:36:25,559 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - ERROR - ❌ TOOL ERROR - NMAP: Only 1 -p option allowed, separate multiple ranges with commas.
QUITTING!

2025-06-26 19:36:25,567 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - ✅ TOOL RESULT - NMAP: completed
2025-06-26 19:36:25,575 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 14% - Phase 1: Running openvas scan...
2025-06-26 19:36:25,579 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on *************
2025-06-26 19:36:25,586 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-26 19:36:25,590 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Intensity: maximum, Timeout: 2400s
2025-06-26 19:36:25,596 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-26 19:36:25,709 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-26 19:36:26,413 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-26 19:36:26,418 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_241fda4b-bcd3-45aa-a1b6-cc1f14f272f8
2025-06-26 19:36:26,424 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 5 vulnerabilities
2025-06-26 19:36:26,431 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 23% - Phase 1: Running metasploit scan...
2025-06-26 19:36:26,434 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on *************
2025-06-26 19:36:26,438 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-26 19:36:26,443 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Intensity: maximum, Timeout: 2400s
2025-06-26 19:36:26,448 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-26 19:36:26,451 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-26 19:36:26,455 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-26 19:37:02,822 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-26 19:37:02,826 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-26 19:37:32,679 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-26 19:37:32,682 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-26 19:38:08,779 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-26 19:38:08,782 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-26 19:38:37,250 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-26 19:38:37,252 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 4 modules - Found 0 potential vulnerabilities
2025-06-26 19:38:37,255 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-26 19:38:37,259 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - ✅ TOOL RESULT - DEEP_SCAN: phase_1_complete
2025-06-26 19:38:37,261 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 🔧 TOOL START - DEEP_SCAN - Command: 🌐 Phase 2: Web Application Security Testing
2025-06-26 19:38:37,268 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - Phase 2: Running nikto scan...
2025-06-26 19:38:37,270 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 🔧 TOOL START - NIKTO - Command: nikto scan on *************
2025-06-26 19:38:37,275 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-26 19:38:37,278 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Intensity: maximum, Timeout: 2400s
2025-06-26 19:38:37,283 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Starting Nikto scan (maximum intensity)...
2025-06-26 19:38:37,286 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Executing Nikto command...
2025-06-26 19:38:37,288 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Command: nikto -h ************* -Format txt -output /tmp/nikto_1750959517.txt -maxtime 2400 -Tuning x
2025-06-26 19:38:37,291 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Scanning... (0s elapsed)
2025-06-26 19:38:47,294 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NIKTO: 28% - Scanning... (10s elapsed)
2025-06-26 19:38:57,300 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NIKTO: 32% - Scanning... (20s elapsed)
2025-06-26 19:39:07,303 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NIKTO: 35% - Scanning... (30s elapsed)
2025-06-26 19:39:17,309 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NIKTO: 39% - Scanning... (40s elapsed)
2025-06-26 19:39:27,314 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NIKTO: 43% - Scanning... (50s elapsed)
2025-06-26 19:39:37,318 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NIKTO: 46% - Scanning... (60s elapsed)
2025-06-26 19:39:47,320 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NIKTO: 50% - Scanning... (70s elapsed)
2025-06-26 19:39:57,323 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NIKTO: 53% - Scanning... (80s elapsed)
2025-06-26 19:40:07,327 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NIKTO: 57% - Scanning... (90s elapsed)
2025-06-26 19:40:17,332 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NIKTO: 61% - Scanning... (100s elapsed)
2025-06-26 19:40:27,336 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NIKTO: 64% - Scanning... (110s elapsed)
2025-06-26 19:40:37,340 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NIKTO: 68% - Scanning... (120s elapsed)
2025-06-26 19:40:47,346 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NIKTO: 71% - Scanning... (130s elapsed)
2025-06-26 19:40:57,351 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NIKTO: 75% - Scanning... (140s elapsed)
2025-06-26 19:41:07,353 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NIKTO: 79% - Scanning... (150s elapsed)
2025-06-26 19:41:17,357 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NIKTO: 82% - Scanning... (160s elapsed)
2025-06-26 19:41:27,362 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NIKTO: 86% - Scanning... (170s elapsed)
2025-06-26 19:43:47,367 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - ERROR - ❌ TOOL ERROR - NIKTO: Scan timeout after 5 minutes
2025-06-26 19:43:47,372 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NIKTO: 90% - Parsing results...
2025-06-26 19:43:47,374 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - NIKTO: 100% - Nikto scan completed
2025-06-26 19:43:47,377 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Scan completed - Found 0 potential issues
2025-06-26 19:43:47,381 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - ✅ TOOL RESULT - NIKTO: completed - Found 0 vulnerabilities
2025-06-26 19:43:47,387 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 41% - Phase 2: Running sqlmap scan...
2025-06-26 19:43:47,390 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 🔧 TOOL START - SQLMAP - Command: sqlmap scan on *************
2025-06-26 19:43:47,396 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-26 19:43:47,398 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Intensity: maximum, Timeout: 2400s
2025-06-26 19:43:47,403 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - SQLMAP: 10% - Starting SQLMap scan (maximum intensity)...
2025-06-26 19:43:47,405 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - SQLMAP: 25% - Executing SQLMap command...
2025-06-26 19:43:47,408 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Command: sqlmap -u ************* --disable-coloring --flush-session --fresh-queries --batch --level=3 --risk=2 --threads=5 --timeout 30
2025-06-26 19:43:47,411 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - SQLMAP: 25% - Testing for SQL injection... (0s elapsed)
2025-06-26 19:43:57,414 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - SQLMAP: 30% - Testing for SQL injection... (10s elapsed)
2025-06-26 19:44:07,417 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - SQLMAP: 35% - Testing for SQL injection... (20s elapsed)
2025-06-26 19:44:17,420 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - SQLMAP: 41% - Testing for SQL injection... (30s elapsed)
2025-06-26 19:44:27,423 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - SQLMAP: 90% - Parsing results...
2025-06-26 19:44:27,427 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - SQLMAP: 100% - SQLMap scan completed
2025-06-26 19:44:27,429 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Found 8 SQL injection points
2025-06-26 19:44:27,433 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - ✅ TOOL RESULT - SQLMAP: completed
2025-06-26 19:44:27,439 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 47% - Phase 2: Running dirb scan...
2025-06-26 19:44:27,442 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 🔧 TOOL START - DIRB - Command: dirb scan on *************
2025-06-26 19:44:27,446 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-26 19:44:27,449 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Intensity: maximum, Timeout: 2400s
2025-06-26 19:44:27,454 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - DIRB: 10% - Starting directory scan...
2025-06-26 19:44:27,466 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - DIRB: 25% - Executing Dirb command...
2025-06-26 19:44:27,472 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Command: dirb ************* /usr/share/dirb/wordlists/small.txt -w -r -S -z 50 -X .php,.html,.txt,.js
2025-06-26 19:44:27,476 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - DIRB: 25% - Scanning directories... (0s elapsed)
2025-06-26 19:44:32,478 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - DIRB: 90% - Parsing results...
2025-06-26 19:44:32,482 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - DIRB: 100% - Directory scan completed
2025-06-26 19:44:32,486 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Found 0 directories
2025-06-26 19:44:32,490 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - ✅ TOOL RESULT - DIRB: completed - Found 0 directories
2025-06-26 19:44:32,498 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 53% - Phase 2: Running gobuster scan...
2025-06-26 19:44:32,501 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 🔧 TOOL START - GOBUSTER - Command: gobuster scan on *************
2025-06-26 19:44:32,507 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-26 19:44:32,511 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Intensity: maximum, Timeout: 2400s
2025-06-26 19:44:32,516 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 10% - Starting GoBuster scan...
2025-06-26 19:44:32,529 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 25% - Executing GoBuster command...
2025-06-26 19:44:32,534 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Command: gobuster dir -u ************* -w /usr/share/dirb/wordlists/common.txt -t 5 -q --no-error --timeout 5s --delay 100ms
2025-06-26 19:44:32,538 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 25% - Brute forcing directories... (0s elapsed)
2025-06-26 19:44:37,541 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 28% - Brute forcing directories... (5s elapsed)
2025-06-26 19:44:42,546 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 32% - Brute forcing directories... (10s elapsed)
2025-06-26 19:44:47,549 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 35% - Brute forcing directories... (15s elapsed)
2025-06-26 19:44:52,553 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 39% - Brute forcing directories... (20s elapsed)
2025-06-26 19:44:57,556 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 43% - Brute forcing directories... (25s elapsed)
2025-06-26 19:45:02,559 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 46% - Brute forcing directories... (30s elapsed)
2025-06-26 19:45:07,562 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 50% - Brute forcing directories... (35s elapsed)
2025-06-26 19:45:12,565 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 53% - Brute forcing directories... (40s elapsed)
2025-06-26 19:45:17,569 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 57% - Brute forcing directories... (45s elapsed)
2025-06-26 19:45:22,573 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 61% - Brute forcing directories... (50s elapsed)
2025-06-26 19:45:27,576 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 64% - Brute forcing directories... (55s elapsed)
2025-06-26 19:45:32,579 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 68% - Brute forcing directories... (60s elapsed)
2025-06-26 19:45:37,583 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 71% - Brute forcing directories... (65s elapsed)
2025-06-26 19:45:42,586 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 75% - Brute forcing directories... (70s elapsed)
2025-06-26 19:45:47,590 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 79% - Brute forcing directories... (75s elapsed)
2025-06-26 19:45:52,594 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 82% - Brute forcing directories... (80s elapsed)
2025-06-26 19:45:57,597 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 86% - Brute forcing directories... (85s elapsed)
2025-06-26 19:46:37,601 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Scan timeout after 2 minutes - completing with current results
2025-06-26 19:46:37,604 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 90% - Parsing results...
2025-06-26 19:46:37,608 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 100% - GoBuster scan completed
2025-06-26 19:46:37,610 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Found 6 paths
2025-06-26 19:46:37,614 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - ✅ TOOL RESULT - GOBUSTER: completed
2025-06-26 19:46:37,621 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 59% - Phase 2: Running zap scan...
2025-06-26 19:46:37,623 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 🔧 TOOL START - ZAP - Command: zap scan on *************
2025-06-26 19:46:37,627 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-26 19:46:37,630 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Intensity: maximum, Timeout: 2400s
2025-06-26 19:46:37,638 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - ZAP: 10% - Starting security scan...
2025-06-26 19:46:37,642 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - ZAP: 25% - Performing HTTP security checks...
2025-06-26 19:46:37,645 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): HTTP request failed: Invalid URL '*************': No scheme supplied. Perhaps you meant https://*************?
2025-06-26 19:46:37,648 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - ZAP: 100% - Security scan completed
2025-06-26 19:46:37,650 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Found 1 security alerts
2025-06-26 19:46:37,655 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - ✅ TOOL RESULT - ZAP: completed
2025-06-26 19:46:37,660 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - ✅ TOOL RESULT - DEEP_SCAN: phase_2_complete
2025-06-26 19:46:37,662 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 🔧 TOOL START - DEEP_SCAN - Command: 📊 Phase 3: Results Analysis and Consolidation
2025-06-26 19:46:37,668 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - Phase 3: Consolidating port scan results...
2025-06-26 19:46:37,676 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 82% - Phase 3: Consolidating vulnerability results...
2025-06-26 19:46:37,679 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 84% - Added 5 vulnerabilities from openvas
2025-06-26 19:46:37,682 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 86% - Added 8 vulnerabilities from sqlmap
2025-06-26 19:46:37,686 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 86% - Added 1 vulnerabilities from zap
2025-06-26 19:46:37,691 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 88% - Phase 3: Calculating final summary...
2025-06-26 19:46:37,694 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 92% - Summary: 0 ports, 14 vulnerabilities
2025-06-26 19:46:37,699 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 96% - Saving vulnerabilities to database...
2025-06-26 19:46:37,707 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 98% - Saved 5 vulnerabilities to database
2025-06-26 19:46:37,710 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 99% - Finalizing scan results...
2025-06-26 19:46:37,717 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - ✅ TOOL RESULT - DEEP_SCAN: completed
2025-06-26 19:46:37,720 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'network': {'nmap': {'status': 'failed', 'scan_id': '3e666ec2-d6e6-4c43-b4b0-adf90a8b8b65', 'target': '*************', 'error': 'Only 1 -p option allowed, separate multiple ranges with commas.\nQUITTING!\n', 'command': '/usr/bin/nmap -T4 -A -p- --script=vuln,safe,discovery -p 1-65535 -oX /tmp/nmap_scan_3e666ec2-d6e6-4c43-b4b0-adf90a8b8b65.xml *************'}, 'openvas': {'status': 'completed', 'scan_id': '241fda4b-bcd3-45aa-a1b6-cc1f14f272f8', 'task_id': 'greenbone_task_241fda4b-bcd3-45aa-a1b6-cc1f14f272f8', 'target_id': 'greenbone_target_241fda4b-bcd3-45aa-a1b6-cc1f14f272f8', 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'SMTP Service Detected', 'severity': 'medium', 'description': 'SMTP service - check for open relay', 'port': 25, 'solution': 'Review SMTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'POP3 Service Detected', 'severity': 'medium', 'description': 'POP3 service - unencrypted email protocol', 'port': 110, 'solution': 'Review POP3 service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'IMAP Service Detected', 'severity': 'medium', 'description': 'IMAP service - check for security configuration', 'port': 143, 'solution': 'Review IMAP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}], 'message': 'Greenbone scan completed for *************', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/portscan/tcp', 'auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 4, 'raw_output': "=== TCP Port Scanner ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0mPORTS => 1-1000\n\x1b[0m\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:22 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:21 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:25 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:53 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:80 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:110 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:106 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:143 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:465 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:993 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:995 - TCP OPEN\n\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ************* - Key Fingerprint: ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIOpTVWZ/k5DUxBH0Ce/hiLXvstsiteakMsWsfmVk2i/j\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - SSH server version: SSH-2.0-OpenSSH_9.6p1 Ubuntu-3ubuntu13.12\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - Server Information and Encryption\n=================================\n\n  Type                     Value                                 Note\n  ----                     -----                                 ----\n  encryption.compression   none\n  encryption.compression   <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.encryption    aes128-ctr\n  encryption.encryption    aes192-ctr\n  encryption.encryption    aes256-ctr\n  encryption.encryption    <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          hmac-sha2-256\n  encryption.hmac          hmac-sha2-512\n  encryption.hmac          hmac-sha1\n  encryption.host_key      rsa-sha2-512\n  encryption.host_key      rsa-sha2-256\n  encryption.host_key      ecdsa-sha2-nistp256                   Weak elliptic curve\n  encryption.host_key      ssh-ed25519\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  curve25519-sha256\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  ecdh-sha2-nistp256\n  encryption.key_exchange  ecdh-sha2-nistp384\n  encryption.key_exchange  ecdh-sha2-nistp521\n  encryption.key_exchange  diffie-hellman-group-exchange-sha256\n  encryption.key_exchange  diffie-hellman-group16-sha512\n  encryption.key_exchange  diffie-hellman-group18-sha512\n  encryption.key_exchange  diffie-hellman-group14-sha256\n  encryption.key_exchange  ext-info-s\n  encryption.key_exchange  <EMAIL>\n  fingerprint_db           ssh.banner\n  openssh.comment          Ubuntu-3ubuntu13.12\n  os.certainty             0.75\n  os.cpe23                 cpe:/o:canonical:ubuntu_linux:-\n  os.family                Linux\n  os.product               Linux\n  os.vendor                Ubuntu\n  service.cpe23            cpe:/a:openbsd:openssh:9.6p1\n  service.family           OpenSSH\n  service.product          OpenSSH\n  service.protocol         ssh\n  service.vendor           OpenBSD\n  service.version          9.6p1\n\n\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m *************:445     - Rex::ConnectionRefused: The connection was refused by the remote host (*************:445).\n\x1b[1m\x1b[34m[*]\x1b[0m *************:445     - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'web': {'nikto': {'status': 'completed', 'vulnerabilities': [], 'scan_time': '310 seconds', 'total_tests': 17, 'raw_output': '- Nikto v2.1.5\n---------------------------------------------------------------------------\n+ Target IP:          *************\n+ Target Hostname:    ip82-165-144-72.pbiaas.com\n+ Target Port:        80\n+ Start Time:         2025-06-26 19:38:37 (GMT2)\n---------------------------------------------------------------------------\n+ Server: No banner retrieved\n+ Retrieved x-powered-by header: Next.js\n+ Server leaks inodes via ETags, header found with file /, fields: 0xzck1f1pvdb1e2 \n+ The anti-clickjacking X-Frame-Options header is not present.\n+ Uncommon header \'refresh\' found, with contents: 0;url=/Is5Sikjh\n+ No CGI Directories found (use \'-C all\' to force check all possible dirs)\n+ /node/view/666\\"><script>alert(document.domain)</script>: Drupal 4.2.0 RC is vulnerable to Cross Site Scripting (XSS). http://www.cert.org/advisories/CA-2000-02.html.\n+ /index.php/\\"><script><script>alert(document.cookie)</script><: eZ publish v3 and prior allow Cross Site Scripting (XSS). http://www.cert.org/advisories/CA-2000-02.html.\n+ OSVDB-6659: /wyB5biCaErHLm0dbWMpevMtcT5zeQyTMgVdO192sMLLu6oMSw4gc8DpAfNImgtpd02LpaHhydMUwTZxCIri3EyGsbgDoI33CWn8hlpAt8rSzdRoMFmRZLbqMi2EevFRxSW6wUiC49OWKwXOz5csJ6SadG41EOpmcngGWAejdTi5rczXeMnXi4xMhaLe3Vwm054JChdM3KCbUzGxS6Ekyli6Tt3BZnNC<font%20size=50><script>alert(11)</script><!--//--: MyWebServer 1.0.2 is vulnerable to Cross Site Scripting (XSS). http://www.cert.org/advisories/CA-2000-02.html.\n'}, 'sqlmap': {'status': 'completed', 'injections': [{'parameter': 'unknown', 'type': 'SQL injection', 'dbms': 'MySQL'}, {'parameter': 'unknown', 'type': 'SQL injection', 'dbms': 'MySQL'}, {'parameter': 'unknown', 'type': 'boolean-based blind', 'dbms': 'MySQL'}, {'parameter': 'unknown', 'type': 'SQL injection', 'dbms': 'MySQL'}, {'parameter': 'unknown', 'type': 'SQL injection', 'dbms': 'MySQL'}, {'parameter': 'unknown', 'type': 'boolean-based blind', 'dbms': 'MySQL'}, {'parameter': 'unknown', 'type': 'SQL injection', 'dbms': 'MySQL'}, {'parameter': 'unknown', 'type': 'SQL injection', 'dbms': 'MySQL'}], 'scan_time': '40 seconds', 'raw_output': '        ___\n       __H__\n ___ ___["]_____ ___ ___  {1.8.4#stable}\n|_ -| . [\']     | .\'| . |\n|___|_  [,]_|_|_|__,|  _|\n      |_|V...       |_|   https://sqlmap.org\n\n[!] legal disclaimer: Usage of sqlmap for attacking targets without prior mutual consent is illegal. It is the end user\'s responsibility to obey all applicable local, state and federal laws. Developers assume no liability and are not responsible for any misuse or damage caused by this program\n\n[*] starting @ 19:43:47 /2025-06-26/\n\n\n[19:43:47] [INFO] testing connection to the target URL\n\n[19:43:47] [INFO] checking if the target is protected by some kind of WAF/IPS\n\n[19:43:48] [INFO] testing if the target URL content is stable\n\n[19:43:48] [WARNING] target URL content is not stable (i.e. content differs). sqlmap will base the page comparison on a sequence matcher. If no dynamic nor injectable parameters are detected, or in case of junk results, refer to user\'s manual paragraph \'Page comparison\'\nhow do you want to proceed? [(C)ontinue/(s)tring/(r)egex/(q)uit] C\n\n[19:43:48] [INFO] testing if parameter \'User-Agent\' is dynamic\n\n[19:43:48] [WARNING] parameter \'User-Agent\' does not appear to be dynamic\n\n[19:43:48] [WARNING] heuristic (basic) test shows that parameter \'User-Agent\' might not be injectable\n\n[19:43:48] [INFO] testing for SQL injection on parameter \'User-Agent\'\n\n[19:43:48] [INFO] testing \'AND boolean-based blind - WHERE or HAVING clause\'\n\n[19:43:50] [INFO] parameter \'User-Agent\' appears to be \'AND boolean-based blind - WHERE or HAVING clause\' injectable \n\n[19:43:50] [INFO] heuristic (extended) test shows that the back-end DBMS could be \'Microsoft Access\' \nit looks like the back-end DBMS is \'Microsoft Access\'. Do you want to skip test payloads specific for other DBMSes? [Y/n] Y\nfor the remaining tests, do you want to include all tests for \'Microsoft Access\' extending provided level (3) and risk (2) values? [Y/n] Y\n\n[19:43:50] [INFO] testing \'Generic inline queries\'\n\n[19:43:50] [INFO] testing \'Generic UN'}, 'dirb': {'status': 'completed', 'directories': [], 'scan_time': '5 seconds', 'raw_output': '\n-----------------\nDIRB v2.22    \nBy The Dark Raver\n-----------------\n\n\n(!) FATAL: Invalid URL format: *************/\n    (Use: "http://host/" or "https://host/" for SSL)\n'}, 'gobuster': {'status': 'completed', 'found_paths': [{'path': '/about', 'status': 200, 'size': 1807}, {'path': '/admin', 'status': 200, 'size': 1807}, {'path': '/blog', 'status': 200, 'size': 1805}, {'path': '/contact', 'status': 200, 'size': 1811}, {'path': '/favicon.ico', 'status': 200, 'size': 25931}, {'path': '/profile', 'status': 200, 'size': 1811}], 'scan_time': '125 seconds', 'raw_output': '\n\x1b[2K/about                (Status: 200) [Size: 1807]\n\n\x1b[2K/admin                (Status: 200) [Size: 1807]\n\n\x1b[2K/blog                 (Status: 200) [Size: 1805]\n\n\x1b[2K/cgi-bin/             (Status: 308) [Size: 8] [--> /cgi-bin]\n\n\x1b[2K/contact              (Status: 200) [Size: 1811]\n\n\x1b[2K/favicon.ico          (Status: 200) [Size: 25931]\n\n\x1b[2K/profile              (Status: 200) [Size: 1811]\n'}, 'zap': {'status': 'completed', 'alerts': [{'risk': 'Medium', 'name': 'Connection Issue', 'count': 1, 'description': "Could not analyze target: Invalid URL '*************': No scheme supplied. Perhaps you meant https://*************?"}], 'scan_time': '0 seconds', 'raw_output': 'Basic security scan completed. Found 1 potential issues.'}}, 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'SMTP Service Detected', 'severity': 'medium', 'description': 'SMTP service - check for open relay', 'port': 25, 'solution': 'Review SMTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'POP3 Service Detected', 'severity': 'medium', 'description': 'POP3 service - unencrypted email protocol', 'port': 110, 'solution': 'Review POP3 service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'IMAP Service Detected', 'severity': 'medium', 'description': 'IMAP service - check for security configuration', 'port': 143, 'solution': 'Review IMAP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'type': 'SQL Injection - SQL injection', 'severity': 'high', 'description': 'SQL injection found in parameter: unknown', 'parameter': 'unknown', 'dbms': 'MySQL', 'source_tool': 'sqlmap', 'category': 'web'}, {'type': 'SQL Injection - SQL injection', 'severity': 'high', 'description': 'SQL injection found in parameter: unknown', 'parameter': 'unknown', 'dbms': 'MySQL', 'source_tool': 'sqlmap', 'category': 'web'}, {'type': 'SQL Injection - boolean-based blind', 'severity': 'high', 'description': 'SQL injection found in parameter: unknown', 'parameter': 'unknown', 'dbms': 'MySQL', 'source_tool': 'sqlmap', 'category': 'web'}, {'type': 'SQL Injection - SQL injection', 'severity': 'high', 'description': 'SQL injection found in parameter: unknown', 'parameter': 'unknown', 'dbms': 'MySQL', 'source_tool': 'sqlmap', 'category': 'web'}, {'type': 'SQL Injection - SQL injection', 'severity': 'high', 'description': 'SQL injection found in parameter: unknown', 'parameter': 'unknown', 'dbms': 'MySQL', 'source_tool': 'sqlmap', 'category': 'web'}, {'type': 'SQL Injection - boolean-based blind', 'severity': 'high', 'description': 'SQL injection found in parameter: unknown', 'parameter': 'unknown', 'dbms': 'MySQL', 'source_tool': 'sqlmap', 'category': 'web'}, {'type': 'SQL Injection - SQL injection', 'severity': 'high', 'description': 'SQL injection found in parameter: unknown', 'parameter': 'unknown', 'dbms': 'MySQL', 'source_tool': 'sqlmap', 'category': 'web'}, {'type': 'SQL Injection - SQL injection', 'severity': 'high', 'description': 'SQL injection found in parameter: unknown', 'parameter': 'unknown', 'dbms': 'MySQL', 'source_tool': 'sqlmap', 'category': 'web'}, {'type': 'Connection Issue', 'severity': 'medium', 'description': "Could not analyze target: Invalid URL '*************': No scheme supplied. Perhaps you meant https://*************?", 'source_tool': 'zap', 'category': 'web'}], 'ports': [], 'summary': {'total_ports': 0, 'open_ports': 0, 'total_vulnerabilities': 14, 'high_severity': 8, 'medium_severity': 6, 'low_severity': 0, 'scan_phases': 3, 'tools_executed': 8}}
2025-06-26 19:46:37,727 - scan_25caaa51-11c5-4f2a-9967-1bbbf5c5ccdb - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 612.3s
