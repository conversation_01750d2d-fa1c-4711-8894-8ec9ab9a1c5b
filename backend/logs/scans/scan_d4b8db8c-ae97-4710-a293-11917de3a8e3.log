2025-06-28 04:32:32,025 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 🚀 SCAN STARTED - Category: web, Type: basic, Target: http://***********, Tools: nikto, sqlmap, dirb, gobuster, zap
2025-06-28 04:32:32,036 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 🔧 TOOL START - NIKTO - Command: nikto scan on http://***********
2025-06-28 04:32:32,039 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 🔧 TOOL START - SQLMAP - Command: sqlmap scan on http://***********
2025-06-28 04:32:32,043 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 🔧 TOOL START - DIRB - Command: dirb scan on http://***********
2025-06-28 04:32:32,047 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 🔧 TOOL START - GOBUSTER - Command: gobuster scan on http://***********
2025-06-28 04:32:32,054 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-28 04:32:32,054 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 🔧 TOOL START - ZAP - Command: zap scan on http://***********
2025-06-28 04:32:32,055 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-28 04:32:32,060 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-28 04:32:32,061 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Intensity: low, Timeout: 300s
2025-06-28 04:32:32,062 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-28 04:32:32,069 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-28 04:32:32,071 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Intensity: low, Timeout: 300s
2025-06-28 04:32:32,072 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Intensity: low, Timeout: 300s
2025-06-28 04:32:32,074 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Intensity: low, Timeout: 300s
2025-06-28 04:32:32,076 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Intensity: low, Timeout: 300s
2025-06-28 04:32:32,079 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Starting Nikto web vulnerability scan...
2025-06-28 04:32:32,082 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 10% - Starting SQLMap scan (low intensity)...
2025-06-28 04:32:32,083 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 10% - Starting GoBuster directory scan (low intensity)...
2025-06-28 04:32:32,083 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 10% - Starting Dirb directory scan (low intensity)...
2025-06-28 04:32:32,085 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - ZAP: 10% - Starting ZAP security scan...
2025-06-28 04:32:32,087 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Command: sqlmap -u http://*********** --batch --level=1 --risk=1 --threads=1
2025-06-28 04:32:32,092 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Command: gobuster dir -u http://*********** -w /usr/share/wordlists/dirb/small.txt -t 10 -q
2025-06-28 04:32:32,094 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Command: dirb http://*********** /usr/share/dirb/wordlists/small.txt -w
2025-06-28 04:32:32,098 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 25% - Testing for SQL injection... (0s elapsed)
2025-06-28 04:32:32,104 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 25% - Brute forcing directories... (0s elapsed)
2025-06-28 04:32:32,106 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 25% - Scanning directories... (0s elapsed)
2025-06-28 04:32:35,088 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Scanning web vulnerabilities... 10% complete
2025-06-28 04:32:36,094 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - ZAP: 10% - ZAP security scanning... 10% complete
2025-06-28 04:32:37,108 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 27% - Testing for SQL injection... (5s elapsed)
2025-06-28 04:32:37,112 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 90% - Parsing results...
2025-06-28 04:32:37,113 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 27% - Scanning directories... (5s elapsed)
2025-06-28 04:32:37,119 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - ✅ TOOL RESULT - GOBUSTER: completed - Found 0 directories
2025-06-28 04:32:38,092 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Scanning web vulnerabilities... 25% complete
2025-06-28 04:32:40,101 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - ZAP: 30% - ZAP security scanning... 30% complete
2025-06-28 04:32:41,101 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - NIKTO: 40% - Scanning web vulnerabilities... 40% complete
2025-06-28 04:32:42,115 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 30% - Testing for SQL injection... (10s elapsed)
2025-06-28 04:32:42,120 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 30% - Scanning directories... (10s elapsed)
2025-06-28 04:32:44,105 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - ZAP: 50% - ZAP security scanning... 50% complete
2025-06-28 04:32:44,107 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - NIKTO: 55% - Scanning web vulnerabilities... 55% complete
2025-06-28 04:32:47,115 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - NIKTO: 70% - Scanning web vulnerabilities... 70% complete
2025-06-28 04:32:47,124 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 33% - Testing for SQL injection... (15s elapsed)
2025-06-28 04:32:47,129 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 33% - Scanning directories... (15s elapsed)
2025-06-28 04:32:48,114 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - ZAP: 70% - ZAP security scanning... 70% complete
2025-06-28 04:32:48,119 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - ZAP: 10% - Starting security scan...
2025-06-28 04:32:48,123 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - ZAP: 25% - Performing HTTP security checks...
2025-06-28 04:32:50,128 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - NIKTO: 85% - Scanning web vulnerabilities... 85% complete
2025-06-28 04:32:50,133 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Starting Nikto scan (low intensity)...
2025-06-28 04:32:50,137 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Executing Nikto command...
2025-06-28 04:32:50,141 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Command: nikto -h http://*********** -Format txt -output /tmp/nikto_1751077970.txt -maxtime 300 -T 1
2025-06-28 04:32:50,151 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Scanning... (0s elapsed)
2025-06-28 04:32:52,133 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 35% - Testing for SQL injection... (20s elapsed)
2025-06-28 04:32:52,140 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 35% - Scanning directories... (20s elapsed)
2025-06-28 04:32:57,141 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 38% - Testing for SQL injection... (25s elapsed)
2025-06-28 04:32:57,147 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 38% - Scanning directories... (25s elapsed)
2025-06-28 04:32:58,142 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): HTTP request failed: HTTPConnectionPool(host='***********', port=80): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x7ddff35e8440>, 'Connection to *********** timed out. (connect timeout=10)'))
2025-06-28 04:32:58,147 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - ZAP: 100% - Security scan completed
2025-06-28 04:32:58,151 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Found 1 security alerts
2025-06-28 04:32:58,159 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - ✅ TOOL RESULT - ZAP: completed
2025-06-28 04:33:00,154 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - NIKTO: 28% - Scanning... (10s elapsed)
2025-06-28 04:33:02,148 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 41% - Testing for SQL injection... (30s elapsed)
2025-06-28 04:33:02,150 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 41% - Scanning directories... (30s elapsed)
2025-06-28 04:33:07,152 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 43% - Testing for SQL injection... (35s elapsed)
2025-06-28 04:33:07,154 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 43% - Scanning directories... (35s elapsed)
2025-06-28 04:33:10,160 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - NIKTO: 32% - Scanning... (20s elapsed)
2025-06-28 04:33:12,163 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 46% - Scanning directories... (40s elapsed)
2025-06-28 04:33:12,164 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 46% - Testing for SQL injection... (40s elapsed)
2025-06-28 04:33:17,168 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 49% - Testing for SQL injection... (45s elapsed)
2025-06-28 04:33:17,169 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 49% - Scanning directories... (45s elapsed)
2025-06-28 04:33:20,164 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - NIKTO: 35% - Scanning... (30s elapsed)
2025-06-28 04:33:22,178 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 52% - Testing for SQL injection... (50s elapsed)
2025-06-28 04:33:22,178 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 52% - Scanning directories... (50s elapsed)
2025-06-28 04:33:27,185 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 54% - Testing for SQL injection... (55s elapsed)
2025-06-28 04:33:27,185 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 54% - Scanning directories... (55s elapsed)
2025-06-28 04:33:30,167 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - NIKTO: 39% - Scanning... (40s elapsed)
2025-06-28 04:33:32,193 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 57% - Testing for SQL injection... (60s elapsed)
2025-06-28 04:33:32,193 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 57% - Scanning directories... (60s elapsed)
2025-06-28 04:33:37,200 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 60% - Testing for SQL injection... (65s elapsed)
2025-06-28 04:33:37,201 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 60% - Scanning directories... (65s elapsed)
2025-06-28 04:33:40,170 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - NIKTO: 43% - Scanning... (50s elapsed)
2025-06-28 04:33:42,206 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 62% - Testing for SQL injection... (70s elapsed)
2025-06-28 04:33:42,207 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 62% - Scanning directories... (70s elapsed)
2025-06-28 04:33:47,215 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 65% - Testing for SQL injection... (75s elapsed)
2025-06-28 04:33:47,215 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 65% - Scanning directories... (75s elapsed)
2025-06-28 04:33:50,176 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - NIKTO: 46% - Scanning... (60s elapsed)
2025-06-28 04:33:52,222 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 68% - Testing for SQL injection... (80s elapsed)
2025-06-28 04:33:52,223 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 68% - Scanning directories... (80s elapsed)
2025-06-28 04:33:57,229 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 71% - Testing for SQL injection... (85s elapsed)
2025-06-28 04:33:57,230 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 71% - Scanning directories... (85s elapsed)
2025-06-28 04:34:00,181 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - NIKTO: 50% - Scanning... (70s elapsed)
2025-06-28 04:34:02,234 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 73% - Testing for SQL injection... (90s elapsed)
2025-06-28 04:34:02,236 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 73% - Scanning directories... (90s elapsed)
2025-06-28 04:34:07,242 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 76% - Testing for SQL injection... (95s elapsed)
2025-06-28 04:34:07,244 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 76% - Scanning directories... (95s elapsed)
2025-06-28 04:34:10,185 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - NIKTO: 53% - Scanning... (80s elapsed)
2025-06-28 04:34:12,248 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 79% - Testing for SQL injection... (100s elapsed)
2025-06-28 04:34:12,249 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 79% - Scanning directories... (100s elapsed)
2025-06-28 04:34:17,257 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 81% - Testing for SQL injection... (105s elapsed)
2025-06-28 04:34:17,259 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 81% - Scanning directories... (105s elapsed)
2025-06-28 04:34:20,196 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - NIKTO: 90% - Parsing results...
2025-06-28 04:34:20,200 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - NIKTO: 100% - Nikto scan completed
2025-06-28 04:34:20,202 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Scan completed - Found 0 potential issues
2025-06-28 04:34:20,208 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - ✅ TOOL RESULT - NIKTO: completed - Found 0 vulnerabilities
2025-06-28 04:34:22,265 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 84% - Testing for SQL injection... (110s elapsed)
2025-06-28 04:34:22,266 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 84% - Scanning directories... (110s elapsed)
2025-06-28 04:34:27,273 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 87% - Testing for SQL injection... (115s elapsed)
2025-06-28 04:34:27,275 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 87% - Scanning directories... (115s elapsed)
2025-06-28 04:34:37,284 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Scan timeout after 2 minutes - completing with current results
2025-06-28 04:34:37,290 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - SQLMAP: 90% - Parsing results...
2025-06-28 04:34:37,294 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 📊 TOOL PROGRESS - DIRB: 90% - Parsing results...
2025-06-28 04:34:37,313 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - ✅ TOOL RESULT - SQLMAP: completed - Found 0 vulnerabilities
2025-06-28 04:34:37,317 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - ✅ TOOL RESULT - DIRB: completed - Found 0 directories
2025-06-28 04:34:37,326 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'gobuster': {'status': 'completed', 'directories': [], 'raw_output': '', 'found_paths': 0}, 'zap': {'status': 'completed', 'alerts': [{'risk': 'Medium', 'name': 'Connection Issue', 'count': 1, 'description': "Could not analyze target: HTTPConnectionPool(host='***********', port=80): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x7ddff35e8440>, 'Connection to *********** timed out. (connect timeout=10)'))"}], 'scan_time': '10 seconds', 'raw_output': 'Basic security scan completed. Found 1 potential issues.'}, 'nikto': {'status': 'completed', 'vulnerabilities': [], 'scan_time': '90 seconds', 'total_tests': 2, 'raw_output': '- Nikto v2.1.5/2.1.5\n'}, 'sqlmap': {'status': 'completed', 'injections': [], 'raw_output': "        ___\n       __H__\n ___ ___[,]_____ ___ ___  {1.8.4#stable}\n|_ -| . [']     | .'| . |\n|___|_  [(]_|_|_|__,|  _|\n      |_|V...       |_|   https://sqlmap.org\n\n[!] legal disclaimer: Usage of sqlmap for attacking targets without prior mutual consent is illegal. It is the end user's responsibility to obey all applicable local, state and federal laws. Developers assume no liability and are not responsible for any misuse or damage caused by this program\n\n[*] starting @ 04:32:32 /2025-06-28/\n\n[04:32:33] [INFO] testing connection to the target URL\n[04:33:03] [CRITICAL] connection timed out to the target URL. sqlmap is going to retry the request(s)\n[04:33:03] [WARNING] if the problem persists please check that the provided target URL is reachable. In case that it is, you can try to rerun with switch '--random-agent' and/or proxy switches ('--proxy', '--proxy-file'...)\n[04:34:33] [CRITICAL] connection timed out to the target URL\n[04:34:33] [WARNING] your sqlmap version is outdated\n\n[*] ending @ 04:34:33 /2025-06-28/\n\n", 'vulnerabilities': []}, 'dirb': {'status': 'completed', 'directories': [], 'raw_output': '\n-----------------\nDIRB v2.22    \nBy The Dark Raver\n-----------------\n\nSTART_TIME: Sat Jun 28 04:32:32 2025\nURL_BASE: http://***********/\nWORDLIST_FILES: /usr/share/dirb/wordlists/small.txt\nOPTION: Not Stopping on warning messages\n\n-----------------\n\n*** Generating Wordlist...\n                                                                               \nGENERATED WORDS: 959\n\n---- Scanning URL: http://***********/ ----\n*** Calculating NOT_FOUND code...\n', 'found_paths': 0}}
2025-06-28 04:34:37,332 - scan_d4b8db8c-ae97-4710-a293-11917de3a8e3 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 125.3s
