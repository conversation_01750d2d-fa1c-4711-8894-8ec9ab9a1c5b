2025-06-28 03:02:16,456 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 🚀 SCAN STARTED - Category: vulnerability, Type: basic, Target: *************, Tools: openvas, metasploit
2025-06-28 03:02:16,460 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 🚀 SCAN STARTED - Category: vulnerability, Type: basic, Target: *************, Tools: openvas, metasploit
2025-06-28 03:02:16,470 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on *************
2025-06-28 03:02:16,471 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on *************
2025-06-28 03:02:16,475 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-28 03:02:16,477 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-28 03:02:16,478 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Intensity: low, Timeout: 300s
2025-06-28 03:02:16,480 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Intensity: low, Timeout: 300s
2025-06-28 03:02:16,482 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS vulnerability scan...
2025-06-28 03:02:16,484 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit exploitation scan...
2025-06-28 03:02:19,489 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Metasploit scanning... 10% complete
2025-06-28 03:02:21,487 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - OpenVAS vulnerability scanning... 10% complete
2025-06-28 03:02:22,498 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 25% - Metasploit scanning... 25% complete
2025-06-28 03:02:25,506 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 40% - Metasploit scanning... 40% complete
2025-06-28 03:02:26,495 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - OPENVAS: 20% - OpenVAS vulnerability scanning... 20% complete
2025-06-28 03:02:28,514 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 55% - Metasploit scanning... 55% complete
2025-06-28 03:02:31,503 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS vulnerability scanning... 30% complete
2025-06-28 03:02:31,522 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 70% - Metasploit scanning... 70% complete
2025-06-28 03:02:34,530 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 85% - Metasploit scanning... 85% complete
2025-06-28 03:02:34,535 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-28 03:02:34,539 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-28 03:02:34,544 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-28 03:02:36,510 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - OPENVAS: 40% - OpenVAS vulnerability scanning... 40% complete
2025-06-28 03:02:41,520 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - OPENVAS: 50% - OpenVAS vulnerability scanning... 50% complete
2025-06-28 03:02:46,526 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - OPENVAS: 60% - OpenVAS vulnerability scanning... 60% complete
2025-06-28 03:02:51,530 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS vulnerability scanning... 70% complete
2025-06-28 03:02:56,534 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - OPENVAS: 80% - OpenVAS vulnerability scanning... 80% complete
2025-06-28 03:02:56,537 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-28 03:02:56,583 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-28 03:02:58,192 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-28 03:02:58,195 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_ba42b1ae-078d-4ba8-992e-73b4a1d2db88
2025-06-28 03:02:58,200 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 4 vulnerabilities
2025-06-28 03:04:03,953 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-28 03:04:03,958 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-28 03:04:39,520 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-28 03:04:39,524 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-28 03:05:24,338 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-28 03:05:24,343 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-28 03:06:06,357 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-28 03:06:06,363 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 4 modules - Found 0 potential vulnerabilities
2025-06-28 03:06:06,370 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-28 03:06:06,392 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'tools': {'openvas': {'status': 'completed', 'scan_id': 'ba42b1ae-078d-4ba8-992e-73b4a1d2db88', 'task_id': 'greenbone_task_ba42b1ae-078d-4ba8-992e-73b4a1d2db88', 'target_id': 'greenbone_target_ba42b1ae-078d-4ba8-992e-73b4a1d2db88', 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'POP3 Service Detected', 'severity': 'medium', 'description': 'POP3 service - unencrypted email protocol', 'port': 110, 'solution': 'Review POP3 service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'IMAP Service Detected', 'severity': 'medium', 'description': 'IMAP service - check for security configuration', 'port': 143, 'solution': 'Review IMAP service configuration and security settings', 'source_tool': 'openvas'}], 'message': 'Greenbone scan completed for *************', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/portscan/tcp', 'auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 4, 'raw_output': "=== TCP Port Scanner ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0mPORTS => 1-1000\n\x1b[0m\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:22 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:21 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:53 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:80 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:106 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:110 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:143 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:443 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:465 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:993 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:995 - TCP OPEN\n\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ************* - Key Fingerprint: ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIOpTVWZ/k5DUxBH0Ce/hiLXvstsiteakMsWsfmVk2i/j\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - SSH server version: SSH-2.0-OpenSSH_9.6p1 Ubuntu-3ubuntu13.12\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - Server Information and Encryption\n=================================\n\n  Type                     Value                                 Note\n  ----                     -----                                 ----\n  encryption.compression   none\n  encryption.compression   <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.encryption    aes128-ctr\n  encryption.encryption    aes192-ctr\n  encryption.encryption    aes256-ctr\n  encryption.encryption    <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          hmac-sha2-256\n  encryption.hmac          hmac-sha2-512\n  encryption.hmac          hmac-sha1\n  encryption.host_key      rsa-sha2-512\n  encryption.host_key      rsa-sha2-256\n  encryption.host_key      ecdsa-sha2-nistp256                   Weak elliptic curve\n  encryption.host_key      ssh-ed25519\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  curve25519-sha256\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  ecdh-sha2-nistp256\n  encryption.key_exchange  ecdh-sha2-nistp384\n  encryption.key_exchange  ecdh-sha2-nistp521\n  encryption.key_exchange  diffie-hellman-group-exchange-sha256\n  encryption.key_exchange  diffie-hellman-group16-sha512\n  encryption.key_exchange  diffie-hellman-group18-sha512\n  encryption.key_exchange  diffie-hellman-group14-sha256\n  encryption.key_exchange  ext-info-s\n  encryption.key_exchange  <EMAIL>\n  fingerprint_db           ssh.banner\n  openssh.comment          Ubuntu-3ubuntu13.12\n  os.certainty             0.75\n  os.cpe23                 cpe:/o:canonical:ubuntu_linux:-\n  os.family                Linux\n  os.product               Linux\n  os.vendor                Ubuntu\n  service.cpe23            cpe:/a:openbsd:openssh:9.6p1\n  service.family           OpenSSH\n  service.product          OpenSSH\n  service.protocol         ssh\n  service.vendor           OpenBSD\n  service.version          9.6p1\n\n\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m *************:445     - Rex::ConnectionRefused: The connection was refused by the remote host (*************:445).\n\x1b[1m\x1b[34m[*]\x1b[0m *************:445     - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'POP3 Service Detected', 'severity': 'medium', 'description': 'POP3 service - unencrypted email protocol', 'port': 110, 'solution': 'Review POP3 service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'IMAP Service Detected', 'severity': 'medium', 'description': 'IMAP service - check for security configuration', 'port': 143, 'solution': 'Review IMAP service configuration and security settings', 'source_tool': 'openvas'}], 'ports': [], 'summary': {'total_vulnerabilities': 4, 'critical_severity': 0, 'high_severity': 0, 'medium_severity': 4, 'low_severity': 0, 'total_ports_scanned': 0, 'tools_executed': 2}}
2025-06-28 03:06:06,400 - scan_65090c4f-a98a-47f7-b664-3e44593afa54 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 229.9s
