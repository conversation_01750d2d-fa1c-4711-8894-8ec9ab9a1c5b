2025-06-28 04:39:30,730 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 🚀 SCAN STARTED - Category: vulnerability, Type: basic, Target: ***********, Tools: openvas, metasploit
2025-06-28 04:39:30,732 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 🚀 SCAN STARTED - Category: vulnerability, Type: basic, Target: ***********, Tools: openvas, metasploit
2025-06-28 04:39:30,738 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on ***********
2025-06-28 04:39:30,739 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on ***********
2025-06-28 04:39:30,741 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-28 04:39:30,742 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-28 04:39:30,744 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Intensity: low, Timeout: 300s
2025-06-28 04:39:30,745 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Intensity: low, Timeout: 300s
2025-06-28 04:39:30,747 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS vulnerability scan...
2025-06-28 04:39:30,748 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit exploitation scan...
2025-06-28 04:39:33,751 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Metasploit scanning... 10% complete
2025-06-28 04:39:35,751 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - OpenVAS vulnerability scanning... 10% complete
2025-06-28 04:39:36,757 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 25% - Metasploit scanning... 25% complete
2025-06-28 04:39:39,762 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 40% - Metasploit scanning... 40% complete
2025-06-28 04:39:40,758 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - OPENVAS: 20% - OpenVAS vulnerability scanning... 20% complete
2025-06-28 04:39:42,767 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 55% - Metasploit scanning... 55% complete
2025-06-28 04:39:45,763 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS vulnerability scanning... 30% complete
2025-06-28 04:39:45,771 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 70% - Metasploit scanning... 70% complete
2025-06-28 04:39:48,778 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 85% - Metasploit scanning... 85% complete
2025-06-28 04:39:48,782 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-28 04:39:48,785 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-28 04:39:48,787 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-28 04:39:50,769 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - OPENVAS: 40% - OpenVAS vulnerability scanning... 40% complete
2025-06-28 04:39:55,773 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - OPENVAS: 50% - OpenVAS vulnerability scanning... 50% complete
2025-06-28 04:40:00,778 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - OPENVAS: 60% - OpenVAS vulnerability scanning... 60% complete
2025-06-28 04:40:05,782 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS vulnerability scanning... 70% complete
2025-06-28 04:40:10,786 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - OPENVAS: 80% - OpenVAS vulnerability scanning... 80% complete
2025-06-28 04:40:10,791 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-28 04:40:11,028 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-28 04:40:35,060 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-28 04:40:35,065 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_fb05e260-931d-49cd-a3f3-01305cf99f6c
2025-06-28 04:40:35,070 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 0 vulnerabilities
2025-06-28 04:41:48,892 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - ERROR - ❌ TOOL ERROR - METASPLOIT: Timeout running TCP Port Scanner
2025-06-28 04:41:48,903 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-28 04:41:48,910 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-28 04:42:41,572 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-28 04:42:41,578 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-28 04:43:43,555 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-28 04:43:43,560 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-28 04:44:18,600 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-28 04:44:18,604 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 3 modules - Found 0 potential vulnerabilities
2025-06-28 04:44:18,611 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-28 04:44:18,622 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'tools': {'openvas': {'status': 'completed', 'scan_id': 'fb05e260-931d-49cd-a3f3-01305cf99f6c', 'task_id': 'greenbone_task_fb05e260-931d-49cd-a3f3-01305cf99f6c', 'target_id': 'greenbone_target_fb05e260-931d-49cd-a3f3-01305cf99f6c', 'vulnerabilities': [], 'message': 'Greenbone scan completed for ***********', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 3, 'raw_output': "=== TCP Port Scanner ===\nTimeout after 120 seconds\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ***********           - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m ***********:445       - Rex::ConnectionTimeout: The connection with (***********:445) timed out.\n\x1b[1m\x1b[34m[*]\x1b[0m ***********:445       - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'vulnerabilities': [], 'ports': [], 'summary': {'total_vulnerabilities': 0, 'critical_severity': 0, 'high_severity': 0, 'medium_severity': 0, 'low_severity': 0, 'total_ports_scanned': 0, 'tools_executed': 2}}
2025-06-28 04:44:18,625 - scan_276ecc63-36ef-42aa-bb7b-21db51bf3238 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 287.9s
