2025-06-25 22:09:06,753 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: *************, Tools: nmap, openvas, metasploit
2025-06-25 22:09:06,757 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: *************, Tools: nmap, openvas, metasploit
2025-06-25 22:09:06,765 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on *************
2025-06-25 22:09:06,767 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on *************
2025-06-25 22:09:06,767 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on *************
2025-06-25 22:09:06,773 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan...
2025-06-25 22:09:06,777 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-25 22:09:06,777 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-25 22:09:06,785 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-25 22:09:06,790 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-25 22:09:06,806 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-06-25 22:09:06,834 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-25 22:09:07,486 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-25 22:09:07,489 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_c70a4621-a63c-45d7-9c01-cf1df36cc2cc
2025-06-25 22:09:07,493 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 5 vulnerabilities
2025-06-25 22:09:26,012 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-06-25 22:09:26,016 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Scan completed: /usr/bin/nmap -sT -sV --version-intensity 5 -p 1-1000 -oX /tmp/nmap_scan_5b46b9e9-ea11-4eae-b26c-2ac4b9f55787.xml *************
2025-06-25 22:09:26,021 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Found 11 ports
2025-06-25 22:09:26,027 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - INFO - ✅ TOOL RESULT - NMAP: completed - Found 11 ports - Found 0 vulnerabilities
2025-06-25 22:09:48,727 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-25 22:09:48,733 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-25 22:10:26,489 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-25 22:10:26,495 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-25 22:11:06,691 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-25 22:11:06,700 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-25 22:11:47,693 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-25 22:11:47,700 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 4 modules - Found 0 potential vulnerabilities
2025-06-25 22:11:47,709 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-25 22:11:47,723 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'tools': {'openvas': {'status': 'completed', 'scan_id': 'c70a4621-a63c-45d7-9c01-cf1df36cc2cc', 'task_id': 'greenbone_task_c70a4621-a63c-45d7-9c01-cf1df36cc2cc', 'target_id': 'greenbone_target_c70a4621-a63c-45d7-9c01-cf1df36cc2cc', 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings'}, {'name': 'SMTP Service Detected', 'severity': 'medium', 'description': 'SMTP service - check for open relay', 'port': 25, 'solution': 'Review SMTP service configuration and security settings'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings'}, {'name': 'POP3 Service Detected', 'severity': 'medium', 'description': 'POP3 service - unencrypted email protocol', 'port': 110, 'solution': 'Review POP3 service configuration and security settings'}, {'name': 'IMAP Service Detected', 'severity': 'medium', 'description': 'IMAP service - check for security configuration', 'port': 143, 'solution': 'Review IMAP service configuration and security settings'}], 'message': 'Greenbone scan completed for *************', 'scan_type': 'gmp_vulnerability_scan'}, 'nmap': {'status': 'completed', 'scan_id': '5b46b9e9-ea11-4eae-b26c-2ac4b9f55787', 'target': '*************', 'start_time': '2025-06-25T20:09:06.810681', 'end_time': '2025-06-25T20:09:26.012539', 'scan_time': 19.201858, 'command': '/usr/bin/nmap -sT -sV --version-intensity 5 -p 1-1000 -oX /tmp/nmap_scan_5b46b9e9-ea11-4eae-b26c-2ac4b9f55787.xml *************', 'ports': [{'port': 21, 'protocol': 'tcp', 'state': 'open', 'service': 'ftp', 'version': 'ProFTPD'}, {'port': 22, 'protocol': 'tcp', 'state': 'open', 'service': 'ssh', 'version': 'OpenSSH 9.6p1 Ubuntu 3ubuntu13.12 (Ubuntu Linux; protocol 2.0)'}, {'port': 25, 'protocol': 'tcp', 'state': 'open', 'service': 'smtp', 'version': 'Postfix smtpd'}, {'port': 53, 'protocol': 'tcp', 'state': 'open', 'service': 'domain', 'version': '(unknown banner: none)'}, {'port': 80, 'protocol': 'tcp', 'state': 'open', 'service': 'http?'}, {'port': 106, 'protocol': 'tcp', 'state': 'open', 'service': 'tcpwrapped'}, {'port': 110, 'protocol': 'tcp', 'state': 'open', 'service': 'pop3', 'version': 'Dovecot pop3d'}, {'port': 143, 'protocol': 'tcp', 'state': 'open', 'service': 'imap', 'version': 'Dovecot imapd'}, {'port': 465, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/smtp', 'version': 'Postfix smtpd'}, {'port': 993, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/imap', 'version': 'Dovecot imapd'}, {'port': 995, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/pop3', 'version': 'Dovecot pop3d'}], 'vulnerabilities': [], 'raw_output': 'Starting Nmap 7.94SVN ( https://nmap.org ) at 2025-06-25 22:09 CEST\nNmap scan report for ip82-165-144-72.pbiaas.com (*************)\nHost is up (0.054s latency).\nNot shown: 989 closed tcp ports (conn-refused)\nPORT    STATE SERVICE    VERSION\n21/tcp  open  ftp        ProFTPD\n22/tcp  open  ssh        OpenSSH 9.6p1 Ubuntu 3ubuntu13.12 (Ubuntu Linux; protocol 2.0)\n25/tcp  open  smtp       Postfix smtpd\n53/tcp  open  domain     (unknown banner: none)\n80/tcp  open  http?\n106/tcp open  tcpwrapped\n110/tcp open  pop3       Dovecot pop3d\n143/tcp open  imap       Dovecot imapd\n465/tcp open  ssl/smtp   Postfix smtpd\n993/tcp open  ssl/imap   Dovecot imapd\n995/tcp open  ssl/pop3   Dovecot pop3d\n1 service unrecognized despite returning data. If you know the service/version, please submit the following fingerprint at https://nmap.org/cgi-bin/submit.cgi?new-service :\nSF-Port80-TCP:V=7.94SVN%I=5%D=6/25%Time=685C576B%P=x86_64-pc-linux-gnu%r(G\nSF:etRequest,802,"HTTP/1\\.1\\x20200\\x20OK\\r\\nCache-Control:\\x20no-store,\\x2\nSF:0must-revalidate\\r\\nX-Powered-By:\\x20Next\\.js\\r\\nETag:\\x20\\"reqxo6oq091\nSF:e2\\"\\r\\nContent-Type:\\x20text/html;\\x20charset=utf-8\\r\\nContent-Length:\nSF:\\x201802\\r\\nVary:\\x20Accept-Encoding\\r\\nDate:\\x20Wed,\\x2025\\x20Jun\\x202\nSF:025\\x2020:09:15\\x20GMT\\r\\nConnection:\\x20close\\r\\n\\r\\n<!DOCTYPE\\x20html\nSF:><html\\x20lang=\\"en\\"><head><style\\x20data-next-hide-fouc=\\"true\\">body\nSF:{display:none}</style><noscript\\x20data-next-hide-fouc=\\"true\\"><style>\nSF:body{display:block}</style></noscript><meta\\x20charSet=\\"utf-8\\"/><meta\nSF:\\x20name=\\"viewport\\"\\x20content=\\"width=device-width\\"/><meta\\x20name=\nSF:\\"next-head-count\\"\\x20content=\\"2\\"/><link\\x20data-next-font=\\"size-ad\nSF:just\\"\\x20rel=\\"preconnect\\"\\x20href=\\"/\\"\\x20crossorigin=\\"anonymous\\"\nSF:/><noscript\\x20data-n-css=\\"\\"></noscript><script\\x20defer=\\"\\"\\x20nomo\nSF:dule=\\"\\"\\x20src=\\"/_next/static/chunks/polyfills\\.js\\?ts=1750882155929\nSF:\\"></script><script\\x20src=\\"/_next/static/chunks/webpack\\.js\\?ts=17508\nSF:82155929\\"\\x20defer=\\"\\"></script><script\\x20src=\\"/_next/static/chunks\nSF:/main\\.js\\?ts=1750882155929\\"")%r(HTTPOptions,803,"HTTP/1\\.1\\x20200\\x20\nSF:OK\\r\\nCache-Control:\\x20no-store,\\x20must-revalidate\\r\\nX-Powered-By:\\x\nSF:20Next\\.js\\r\\nETag:\\x20\\"120jcrumsa41e2\\"\\r\\nContent-Type:\\x20text/html\nSF:;\\x20charset=utf-8\\r\\nContent-Length:\\x201802\\r\\nVary:\\x20Accept-Encodi\nSF:ng\\r\\nDate:\\x20Wed,\\x2025\\x20Jun\\x202025\\x2020:09:16\\x20GMT\\r\\nConnecti\nSF:on:\\x20close\\r\\n\\r\\n<!DOCTYPE\\x20html><html\\x20lang=\\"en\\"><head><style\nSF:\\x20data-next-hide-fouc=\\"true\\">body{display:none}</style><noscript\\x2\nSF:0data-next-hide-fouc=\\"true\\"><style>body{display:block}</style></noscr\nSF:ipt><meta\\x20charSet=\\"utf-8\\"/><meta\\x20name=\\"viewport\\"\\x20content=\\\nSF:"width=device-width\\"/><meta\\x20name=\\"next-head-count\\"\\x20content=\\"2\nSF:\\"/><link\\x20data-next-font=\\"size-adjust\\"\\x20rel=\\"preconnect\\"\\x20hr\nSF:ef=\\"/\\"\\x20crossorigin=\\"anonymous\\"/><noscript\\x20data-n-css=\\"\\"></n\nSF:oscript><script\\x20defer=\\"\\"\\x20nomodule=\\"\\"\\x20src=\\"/_next/static/c\nSF:hunks/polyfills\\.js\\?ts=1750882156078\\"></script><script\\x20src=\\"/_nex\nSF:t/static/chunks/webpack\\.js\\?ts=1750882156078\\"\\x20defer=\\"\\"></script>\nSF:<script\\x20src=\\"/_next/static/chunks/main\\.js\\?ts=1750882156078");\nService Info: Hosts:  dazzling-kilby.82-165-144-72.plesk.page, dazzling-kilby.82-165-144-72.plesk.page; OS: Linux; CPE: cpe:/o:linux:linux_kernel\n\nService detection performed. Please report any incorrect results at https://nmap.org/submit/ .\nNmap done: 1 IP address (1 host up) scanned in 19.18 seconds\n'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/portscan/tcp', 'auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 4, 'raw_output': "=== TCP Port Scanner ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0mPORTS => 1-1000\n\x1b[0m\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:21 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:25 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:22 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:53 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:80 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:106 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:110 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:143 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:465 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:995 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:993 - TCP OPEN\n\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ************* - Key Fingerprint: ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIOpTVWZ/k5DUxBH0Ce/hiLXvstsiteakMsWsfmVk2i/j\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - SSH server version: SSH-2.0-OpenSSH_9.6p1 Ubuntu-3ubuntu13.12\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - Server Information and Encryption\n=================================\n\n  Type                     Value                                 Note\n  ----                     -----                                 ----\n  encryption.compression   none\n  encryption.compression   <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.encryption    aes128-ctr\n  encryption.encryption    aes192-ctr\n  encryption.encryption    aes256-ctr\n  encryption.encryption    <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          hmac-sha2-256\n  encryption.hmac          hmac-sha2-512\n  encryption.hmac          hmac-sha1\n  encryption.host_key      rsa-sha2-512\n  encryption.host_key      rsa-sha2-256\n  encryption.host_key      ecdsa-sha2-nistp256                   Weak elliptic curve\n  encryption.host_key      ssh-ed25519\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  curve25519-sha256\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  ecdh-sha2-nistp256\n  encryption.key_exchange  ecdh-sha2-nistp384\n  encryption.key_exchange  ecdh-sha2-nistp521\n  encryption.key_exchange  diffie-hellman-group-exchange-sha256\n  encryption.key_exchange  diffie-hellman-group16-sha512\n  encryption.key_exchange  diffie-hellman-group18-sha512\n  encryption.key_exchange  diffie-hellman-group14-sha256\n  encryption.key_exchange  ext-info-s\n  encryption.key_exchange  <EMAIL>\n  fingerprint_db           ssh.banner\n  openssh.comment          Ubuntu-3ubuntu13.12\n  os.certainty             0.75\n  os.cpe23                 cpe:/o:canonical:ubuntu_linux:-\n  os.family                Linux\n  os.product               Linux\n  os.vendor                Ubuntu\n  service.cpe23            cpe:/a:openbsd:openssh:9.6p1\n  service.family           OpenSSH\n  service.product          OpenSSH\n  service.protocol         ssh\n  service.vendor           OpenBSD\n  service.version          9.6p1\n\n\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m *************:445     - Rex::ConnectionRefused: The connection was refused by the remote host (*************:445).\n\x1b[1m\x1b[34m[*]\x1b[0m *************:445     - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'ports': [{'port': 21, 'protocol': 'tcp', 'state': 'open', 'service': 'ftp', 'version': 'ProFTPD'}, {'port': 22, 'protocol': 'tcp', 'state': 'open', 'service': 'ssh', 'version': 'OpenSSH 9.6p1 Ubuntu 3ubuntu13.12 (Ubuntu Linux; protocol 2.0)'}, {'port': 25, 'protocol': 'tcp', 'state': 'open', 'service': 'smtp', 'version': 'Postfix smtpd'}, {'port': 53, 'protocol': 'tcp', 'state': 'open', 'service': 'domain', 'version': '(unknown banner: none)'}, {'port': 80, 'protocol': 'tcp', 'state': 'open', 'service': 'http?'}, {'port': 106, 'protocol': 'tcp', 'state': 'open', 'service': 'tcpwrapped'}, {'port': 110, 'protocol': 'tcp', 'state': 'open', 'service': 'pop3', 'version': 'Dovecot pop3d'}, {'port': 143, 'protocol': 'tcp', 'state': 'open', 'service': 'imap', 'version': 'Dovecot imapd'}, {'port': 465, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/smtp', 'version': 'Postfix smtpd'}, {'port': 993, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/imap', 'version': 'Dovecot imapd'}, {'port': 995, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/pop3', 'version': 'Dovecot pop3d'}], 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings'}, {'name': 'SMTP Service Detected', 'severity': 'medium', 'description': 'SMTP service - check for open relay', 'port': 25, 'solution': 'Review SMTP service configuration and security settings'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings'}, {'name': 'POP3 Service Detected', 'severity': 'medium', 'description': 'POP3 service - unencrypted email protocol', 'port': 110, 'solution': 'Review POP3 service configuration and security settings'}, {'name': 'IMAP Service Detected', 'severity': 'medium', 'description': 'IMAP service - check for security configuration', 'port': 143, 'solution': 'Review IMAP service configuration and security settings'}], 'summary': {'total_ports': 11, 'open_ports': 11, 'total_vulnerabilities': 5}}
2025-06-25 22:11:47,734 - scan_cafeb20d-757a-496e-9a59-07b9b046d497 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 161.0s
