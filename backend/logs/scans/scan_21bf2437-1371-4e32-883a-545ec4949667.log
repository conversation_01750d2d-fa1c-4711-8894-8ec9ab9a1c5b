2025-06-26 19:02:07,696 - scan_21bf2437-1371-4e32-883a-545ec4949667 - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: ***********, Tools: nmap, openvas, metasploit
2025-06-26 19:02:07,699 - scan_21bf2437-1371-4e32-883a-545ec4949667 - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: ***********, Tools: nmap, openvas, metasploit
2025-06-26 19:02:07,706 - scan_21bf2437-1371-4e32-883a-545ec4949667 - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on ***********
2025-06-26 19:02:07,707 - scan_21bf2437-1371-4e32-883a-545ec4949667 - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on ***********
2025-06-26 19:02:07,707 - scan_21bf2437-1371-4e32-883a-545ec4949667 - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on ***********
2025-06-26 19:02:07,718 - scan_21bf2437-1371-4e32-883a-545ec4949667 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-26 19:02:07,721 - scan_21bf2437-1371-4e32-883a-545ec4949667 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-26 19:02:07,722 - scan_21bf2437-1371-4e32-883a-545ec4949667 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan...
2025-06-26 19:02:07,729 - scan_21bf2437-1371-4e32-883a-545ec4949667 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-26 19:02:07,732 - scan_21bf2437-1371-4e32-883a-545ec4949667 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-26 19:02:07,755 - scan_21bf2437-1371-4e32-883a-545ec4949667 - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-06-26 19:02:07,943 - scan_21bf2437-1371-4e32-883a-545ec4949667 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-26 19:02:08,008 - scan_21bf2437-1371-4e32-883a-545ec4949667 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-26 19:02:08,010 - scan_21bf2437-1371-4e32-883a-545ec4949667 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_42f4ec3c-8bb8-4025-96ff-9ea0f47ec2c6
2025-06-26 19:02:08,013 - scan_21bf2437-1371-4e32-883a-545ec4949667 - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 3 vulnerabilities
2025-06-26 19:02:40,201 - scan_21bf2437-1371-4e32-883a-545ec4949667 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-26 19:02:40,205 - scan_21bf2437-1371-4e32-883a-545ec4949667 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-26 19:02:55,736 - scan_21bf2437-1371-4e32-883a-545ec4949667 - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-06-26 19:02:55,743 - scan_21bf2437-1371-4e32-883a-545ec4949667 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Scan completed: /usr/bin/nmap -sT -sV --version-intensity 5 -p 1-1000 -oX /tmp/nmap_scan_4c02eaf3-4052-4549-99dd-f35e3d05c090.xml ***********
2025-06-26 19:02:55,745 - scan_21bf2437-1371-4e32-883a-545ec4949667 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Found 5 ports
2025-06-26 19:02:55,748 - scan_21bf2437-1371-4e32-883a-545ec4949667 - INFO - ✅ TOOL RESULT - NMAP: completed - Found 5 ports - Found 0 vulnerabilities
2025-06-26 19:03:08,878 - scan_21bf2437-1371-4e32-883a-545ec4949667 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-26 19:03:08,883 - scan_21bf2437-1371-4e32-883a-545ec4949667 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-26 19:03:43,006 - scan_21bf2437-1371-4e32-883a-545ec4949667 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-26 19:03:43,010 - scan_21bf2437-1371-4e32-883a-545ec4949667 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-26 19:04:12,592 - scan_21bf2437-1371-4e32-883a-545ec4949667 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-26 19:04:12,595 - scan_21bf2437-1371-4e32-883a-545ec4949667 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 4 modules - Found 0 potential vulnerabilities
2025-06-26 19:04:12,599 - scan_21bf2437-1371-4e32-883a-545ec4949667 - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-26 19:04:12,611 - scan_21bf2437-1371-4e32-883a-545ec4949667 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'tools': {'openvas': {'status': 'completed', 'scan_id': '42f4ec3c-8bb8-4025-96ff-9ea0f47ec2c6', 'task_id': 'greenbone_task_42f4ec3c-8bb8-4025-96ff-9ea0f47ec2c6', 'target_id': 'greenbone_target_42f4ec3c-8bb8-4025-96ff-9ea0f47ec2c6', 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings'}, {'name': 'Telnet Service Detected', 'severity': 'high', 'description': 'Telnet service - unencrypted protocol', 'port': 23, 'solution': 'Review Telnet service configuration and security settings'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings'}], 'message': 'Greenbone scan completed for ***********', 'scan_type': 'gmp_vulnerability_scan'}, 'nmap': {'status': 'completed', 'scan_id': '4c02eaf3-4052-4549-99dd-f35e3d05c090', 'target': '***********', 'start_time': '2025-06-26T17:02:07.763314', 'end_time': '2025-06-26T17:02:55.736433', 'scan_time': 47.973119, 'command': '/usr/bin/nmap -sT -sV --version-intensity 5 -p 1-1000 -oX /tmp/nmap_scan_4c02eaf3-4052-4549-99dd-f35e3d05c090.xml ***********', 'ports': [{'port': 21, 'protocol': 'tcp', 'state': 'open', 'service': 'ftp'}, {'port': 23, 'protocol': 'tcp', 'state': 'open', 'service': 'tcpwrapped'}, {'port': 53, 'protocol': 'tcp', 'state': 'open', 'service': 'domain', 'version': '(generic dns response: SERVFAIL)'}, {'port': 80, 'protocol': 'tcp', 'state': 'open', 'service': 'http?'}, {'port': 443, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/https?'}], 'vulnerabilities': [], 'raw_output': 'Starting Nmap 7.94SVN ( https://nmap.org ) at 2025-06-26 19:02 CEST\nNmap scan report for _gateway (***********)\nHost is up (0.0083s latency).\nNot shown: 995 closed tcp ports (conn-refused)\nPORT    STATE SERVICE    VERSION\n21/tcp  open  ftp\n23/tcp  open  tcpwrapped\n53/tcp  open  domain     (generic dns response: SERVFAIL)\n80/tcp  open  http?\n443/tcp open  ssl/https?\n1 service unrecognized despite returning data. If you know the service/version, please submit the following fingerprint at https://nmap.org/cgi-bin/submit.cgi?new-service :\nSF-Port80-TCP:V=7.94SVN%I=5%D=6/26%Time=685D7D17%P=x86_64-pc-linux-gnu%r(G\nSF:etRequest,110,"HTTP/1\\.1\\x20200\\x20OK\\r\\nServer:\\x20RTK\\x20Web\\x200\\.9\\\nSF:r\\nSet-Cookie:\\x20SessionID=;\\x20path=/\\r\\nContent-Type:\\x20text/html\\r\nSF:\\nContent-Length:\\x20151\\x20\\x20\\x20\\r\\n\\r\\n<html><head><meta\\x20HTTP-E\nSF:QUIV=\\"Pragma\\"\\x20CONTENT=\\"no-cache\\"><script\\x20language=\'javascript\nSF:\'>parent\\.location=\\"/login\\.htm\\"</script></head><body></body></html>"\nSF:)%r(HTTPOptions,ED,"HTTP/1\\.1\\x20400\\x20Bad\\x20Request\\r\\nConnection:\\x\nSF:20close\\r\\nServer:\\x20RTK\\x20Web\\x200\\.9\\r\\nX-Frame-Options:\\x20SAMEORI\nSF:GIN\\r\\n\\r\\n<HTML><HEAD><TITLE>400\\x20Bad\\x20Request\\r\\n</TITLE></HEAD>\\\nSF:r\\n<BODY><H1>Error:\\x20400\\x20Bad\\x20Request\\r\\n</H1>\\x20<P>The\\x20requ\nSF:ested\\x20Url\\x20is\\x20\\"\\"\\.</P>\\x20</BODY></HTML>\\n")%r(RTSPRequest,ED\nSF:,"HTTP/1\\.1\\x20400\\x20Bad\\x20Request\\r\\nConnection:\\x20close\\r\\nServer:\nSF:\\x20RTK\\x20Web\\x200\\.9\\r\\nX-Frame-Options:\\x20SAMEORIGIN\\r\\n\\r\\n<HTML><\nSF:HEAD><TITLE>400\\x20Bad\\x20Request\\r\\n</TITLE></HEAD>\\r\\n<BODY><H1>Error\nSF::\\x20400\\x20Bad\\x20Request\\r\\n</H1>\\x20<P>The\\x20requested\\x20Url\\x20is\nSF:\\x20\\"\\"\\.</P>\\x20</BODY></HTML>\\n")%r(FourOhFourRequest,110,"HTTP/1\\.1\nSF:\\x20200\\x20OK\\r\\nServer:\\x20RTK\\x20Web\\x200\\.9\\r\\nSet-Cookie:\\x20Sessio\nSF:nID=;\\x20path=/\\r\\nContent-Type:\\x20text/html\\r\\nContent-Length:\\x20151\nSF:\\x20\\x20\\x20\\r\\n\\r\\n<html><head><meta\\x20HTTP-EQUIV=\\"Pragma\\"\\x20CONTE\nSF:NT=\\"no-cache\\"><script\\x20language=\'javascript\'>parent\\.location=\\"/lo\nSF:gin\\.htm\\"</script></head><body></body></html>")%r(GenericLines,ED,"HTT\nSF:P/1\\.1\\x20400\\x20Bad\\x20Request\\r\\nConnection:\\x20close\\r\\nServer:\\x20R\nSF:TK\\x20Web\\x200\\.9\\r\\nX-Frame-Options:\\x20SAMEORIGIN\\r\\n\\r\\n<HTML><HEAD>\nSF:<TITLE>400\\x20Bad\\x20Request\\r\\n</TITLE></HEAD>\\r\\n<BODY><H1>Error:\\x20\nSF:400\\x20Bad\\x20Request\\r\\n</H1>\\x20<P>The\\x20requested\\x20Url\\x20is\\x20\\\nSF:"\\"\\.</P>\\x20</BODY></HTML>\\n")%r(SIPOptions,110,"HTTP/1\\.1\\x20200\\x20O\nSF:K\\r\\nServer:\\x20RTK\\x20Web\\x200\\.9\\r\\nSet-Cookie:\\x20SessionID=;\\x20pat\nSF:h=/\\r\\nContent-Type:\\x20text/html\\r\\nContent-Length:\\x20151\\x20\\x20\\x20\nSF:\\r\\n\\r\\n<html><head><meta\\x20HTTP-EQUIV=\\"Pragma\\"\\x20CONTENT=\\"no-cach\nSF:e\\"><script\\x20language=\'javascript\'>parent\\.location=\\"/login\\.htm\\"</\nSF:script></head><body></body></html>");\n\nService detection performed. Please report any incorrect results at https://nmap.org/submit/ .\nNmap done: 1 IP address (1 host up) scanned in 47.95 seconds\n'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/portscan/tcp', 'auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 4, 'raw_output': "=== TCP Port Scanner ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0mPORTS => 1-1000\n\x1b[0m\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:21 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:23 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:53 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:80 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:443 - TCP OPEN\n\x1b[1m\x1b[34m[*]\x1b[0m ***********           - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ***********           - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m Error: ***********: Errno::ECONNREFUSED Connection refused - connect(2) for ***********:22\n\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m ***********:445       - Rex::ConnectionRefused: The connection was refused by the remote host (***********:445).\n\x1b[1m\x1b[34m[*]\x1b[0m ***********:445       - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'ports': [{'port': 21, 'protocol': 'tcp', 'state': 'open', 'service': 'ftp'}, {'port': 23, 'protocol': 'tcp', 'state': 'open', 'service': 'tcpwrapped'}, {'port': 53, 'protocol': 'tcp', 'state': 'open', 'service': 'domain', 'version': '(generic dns response: SERVFAIL)'}, {'port': 80, 'protocol': 'tcp', 'state': 'open', 'service': 'http?'}, {'port': 443, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/https?'}], 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings'}, {'name': 'Telnet Service Detected', 'severity': 'high', 'description': 'Telnet service - unencrypted protocol', 'port': 23, 'solution': 'Review Telnet service configuration and security settings'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings'}], 'summary': {'total_ports': 5, 'open_ports': 5, 'total_vulnerabilities': 3}}
2025-06-26 19:04:12,614 - scan_21bf2437-1371-4e32-883a-545ec4949667 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 124.9s
