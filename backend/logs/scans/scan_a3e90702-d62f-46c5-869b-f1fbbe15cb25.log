2025-06-25 21:40:32,223 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: *************, Tools: nmap, openvas, metasploit
2025-06-25 21:40:32,230 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: *************, Tools: nmap, openvas, metasploit
2025-06-25 21:40:32,236 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on *************
2025-06-25 21:40:32,236 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on *************
2025-06-25 21:40:32,237 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on *************
2025-06-25 21:40:32,246 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan...
2025-06-25 21:40:32,248 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-25 21:40:32,253 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-25 21:40:32,256 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - ERROR - ❌ TOOL ERROR - OPENVAS: cannot import name 'OpenVASService' from 'app.services.openvas_service' (/home/<USER>/PICA_V1.0.01/backend/app/services/openvas_service.py)
2025-06-25 21:40:32,257 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-25 21:40:32,262 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-25 21:40:32,262 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - INFO - ✅ TOOL RESULT - OPENVAS: completed
2025-06-25 21:40:32,279 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-06-25 21:41:06,891 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-06-25 21:41:06,894 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Scan completed: /usr/bin/nmap -sT -sV --version-intensity 5 -p 1-1000 -oX /tmp/nmap_scan_c10dab7f-2d0b-43aa-9eab-29495d9560be.xml *************
2025-06-25 21:41:06,897 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Found 3 ports
2025-06-25 21:41:06,900 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - INFO - ✅ TOOL RESULT - NMAP: completed - Found 3 ports - Found 0 vulnerabilities
2025-06-25 21:41:46,784 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-25 21:41:46,793 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-25 21:42:37,767 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-25 21:42:37,771 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-25 21:43:14,979 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-25 21:43:14,982 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-25 21:43:50,464 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-25 21:43:50,466 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 4 modules - Found 0 potential vulnerabilities
2025-06-25 21:43:50,470 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-25 21:43:50,474 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'tools': {'openvas': {'status': 'failed', 'error': "cannot import name 'OpenVASService' from 'app.services.openvas_service' (/home/<USER>/PICA_V1.0.01/backend/app/services/openvas_service.py)"}, 'nmap': {'status': 'completed', 'scan_id': 'c10dab7f-2d0b-43aa-9eab-29495d9560be', 'target': '*************', 'start_time': '2025-06-25T19:40:32.283969', 'end_time': '2025-06-25T19:41:06.891352', 'scan_time': 34.607383, 'command': '/usr/bin/nmap -sT -sV --version-intensity 5 -p 1-1000 -oX /tmp/nmap_scan_c10dab7f-2d0b-43aa-9eab-29495d9560be.xml *************', 'ports': [{'port': 22, 'protocol': 'tcp', 'state': 'open', 'service': 'ssh', 'version': 'OpenSSH 9.6p1 Ubuntu 3ubuntu13.11 (Ubuntu Linux; protocol 2.0)'}, {'port': 80, 'protocol': 'tcp', 'state': 'open', 'service': 'http', 'version': 'Apache httpd 2.4.58 ((Ubuntu))'}, {'port': 443, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/http', 'version': 'Apache httpd 2.4.58 ((Ubuntu))'}], 'vulnerabilities': [], 'raw_output': 'Starting Nmap 7.94SVN ( https://nmap.org ) at 2025-06-25 21:40 CEST\nNmap scan report for vps-3811c29b.vps.ovh.ca (*************)\nHost is up (0.22s latency).\nNot shown: 997 closed tcp ports (conn-refused)\nPORT    STATE SERVICE  VERSION\n22/tcp  open  ssh      OpenSSH 9.6p1 Ubuntu 3ubuntu13.11 (Ubuntu Linux; protocol 2.0)\n80/tcp  open  http     Apache httpd 2.4.58 ((Ubuntu))\n443/tcp open  ssl/http Apache httpd 2.4.58 ((Ubuntu))\nService Info: OS: Linux; CPE: cpe:/o:linux:linux_kernel\n\nService detection performed. Please report any incorrect results at https://nmap.org/submit/ .\nNmap done: 1 IP address (1 host up) scanned in 34.58 seconds\n'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/portscan/tcp', 'auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 4, 'raw_output': "=== TCP Port Scanner ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0mPORTS => 1-1000\n\x1b[0m\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:22 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:80 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:443 - TCP OPEN\n\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ************* - Key Fingerprint: ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIJU7bXzWjVFCRr70wCRrw80liBHhsKANhaQHwAVEuPka\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - SSH server version: SSH-2.0-OpenSSH_9.6p1 Ubuntu-3ubuntu13.11\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - Server Information and Encryption\n=================================\n\n  Type                     Value                                 Note\n  ----                     -----                                 ----\n  encryption.compression   none\n  encryption.compression   <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.encryption    aes128-ctr\n  encryption.encryption    aes192-ctr\n  encryption.encryption    aes256-ctr\n  encryption.encryption    <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          hmac-sha2-256\n  encryption.hmac          hmac-sha2-512\n  encryption.hmac          hmac-sha1\n  encryption.host_key      rsa-sha2-512\n  encryption.host_key      rsa-sha2-256\n  encryption.host_key      ecdsa-sha2-nistp256                   Weak elliptic curve\n  encryption.host_key      ssh-ed25519\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  curve25519-sha256\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  ecdh-sha2-nistp256\n  encryption.key_exchange  ecdh-sha2-nistp384\n  encryption.key_exchange  ecdh-sha2-nistp521\n  encryption.key_exchange  diffie-hellman-group-exchange-sha256\n  encryption.key_exchange  diffie-hellman-group16-sha512\n  encryption.key_exchange  diffie-hellman-group18-sha512\n  encryption.key_exchange  diffie-hellman-group14-sha256\n  encryption.key_exchange  ext-info-s\n  encryption.key_exchange  <EMAIL>\n  fingerprint_db           ssh.banner\n  openssh.comment          Ubuntu-3ubuntu13.11\n  os.certainty             0.75\n  os.cpe23                 cpe:/o:canonical:ubuntu_linux:-\n  os.family                Linux\n  os.product               Linux\n  os.vendor                Ubuntu\n  service.cpe23            cpe:/a:openbsd:openssh:9.6p1\n  service.family           OpenSSH\n  service.product          OpenSSH\n  service.protocol         ssh\n  service.vendor           OpenBSD\n  service.version          9.6p1\n\n\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m *************:445     - Rex::ConnectionRefused: The connection was refused by the remote host (*************:445).\n\x1b[1m\x1b[34m[*]\x1b[0m *************:445     - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'ports': [{'port': 22, 'protocol': 'tcp', 'state': 'open', 'service': 'ssh', 'version': 'OpenSSH 9.6p1 Ubuntu 3ubuntu13.11 (Ubuntu Linux; protocol 2.0)'}, {'port': 80, 'protocol': 'tcp', 'state': 'open', 'service': 'http', 'version': 'Apache httpd 2.4.58 ((Ubuntu))'}, {'port': 443, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/http', 'version': 'Apache httpd 2.4.58 ((Ubuntu))'}], 'vulnerabilities': [], 'summary': {'total_ports': 3, 'open_ports': 3, 'total_vulnerabilities': 0}}
2025-06-25 21:43:50,477 - scan_a3e90702-d62f-46c5-869b-f1fbbe15cb25 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 198.3s
