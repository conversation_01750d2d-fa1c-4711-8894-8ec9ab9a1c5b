2025-06-28 04:01:19,123 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 🚀 SCAN STARTED - Category: vulnerability, Type: basic, Target: ***********, Tools: openvas, metasploit
2025-06-28 04:01:19,127 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 🚀 SCAN STARTED - Category: vulnerability, Type: basic, Target: ***********, Tools: openvas, metasploit
2025-06-28 04:01:19,137 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on ***********
2025-06-28 04:01:19,138 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on ***********
2025-06-28 04:01:19,142 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-28 04:01:19,142 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-28 04:01:19,144 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Intensity: low, Timeout: 300s
2025-06-28 04:01:19,144 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Intensity: low, Timeout: 300s
2025-06-28 04:01:19,147 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS vulnerability scan...
2025-06-28 04:01:19,148 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit exploitation scan...
2025-06-28 04:01:22,152 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Metasploit scanning... 10% complete
2025-06-28 04:01:24,152 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - OpenVAS vulnerability scanning... 10% complete
2025-06-28 04:01:25,157 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 25% - Metasploit scanning... 25% complete
2025-06-28 04:01:28,164 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 40% - Metasploit scanning... 40% complete
2025-06-28 04:01:29,161 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - OPENVAS: 20% - OpenVAS vulnerability scanning... 20% complete
2025-06-28 04:01:31,170 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 55% - Metasploit scanning... 55% complete
2025-06-28 04:01:34,167 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS vulnerability scanning... 30% complete
2025-06-28 04:01:34,176 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 70% - Metasploit scanning... 70% complete
2025-06-28 04:01:37,184 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 85% - Metasploit scanning... 85% complete
2025-06-28 04:01:37,189 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-28 04:01:37,193 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-28 04:01:37,197 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-28 04:01:39,174 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - OPENVAS: 40% - OpenVAS vulnerability scanning... 40% complete
2025-06-28 04:01:44,180 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - OPENVAS: 50% - OpenVAS vulnerability scanning... 50% complete
2025-06-28 04:01:49,184 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - OPENVAS: 60% - OpenVAS vulnerability scanning... 60% complete
2025-06-28 04:01:54,190 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS vulnerability scanning... 70% complete
2025-06-28 04:01:59,195 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - OPENVAS: 80% - OpenVAS vulnerability scanning... 80% complete
2025-06-28 04:01:59,197 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-28 04:01:59,240 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-28 04:02:23,268 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-28 04:02:23,272 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_89e5a5c6-4714-4720-8778-57448e664f80
2025-06-28 04:02:23,281 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 0 vulnerabilities
2025-06-28 04:03:37,291 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - ERROR - ❌ TOOL ERROR - METASPLOIT: Timeout running TCP Port Scanner
2025-06-28 04:03:37,296 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-28 04:03:37,301 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-28 04:04:16,328 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-28 04:04:16,332 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-28 04:05:15,054 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-28 04:05:15,057 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-28 04:05:47,094 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-28 04:05:47,098 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 3 modules - Found 0 potential vulnerabilities
2025-06-28 04:05:47,104 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-28 04:05:47,114 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'tools': {'openvas': {'status': 'completed', 'scan_id': '89e5a5c6-4714-4720-8778-57448e664f80', 'task_id': 'greenbone_task_89e5a5c6-4714-4720-8778-57448e664f80', 'target_id': 'greenbone_target_89e5a5c6-4714-4720-8778-57448e664f80', 'vulnerabilities': [], 'message': 'Greenbone scan completed for ***********', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 3, 'raw_output': "=== TCP Port Scanner ===\nTimeout after 120 seconds\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ***********           - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m ***********:445       - Rex::ConnectionTimeout: The connection with (***********:445) timed out.\n\x1b[1m\x1b[34m[*]\x1b[0m ***********:445       - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'vulnerabilities': [], 'ports': [], 'summary': {'total_vulnerabilities': 0, 'critical_severity': 0, 'high_severity': 0, 'medium_severity': 0, 'low_severity': 0, 'total_ports_scanned': 0, 'tools_executed': 2}}
2025-06-28 04:05:47,118 - scan_cd47a036-81e3-4f19-8319-110e49086fe2 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 268.0s
