2025-06-26 17:31:01,229 - scan_d738d86d-4f5c-440d-a342-3defe6362ba3 - INFO - 🚀 SCAN STARTED - Category: vulnerability, Type: basic, Target: *************, Tools: openvas, metasploit
2025-06-26 17:31:01,233 - scan_d738d86d-4f5c-440d-a342-3defe6362ba3 - INFO - 🚀 SCAN STARTED - Category: vulnerability, Type: basic, Target: *************, Tools: openvas, metasploit
2025-06-26 17:31:01,239 - scan_d738d86d-4f5c-440d-a342-3defe6362ba3 - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on *************
2025-06-26 17:31:01,240 - scan_d738d86d-4f5c-440d-a342-3defe6362ba3 - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on *************
2025-06-26 17:31:01,244 - scan_d738d86d-4f5c-440d-a342-3defe6362ba3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-26 17:31:01,246 - scan_d738d86d-4f5c-440d-a342-3defe6362ba3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-26 17:31:01,249 - scan_d738d86d-4f5c-440d-a342-3defe6362ba3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-26 17:31:01,253 - scan_d738d86d-4f5c-440d-a342-3defe6362ba3 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-26 17:31:01,363 - scan_d738d86d-4f5c-440d-a342-3defe6362ba3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-26 17:31:02,015 - scan_d738d86d-4f5c-440d-a342-3defe6362ba3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-26 17:31:02,018 - scan_d738d86d-4f5c-440d-a342-3defe6362ba3 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_7fbdb2a6-1239-4e19-bc88-99b3afa4e94d
2025-06-26 17:31:02,021 - scan_d738d86d-4f5c-440d-a342-3defe6362ba3 - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 5 vulnerabilities
2025-06-26 17:31:53,643 - scan_d738d86d-4f5c-440d-a342-3defe6362ba3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-26 17:31:53,646 - scan_d738d86d-4f5c-440d-a342-3defe6362ba3 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-26 17:32:29,416 - scan_d738d86d-4f5c-440d-a342-3defe6362ba3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-26 17:32:29,424 - scan_d738d86d-4f5c-440d-a342-3defe6362ba3 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-26 17:33:08,396 - scan_d738d86d-4f5c-440d-a342-3defe6362ba3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-26 17:33:08,402 - scan_d738d86d-4f5c-440d-a342-3defe6362ba3 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-26 17:33:45,530 - scan_d738d86d-4f5c-440d-a342-3defe6362ba3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-26 17:33:45,536 - scan_d738d86d-4f5c-440d-a342-3defe6362ba3 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 4 modules - Found 0 potential vulnerabilities
2025-06-26 17:33:45,543 - scan_d738d86d-4f5c-440d-a342-3defe6362ba3 - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-26 17:33:45,561 - scan_d738d86d-4f5c-440d-a342-3defe6362ba3 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'tools': {'openvas': {'status': 'completed', 'scan_id': '7fbdb2a6-1239-4e19-bc88-99b3afa4e94d', 'task_id': 'greenbone_task_7fbdb2a6-1239-4e19-bc88-99b3afa4e94d', 'target_id': 'greenbone_target_7fbdb2a6-1239-4e19-bc88-99b3afa4e94d', 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'SMTP Service Detected', 'severity': 'medium', 'description': 'SMTP service - check for open relay', 'port': 25, 'solution': 'Review SMTP service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'POP3 Service Detected', 'severity': 'medium', 'description': 'POP3 service - unencrypted email protocol', 'port': 110, 'solution': 'Review POP3 service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'IMAP Service Detected', 'severity': 'medium', 'description': 'IMAP service - check for security configuration', 'port': 143, 'solution': 'Review IMAP service configuration and security settings', 'source_tool': 'openvas'}], 'message': 'Greenbone scan completed for *************', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/portscan/tcp', 'auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 4, 'raw_output': "=== TCP Port Scanner ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0mPORTS => 1-1000\n\x1b[0m\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:22 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:25 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:21 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:53 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:80 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:106 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:110 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:143 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:465 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:993 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:995 - TCP OPEN\n\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ************* - Key Fingerprint: ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIOpTVWZ/k5DUxBH0Ce/hiLXvstsiteakMsWsfmVk2i/j\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - SSH server version: SSH-2.0-OpenSSH_9.6p1 Ubuntu-3ubuntu13.12\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - Server Information and Encryption\n=================================\n\n  Type                     Value                                 Note\n  ----                     -----                                 ----\n  encryption.compression   none\n  encryption.compression   <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.encryption    aes128-ctr\n  encryption.encryption    aes192-ctr\n  encryption.encryption    aes256-ctr\n  encryption.encryption    <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          hmac-sha2-256\n  encryption.hmac          hmac-sha2-512\n  encryption.hmac          hmac-sha1\n  encryption.host_key      rsa-sha2-512\n  encryption.host_key      rsa-sha2-256\n  encryption.host_key      ecdsa-sha2-nistp256                   Weak elliptic curve\n  encryption.host_key      ssh-ed25519\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  curve25519-sha256\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  ecdh-sha2-nistp256\n  encryption.key_exchange  ecdh-sha2-nistp384\n  encryption.key_exchange  ecdh-sha2-nistp521\n  encryption.key_exchange  diffie-hellman-group-exchange-sha256\n  encryption.key_exchange  diffie-hellman-group16-sha512\n  encryption.key_exchange  diffie-hellman-group18-sha512\n  encryption.key_exchange  diffie-hellman-group14-sha256\n  encryption.key_exchange  ext-info-s\n  encryption.key_exchange  <EMAIL>\n  fingerprint_db           ssh.banner\n  openssh.comment          Ubuntu-3ubuntu13.12\n  os.certainty             0.75\n  os.cpe23                 cpe:/o:canonical:ubuntu_linux:-\n  os.family                Linux\n  os.product               Linux\n  os.vendor                Ubuntu\n  service.cpe23            cpe:/a:openbsd:openssh:9.6p1\n  service.family           OpenSSH\n  service.product          OpenSSH\n  service.protocol         ssh\n  service.vendor           OpenBSD\n  service.version          9.6p1\n\n\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m *************:445     - Rex::ConnectionRefused: The connection was refused by the remote host (*************:445).\n\x1b[1m\x1b[34m[*]\x1b[0m *************:445     - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'SMTP Service Detected', 'severity': 'medium', 'description': 'SMTP service - check for open relay', 'port': 25, 'solution': 'Review SMTP service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'POP3 Service Detected', 'severity': 'medium', 'description': 'POP3 service - unencrypted email protocol', 'port': 110, 'solution': 'Review POP3 service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'IMAP Service Detected', 'severity': 'medium', 'description': 'IMAP service - check for security configuration', 'port': 143, 'solution': 'Review IMAP service configuration and security settings', 'source_tool': 'openvas'}], 'ports': [], 'summary': {'total_vulnerabilities': 5, 'critical_severity': 0, 'high_severity': 0, 'medium_severity': 5, 'low_severity': 0, 'total_ports_scanned': 0, 'tools_executed': 2}}
2025-06-26 17:33:45,567 - scan_d738d86d-4f5c-440d-a342-3defe6362ba3 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 164.3s
