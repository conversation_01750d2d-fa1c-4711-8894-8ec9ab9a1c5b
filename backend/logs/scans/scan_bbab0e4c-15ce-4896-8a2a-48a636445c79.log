2025-06-30 15:35:25,086 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: ***********, Tools: nmap, openvas, metasploit
2025-06-30 15:35:25,090 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: ***********, Tools: nmap, openvas, metasploit
2025-06-30 15:35:25,095 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on ***********
2025-06-30 15:35:25,095 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on ***********
2025-06-30 15:35:25,097 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on ***********
2025-06-30 15:35:25,100 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-30 15:35:25,102 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-30 15:35:25,102 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-30 15:35:25,102 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Intensity: low, Timeout: 300s
2025-06-30 15:35:25,104 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Intensity: low, Timeout: 300s
2025-06-30 15:35:25,104 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Intensity: low, Timeout: 300s
2025-06-30 15:35:25,105 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap port scan...
2025-06-30 15:35:25,106 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit exploitation scan...
2025-06-30 15:35:25,106 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS vulnerability scan...
2025-06-30 15:35:27,108 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Scanning ports... 10% complete
2025-06-30 15:35:28,108 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Metasploit scanning... 10% complete
2025-06-30 15:35:29,114 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - NMAP: 20% - Scanning ports... 20% complete
2025-06-30 15:35:30,108 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - OpenVAS vulnerability scanning... 10% complete
2025-06-30 15:35:31,114 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 25% - Metasploit scanning... 25% complete
2025-06-30 15:35:31,120 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Scanning ports... 30% complete
2025-06-30 15:35:33,125 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - NMAP: 40% - Scanning ports... 40% complete
2025-06-30 15:35:34,121 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 40% - Metasploit scanning... 40% complete
2025-06-30 15:35:35,112 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - OPENVAS: 20% - OpenVAS vulnerability scanning... 20% complete
2025-06-30 15:35:35,131 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - NMAP: 50% - Scanning ports... 50% complete
2025-06-30 15:35:37,127 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 55% - Metasploit scanning... 55% complete
2025-06-30 15:35:37,137 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - NMAP: 60% - Scanning ports... 60% complete
2025-06-30 15:35:39,141 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - NMAP: 70% - Scanning ports... 70% complete
2025-06-30 15:35:40,118 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS vulnerability scanning... 30% complete
2025-06-30 15:35:40,134 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 70% - Metasploit scanning... 70% complete
2025-06-30 15:35:41,146 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - NMAP: 80% - Scanning ports... 80% complete
2025-06-30 15:35:41,151 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan (low intensity)...
2025-06-30 15:35:41,166 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-06-30 15:35:41,169 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using scan config: Quick scan with minimal intrusion
2025-06-30 15:35:41,172 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Nmap options: -T3 -F
2025-06-30 15:35:41,174 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Port range: 1-1000
2025-06-30 15:35:41,182 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-06-30 15:35:41,184 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - ERROR - ❌ TOOL ERROR - NMAP: You cannot use -F (fast scan) with -p (explicit port selection) but see --top-ports and --port-ratio to fast scan a range of ports
QUITTING!

2025-06-30 15:35:41,187 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - ✅ TOOL RESULT - NMAP: completed
2025-06-30 15:35:43,141 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 85% - Metasploit scanning... 85% complete
2025-06-30 15:35:43,146 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-30 15:35:43,149 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-30 15:35:43,151 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-30 15:35:45,124 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - OPENVAS: 40% - OpenVAS vulnerability scanning... 40% complete
2025-06-30 15:35:50,127 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - OPENVAS: 50% - OpenVAS vulnerability scanning... 50% complete
2025-06-30 15:35:55,131 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - OPENVAS: 60% - OpenVAS vulnerability scanning... 60% complete
2025-06-30 15:36:00,134 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS vulnerability scanning... 70% complete
2025-06-30 15:36:04,575 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-30 15:36:04,579 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-30 15:36:05,139 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - OPENVAS: 80% - OpenVAS vulnerability scanning... 80% complete
2025-06-30 15:36:05,144 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-30 15:36:05,194 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-30 15:36:05,240 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-30 15:36:05,245 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_5331b71a-63b2-49cd-81af-3e19050141c2
2025-06-30 15:36:05,257 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 3 vulnerabilities
2025-06-30 15:36:28,178 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-30 15:36:28,183 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-30 15:36:45,445 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-30 15:36:45,448 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-30 15:37:02,176 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-30 15:37:02,178 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 4 modules - Found 0 potential vulnerabilities
2025-06-30 15:37:02,180 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-30 15:37:02,186 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'tools': {'nmap': {'status': 'failed', 'scan_id': '7a8cdd1f-5b02-4061-9d16-2e92169d357d', 'target': '***********', 'error': 'You cannot use -F (fast scan) with -p (explicit port selection) but see --top-ports and --port-ratio to fast scan a range of ports\nQUITTING!\n', 'command': '/usr/bin/nmap -T3 -F -p 1-1000 -oX /tmp/nmap_scan_7a8cdd1f-5b02-4061-9d16-2e92169d357d.xml ***********'}, 'openvas': {'status': 'completed', 'scan_id': '5331b71a-63b2-49cd-81af-3e19050141c2', 'task_id': 'greenbone_task_5331b71a-63b2-49cd-81af-3e19050141c2', 'target_id': 'greenbone_target_5331b71a-63b2-49cd-81af-3e19050141c2', 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings'}, {'name': 'Telnet Service Detected', 'severity': 'high', 'description': 'Telnet service - unencrypted protocol', 'port': 23, 'solution': 'Review Telnet service configuration and security settings'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings'}], 'message': 'Greenbone scan completed for ***********', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/portscan/tcp', 'auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 4, 'raw_output': "=== TCP Port Scanner ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0mPORTS => 1-1000\n\x1b[0m\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:23 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:21 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:53 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:80 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:443 - TCP OPEN\n\x1b[1m\x1b[34m[*]\x1b[0m ***********           - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ***********           - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m Error: ***********: Errno::ECONNREFUSED Connection refused - connect(2) for ***********:22\n\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m ***********:445       - Rex::ConnectionRefused: The connection was refused by the remote host (***********:445).\n\x1b[1m\x1b[34m[*]\x1b[0m ***********:445       - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'ports': [], 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings'}, {'name': 'Telnet Service Detected', 'severity': 'high', 'description': 'Telnet service - unencrypted protocol', 'port': 23, 'solution': 'Review Telnet service configuration and security settings'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings'}], 'summary': {'total_ports': 0, 'open_ports': 0, 'total_vulnerabilities': 3}}
2025-06-30 15:37:02,189 - scan_bbab0e4c-15ce-4896-8a2a-48a636445c79 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 97.1s
