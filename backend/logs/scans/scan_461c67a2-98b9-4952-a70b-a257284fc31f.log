2025-06-24 23:25:54,385 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 🚀 SCAN STARTED - Category: web, Type: basic, Target: http://***********, Tools: nikto, sqlmap, dirb, gobuster, zap
2025-06-24 23:25:54,391 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 🔧 TOOL START - NIKTO - Command: nikto scan on http://***********
2025-06-24 23:25:54,392 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 🔧 TOOL START - SQLMAP - Command: sqlmap scan on http://***********
2025-06-24 23:25:54,393 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 🔧 TOOL START - DIRB - Command: dirb scan on http://***********
2025-06-24 23:25:54,394 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 🔧 TOOL START - GOBUSTER - Command: gobuster scan on http://***********
2025-06-24 23:25:54,396 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 🔧 TOOL START - ZAP - Command: zap scan on http://***********
2025-06-24 23:25:54,399 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Starting Nikto scan...
2025-06-24 23:25:54,407 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 10% - Starting directory scan...
2025-06-24 23:25:54,411 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - ZAP: 10% - Starting security scan...
2025-06-24 23:25:54,412 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - SQLMAP: 10% - Starting SQLMap scan...
2025-06-24 23:25:54,414 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - GOBUSTER: 10% - Starting GoBuster scan...
2025-06-24 23:25:54,415 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - ZAP: 25% - Performing HTTP security checks...
2025-06-24 23:25:54,416 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Executing Nikto command...
2025-06-24 23:25:54,418 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - SQLMAP: 25% - Executing SQLMap command...
2025-06-24 23:25:54,423 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Command: nikto -h http://*********** -Format txt -output /tmp/nikto_1750800354.txt -maxtime 180 -Tuning 1,2,3,4,5,6,7,8,9,0,a,b,c
2025-06-24 23:25:54,427 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Command: sqlmap -u http://*********** --batch --level=1 --risk=1 --timeout=15 --retries=1 --threads=1 --technique=B --no-cast --disable-coloring --flush-session --fresh-queries --crawl=1
2025-06-24 23:25:54,429 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Scanning... (0s elapsed)
2025-06-24 23:25:54,431 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - SQLMAP: 25% - Testing for SQL injection... (0s elapsed)
2025-06-24 23:25:54,434 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 25% - Executing Dirb command...
2025-06-24 23:25:54,434 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - GOBUSTER: 25% - Executing GoBuster command...
2025-06-24 23:25:54,437 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Command: dirb http://*********** /usr/share/dirb/wordlists/small.txt -w -r -S -z 50 -X .php,.html,.txt,.js
2025-06-24 23:25:54,438 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Command: gobuster dir -u http://*********** -w /usr/share/dirb/wordlists/common.txt -t 5 -q --no-error --timeout 5s --delay 100ms
2025-06-24 23:25:54,442 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 25% - Scanning directories... (0s elapsed)
2025-06-24 23:25:54,443 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - GOBUSTER: 25% - Brute forcing directories... (0s elapsed)
2025-06-24 23:25:59,446 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 27% - Scanning directories... (5s elapsed)
2025-06-24 23:25:59,448 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - GOBUSTER: 28% - Brute forcing directories... (5s elapsed)
2025-06-24 23:26:04,432 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - NIKTO: 28% - Scanning... (10s elapsed)
2025-06-24 23:26:04,436 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - SQLMAP: 30% - Testing for SQL injection... (10s elapsed)
2025-06-24 23:26:04,437 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): HTTP request failed: HTTPConnectionPool(host='***********', port=80): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x761009bb7710>, 'Connection to *********** timed out. (connect timeout=10)'))
2025-06-24 23:26:04,443 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - ZAP: 100% - Security scan completed
2025-06-24 23:26:04,448 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Found 1 security alerts
2025-06-24 23:26:04,451 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 30% - Scanning directories... (10s elapsed)
2025-06-24 23:26:04,454 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): GoBuster completed with no results: Error: error on running gobuster: unable to connect to http://***********/: Get "http://***********/": context deadline exceeded (Client.Timeout exceeded while awaiting headers)

2025-06-24 23:26:04,455 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - ✅ TOOL RESULT - ZAP: completed
2025-06-24 23:26:04,461 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - ✅ TOOL RESULT - GOBUSTER: completed
2025-06-24 23:26:09,459 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 33% - Scanning directories... (15s elapsed)
2025-06-24 23:26:14,440 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - NIKTO: 32% - Scanning... (20s elapsed)
2025-06-24 23:26:14,443 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - SQLMAP: 35% - Testing for SQL injection... (20s elapsed)
2025-06-24 23:26:14,462 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 35% - Scanning directories... (20s elapsed)
2025-06-24 23:26:19,467 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 38% - Scanning directories... (25s elapsed)
2025-06-24 23:26:24,446 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - NIKTO: 35% - Scanning... (30s elapsed)
2025-06-24 23:26:24,448 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - SQLMAP: 41% - Testing for SQL injection... (30s elapsed)
2025-06-24 23:26:24,475 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 41% - Scanning directories... (30s elapsed)
2025-06-24 23:26:29,480 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 43% - Scanning directories... (35s elapsed)
2025-06-24 23:26:34,452 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - NIKTO: 39% - Scanning... (40s elapsed)
2025-06-24 23:26:34,455 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - SQLMAP: 90% - Parsing results...
2025-06-24 23:26:34,459 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - SQLMAP: 100% - SQLMap scan completed
2025-06-24 23:26:34,462 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): No SQL injection vulnerabilities detected
2025-06-24 23:26:34,465 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - ✅ TOOL RESULT - SQLMAP: completed
2025-06-24 23:26:34,486 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 46% - Scanning directories... (40s elapsed)
2025-06-24 23:26:39,490 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 49% - Scanning directories... (45s elapsed)
2025-06-24 23:26:44,456 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - NIKTO: 43% - Scanning... (50s elapsed)
2025-06-24 23:26:44,496 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 52% - Scanning directories... (50s elapsed)
2025-06-24 23:26:49,502 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 54% - Scanning directories... (55s elapsed)
2025-06-24 23:26:54,462 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - NIKTO: 46% - Scanning... (60s elapsed)
2025-06-24 23:26:54,508 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 57% - Scanning directories... (60s elapsed)
2025-06-24 23:26:59,514 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 60% - Scanning directories... (65s elapsed)
2025-06-24 23:27:04,468 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - NIKTO: 50% - Scanning... (70s elapsed)
2025-06-24 23:27:04,519 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 62% - Scanning directories... (70s elapsed)
2025-06-24 23:27:09,526 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 65% - Scanning directories... (75s elapsed)
2025-06-24 23:27:14,474 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - NIKTO: 53% - Scanning... (80s elapsed)
2025-06-24 23:27:14,532 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 68% - Scanning directories... (80s elapsed)
2025-06-24 23:27:19,540 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 71% - Scanning directories... (85s elapsed)
2025-06-24 23:27:24,480 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - NIKTO: 57% - Scanning... (90s elapsed)
2025-06-24 23:27:24,545 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 73% - Scanning directories... (90s elapsed)
2025-06-24 23:27:29,551 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 76% - Scanning directories... (95s elapsed)
2025-06-24 23:27:34,487 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - NIKTO: 90% - Parsing results...
2025-06-24 23:27:34,492 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - NIKTO: 100% - Nikto scan completed
2025-06-24 23:27:34,498 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Scan completed - Found 0 potential issues
2025-06-24 23:27:34,506 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - ✅ TOOL RESULT - NIKTO: completed - Found 0 vulnerabilities
2025-06-24 23:27:34,557 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 79% - Scanning directories... (100s elapsed)
2025-06-24 23:27:39,563 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 81% - Scanning directories... (105s elapsed)
2025-06-24 23:27:44,569 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 84% - Scanning directories... (110s elapsed)
2025-06-24 23:27:49,574 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 87% - Scanning directories... (115s elapsed)
2025-06-24 23:27:59,580 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Scan timeout after 2 minutes - completing with current results
2025-06-24 23:27:59,586 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 90% - Parsing results...
2025-06-24 23:27:59,591 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 📊 TOOL PROGRESS - DIRB: 100% - Directory scan completed
2025-06-24 23:27:59,597 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Found 0 directories
2025-06-24 23:27:59,607 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - ✅ TOOL RESULT - DIRB: completed - Found 0 directories
2025-06-24 23:27:59,615 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'zap': {'status': 'completed', 'alerts': [{'risk': 'Medium', 'name': 'Connection Issue', 'count': 1, 'description': "Could not analyze target: HTTPConnectionPool(host='***********', port=80): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x761009bb7710>, 'Connection to *********** timed out. (connect timeout=10)'))"}], 'scan_time': '10 seconds', 'raw_output': 'Basic security scan completed. Found 1 potential issues.'}, 'gobuster': {'status': 'completed', 'found_paths': [], 'scan_time': '10 seconds', 'raw_output': ''}, 'sqlmap': {'status': 'completed', 'injections': [], 'scan_time': '40 seconds', 'raw_output': "        ___\n       __H__\n ___ ___[,]_____ ___ ___  {1.8.4#stable}\n|_ -| . [)]     | .'| . |\n|___|_  [']_|_|_|__,|  _|\n      |_|V...       |_|   https://sqlmap.org\n\n[!] legal disclaimer: Usage of sqlmap for attacking targets without prior mutual consent is illegal. It is the end user's responsibility to obey all applicable local, state and federal laws. Developers assume no liability and are not responsible for any misuse or damage caused by this program\n\n[*] starting @ 23:25:54 /2025-06-24/\n\ndo you want to check for the existence of site's sitemap(.xml) [y/N] N\n\n[23:25:54] [INFO] starting crawler for target URL 'http://***********'\n\n[23:25:54] [INFO] searching for links with depth 1\n\n[23:26:09] [CRITICAL] connection timed out to the target URL. sqlmap is going to retry the request(s)\n\n[23:26:09] [WARNING] if the problem persists please check that the provided target URL is reachable. In case that it is, you can try to rerun with switch '--random-agent' and/or proxy switches ('--proxy', '--proxy-file'...)\n\n[23:26:24] [CRITICAL] connection exception detected ('connection timed out to the target URL'). skipping URL 'http://***********'\n\n[23:26:24] [WARNING] no usable links found (with GET parameters)\n\n[23:26:25] [WARNING] your sqlmap version is outdated\n\n[*] ending @ 23:26:25 /2025-06-24/\n\n"}, 'nikto': {'status': 'completed', 'vulnerabilities': [], 'scan_time': '100 seconds', 'total_tests': 2, 'raw_output': '- Nikto v2.1.5/2.1.5\n'}, 'dirb': {'status': 'completed', 'directories': [], 'scan_time': '125 seconds', 'raw_output': '\n-----------------\nDIRB v2.22    \nBy The Dark Raver\n-----------------\n\nSTART_TIME: Tue Jun 24 23:25:54 2025\nURL_BASE: http://***********/\nWORDLIST_FILES: /usr/share/dirb/wordlists/small.txt\nOPTION: Not Recursive\nOPTION: Silent Mode\nOPTION: Not Stopping on warning messages\nEXTENSIONS_LIST: (.php,.html,.txt,.js) | (.php)(.html)(.txt)(.js) [NUM = 4]\nSPEED_DELAY: 50 milliseconds\n\n-----------------\n\nGENERATED WORDS: 959\n\n---- Scanning URL: http://***********/ ----\n'}}
2025-06-24 23:27:59,621 - scan_461c67a2-98b9-4952-a70b-a257284fc31f - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 125.2s
