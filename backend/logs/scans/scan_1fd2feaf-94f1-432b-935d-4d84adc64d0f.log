2025-06-28 03:21:16,786 - scan_1fd2feaf-94f1-432b-935d-4d84adc64d0f - INFO - 🚀 SCAN STARTED - Category: vulnerability, Type: basic, Target: 82.165.144.72, Tools: openvas, metasploit
2025-06-28 03:21:16,791 - scan_1fd2feaf-94f1-432b-935d-4d84adc64d0f - INFO - 🚀 SCAN STARTED - Category: vulnerability, Type: basic, Target: 82.165.144.72, Tools: openvas, metasploit
2025-06-28 03:21:16,803 - scan_1fd2feaf-94f1-432b-935d-4d84adc64d0f - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on 82.165.144.72
2025-06-28 03:21:16,804 - scan_1fd2feaf-94f1-432b-935d-4d84adc64d0f - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on 82.165.144.72
2025-06-28 03:21:16,809 - scan_1fd2feaf-94f1-432b-935d-4d84adc64d0f - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-28 03:21:16,810 - scan_1fd2feaf-94f1-432b-935d-4d84adc64d0f - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-28 03:21:16,813 - scan_1fd2feaf-94f1-432b-935d-4d84adc64d0f - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Intensity: low, Timeout: 300s
2025-06-28 03:21:16,814 - scan_1fd2feaf-94f1-432b-935d-4d84adc64d0f - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Intensity: low, Timeout: 300s
2025-06-28 03:21:16,820 - scan_1fd2feaf-94f1-432b-935d-4d84adc64d0f - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS vulnerability scan...
2025-06-28 03:21:16,821 - scan_1fd2feaf-94f1-432b-935d-4d84adc64d0f - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit exploitation scan...
2025-06-28 03:21:16,824 - scan_1fd2feaf-94f1-432b-935d-4d84adc64d0f - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Using low scan configuration: Quick scan with minimal intrusion
2025-06-28 03:21:16,825 - scan_1fd2feaf-94f1-432b-935d-4d84adc64d0f - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-28 03:21:16,828 - scan_1fd2feaf-94f1-432b-935d-4d84adc64d0f - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Intensity: low, Timeout: 300s
2025-06-28 03:21:16,837 - scan_1fd2feaf-94f1-432b-935d-4d84adc64d0f - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-28 03:21:16,841 - scan_1fd2feaf-94f1-432b-935d-4d84adc64d0f - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-28 03:21:17,052 - scan_1fd2feaf-94f1-432b-935d-4d84adc64d0f - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-28 03:21:17,735 - scan_1fd2feaf-94f1-432b-935d-4d84adc64d0f - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-28 03:21:17,741 - scan_1fd2feaf-94f1-432b-935d-4d84adc64d0f - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_2386b6ee-3792-48e7-a87e-91737b05c299
2025-06-28 03:21:17,749 - scan_1fd2feaf-94f1-432b-935d-4d84adc64d0f - INFO - 📊 TOOL PROGRESS - OPENVAS: 100% - OpenVAS scan completed
2025-06-28 03:21:17,760 - scan_1fd2feaf-94f1-432b-935d-4d84adc64d0f - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 4 vulnerabilities
2025-06-28 03:23:13,970 - scan_1fd2feaf-94f1-432b-935d-4d84adc64d0f - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed: TCP Port Scanner
2025-06-28 03:23:13,977 - scan_1fd2feaf-94f1-432b-935d-4d84adc64d0f - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-28 03:23:13,981 - scan_1fd2feaf-94f1-432b-935d-4d84adc64d0f - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
