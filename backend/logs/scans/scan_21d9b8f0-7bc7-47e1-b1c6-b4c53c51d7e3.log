2025-06-30 15:32:16,678 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: ***********, Tools: nmap, openvas, metasploit
2025-06-30 15:32:16,690 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: ***********, Tools: nmap, openvas, metasploit
2025-06-30 15:32:16,693 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on ***********
2025-06-30 15:32:16,694 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on ***********
2025-06-30 15:32:16,695 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on ***********
2025-06-30 15:32:16,696 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-30 15:32:16,697 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-30 15:32:16,697 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Intensity: low, Timeout: 300s
2025-06-30 15:32:16,698 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Intensity: low, Timeout: 300s
2025-06-30 15:32:16,700 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap port scan...
2025-06-30 15:32:16,700 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-30 15:32:16,701 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit exploitation scan...
2025-06-30 15:32:16,702 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Intensity: low, Timeout: 300s
2025-06-30 15:32:16,704 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS vulnerability scan...
2025-06-30 15:32:18,703 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Scanning ports... 10% complete
2025-06-30 15:32:19,703 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Metasploit scanning... 10% complete
2025-06-30 15:32:20,707 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - NMAP: 20% - Scanning ports... 20% complete
2025-06-30 15:32:21,706 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - OpenVAS vulnerability scanning... 10% complete
2025-06-30 15:32:22,707 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 25% - Metasploit scanning... 25% complete
2025-06-30 15:32:22,713 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Scanning ports... 30% complete
2025-06-30 15:32:24,716 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - NMAP: 40% - Scanning ports... 40% complete
2025-06-30 15:32:25,710 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 40% - Metasploit scanning... 40% complete
2025-06-30 15:32:26,712 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 20% - OpenVAS vulnerability scanning... 20% complete
2025-06-30 15:32:26,723 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - NMAP: 50% - Scanning ports... 50% complete
2025-06-30 15:32:28,718 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 55% - Metasploit scanning... 55% complete
2025-06-30 15:32:28,729 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - NMAP: 60% - Scanning ports... 60% complete
2025-06-30 15:32:30,736 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - NMAP: 70% - Scanning ports... 70% complete
2025-06-30 15:32:31,719 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS vulnerability scanning... 30% complete
2025-06-30 15:32:31,724 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 70% - Metasploit scanning... 70% complete
2025-06-30 15:32:32,743 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - NMAP: 80% - Scanning ports... 80% complete
2025-06-30 15:32:32,748 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan (low intensity)...
2025-06-30 15:32:32,783 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-06-30 15:32:32,787 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using scan config: Quick scan with minimal intrusion
2025-06-30 15:32:32,789 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Nmap options: -T3 -F
2025-06-30 15:32:32,791 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Port range: 1-1000
2025-06-30 15:32:32,804 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-06-30 15:32:32,807 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - ERROR - ❌ TOOL ERROR - NMAP: You cannot use -F (fast scan) with -p (explicit port selection) but see --top-ports and --port-ratio to fast scan a range of ports
QUITTING!

2025-06-30 15:32:32,812 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - ✅ TOOL RESULT - NMAP: completed
2025-06-30 15:32:34,730 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 85% - Metasploit scanning... 85% complete
2025-06-30 15:32:34,734 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-30 15:32:34,737 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-30 15:32:34,740 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-30 15:32:36,725 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 40% - OpenVAS vulnerability scanning... 40% complete
2025-06-30 15:32:41,729 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 50% - OpenVAS vulnerability scanning... 50% complete
2025-06-30 15:32:46,737 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 60% - OpenVAS vulnerability scanning... 60% complete
2025-06-30 15:32:51,740 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS vulnerability scanning... 70% complete
2025-06-30 15:32:56,744 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 80% - OpenVAS vulnerability scanning... 80% complete
2025-06-30 15:32:56,746 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-30 15:32:56,859 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-30 15:32:56,908 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-30 15:32:56,910 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_e80d7f14-f624-4404-9767-dfdc944ba073
2025-06-30 15:32:56,913 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 3 vulnerabilities
2025-06-30 15:33:04,227 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-30 15:33:04,228 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-30 15:33:20,805 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-30 15:33:20,807 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-30 15:33:37,517 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-30 15:33:37,519 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-30 15:33:53,871 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-30 15:33:53,873 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 4 modules - Found 0 potential vulnerabilities
2025-06-30 15:33:53,875 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-30 15:33:53,884 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'tools': {'nmap': {'status': 'failed', 'scan_id': 'c4ab7d14-e389-4e9a-9774-3bab5bf086e5', 'target': '***********', 'error': 'You cannot use -F (fast scan) with -p (explicit port selection) but see --top-ports and --port-ratio to fast scan a range of ports\nQUITTING!\n', 'command': '/usr/bin/nmap -T3 -F -p 1-1000 -oX /tmp/nmap_scan_c4ab7d14-e389-4e9a-9774-3bab5bf086e5.xml ***********'}, 'openvas': {'status': 'completed', 'scan_id': 'e80d7f14-f624-4404-9767-dfdc944ba073', 'task_id': 'greenbone_task_e80d7f14-f624-4404-9767-dfdc944ba073', 'target_id': 'greenbone_target_e80d7f14-f624-4404-9767-dfdc944ba073', 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings'}, {'name': 'Telnet Service Detected', 'severity': 'high', 'description': 'Telnet service - unencrypted protocol', 'port': 23, 'solution': 'Review Telnet service configuration and security settings'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings'}], 'message': 'Greenbone scan completed for ***********', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/portscan/tcp', 'auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 4, 'raw_output': "=== TCP Port Scanner ===\nRunning the 'init' command for the database:\nExisting database found, attempting to start it\nStarting database at /home/<USER>/snap/metasploit-framework/common/.msf4/db...waiting for server to start.... done\nserver started\n\x1b[1m\x1b[32msuccess\x1b[0m\x1b[0m\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0mPORTS => 1-1000\n\x1b[0m\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:21 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:23 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:53 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:80 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:443 - TCP OPEN\n\x1b[1m\x1b[34m[*]\x1b[0m ***********           - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ***********           - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m Error: ***********: Errno::ECONNREFUSED Connection refused - connect(2) for ***********:22\n\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m ***********:445       - Rex::ConnectionRefused: The connection was refused by the remote host (***********:445).\n\x1b[1m\x1b[34m[*]\x1b[0m ***********:445       - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'ports': [], 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings'}, {'name': 'Telnet Service Detected', 'severity': 'high', 'description': 'Telnet service - unencrypted protocol', 'port': 23, 'solution': 'Review Telnet service configuration and security settings'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings'}], 'summary': {'total_ports': 0, 'open_ports': 0, 'total_vulnerabilities': 3}}
2025-06-30 15:33:53,885 - scan_21d9b8f0-7bc7-47e1-b1c6-b4c53c51d7e3 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 97.2s
