2025-06-28 04:12:29,081 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: ***********, Tools: nmap, openvas, metasploit
2025-06-28 04:12:29,084 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: ***********, Tools: nmap, openvas, metasploit
2025-06-28 04:12:29,091 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on ***********
2025-06-28 04:12:29,093 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on ***********
2025-06-28 04:12:29,094 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on ***********
2025-06-28 04:12:29,098 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-28 04:12:29,100 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-28 04:12:29,101 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-28 04:12:29,104 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Intensity: low, Timeout: 300s
2025-06-28 04:12:29,106 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Intensity: low, Timeout: 300s
2025-06-28 04:12:29,108 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Intensity: low, Timeout: 300s
2025-06-28 04:12:29,116 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap port scan...
2025-06-28 04:12:29,117 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit exploitation scan...
2025-06-28 04:12:29,118 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS vulnerability scan...
2025-06-28 04:12:31,123 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Scanning ports... 10% complete
2025-06-28 04:12:32,128 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Metasploit scanning... 10% complete
2025-06-28 04:12:33,131 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - NMAP: 20% - Scanning ports... 20% complete
2025-06-28 04:12:34,129 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - OpenVAS vulnerability scanning... 10% complete
2025-06-28 04:12:35,134 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 25% - Metasploit scanning... 25% complete
2025-06-28 04:12:35,139 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Scanning ports... 30% complete
2025-06-28 04:12:37,143 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - NMAP: 40% - Scanning ports... 40% complete
2025-06-28 04:12:38,140 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 40% - Metasploit scanning... 40% complete
2025-06-28 04:12:39,135 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - OPENVAS: 20% - OpenVAS vulnerability scanning... 20% complete
2025-06-28 04:12:39,148 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - NMAP: 50% - Scanning ports... 50% complete
2025-06-28 04:12:41,146 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 55% - Metasploit scanning... 55% complete
2025-06-28 04:12:41,152 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - NMAP: 60% - Scanning ports... 60% complete
2025-06-28 04:12:43,159 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - NMAP: 70% - Scanning ports... 70% complete
2025-06-28 04:12:44,140 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS vulnerability scanning... 30% complete
2025-06-28 04:12:44,153 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 70% - Metasploit scanning... 70% complete
2025-06-28 04:12:45,166 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - NMAP: 80% - Scanning ports... 80% complete
2025-06-28 04:12:45,172 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan (low intensity)...
2025-06-28 04:12:45,204 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-06-28 04:12:45,208 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using scan config: Quick scan with minimal intrusion
2025-06-28 04:12:45,212 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Nmap options: -T3 -F
2025-06-28 04:12:45,217 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Port range: 1-1000
2025-06-28 04:12:45,236 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-06-28 04:12:45,241 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - ERROR - ❌ TOOL ERROR - NMAP: You cannot use -F (fast scan) with -p (explicit port selection) but see --top-ports and --port-ratio to fast scan a range of ports
QUITTING!

2025-06-28 04:12:45,250 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - ✅ TOOL RESULT - NMAP: completed
2025-06-28 04:12:47,159 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 85% - Metasploit scanning... 85% complete
2025-06-28 04:12:47,165 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-28 04:12:47,168 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-28 04:12:47,172 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-28 04:12:49,146 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - OPENVAS: 40% - OpenVAS vulnerability scanning... 40% complete
2025-06-28 04:12:54,152 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - OPENVAS: 50% - OpenVAS vulnerability scanning... 50% complete
2025-06-28 04:12:59,159 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - OPENVAS: 60% - OpenVAS vulnerability scanning... 60% complete
2025-06-28 04:13:04,166 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS vulnerability scanning... 70% complete
2025-06-28 04:13:09,172 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - OPENVAS: 80% - OpenVAS vulnerability scanning... 80% complete
2025-06-28 04:13:09,177 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-28 04:13:09,216 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-28 04:13:33,245 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-28 04:13:33,250 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_0d73a6d7-fad6-402e-98b0-4817f4f19d97
2025-06-28 04:13:33,258 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 0 vulnerabilities
2025-06-28 04:14:47,274 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - ERROR - ❌ TOOL ERROR - METASPLOIT: Timeout running TCP Port Scanner
2025-06-28 04:14:47,277 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-28 04:14:47,280 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-28 04:15:45,743 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-28 04:15:45,746 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-28 04:16:43,009 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-28 04:16:43,012 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-28 04:17:20,930 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-28 04:17:20,934 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 3 modules - Found 0 potential vulnerabilities
2025-06-28 04:17:20,940 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-28 04:17:20,948 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'tools': {'nmap': {'status': 'failed', 'scan_id': 'f3e04c00-2e66-485a-bb33-f07110ee05fc', 'target': '***********', 'error': 'You cannot use -F (fast scan) with -p (explicit port selection) but see --top-ports and --port-ratio to fast scan a range of ports\nQUITTING!\n', 'command': '/usr/bin/nmap -T3 -F -p 1-1000 -oX /tmp/nmap_scan_f3e04c00-2e66-485a-bb33-f07110ee05fc.xml ***********'}, 'openvas': {'status': 'completed', 'scan_id': '0d73a6d7-fad6-402e-98b0-4817f4f19d97', 'task_id': 'greenbone_task_0d73a6d7-fad6-402e-98b0-4817f4f19d97', 'target_id': 'greenbone_target_0d73a6d7-fad6-402e-98b0-4817f4f19d97', 'vulnerabilities': [], 'message': 'Greenbone scan completed for ***********', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 3, 'raw_output': "=== TCP Port Scanner ===\nTimeout after 120 seconds\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ***********           - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m ***********:445       - Rex::ConnectionTimeout: The connection with (***********:445) timed out.\n\x1b[1m\x1b[34m[*]\x1b[0m ***********:445       - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'ports': [], 'vulnerabilities': [], 'summary': {'total_ports': 0, 'open_ports': 0, 'total_vulnerabilities': 0}}
2025-06-28 04:17:20,953 - scan_29681729-8dcb-4467-bc4c-5d876b6c7254 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 291.9s
