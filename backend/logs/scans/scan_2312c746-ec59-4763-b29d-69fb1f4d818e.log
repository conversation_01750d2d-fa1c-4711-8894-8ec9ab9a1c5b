2025-06-28 03:33:05,081 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 🚀 SCAN STARTED - Category: vulnerability, Type: basic, Target: *************, Tools: openvas, metasploit
2025-06-28 03:33:05,085 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 🚀 SCAN STARTED - Category: vulnerability, Type: basic, Target: *************, Tools: openvas, metasploit
2025-06-28 03:33:05,094 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on *************
2025-06-28 03:33:05,095 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on *************
2025-06-28 03:33:05,100 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-28 03:33:05,101 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-28 03:33:05,104 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Intensity: low, Timeout: 300s
2025-06-28 03:33:05,104 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Intensity: low, Timeout: 300s
2025-06-28 03:33:05,109 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS vulnerability scan...
2025-06-28 03:33:05,110 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit exploitation scan...
2025-06-28 03:33:08,118 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Metasploit scanning... 10% complete
2025-06-28 03:33:10,115 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - OpenVAS vulnerability scanning... 10% complete
2025-06-28 03:33:11,134 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - METASPLOIT: 25% - Metasploit scanning... 25% complete
2025-06-28 03:33:14,143 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - METASPLOIT: 40% - Metasploit scanning... 40% complete
2025-06-28 03:33:15,125 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - OPENVAS: 20% - OpenVAS vulnerability scanning... 20% complete
2025-06-28 03:33:17,151 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - METASPLOIT: 55% - Metasploit scanning... 55% complete
2025-06-28 03:33:20,129 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS vulnerability scanning... 30% complete
2025-06-28 03:33:20,163 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - METASPLOIT: 70% - Metasploit scanning... 70% complete
2025-06-28 03:33:23,171 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - METASPLOIT: 85% - Metasploit scanning... 85% complete
2025-06-28 03:33:23,177 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-28 03:33:23,183 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-28 03:33:23,190 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-28 03:33:25,139 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - OPENVAS: 40% - OpenVAS vulnerability scanning... 40% complete
2025-06-28 03:33:30,148 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - OPENVAS: 50% - OpenVAS vulnerability scanning... 50% complete
2025-06-28 03:33:35,157 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - OPENVAS: 60% - OpenVAS vulnerability scanning... 60% complete
2025-06-28 03:33:40,166 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS vulnerability scanning... 70% complete
2025-06-28 03:33:45,175 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - OPENVAS: 80% - OpenVAS vulnerability scanning... 80% complete
2025-06-28 03:33:45,182 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-28 03:33:45,250 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-28 03:33:45,959 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-28 03:33:45,964 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_34636826-ea97-4ec1-8512-fc0626b4c97c
2025-06-28 03:33:45,975 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 4 vulnerabilities
2025-06-28 03:35:23,218 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - ERROR - ❌ TOOL ERROR - METASPLOIT: Timeout running TCP Port Scanner
2025-06-28 03:35:23,223 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-28 03:35:23,227 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-28 03:36:29,018 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-28 03:36:29,024 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-28 03:37:33,487 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-28 03:37:33,492 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-28 03:38:30,658 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-28 03:38:30,666 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 3 modules - Found 0 potential vulnerabilities
2025-06-28 03:38:30,677 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-28 03:38:30,700 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'tools': {'openvas': {'status': 'completed', 'scan_id': '34636826-ea97-4ec1-8512-fc0626b4c97c', 'task_id': 'greenbone_task_34636826-ea97-4ec1-8512-fc0626b4c97c', 'target_id': 'greenbone_target_34636826-ea97-4ec1-8512-fc0626b4c97c', 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'POP3 Service Detected', 'severity': 'medium', 'description': 'POP3 service - unencrypted email protocol', 'port': 110, 'solution': 'Review POP3 service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'IMAP Service Detected', 'severity': 'medium', 'description': 'IMAP service - check for security configuration', 'port': 143, 'solution': 'Review IMAP service configuration and security settings', 'source_tool': 'openvas'}], 'message': 'Greenbone scan completed for *************', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 3, 'raw_output': "=== TCP Port Scanner ===\nTimeout after 120 seconds\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ************* - Key Fingerprint: ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIOpTVWZ/k5DUxBH0Ce/hiLXvstsiteakMsWsfmVk2i/j\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - SSH server version: SSH-2.0-OpenSSH_9.6p1 Ubuntu-3ubuntu13.12\n\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m *************:445     - Rex::ConnectionRefused: The connection was refused by the remote host (*************:445).\n\x1b[1m\x1b[34m[*]\x1b[0m *************:445     - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'POP3 Service Detected', 'severity': 'medium', 'description': 'POP3 service - unencrypted email protocol', 'port': 110, 'solution': 'Review POP3 service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'IMAP Service Detected', 'severity': 'medium', 'description': 'IMAP service - check for security configuration', 'port': 143, 'solution': 'Review IMAP service configuration and security settings', 'source_tool': 'openvas'}], 'ports': [], 'summary': {'total_vulnerabilities': 4, 'critical_severity': 0, 'high_severity': 0, 'medium_severity': 4, 'low_severity': 0, 'total_ports_scanned': 0, 'tools_executed': 2}}
2025-06-28 03:38:30,707 - scan_2312c746-ec59-4763-b29d-69fb1f4d818e - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 325.6s
