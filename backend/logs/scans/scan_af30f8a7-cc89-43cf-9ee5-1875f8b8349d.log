2025-06-30 19:17:55,012 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 🚀 SCAN STARTED - Category: deep, Type: comprehensive, Target: ************, Tools: nmap, openvas, metasploit, nikto, sqlmap, dirb, gobuster, zap
2025-06-30 19:17:55,015 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 🚀 SCAN STARTED - Category: deep, Type: comprehensive, Target: ************, Tools: nmap, openvas, metasploit, nikto, sqlmap, dirb, gobuster, zap
2025-06-30 19:17:55,017 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 🔧 TOOL START - DEEP_SCAN - Command: 🚀 Starting Deep Scan - All Tools Running in Parallel
2025-06-30 19:17:55,023 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Launching all tools in parallel...
2025-06-30 19:17:55,025 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started nmap in background
2025-06-30 19:17:55,025 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on ************
2025-06-30 19:17:55,027 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started openvas in background
2025-06-30 19:17:55,028 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on ************
2025-06-30 19:17:55,028 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 19:17:55,030 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started metasploit in background
2025-06-30 19:17:55,030 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on ************
2025-06-30 19:17:55,030 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 19:17:55,031 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 19:17:55,034 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 🔧 TOOL START - NIKTO - Command: nikto scan on ************
2025-06-30 19:17:55,034 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap port scan...
2025-06-30 19:17:55,034 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started nikto in background
2025-06-30 19:17:55,035 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 19:17:55,036 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 19:17:55,038 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 19:17:55,040 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started sqlmap in background
2025-06-30 19:17:55,040 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 19:17:55,041 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 🔧 TOOL START - SQLMAP - Command: sqlmap scan on ************
2025-06-30 19:17:55,041 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 19:17:55,041 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS vulnerability scan...
2025-06-30 19:17:55,045 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started dirb in background
2025-06-30 19:17:55,045 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 19:17:55,046 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 🔧 TOOL START - DIRB - Command: dirb scan on ************
2025-06-30 19:17:55,046 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Formatted target for web tool: ************ -> http://************
2025-06-30 19:17:55,047 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit exploitation scan...
2025-06-30 19:17:55,048 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started gobuster in background
2025-06-30 19:17:55,049 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 🔧 TOOL START - GOBUSTER - Command: gobuster scan on ************
2025-06-30 19:17:55,050 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 19:17:55,050 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Starting Nikto web vulnerability scan...
2025-06-30 19:17:55,052 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 19:17:55,053 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started zap in background
2025-06-30 19:17:55,054 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 🔧 TOOL START - ZAP - Command: zap scan on ************
2025-06-30 19:17:55,055 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Formatted target for web tool: ************ -> http://************
2025-06-30 19:17:55,055 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 19:17:55,055 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 19:17:55,058 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 19:17:55,058 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 15% - All 8 tools are now running in parallel
2025-06-30 19:17:55,058 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 19:17:55,059 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Formatted target for web tool: ************ -> http://************
2025-06-30 19:17:55,060 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 10% - Starting SQLMap scan (maximum intensity)...
2025-06-30 19:17:55,061 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 19:17:55,062 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Formatted target for web tool: ************ -> http://************
2025-06-30 19:17:55,064 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Command: sqlmap -u http://************ --batch --level=5 --risk=3 --threads=5 --tamper=space2comment
2025-06-30 19:17:55,065 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 10% - Starting Dirb directory scan (maximum intensity)...
2025-06-30 19:17:55,065 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Formatted target for web tool: ************ -> http://************
2025-06-30 19:17:55,067 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 10% - Starting GoBuster directory scan (maximum intensity)...
2025-06-30 19:17:55,069 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Command: dirb http://************ /usr/share/dirb/wordlists/big.txt -w
2025-06-30 19:17:55,069 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - ZAP: 10% - Starting ZAP security scan...
2025-06-30 19:17:55,070 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Command: gobuster dir -u http://************ -w /usr/share/wordlists/dirb/big.txt -t 50 -q
2025-06-30 19:17:55,070 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 25% - Testing for SQL injection... (0s elapsed)
2025-06-30 19:17:55,073 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 25% - Scanning directories... (0s elapsed)
2025-06-30 19:17:55,074 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 25% - Brute forcing directories... (0s elapsed)
2025-06-30 19:17:57,040 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Scanning ports... 10% complete
2025-06-30 19:17:57,066 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 15% - 0/8 tools completed
2025-06-30 19:17:58,053 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Metasploit scanning... 10% complete
2025-06-30 19:17:58,054 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Scanning web vulnerabilities... 10% complete
2025-06-30 19:17:59,045 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - NMAP: 20% - Scanning ports... 20% complete
2025-06-30 19:17:59,072 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - ZAP: 10% - ZAP security scanning... 10% complete
2025-06-30 19:17:59,073 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 15% - 0/8 tools completed
2025-06-30 19:18:00,048 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - OpenVAS vulnerability scanning... 10% complete
2025-06-30 19:18:00,076 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 27% - Testing for SQL injection... (5s elapsed)
2025-06-30 19:18:00,079 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 27% - Scanning directories... (5s elapsed)
2025-06-30 19:18:00,081 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 28% - Brute forcing directories... (5s elapsed)
2025-06-30 19:18:01,052 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Scanning ports... 30% complete
2025-06-30 19:18:01,056 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - METASPLOIT: 25% - Metasploit scanning... 25% complete
2025-06-30 19:18:01,056 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Scanning web vulnerabilities... 25% complete
2025-06-30 19:18:01,080 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 15% - 0/8 tools completed
2025-06-30 19:18:03,056 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - NMAP: 40% - Scanning ports... 40% complete
2025-06-30 19:18:03,080 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - ZAP: 30% - ZAP security scanning... 30% complete
2025-06-30 19:18:03,083 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 15% - 0/8 tools completed
2025-06-30 19:18:04,059 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - METASPLOIT: 40% - Metasploit scanning... 40% complete
2025-06-30 19:18:04,060 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - NIKTO: 40% - Scanning web vulnerabilities... 40% complete
2025-06-30 19:18:05,051 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - OPENVAS: 20% - OpenVAS vulnerability scanning... 20% complete
2025-06-30 19:18:05,058 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - NMAP: 50% - Scanning ports... 50% complete
2025-06-30 19:18:05,085 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 15% - 0/8 tools completed
2025-06-30 19:18:05,085 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 30% - Testing for SQL injection... (10s elapsed)
2025-06-30 19:18:05,086 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 30% - Scanning directories... (10s elapsed)
2025-06-30 19:18:05,086 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 32% - Brute forcing directories... (10s elapsed)
2025-06-30 19:18:07,061 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - NMAP: 60% - Scanning ports... 60% complete
2025-06-30 19:18:07,062 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - METASPLOIT: 55% - Metasploit scanning... 55% complete
2025-06-30 19:18:07,062 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - NIKTO: 55% - Scanning web vulnerabilities... 55% complete
2025-06-30 19:18:07,082 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - ZAP: 50% - ZAP security scanning... 50% complete
2025-06-30 19:18:07,088 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 15% - 0/8 tools completed
2025-06-30 19:18:09,064 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - NMAP: 70% - Scanning ports... 70% complete
2025-06-30 19:18:09,092 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 15% - 0/8 tools completed
2025-06-30 19:18:10,054 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS vulnerability scanning... 30% complete
2025-06-30 19:18:10,065 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - METASPLOIT: 70% - Metasploit scanning... 70% complete
2025-06-30 19:18:10,066 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - NIKTO: 70% - Scanning web vulnerabilities... 70% complete
2025-06-30 19:18:10,089 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 33% - Scanning directories... (15s elapsed)
2025-06-30 19:18:10,090 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 35% - Brute forcing directories... (15s elapsed)
2025-06-30 19:18:10,090 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 33% - Testing for SQL injection... (15s elapsed)
2025-06-30 19:18:11,067 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - NMAP: 80% - Scanning ports... 80% complete
2025-06-30 19:18:11,069 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan (maximum intensity)...
2025-06-30 19:18:11,080 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-06-30 19:18:11,082 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using scan config: Complete audit with all available tools and options
2025-06-30 19:18:11,083 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Nmap options: -T4 -A --script=vuln,safe,discovery
2025-06-30 19:18:11,084 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Port range: 1-65535
2025-06-30 19:18:11,086 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - ZAP: 70% - ZAP security scanning... 70% complete
2025-06-30 19:18:11,087 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - ZAP: 10% - Starting security scan...
2025-06-30 19:18:11,088 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - ZAP: 25% - Performing HTTP security checks...
2025-06-30 19:18:11,092 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - ZAP: 50% - Analyzing security headers...
2025-06-30 19:18:11,093 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - ZAP: 75% - Checking response content...
2025-06-30 19:18:11,094 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - ZAP: 100% - Security scan completed
2025-06-30 19:18:11,095 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 15% - 0/8 tools completed
2025-06-30 19:18:11,096 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Found 6 security alerts
2025-06-30 19:18:11,098 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - ✅ TOOL RESULT - ZAP: completed
2025-06-30 19:18:13,068 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - METASPLOIT: 85% - Metasploit scanning... 85% complete
2025-06-30 19:18:13,069 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - NIKTO: 85% - Scanning web vulnerabilities... 85% complete
2025-06-30 19:18:13,070 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-30 19:18:13,070 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Starting Nikto scan (maximum intensity)...
2025-06-30 19:18:13,072 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-30 19:18:13,072 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Executing Nikto command...
2025-06-30 19:18:13,073 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-30 19:18:13,074 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Command: nikto -h http://************ -Format txt -output /tmp/nikto_1751303893.txt -maxtime 2400 -Tuning x
2025-06-30 19:18:13,076 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Scanning... (0s elapsed)
2025-06-30 19:18:13,097 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 25% - 1/8 tools completed
2025-06-30 19:18:15,057 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - OPENVAS: 40% - OpenVAS vulnerability scanning... 40% complete
2025-06-30 19:18:15,093 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 35% - Scanning directories... (20s elapsed)
2025-06-30 19:18:15,093 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 39% - Brute forcing directories... (20s elapsed)
2025-06-30 19:18:15,093 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 35% - Testing for SQL injection... (20s elapsed)
2025-06-30 19:18:15,099 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 25% - 1/8 tools completed
2025-06-30 19:18:17,102 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 25% - 1/8 tools completed
2025-06-30 19:18:19,105 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 25% - 1/8 tools completed
2025-06-30 19:18:20,060 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - OPENVAS: 50% - OpenVAS vulnerability scanning... 50% complete
2025-06-30 19:18:20,096 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 38% - Scanning directories... (25s elapsed)
2025-06-30 19:18:20,096 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 43% - Brute forcing directories... (25s elapsed)
2025-06-30 19:18:20,097 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 38% - Testing for SQL injection... (25s elapsed)
2025-06-30 19:18:21,108 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 25% - 1/8 tools completed
2025-06-30 19:18:23,078 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - NIKTO: 90% - Parsing results...
2025-06-30 19:18:23,082 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - NIKTO: 100% - Nikto scan completed
2025-06-30 19:18:23,084 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Scan completed - Found 5 potential issues
2025-06-30 19:18:23,087 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - ✅ TOOL RESULT - NIKTO: completed - Found 5 vulnerabilities
2025-06-30 19:18:23,111 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - 2/8 tools completed
2025-06-30 19:18:25,062 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - OPENVAS: 60% - OpenVAS vulnerability scanning... 60% complete
2025-06-30 19:18:25,100 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 46% - Brute forcing directories... (30s elapsed)
2025-06-30 19:18:25,100 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 41% - Scanning directories... (30s elapsed)
2025-06-30 19:18:25,101 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 41% - Testing for SQL injection... (30s elapsed)
2025-06-30 19:18:25,114 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - 2/8 tools completed
2025-06-30 19:18:27,118 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - 2/8 tools completed
2025-06-30 19:18:29,121 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - 2/8 tools completed
2025-06-30 19:18:30,065 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS vulnerability scanning... 70% complete
2025-06-30 19:18:30,104 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 50% - Brute forcing directories... (35s elapsed)
2025-06-30 19:18:30,105 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 43% - Scanning directories... (35s elapsed)
2025-06-30 19:18:30,106 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 43% - Testing for SQL injection... (35s elapsed)
2025-06-30 19:18:31,124 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - 2/8 tools completed
2025-06-30 19:18:33,126 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - 2/8 tools completed
2025-06-30 19:18:33,861 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-30 19:18:33,863 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-30 19:18:35,068 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - OPENVAS: 80% - OpenVAS vulnerability scanning... 80% complete
2025-06-30 19:18:35,070 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-30 19:18:35,092 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-30 19:18:35,095 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-30 19:18:35,096 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_1441914e-43f8-4174-a288-8a36c593e08b
2025-06-30 19:18:35,099 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 3 vulnerabilities
2025-06-30 19:18:35,107 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 53% - Brute forcing directories... (40s elapsed)
2025-06-30 19:18:35,107 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 46% - Scanning directories... (40s elapsed)
2025-06-30 19:18:35,108 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 46% - Testing for SQL injection... (40s elapsed)
2025-06-30 19:18:35,129 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:18:37,132 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:18:39,135 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:18:40,110 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 57% - Brute forcing directories... (45s elapsed)
2025-06-30 19:18:40,111 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 49% - Scanning directories... (45s elapsed)
2025-06-30 19:18:40,111 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 49% - Testing for SQL injection... (45s elapsed)
2025-06-30 19:18:41,138 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:18:43,141 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:18:45,112 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 61% - Brute forcing directories... (50s elapsed)
2025-06-30 19:18:45,114 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 52% - Scanning directories... (50s elapsed)
2025-06-30 19:18:45,115 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 52% - Testing for SQL injection... (50s elapsed)
2025-06-30 19:18:45,144 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:18:47,147 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:18:49,150 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:18:50,116 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 64% - Brute forcing directories... (55s elapsed)
2025-06-30 19:18:50,116 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 54% - Scanning directories... (55s elapsed)
2025-06-30 19:18:50,117 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 54% - Testing for SQL injection... (55s elapsed)
2025-06-30 19:18:50,747 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-30 19:18:50,749 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-30 19:18:51,153 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:18:53,156 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:18:55,120 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 68% - Brute forcing directories... (60s elapsed)
2025-06-30 19:18:55,121 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 57% - Scanning directories... (60s elapsed)
2025-06-30 19:18:55,121 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 57% - Testing for SQL injection... (60s elapsed)
2025-06-30 19:18:55,159 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:18:57,161 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:18:59,164 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:19:00,123 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 71% - Brute forcing directories... (65s elapsed)
2025-06-30 19:19:00,124 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 60% - Scanning directories... (65s elapsed)
2025-06-30 19:19:00,125 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 60% - Testing for SQL injection... (65s elapsed)
2025-06-30 19:19:01,167 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:19:03,170 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:19:05,127 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 62% - Scanning directories... (70s elapsed)
2025-06-30 19:19:05,127 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 75% - Brute forcing directories... (70s elapsed)
2025-06-30 19:19:05,128 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 62% - Testing for SQL injection... (70s elapsed)
2025-06-30 19:19:05,172 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:19:07,175 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:19:09,178 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:19:09,206 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-30 19:19:09,208 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-30 19:19:10,131 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 65% - Scanning directories... (75s elapsed)
2025-06-30 19:19:10,132 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 65% - Testing for SQL injection... (75s elapsed)
2025-06-30 19:19:10,132 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 79% - Brute forcing directories... (75s elapsed)
2025-06-30 19:19:11,181 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:19:13,185 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:19:15,135 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 68% - Scanning directories... (80s elapsed)
2025-06-30 19:19:15,136 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 68% - Testing for SQL injection... (80s elapsed)
2025-06-30 19:19:15,136 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 82% - Brute forcing directories... (80s elapsed)
2025-06-30 19:19:15,188 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:19:17,191 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:19:19,194 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:19:20,140 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 71% - Testing for SQL injection... (85s elapsed)
2025-06-30 19:19:20,140 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 71% - Scanning directories... (85s elapsed)
2025-06-30 19:19:20,140 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 86% - Brute forcing directories... (85s elapsed)
2025-06-30 19:19:21,197 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:19:23,200 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:19:25,144 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 73% - Testing for SQL injection... (90s elapsed)
2025-06-30 19:19:25,144 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 73% - Scanning directories... (90s elapsed)
2025-06-30 19:19:25,203 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 19:19:26,288 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-30 19:19:26,290 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 4 modules - Found 0 potential vulnerabilities
2025-06-30 19:19:26,293 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-30 19:19:27,206 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:19:29,210 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:19:30,147 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 76% - Testing for SQL injection... (95s elapsed)
2025-06-30 19:19:30,147 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 76% - Scanning directories... (95s elapsed)
2025-06-30 19:19:31,215 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:19:33,220 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:19:35,152 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 79% - Testing for SQL injection... (100s elapsed)
2025-06-30 19:19:35,153 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 79% - Scanning directories... (100s elapsed)
2025-06-30 19:19:35,224 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:19:37,230 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:19:39,237 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:19:40,159 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 81% - Testing for SQL injection... (105s elapsed)
2025-06-30 19:19:40,160 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 81% - Scanning directories... (105s elapsed)
2025-06-30 19:19:41,243 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:19:43,247 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:19:45,165 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 84% - Testing for SQL injection... (110s elapsed)
2025-06-30 19:19:45,167 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 84% - Scanning directories... (110s elapsed)
2025-06-30 19:19:45,250 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:19:47,255 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:19:49,261 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:19:50,172 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 87% - Testing for SQL injection... (115s elapsed)
2025-06-30 19:19:50,173 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 87% - Scanning directories... (115s elapsed)
2025-06-30 19:19:51,267 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:19:53,271 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:19:55,275 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:19:57,278 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:19:59,281 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 19:20:00,144 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Scan timeout after 2 minutes - completing with current results
2025-06-30 19:20:00,150 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 90% - Parsing results...
2025-06-30 19:20:00,158 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - ✅ TOOL RESULT - GOBUSTER: completed - Found 0 directories
2025-06-30 19:20:00,176 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Scan timeout after 2 minutes - completing with current results
2025-06-30 19:20:00,182 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DIRB: 90% - Parsing results...
2025-06-30 19:20:00,192 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - ✅ TOOL RESULT - DIRB: completed - Found 0 directories
2025-06-30 19:20:01,284 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 19:20:03,288 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 19:20:05,294 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 19:20:07,144 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-06-30 19:20:07,147 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Scan completed: /usr/bin/nmap -T4 -A --script=vuln,safe,discovery -p 1-65535 -oX /tmp/nmap_scan_40f7f6de-e1d5-46a9-9977-65d2fffe2c4a.xml ************
2025-06-30 19:20:07,149 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Found 4 ports
2025-06-30 19:20:07,155 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - ✅ TOOL RESULT - NMAP: completed - Found 4 ports - Found 177 vulnerabilities
2025-06-30 19:20:07,303 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:09,315 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:11,324 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:13,332 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:15,342 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:17,351 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:19,360 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:21,368 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:23,376 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:25,386 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:27,395 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:29,411 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:31,422 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:33,432 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:35,436 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:37,441 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:39,449 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:41,456 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:43,466 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:45,475 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:47,482 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:49,494 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:51,502 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:53,508 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:55,517 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:57,526 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:20:59,532 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 19:21:00,178 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Scan timeout after 3 minutes - completing with current results
2025-06-30 19:21:00,187 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - SQLMAP: 90% - Parsing results...
2025-06-30 19:21:00,201 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - ✅ TOOL RESULT - SQLMAP: completed - Found 0 vulnerabilities
2025-06-30 19:21:01,541 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - ✅ TOOL RESULT - DEEP_SCAN: all_tools_complete
2025-06-30 19:21:01,547 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 🔧 TOOL START - DEEP_SCAN - Command: 📊 Results Analysis and Consolidation
2025-06-30 19:21:01,568 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 96% - Consolidating port scan results...
2025-06-30 19:21:01,575 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 96% - Found 4 ports from nmap
2025-06-30 19:21:01,578 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 97% - Found 4 ports from metasploit
2025-06-30 19:21:01,585 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 97% - Consolidating vulnerability results...
2025-06-30 19:21:01,589 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 84% - Added 177 vulnerabilities from nmap
2025-06-30 19:21:01,590 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 84% - Added 3 vulnerabilities from openvas
2025-06-30 19:21:01,594 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 98% - Added 5 vulnerabilities from nikto
2025-06-30 19:21:01,596 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 98% - Added 6 vulnerabilities from zap
2025-06-30 19:21:01,600 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 98% - Calculating final summary...
2025-06-30 19:21:01,603 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 99% - Summary: 8 ports, 191 vulnerabilities
2025-06-30 19:21:01,607 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 99% - Saving vulnerabilities to database...
2025-06-30 19:21:01,956 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 98% - Saved 185 vulnerabilities to database
2025-06-30 19:21:01,958 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 99% - Creating frontend-compatible results...
2025-06-30 19:21:01,962 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 99% - Finalizing scan results...
2025-06-30 19:21:01,973 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - ✅ TOOL RESULT - DEEP_SCAN: completed
2025-06-30 19:21:01,980 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'network': {'nmap': {'status': 'completed', 'scan_id': '40f7f6de-e1d5-46a9-9977-65d2fffe2c4a', 'target': '************', 'start_time': '2025-06-30T17:18:11.085825', 'end_time': '2025-06-30T17:20:07.141201', 'scan_time': 116.055376, 'command': '/usr/bin/nmap -T4 -A --script=vuln,safe,discovery -p 1-65535 -oX /tmp/nmap_scan_40f7f6de-e1d5-46a9-9977-65d2fffe2c4a.xml ************', 'ports': [{'port': 25, 'protocol': 'tcp', 'state': 'open', 'service': 'smtp', 'version': 'Postfix smtpd'}, {'port': 80, 'protocol': 'tcp', 'state': 'open', 'service': 'http', 'version': 'Apache httpd 2.4.58 ((Ubuntu))'}, {'port': 139, 'protocol': 'tcp', 'state': 'open', 'service': 'netbios-ssn', 'version': 'Samba smbd 4.6.2'}, {'port': 445, 'protocol': 'tcp', 'state': 'open', 'service': 'netbios-ssn', 'version': 'Samba smbd 4.6.2'}], 'vulnerabilities': [{'name': '|   After NULL UDP avahi packet DoS (CVE-2011-1002).', 'severity': 'medium', 'description': '|   After NULL UDP avahi packet DoS (CVE-2011-1002).', 'tool': 'nmap', 'cve': 'CVE-2011-1002', 'id': 'CVE-2011-1002', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|_  Hosts are all up (not vulnerable).', 'severity': 'medium', 'description': '|_  Hosts are all up (not vulnerable).', 'tool': 'nmap', 'cve': None, 'source_tool': 'nmap', 'category': 'network'}, {'name': '|_  The SMTP server is not Exim: NOT VULNERABLE', 'severity': 'medium', 'description': '|_  The SMTP server is not Exim: NOT VULNERABLE', 'tool': 'nmap', 'cve': None, 'source_tool': 'nmap', 'category': 'network'}, {'name': '|   VULNERABLE:', 'severity': 'medium', 'description': '|   VULNERABLE:', 'tool': 'nmap', 'cve': None, 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     State: VULNERABLE', 'severity': 'medium', 'description': '|     State: VULNERABLE', 'tool': 'nmap', 'cve': None, 'source_tool': 'nmap', 'category': 'network'}, {'name': '|       eavesdropping, and are vulnerable to active man-in-the-middle attacks', 'severity': 'medium', 'description': '|       eavesdropping, and are vulnerable to active man-in-the-middle attacks', 'tool': 'nmap', 'cve': None, 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2024-38476\t9.8\thttps://vulners.com/cve/CVE-2024-38476', 'severity': 'medium', 'description': '|     \tCVE-2024-38476\t9.8\thttps://vulners.com/cve/CVE-2024-38476', 'tool': 'nmap', 'cve': 'CVE-2024-38476', 'id': 'CVE-2024-38476', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2024-38474\t9.8\thttps://vulners.com/cve/CVE-2024-38474', 'severity': 'medium', 'description': '|     \tCVE-2024-38474\t9.8\thttps://vulners.com/cve/CVE-2024-38474', 'tool': 'nmap', 'cve': 'CVE-2024-38474', 'id': 'CVE-2024-38474', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2024-40898\t9.1\thttps://vulners.com/cve/CVE-2024-40898', 'severity': 'medium', 'description': '|     \tCVE-2024-40898\t9.1\thttps://vulners.com/cve/CVE-2024-40898', 'tool': 'nmap', 'cve': 'CVE-2024-40898', 'id': 'CVE-2024-40898', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2024-38475\t9.1\thttps://vulners.com/cve/CVE-2024-38475', 'severity': 'medium', 'description': '|     \tCVE-2024-38475\t9.1\thttps://vulners.com/cve/CVE-2024-38475', 'tool': 'nmap', 'cve': 'CVE-2024-38475', 'id': 'CVE-2024-38475', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2024-38473\t8.1\thttps://vulners.com/cve/CVE-2024-38473', 'severity': 'medium', 'description': '|     \tCVE-2024-38473\t8.1\thttps://vulners.com/cve/CVE-2024-38473', 'tool': 'nmap', 'cve': 'CVE-2024-38473', 'id': 'CVE-2024-38473', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2024-39573\t7.5\thttps://vulners.com/cve/CVE-2024-39573', 'severity': 'medium', 'description': '|     \tCVE-2024-39573\t7.5\thttps://vulners.com/cve/CVE-2024-39573', 'tool': 'nmap', 'cve': 'CVE-2024-39573', 'id': 'CVE-2024-39573', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2024-38477\t7.5\thttps://vulners.com/cve/CVE-2024-38477', 'severity': 'medium', 'description': '|     \tCVE-2024-38477\t7.5\thttps://vulners.com/cve/CVE-2024-38477', 'tool': 'nmap', 'cve': 'CVE-2024-38477', 'id': 'CVE-2024-38477', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2024-38472\t7.5\thttps://vulners.com/cve/CVE-2024-38472', 'severity': 'medium', 'description': '|     \tCVE-2024-38472\t7.5\thttps://vulners.com/cve/CVE-2024-38472', 'tool': 'nmap', 'cve': 'CVE-2024-38472', 'id': 'CVE-2024-38472', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2024-27316\t7.5\thttps://vulners.com/cve/CVE-2024-27316', 'severity': 'medium', 'description': '|     \tCVE-2024-27316\t7.5\thttps://vulners.com/cve/CVE-2024-27316', 'tool': 'nmap', 'cve': 'CVE-2024-27316', 'id': 'CVE-2024-27316', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-38709\t7.3\thttps://vulners.com/cve/CVE-2023-38709', 'severity': 'medium', 'description': '|     \tCVE-2023-38709\t7.3\thttps://vulners.com/cve/CVE-2023-38709', 'tool': 'nmap', 'cve': 'CVE-2023-38709', 'id': 'CVE-2023-38709', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2024-24795\t6.3\thttps://vulners.com/cve/CVE-2024-24795', 'severity': 'medium', 'description': '|     \tCVE-2024-24795\t6.3\thttps://vulners.com/cve/CVE-2024-24795', 'tool': 'nmap', 'cve': 'CVE-2024-24795', 'id': 'CVE-2024-24795', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2024-39884\t6.2\thttps://vulners.com/cve/CVE-2024-39884', 'severity': 'medium', 'description': '|     \tCVE-2024-39884\t6.2\thttps://vulners.com/cve/CVE-2024-39884', 'tool': 'nmap', 'cve': 'CVE-2024-39884', 'id': 'CVE-2024-39884', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|_    \tCVE-2024-36387\t5.4\thttps://vulners.com/cve/CVE-2024-36387', 'severity': 'medium', 'description': '|_    \tCVE-2024-36387\t5.4\thttps://vulners.com/cve/CVE-2024-36387', 'tool': 'nmap', 'cve': 'CVE-2024-36387', 'id': 'CVE-2024-36387', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-1472\t10.0\thttps://vulners.com/cve/CVE-2020-1472', 'severity': 'medium', 'description': '|     \tCVE-2020-1472\t10.0\thttps://vulners.com/cve/CVE-2020-1472', 'tool': 'nmap', 'cve': 'CVE-2020-1472', 'id': 'CVE-2020-1472', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-7494\t10.0\thttps://vulners.com/cve/CVE-2017-7494', 'severity': 'medium', 'description': '|     \tCVE-2017-7494\t10.0\thttps://vulners.com/cve/CVE-2017-7494', 'tool': 'nmap', 'cve': 'CVE-2017-7494', 'id': 'CVE-2017-7494', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-3961\t9.8\thttps://vulners.com/cve/CVE-2023-3961', 'severity': 'medium', 'description': '|     \tCVE-2023-3961\t9.8\thttps://vulners.com/cve/CVE-2023-3961', 'tool': 'nmap', 'cve': 'CVE-2023-3961', 'id': 'CVE-2023-3961', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-45141\t9.8\thttps://vulners.com/cve/CVE-2022-45141', 'severity': 'medium', 'description': '|     \tCVE-2022-45141\t9.8\thttps://vulners.com/cve/CVE-2022-45141', 'tool': 'nmap', 'cve': 'CVE-2022-45141', 'id': 'CVE-2022-45141', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-14746\t9.8\thttps://vulners.com/cve/CVE-2017-14746', 'severity': 'medium', 'description': '|     \tCVE-2017-14746\t9.8\thttps://vulners.com/cve/CVE-2017-14746', 'tool': 'nmap', 'cve': 'CVE-2017-14746', 'id': 'CVE-2017-14746', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-44142\t9.0\thttps://vulners.com/cve/CVE-2021-44142', 'severity': 'medium', 'description': '|     \tCVE-2021-44142\t9.0\thttps://vulners.com/cve/CVE-2021-44142', 'tool': 'nmap', 'cve': 'CVE-2021-44142', 'id': 'CVE-2021-44142', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-25719\t9.0\thttps://vulners.com/cve/CVE-2020-25719', 'severity': 'medium', 'description': '|     \tCVE-2020-25719\t9.0\thttps://vulners.com/cve/CVE-2020-25719', 'tool': 'nmap', 'cve': 'CVE-2020-25719', 'id': 'CVE-2020-25719', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-17049\t9.0\thttps://vulners.com/cve/CVE-2020-17049', 'severity': 'medium', 'description': '|     \tCVE-2020-17049\t9.0\thttps://vulners.com/cve/CVE-2020-17049', 'tool': 'nmap', 'cve': 'CVE-2020-17049', 'id': 'CVE-2020-17049', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-42898\t8.8\thttps://vulners.com/cve/CVE-2022-42898', 'severity': 'medium', 'description': '|     \tCVE-2022-42898\t8.8\thttps://vulners.com/cve/CVE-2022-42898', 'tool': 'nmap', 'cve': 'CVE-2022-42898', 'id': 'CVE-2022-42898', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-32744\t8.8\thttps://vulners.com/cve/CVE-2022-32744', 'severity': 'medium', 'description': '|     \tCVE-2022-32744\t8.8\thttps://vulners.com/cve/CVE-2022-32744', 'tool': 'nmap', 'cve': 'CVE-2022-32744', 'id': 'CVE-2022-32744', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-2031\t8.8\thttps://vulners.com/cve/CVE-2022-2031', 'severity': 'medium', 'description': '|     \tCVE-2022-2031\t8.8\thttps://vulners.com/cve/CVE-2022-2031', 'tool': 'nmap', 'cve': 'CVE-2022-2031', 'id': 'CVE-2022-2031', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-0336\t8.8\thttps://vulners.com/cve/CVE-2022-0336', 'severity': 'medium', 'description': '|     \tCVE-2022-0336\t8.8\thttps://vulners.com/cve/CVE-2022-0336', 'tool': 'nmap', 'cve': 'CVE-2022-0336', 'id': 'CVE-2022-0336', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-3738\t8.8\thttps://vulners.com/cve/CVE-2021-3738', 'severity': 'medium', 'description': '|     \tCVE-2021-3738\t8.8\thttps://vulners.com/cve/CVE-2021-3738', 'tool': 'nmap', 'cve': 'CVE-2021-3738', 'id': 'CVE-2021-3738', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-25722\t8.8\thttps://vulners.com/cve/CVE-2020-25722', 'severity': 'medium', 'description': '|     \tCVE-2020-25722\t8.8\thttps://vulners.com/cve/CVE-2020-25722', 'tool': 'nmap', 'cve': 'CVE-2020-25722', 'id': 'CVE-2020-25722', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-25721\t8.8\thttps://vulners.com/cve/CVE-2020-25721', 'severity': 'medium', 'description': '|     \tCVE-2020-25721\t8.8\thttps://vulners.com/cve/CVE-2020-25721', 'tool': 'nmap', 'cve': 'CVE-2020-25721', 'id': 'CVE-2020-25721', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-25718\t8.8\thttps://vulners.com/cve/CVE-2020-25718', 'severity': 'medium', 'description': '|     \tCVE-2020-25718\t8.8\thttps://vulners.com/cve/CVE-2020-25718', 'tool': 'nmap', 'cve': 'CVE-2020-25718', 'id': 'CVE-2020-25718', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-10858\t8.8\thttps://vulners.com/cve/CVE-2018-10858', 'severity': 'medium', 'description': '|     \tCVE-2018-10858\t8.8\thttps://vulners.com/cve/CVE-2018-10858', 'tool': 'nmap', 'cve': 'CVE-2018-10858', 'id': 'CVE-2018-10858', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-1057\t8.8\thttps://vulners.com/cve/CVE-2018-1057', 'severity': 'medium', 'description': '|     \tCVE-2018-1057\t8.8\thttps://vulners.com/cve/CVE-2018-1057', 'tool': 'nmap', 'cve': 'CVE-2018-1057', 'id': 'CVE-2018-1057', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-25717\t8.5\thttps://vulners.com/cve/CVE-2020-25717', 'severity': 'medium', 'description': '|     \tCVE-2020-25717\t8.5\thttps://vulners.com/cve/CVE-2020-25717', 'tool': 'nmap', 'cve': 'CVE-2020-25717', 'id': 'CVE-2020-25717', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-38023\t8.1\thttps://vulners.com/cve/CVE-2022-38023', 'severity': 'medium', 'description': '|     \tCVE-2022-38023\t8.1\thttps://vulners.com/cve/CVE-2022-38023', 'tool': 'nmap', 'cve': 'CVE-2022-38023', 'id': 'CVE-2022-38023', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-37966\t8.1\thttps://vulners.com/cve/CVE-2022-37966', 'severity': 'medium', 'description': '|     \tCVE-2022-37966\t8.1\thttps://vulners.com/cve/CVE-2022-37966', 'tool': 'nmap', 'cve': 'CVE-2022-37966', 'id': 'CVE-2022-37966', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-32745\t8.1\thttps://vulners.com/cve/CVE-2022-32745', 'severity': 'medium', 'description': '|     \tCVE-2022-32745\t8.1\thttps://vulners.com/cve/CVE-2022-32745', 'tool': 'nmap', 'cve': 'CVE-2022-32745', 'id': 'CVE-2022-32745', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-11103\t8.1\thttps://vulners.com/cve/CVE-2017-11103', 'severity': 'medium', 'description': '|     \tCVE-2017-11103\t8.1\thttps://vulners.com/cve/CVE-2017-11103', 'tool': 'nmap', 'cve': 'CVE-2017-11103', 'id': 'CVE-2017-11103', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-10745\t7.8\thttps://vulners.com/cve/CVE-2020-10745', 'severity': 'medium', 'description': '|     \tCVE-2020-10745\t7.8\thttps://vulners.com/cve/CVE-2020-10745', 'tool': 'nmap', 'cve': 'CVE-2020-10745', 'id': 'CVE-2020-10745', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-0614\t7.7\thttps://vulners.com/cve/CVE-2023-0614', 'severity': 'medium', 'description': '|     \tCVE-2023-0614\t7.7\thttps://vulners.com/cve/CVE-2023-0614', 'tool': 'nmap', 'cve': 'CVE-2023-0614', 'id': 'CVE-2023-0614', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-4154\t7.5\thttps://vulners.com/cve/CVE-2023-4154', 'severity': 'medium', 'description': '|     \tCVE-2023-4154\t7.5\thttps://vulners.com/cve/CVE-2023-4154', 'tool': 'nmap', 'cve': 'CVE-2023-4154', 'id': 'CVE-2023-4154', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-34966\t7.5\thttps://vulners.com/cve/CVE-2023-34966', 'severity': 'medium', 'description': '|     \tCVE-2023-34966\t7.5\thttps://vulners.com/cve/CVE-2023-34966', 'tool': 'nmap', 'cve': 'CVE-2023-34966', 'id': 'CVE-2023-34966', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-32743\t7.5\thttps://vulners.com/cve/CVE-2022-32743', 'severity': 'medium', 'description': '|     \tCVE-2022-32743\t7.5\thttps://vulners.com/cve/CVE-2022-32743', 'tool': 'nmap', 'cve': 'CVE-2022-32743', 'id': 'CVE-2022-32743', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-23192\t7.5\thttps://vulners.com/cve/CVE-2021-23192', 'severity': 'medium', 'description': '|     \tCVE-2021-23192\t7.5\thttps://vulners.com/cve/CVE-2021-23192', 'tool': 'nmap', 'cve': 'CVE-2021-23192', 'id': 'CVE-2021-23192', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-20277\t7.5\thttps://vulners.com/cve/CVE-2021-20277', 'severity': 'medium', 'description': '|     \tCVE-2021-20277\t7.5\thttps://vulners.com/cve/CVE-2021-20277', 'tool': 'nmap', 'cve': 'CVE-2021-20277', 'id': 'CVE-2021-20277', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-27840\t7.5\thttps://vulners.com/cve/CVE-2020-27840', 'severity': 'medium', 'description': '|     \tCVE-2020-27840\t7.5\thttps://vulners.com/cve/CVE-2020-27840', 'tool': 'nmap', 'cve': 'CVE-2020-27840', 'id': 'CVE-2020-27840', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-14303\t7.5\thttps://vulners.com/cve/CVE-2020-14303', 'severity': 'medium', 'description': '|     \tCVE-2020-14303\t7.5\thttps://vulners.com/cve/CVE-2020-14303', 'tool': 'nmap', 'cve': 'CVE-2020-14303', 'id': 'CVE-2020-14303', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-10704\t7.5\thttps://vulners.com/cve/CVE-2020-10704', 'severity': 'medium', 'description': '|     \tCVE-2020-10704\t7.5\thttps://vulners.com/cve/CVE-2020-10704', 'tool': 'nmap', 'cve': 'CVE-2020-10704', 'id': 'CVE-2020-10704', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-16860\t7.5\thttps://vulners.com/cve/CVE-2018-16860', 'severity': 'medium', 'description': '|     \tCVE-2018-16860\t7.5\thttps://vulners.com/cve/CVE-2018-16860', 'tool': 'nmap', 'cve': 'CVE-2018-16860', 'id': 'CVE-2018-16860', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-15275\t7.5\thttps://vulners.com/cve/CVE-2017-15275', 'severity': 'medium', 'description': '|     \tCVE-2017-15275\t7.5\thttps://vulners.com/cve/CVE-2017-15275', 'tool': 'nmap', 'cve': 'CVE-2017-15275', 'id': 'CVE-2017-15275', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-12151\t7.4\thttps://vulners.com/cve/CVE-2017-12151', 'severity': 'medium', 'description': '|     \tCVE-2017-12151\t7.4\thttps://vulners.com/cve/CVE-2017-12151', 'tool': 'nmap', 'cve': 'CVE-2017-12151', 'id': 'CVE-2017-12151', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-12150\t7.4\thttps://vulners.com/cve/CVE-2017-12150', 'severity': 'medium', 'description': '|     \tCVE-2017-12150\t7.4\thttps://vulners.com/cve/CVE-2017-12150', 'tool': 'nmap', 'cve': 'CVE-2017-12150', 'id': 'CVE-2017-12150', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-37967\t7.2\thttps://vulners.com/cve/CVE-2022-37967', 'severity': 'medium', 'description': '|     \tCVE-2022-37967\t7.2\thttps://vulners.com/cve/CVE-2022-37967', 'tool': 'nmap', 'cve': 'CVE-2022-37967', 'id': 'CVE-2022-37967', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-12163\t7.1\thttps://vulners.com/cve/CVE-2017-12163', 'severity': 'medium', 'description': '|     \tCVE-2017-12163\t7.1\thttps://vulners.com/cve/CVE-2017-12163', 'tool': 'nmap', 'cve': 'CVE-2017-12163', 'id': 'CVE-2017-12163', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-20316\t6.8\thttps://vulners.com/cve/CVE-2021-20316', 'severity': 'medium', 'description': '|     \tCVE-2021-20316\t6.8\thttps://vulners.com/cve/CVE-2021-20316', 'tool': 'nmap', 'cve': 'CVE-2021-20316', 'id': 'CVE-2021-20316', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-20254\t6.8\thttps://vulners.com/cve/CVE-2021-20254', 'severity': 'medium', 'description': '|     \tCVE-2021-20254\t6.8\thttps://vulners.com/cve/CVE-2021-20254', 'tool': 'nmap', 'cve': 'CVE-2021-20254', 'id': 'CVE-2021-20254', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-5568\t6.5\thttps://vulners.com/cve/CVE-2023-5568', 'severity': 'medium', 'description': '|     \tCVE-2023-5568\t6.5\thttps://vulners.com/cve/CVE-2023-5568', 'tool': 'nmap', 'cve': 'CVE-2023-5568', 'id': 'CVE-2023-5568', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-42670\t6.5\thttps://vulners.com/cve/CVE-2023-42670', 'severity': 'medium', 'description': '|     \tCVE-2023-42670\t6.5\thttps://vulners.com/cve/CVE-2023-42670', 'tool': 'nmap', 'cve': 'CVE-2023-42670', 'id': 'CVE-2023-42670', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-42669\t6.5\thttps://vulners.com/cve/CVE-2023-42669', 'severity': 'medium', 'description': '|     \tCVE-2023-42669\t6.5\thttps://vulners.com/cve/CVE-2023-42669', 'tool': 'nmap', 'cve': 'CVE-2023-42669', 'id': 'CVE-2023-42669', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-4091\t6.5\thttps://vulners.com/cve/CVE-2023-4091', 'severity': 'medium', 'description': '|     \tCVE-2023-4091\t6.5\thttps://vulners.com/cve/CVE-2023-4091', 'tool': 'nmap', 'cve': 'CVE-2023-4091', 'id': 'CVE-2023-4091', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-3592\t6.5\thttps://vulners.com/cve/CVE-2022-3592', 'severity': 'medium', 'description': '|     \tCVE-2022-3592\t6.5\thttps://vulners.com/cve/CVE-2022-3592', 'tool': 'nmap', 'cve': 'CVE-2022-3592', 'id': 'CVE-2022-3592', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-3437\t6.5\thttps://vulners.com/cve/CVE-2022-3437', 'severity': 'medium', 'description': '|     \tCVE-2022-3437\t6.5\thttps://vulners.com/cve/CVE-2022-3437', 'tool': 'nmap', 'cve': 'CVE-2022-3437', 'id': 'CVE-2022-3437', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-3671\t6.5\thttps://vulners.com/cve/CVE-2021-3671', 'severity': 'medium', 'description': '|     \tCVE-2021-3671\t6.5\thttps://vulners.com/cve/CVE-2021-3671', 'tool': 'nmap', 'cve': 'CVE-2021-3671', 'id': 'CVE-2021-3671', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-3670\t6.5\thttps://vulners.com/cve/CVE-2021-3670', 'severity': 'medium', 'description': '|     \tCVE-2021-3670\t6.5\thttps://vulners.com/cve/CVE-2021-3670', 'tool': 'nmap', 'cve': 'CVE-2021-3670', 'id': 'CVE-2021-3670', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-14383\t6.5\thttps://vulners.com/cve/CVE-2020-14383', 'severity': 'medium', 'description': '|     \tCVE-2020-14383\t6.5\thttps://vulners.com/cve/CVE-2020-14383', 'tool': 'nmap', 'cve': 'CVE-2020-14383', 'id': 'CVE-2020-14383', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-10760\t6.5\thttps://vulners.com/cve/CVE-2020-10760', 'severity': 'medium', 'description': '|     \tCVE-2020-10760\t6.5\thttps://vulners.com/cve/CVE-2020-10760', 'tool': 'nmap', 'cve': 'CVE-2020-10760', 'id': 'CVE-2020-10760', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-10730\t6.5\thttps://vulners.com/cve/CVE-2020-10730', 'severity': 'medium', 'description': '|     \tCVE-2020-10730\t6.5\thttps://vulners.com/cve/CVE-2020-10730', 'tool': 'nmap', 'cve': 'CVE-2020-10730', 'id': 'CVE-2020-10730', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-3824\t6.5\thttps://vulners.com/cve/CVE-2019-3824', 'severity': 'medium', 'description': '|     \tCVE-2019-3824\t6.5\thttps://vulners.com/cve/CVE-2019-3824', 'tool': 'nmap', 'cve': 'CVE-2019-3824', 'id': 'CVE-2019-3824', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-10218\t6.5\thttps://vulners.com/cve/CVE-2019-10218', 'severity': 'medium', 'description': '|     \tCVE-2019-10218\t6.5\thttps://vulners.com/cve/CVE-2019-10218', 'tool': 'nmap', 'cve': 'CVE-2019-10218', 'id': 'CVE-2019-10218', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-16851\t6.5\thttps://vulners.com/cve/CVE-2018-16851', 'severity': 'medium', 'description': '|     \tCVE-2018-16851\t6.5\thttps://vulners.com/cve/CVE-2018-16851', 'tool': 'nmap', 'cve': 'CVE-2018-16851', 'id': 'CVE-2018-16851', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-16841\t6.5\thttps://vulners.com/cve/CVE-2018-16841', 'severity': 'medium', 'description': '|     \tCVE-2018-16841\t6.5\thttps://vulners.com/cve/CVE-2018-16841', 'tool': 'nmap', 'cve': 'CVE-2018-16841', 'id': 'CVE-2018-16841', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-14629\t6.5\thttps://vulners.com/cve/CVE-2018-14629', 'severity': 'medium', 'description': '|     \tCVE-2018-14629\t6.5\thttps://vulners.com/cve/CVE-2018-14629', 'tool': 'nmap', 'cve': 'CVE-2018-14629', 'id': 'CVE-2018-14629', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-10919\t6.5\thttps://vulners.com/cve/CVE-2018-10919', 'severity': 'medium', 'description': '|     \tCVE-2018-10919\t6.5\thttps://vulners.com/cve/CVE-2018-10919', 'tool': 'nmap', 'cve': 'CVE-2018-10919', 'id': 'CVE-2018-10919', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-14870\t6.4\thttps://vulners.com/cve/CVE-2019-14870', 'severity': 'medium', 'description': '|     \tCVE-2019-14870\t6.4\thttps://vulners.com/cve/CVE-2019-14870', 'tool': 'nmap', 'cve': 'CVE-2019-14870', 'id': 'CVE-2019-14870', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-0922\t5.9\thttps://vulners.com/cve/CVE-2023-0922', 'severity': 'medium', 'description': '|     \tCVE-2023-0922\t5.9\thttps://vulners.com/cve/CVE-2023-0922', 'tool': 'nmap', 'cve': 'CVE-2023-0922', 'id': 'CVE-2023-0922', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-20251\t5.9\thttps://vulners.com/cve/CVE-2021-20251', 'severity': 'medium', 'description': '|     \tCVE-2021-20251\t5.9\thttps://vulners.com/cve/CVE-2021-20251', 'tool': 'nmap', 'cve': 'CVE-2021-20251', 'id': 'CVE-2021-20251', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2016-2124\t5.9\thttps://vulners.com/cve/CVE-2016-2124', 'severity': 'medium', 'description': '|     \tCVE-2016-2124\t5.9\thttps://vulners.com/cve/CVE-2016-2124', 'tool': 'nmap', 'cve': 'CVE-2016-2124', 'id': 'CVE-2016-2124', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-1615\t5.5\thttps://vulners.com/cve/CVE-2022-1615', 'severity': 'medium', 'description': '|     \tCVE-2022-1615\t5.5\thttps://vulners.com/cve/CVE-2022-1615', 'tool': 'nmap', 'cve': 'CVE-2022-1615', 'id': 'CVE-2022-1615', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-14323\t5.5\thttps://vulners.com/cve/CVE-2020-14323', 'severity': 'medium', 'description': '|     \tCVE-2020-14323\t5.5\thttps://vulners.com/cve/CVE-2020-14323', 'tool': 'nmap', 'cve': 'CVE-2020-14323', 'id': 'CVE-2020-14323', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-3880\t5.5\thttps://vulners.com/cve/CVE-2019-3880', 'severity': 'medium', 'description': '|     \tCVE-2019-3880\t5.5\thttps://vulners.com/cve/CVE-2019-3880', 'tool': 'nmap', 'cve': 'CVE-2019-3880', 'id': 'CVE-2019-3880', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-14902\t5.5\thttps://vulners.com/cve/CVE-2019-14902', 'severity': 'medium', 'description': '|     \tCVE-2019-14902\t5.5\thttps://vulners.com/cve/CVE-2019-14902', 'tool': 'nmap', 'cve': 'CVE-2019-14902', 'id': 'CVE-2019-14902', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-32746\t5.4\thttps://vulners.com/cve/CVE-2022-32746', 'severity': 'medium', 'description': '|     \tCVE-2022-32746\t5.4\thttps://vulners.com/cve/CVE-2022-32746', 'tool': 'nmap', 'cve': 'CVE-2022-32746', 'id': 'CVE-2022-32746', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-14833\t5.4\thttps://vulners.com/cve/CVE-2019-14833', 'severity': 'medium', 'description': '|     \tCVE-2019-14833\t5.4\thttps://vulners.com/cve/CVE-2019-14833', 'tool': 'nmap', 'cve': 'CVE-2019-14833', 'id': 'CVE-2019-14833', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-34968\t5.3\thttps://vulners.com/cve/CVE-2023-34968', 'severity': 'medium', 'description': '|     \tCVE-2023-34968\t5.3\thttps://vulners.com/cve/CVE-2023-34968', 'tool': 'nmap', 'cve': 'CVE-2023-34968', 'id': 'CVE-2023-34968', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-34967\t5.3\thttps://vulners.com/cve/CVE-2023-34967', 'severity': 'medium', 'description': '|     \tCVE-2023-34967\t5.3\thttps://vulners.com/cve/CVE-2023-34967', 'tool': 'nmap', 'cve': 'CVE-2023-34967', 'id': 'CVE-2023-34967', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-14861\t5.3\thttps://vulners.com/cve/CVE-2019-14861', 'severity': 'medium', 'description': '|     \tCVE-2019-14861\t5.3\thttps://vulners.com/cve/CVE-2019-14861', 'tool': 'nmap', 'cve': 'CVE-2019-14861', 'id': 'CVE-2019-14861', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-14847\t4.9\thttps://vulners.com/cve/CVE-2019-14847', 'severity': 'medium', 'description': '|     \tCVE-2019-14847\t4.9\thttps://vulners.com/cve/CVE-2019-14847', 'tool': 'nmap', 'cve': 'CVE-2019-14847', 'id': 'CVE-2019-14847', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-0225\t4.3\thttps://vulners.com/cve/CVE-2023-0225', 'severity': 'medium', 'description': '|     \tCVE-2023-0225\t4.3\thttps://vulners.com/cve/CVE-2023-0225', 'tool': 'nmap', 'cve': 'CVE-2023-0225', 'id': 'CVE-2023-0225', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-32742\t4.3\thttps://vulners.com/cve/CVE-2022-32742', 'severity': 'medium', 'description': '|     \tCVE-2022-32742\t4.3\thttps://vulners.com/cve/CVE-2022-32742', 'tool': 'nmap', 'cve': 'CVE-2022-32742', 'id': 'CVE-2022-32742', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-44141\t4.3\thttps://vulners.com/cve/CVE-2021-44141', 'severity': 'medium', 'description': '|     \tCVE-2021-44141\t4.3\thttps://vulners.com/cve/CVE-2021-44141', 'tool': 'nmap', 'cve': 'CVE-2021-44141', 'id': 'CVE-2021-44141', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-14318\t4.3\thttps://vulners.com/cve/CVE-2020-14318', 'severity': 'medium', 'description': '|     \tCVE-2020-14318\t4.3\thttps://vulners.com/cve/CVE-2020-14318', 'tool': 'nmap', 'cve': 'CVE-2020-14318', 'id': 'CVE-2020-14318', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-14628\t4.3\thttps://vulners.com/cve/CVE-2018-14628', 'severity': 'medium', 'description': '|     \tCVE-2018-14628\t4.3\thttps://vulners.com/cve/CVE-2018-14628', 'tool': 'nmap', 'cve': 'CVE-2018-14628', 'id': 'CVE-2018-14628', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-1050\t4.3\thttps://vulners.com/cve/CVE-2018-1050', 'severity': 'medium', 'description': '|     \tCVE-2018-1050\t4.3\thttps://vulners.com/cve/CVE-2018-1050', 'tool': 'nmap', 'cve': 'CVE-2018-1050', 'id': 'CVE-2018-1050', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-43566\t2.5\thttps://vulners.com/cve/CVE-2021-43566', 'severity': 'medium', 'description': '|     \tCVE-2021-43566\t2.5\thttps://vulners.com/cve/CVE-2021-43566', 'tool': 'nmap', 'cve': 'CVE-2021-43566', 'id': 'CVE-2021-43566', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-1472\t10.0\thttps://vulners.com/cve/CVE-2020-1472', 'severity': 'medium', 'description': '|     \tCVE-2020-1472\t10.0\thttps://vulners.com/cve/CVE-2020-1472', 'tool': 'nmap', 'cve': 'CVE-2020-1472', 'id': 'CVE-2020-1472', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-7494\t10.0\thttps://vulners.com/cve/CVE-2017-7494', 'severity': 'medium', 'description': '|     \tCVE-2017-7494\t10.0\thttps://vulners.com/cve/CVE-2017-7494', 'tool': 'nmap', 'cve': 'CVE-2017-7494', 'id': 'CVE-2017-7494', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-3961\t9.8\thttps://vulners.com/cve/CVE-2023-3961', 'severity': 'medium', 'description': '|     \tCVE-2023-3961\t9.8\thttps://vulners.com/cve/CVE-2023-3961', 'tool': 'nmap', 'cve': 'CVE-2023-3961', 'id': 'CVE-2023-3961', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-45141\t9.8\thttps://vulners.com/cve/CVE-2022-45141', 'severity': 'medium', 'description': '|     \tCVE-2022-45141\t9.8\thttps://vulners.com/cve/CVE-2022-45141', 'tool': 'nmap', 'cve': 'CVE-2022-45141', 'id': 'CVE-2022-45141', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-14746\t9.8\thttps://vulners.com/cve/CVE-2017-14746', 'severity': 'medium', 'description': '|     \tCVE-2017-14746\t9.8\thttps://vulners.com/cve/CVE-2017-14746', 'tool': 'nmap', 'cve': 'CVE-2017-14746', 'id': 'CVE-2017-14746', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-44142\t9.0\thttps://vulners.com/cve/CVE-2021-44142', 'severity': 'medium', 'description': '|     \tCVE-2021-44142\t9.0\thttps://vulners.com/cve/CVE-2021-44142', 'tool': 'nmap', 'cve': 'CVE-2021-44142', 'id': 'CVE-2021-44142', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-25719\t9.0\thttps://vulners.com/cve/CVE-2020-25719', 'severity': 'medium', 'description': '|     \tCVE-2020-25719\t9.0\thttps://vulners.com/cve/CVE-2020-25719', 'tool': 'nmap', 'cve': 'CVE-2020-25719', 'id': 'CVE-2020-25719', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-17049\t9.0\thttps://vulners.com/cve/CVE-2020-17049', 'severity': 'medium', 'description': '|     \tCVE-2020-17049\t9.0\thttps://vulners.com/cve/CVE-2020-17049', 'tool': 'nmap', 'cve': 'CVE-2020-17049', 'id': 'CVE-2020-17049', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-42898\t8.8\thttps://vulners.com/cve/CVE-2022-42898', 'severity': 'medium', 'description': '|     \tCVE-2022-42898\t8.8\thttps://vulners.com/cve/CVE-2022-42898', 'tool': 'nmap', 'cve': 'CVE-2022-42898', 'id': 'CVE-2022-42898', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-32744\t8.8\thttps://vulners.com/cve/CVE-2022-32744', 'severity': 'medium', 'description': '|     \tCVE-2022-32744\t8.8\thttps://vulners.com/cve/CVE-2022-32744', 'tool': 'nmap', 'cve': 'CVE-2022-32744', 'id': 'CVE-2022-32744', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-2031\t8.8\thttps://vulners.com/cve/CVE-2022-2031', 'severity': 'medium', 'description': '|     \tCVE-2022-2031\t8.8\thttps://vulners.com/cve/CVE-2022-2031', 'tool': 'nmap', 'cve': 'CVE-2022-2031', 'id': 'CVE-2022-2031', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-0336\t8.8\thttps://vulners.com/cve/CVE-2022-0336', 'severity': 'medium', 'description': '|     \tCVE-2022-0336\t8.8\thttps://vulners.com/cve/CVE-2022-0336', 'tool': 'nmap', 'cve': 'CVE-2022-0336', 'id': 'CVE-2022-0336', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-3738\t8.8\thttps://vulners.com/cve/CVE-2021-3738', 'severity': 'medium', 'description': '|     \tCVE-2021-3738\t8.8\thttps://vulners.com/cve/CVE-2021-3738', 'tool': 'nmap', 'cve': 'CVE-2021-3738', 'id': 'CVE-2021-3738', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-25722\t8.8\thttps://vulners.com/cve/CVE-2020-25722', 'severity': 'medium', 'description': '|     \tCVE-2020-25722\t8.8\thttps://vulners.com/cve/CVE-2020-25722', 'tool': 'nmap', 'cve': 'CVE-2020-25722', 'id': 'CVE-2020-25722', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-25721\t8.8\thttps://vulners.com/cve/CVE-2020-25721', 'severity': 'medium', 'description': '|     \tCVE-2020-25721\t8.8\thttps://vulners.com/cve/CVE-2020-25721', 'tool': 'nmap', 'cve': 'CVE-2020-25721', 'id': 'CVE-2020-25721', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-25718\t8.8\thttps://vulners.com/cve/CVE-2020-25718', 'severity': 'medium', 'description': '|     \tCVE-2020-25718\t8.8\thttps://vulners.com/cve/CVE-2020-25718', 'tool': 'nmap', 'cve': 'CVE-2020-25718', 'id': 'CVE-2020-25718', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-10858\t8.8\thttps://vulners.com/cve/CVE-2018-10858', 'severity': 'medium', 'description': '|     \tCVE-2018-10858\t8.8\thttps://vulners.com/cve/CVE-2018-10858', 'tool': 'nmap', 'cve': 'CVE-2018-10858', 'id': 'CVE-2018-10858', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-1057\t8.8\thttps://vulners.com/cve/CVE-2018-1057', 'severity': 'medium', 'description': '|     \tCVE-2018-1057\t8.8\thttps://vulners.com/cve/CVE-2018-1057', 'tool': 'nmap', 'cve': 'CVE-2018-1057', 'id': 'CVE-2018-1057', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-25717\t8.5\thttps://vulners.com/cve/CVE-2020-25717', 'severity': 'medium', 'description': '|     \tCVE-2020-25717\t8.5\thttps://vulners.com/cve/CVE-2020-25717', 'tool': 'nmap', 'cve': 'CVE-2020-25717', 'id': 'CVE-2020-25717', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-38023\t8.1\thttps://vulners.com/cve/CVE-2022-38023', 'severity': 'medium', 'description': '|     \tCVE-2022-38023\t8.1\thttps://vulners.com/cve/CVE-2022-38023', 'tool': 'nmap', 'cve': 'CVE-2022-38023', 'id': 'CVE-2022-38023', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-37966\t8.1\thttps://vulners.com/cve/CVE-2022-37966', 'severity': 'medium', 'description': '|     \tCVE-2022-37966\t8.1\thttps://vulners.com/cve/CVE-2022-37966', 'tool': 'nmap', 'cve': 'CVE-2022-37966', 'id': 'CVE-2022-37966', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-32745\t8.1\thttps://vulners.com/cve/CVE-2022-32745', 'severity': 'medium', 'description': '|     \tCVE-2022-32745\t8.1\thttps://vulners.com/cve/CVE-2022-32745', 'tool': 'nmap', 'cve': 'CVE-2022-32745', 'id': 'CVE-2022-32745', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-11103\t8.1\thttps://vulners.com/cve/CVE-2017-11103', 'severity': 'medium', 'description': '|     \tCVE-2017-11103\t8.1\thttps://vulners.com/cve/CVE-2017-11103', 'tool': 'nmap', 'cve': 'CVE-2017-11103', 'id': 'CVE-2017-11103', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-10745\t7.8\thttps://vulners.com/cve/CVE-2020-10745', 'severity': 'medium', 'description': '|     \tCVE-2020-10745\t7.8\thttps://vulners.com/cve/CVE-2020-10745', 'tool': 'nmap', 'cve': 'CVE-2020-10745', 'id': 'CVE-2020-10745', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-0614\t7.7\thttps://vulners.com/cve/CVE-2023-0614', 'severity': 'medium', 'description': '|     \tCVE-2023-0614\t7.7\thttps://vulners.com/cve/CVE-2023-0614', 'tool': 'nmap', 'cve': 'CVE-2023-0614', 'id': 'CVE-2023-0614', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-4154\t7.5\thttps://vulners.com/cve/CVE-2023-4154', 'severity': 'medium', 'description': '|     \tCVE-2023-4154\t7.5\thttps://vulners.com/cve/CVE-2023-4154', 'tool': 'nmap', 'cve': 'CVE-2023-4154', 'id': 'CVE-2023-4154', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-34966\t7.5\thttps://vulners.com/cve/CVE-2023-34966', 'severity': 'medium', 'description': '|     \tCVE-2023-34966\t7.5\thttps://vulners.com/cve/CVE-2023-34966', 'tool': 'nmap', 'cve': 'CVE-2023-34966', 'id': 'CVE-2023-34966', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-32743\t7.5\thttps://vulners.com/cve/CVE-2022-32743', 'severity': 'medium', 'description': '|     \tCVE-2022-32743\t7.5\thttps://vulners.com/cve/CVE-2022-32743', 'tool': 'nmap', 'cve': 'CVE-2022-32743', 'id': 'CVE-2022-32743', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-23192\t7.5\thttps://vulners.com/cve/CVE-2021-23192', 'severity': 'medium', 'description': '|     \tCVE-2021-23192\t7.5\thttps://vulners.com/cve/CVE-2021-23192', 'tool': 'nmap', 'cve': 'CVE-2021-23192', 'id': 'CVE-2021-23192', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-20277\t7.5\thttps://vulners.com/cve/CVE-2021-20277', 'severity': 'medium', 'description': '|     \tCVE-2021-20277\t7.5\thttps://vulners.com/cve/CVE-2021-20277', 'tool': 'nmap', 'cve': 'CVE-2021-20277', 'id': 'CVE-2021-20277', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-27840\t7.5\thttps://vulners.com/cve/CVE-2020-27840', 'severity': 'medium', 'description': '|     \tCVE-2020-27840\t7.5\thttps://vulners.com/cve/CVE-2020-27840', 'tool': 'nmap', 'cve': 'CVE-2020-27840', 'id': 'CVE-2020-27840', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-14303\t7.5\thttps://vulners.com/cve/CVE-2020-14303', 'severity': 'medium', 'description': '|     \tCVE-2020-14303\t7.5\thttps://vulners.com/cve/CVE-2020-14303', 'tool': 'nmap', 'cve': 'CVE-2020-14303', 'id': 'CVE-2020-14303', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-10704\t7.5\thttps://vulners.com/cve/CVE-2020-10704', 'severity': 'medium', 'description': '|     \tCVE-2020-10704\t7.5\thttps://vulners.com/cve/CVE-2020-10704', 'tool': 'nmap', 'cve': 'CVE-2020-10704', 'id': 'CVE-2020-10704', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-16860\t7.5\thttps://vulners.com/cve/CVE-2018-16860', 'severity': 'medium', 'description': '|     \tCVE-2018-16860\t7.5\thttps://vulners.com/cve/CVE-2018-16860', 'tool': 'nmap', 'cve': 'CVE-2018-16860', 'id': 'CVE-2018-16860', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-15275\t7.5\thttps://vulners.com/cve/CVE-2017-15275', 'severity': 'medium', 'description': '|     \tCVE-2017-15275\t7.5\thttps://vulners.com/cve/CVE-2017-15275', 'tool': 'nmap', 'cve': 'CVE-2017-15275', 'id': 'CVE-2017-15275', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-12151\t7.4\thttps://vulners.com/cve/CVE-2017-12151', 'severity': 'medium', 'description': '|     \tCVE-2017-12151\t7.4\thttps://vulners.com/cve/CVE-2017-12151', 'tool': 'nmap', 'cve': 'CVE-2017-12151', 'id': 'CVE-2017-12151', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-12150\t7.4\thttps://vulners.com/cve/CVE-2017-12150', 'severity': 'medium', 'description': '|     \tCVE-2017-12150\t7.4\thttps://vulners.com/cve/CVE-2017-12150', 'tool': 'nmap', 'cve': 'CVE-2017-12150', 'id': 'CVE-2017-12150', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-37967\t7.2\thttps://vulners.com/cve/CVE-2022-37967', 'severity': 'medium', 'description': '|     \tCVE-2022-37967\t7.2\thttps://vulners.com/cve/CVE-2022-37967', 'tool': 'nmap', 'cve': 'CVE-2022-37967', 'id': 'CVE-2022-37967', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-12163\t7.1\thttps://vulners.com/cve/CVE-2017-12163', 'severity': 'medium', 'description': '|     \tCVE-2017-12163\t7.1\thttps://vulners.com/cve/CVE-2017-12163', 'tool': 'nmap', 'cve': 'CVE-2017-12163', 'id': 'CVE-2017-12163', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-20316\t6.8\thttps://vulners.com/cve/CVE-2021-20316', 'severity': 'medium', 'description': '|     \tCVE-2021-20316\t6.8\thttps://vulners.com/cve/CVE-2021-20316', 'tool': 'nmap', 'cve': 'CVE-2021-20316', 'id': 'CVE-2021-20316', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-20254\t6.8\thttps://vulners.com/cve/CVE-2021-20254', 'severity': 'medium', 'description': '|     \tCVE-2021-20254\t6.8\thttps://vulners.com/cve/CVE-2021-20254', 'tool': 'nmap', 'cve': 'CVE-2021-20254', 'id': 'CVE-2021-20254', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-5568\t6.5\thttps://vulners.com/cve/CVE-2023-5568', 'severity': 'medium', 'description': '|     \tCVE-2023-5568\t6.5\thttps://vulners.com/cve/CVE-2023-5568', 'tool': 'nmap', 'cve': 'CVE-2023-5568', 'id': 'CVE-2023-5568', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-42670\t6.5\thttps://vulners.com/cve/CVE-2023-42670', 'severity': 'medium', 'description': '|     \tCVE-2023-42670\t6.5\thttps://vulners.com/cve/CVE-2023-42670', 'tool': 'nmap', 'cve': 'CVE-2023-42670', 'id': 'CVE-2023-42670', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-42669\t6.5\thttps://vulners.com/cve/CVE-2023-42669', 'severity': 'medium', 'description': '|     \tCVE-2023-42669\t6.5\thttps://vulners.com/cve/CVE-2023-42669', 'tool': 'nmap', 'cve': 'CVE-2023-42669', 'id': 'CVE-2023-42669', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-4091\t6.5\thttps://vulners.com/cve/CVE-2023-4091', 'severity': 'medium', 'description': '|     \tCVE-2023-4091\t6.5\thttps://vulners.com/cve/CVE-2023-4091', 'tool': 'nmap', 'cve': 'CVE-2023-4091', 'id': 'CVE-2023-4091', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-3592\t6.5\thttps://vulners.com/cve/CVE-2022-3592', 'severity': 'medium', 'description': '|     \tCVE-2022-3592\t6.5\thttps://vulners.com/cve/CVE-2022-3592', 'tool': 'nmap', 'cve': 'CVE-2022-3592', 'id': 'CVE-2022-3592', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-3437\t6.5\thttps://vulners.com/cve/CVE-2022-3437', 'severity': 'medium', 'description': '|     \tCVE-2022-3437\t6.5\thttps://vulners.com/cve/CVE-2022-3437', 'tool': 'nmap', 'cve': 'CVE-2022-3437', 'id': 'CVE-2022-3437', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-3671\t6.5\thttps://vulners.com/cve/CVE-2021-3671', 'severity': 'medium', 'description': '|     \tCVE-2021-3671\t6.5\thttps://vulners.com/cve/CVE-2021-3671', 'tool': 'nmap', 'cve': 'CVE-2021-3671', 'id': 'CVE-2021-3671', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-3670\t6.5\thttps://vulners.com/cve/CVE-2021-3670', 'severity': 'medium', 'description': '|     \tCVE-2021-3670\t6.5\thttps://vulners.com/cve/CVE-2021-3670', 'tool': 'nmap', 'cve': 'CVE-2021-3670', 'id': 'CVE-2021-3670', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-14383\t6.5\thttps://vulners.com/cve/CVE-2020-14383', 'severity': 'medium', 'description': '|     \tCVE-2020-14383\t6.5\thttps://vulners.com/cve/CVE-2020-14383', 'tool': 'nmap', 'cve': 'CVE-2020-14383', 'id': 'CVE-2020-14383', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-10760\t6.5\thttps://vulners.com/cve/CVE-2020-10760', 'severity': 'medium', 'description': '|     \tCVE-2020-10760\t6.5\thttps://vulners.com/cve/CVE-2020-10760', 'tool': 'nmap', 'cve': 'CVE-2020-10760', 'id': 'CVE-2020-10760', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-10730\t6.5\thttps://vulners.com/cve/CVE-2020-10730', 'severity': 'medium', 'description': '|     \tCVE-2020-10730\t6.5\thttps://vulners.com/cve/CVE-2020-10730', 'tool': 'nmap', 'cve': 'CVE-2020-10730', 'id': 'CVE-2020-10730', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-3824\t6.5\thttps://vulners.com/cve/CVE-2019-3824', 'severity': 'medium', 'description': '|     \tCVE-2019-3824\t6.5\thttps://vulners.com/cve/CVE-2019-3824', 'tool': 'nmap', 'cve': 'CVE-2019-3824', 'id': 'CVE-2019-3824', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-10218\t6.5\thttps://vulners.com/cve/CVE-2019-10218', 'severity': 'medium', 'description': '|     \tCVE-2019-10218\t6.5\thttps://vulners.com/cve/CVE-2019-10218', 'tool': 'nmap', 'cve': 'CVE-2019-10218', 'id': 'CVE-2019-10218', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-16851\t6.5\thttps://vulners.com/cve/CVE-2018-16851', 'severity': 'medium', 'description': '|     \tCVE-2018-16851\t6.5\thttps://vulners.com/cve/CVE-2018-16851', 'tool': 'nmap', 'cve': 'CVE-2018-16851', 'id': 'CVE-2018-16851', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-16841\t6.5\thttps://vulners.com/cve/CVE-2018-16841', 'severity': 'medium', 'description': '|     \tCVE-2018-16841\t6.5\thttps://vulners.com/cve/CVE-2018-16841', 'tool': 'nmap', 'cve': 'CVE-2018-16841', 'id': 'CVE-2018-16841', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-14629\t6.5\thttps://vulners.com/cve/CVE-2018-14629', 'severity': 'medium', 'description': '|     \tCVE-2018-14629\t6.5\thttps://vulners.com/cve/CVE-2018-14629', 'tool': 'nmap', 'cve': 'CVE-2018-14629', 'id': 'CVE-2018-14629', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-10919\t6.5\thttps://vulners.com/cve/CVE-2018-10919', 'severity': 'medium', 'description': '|     \tCVE-2018-10919\t6.5\thttps://vulners.com/cve/CVE-2018-10919', 'tool': 'nmap', 'cve': 'CVE-2018-10919', 'id': 'CVE-2018-10919', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-14870\t6.4\thttps://vulners.com/cve/CVE-2019-14870', 'severity': 'medium', 'description': '|     \tCVE-2019-14870\t6.4\thttps://vulners.com/cve/CVE-2019-14870', 'tool': 'nmap', 'cve': 'CVE-2019-14870', 'id': 'CVE-2019-14870', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-0922\t5.9\thttps://vulners.com/cve/CVE-2023-0922', 'severity': 'medium', 'description': '|     \tCVE-2023-0922\t5.9\thttps://vulners.com/cve/CVE-2023-0922', 'tool': 'nmap', 'cve': 'CVE-2023-0922', 'id': 'CVE-2023-0922', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-20251\t5.9\thttps://vulners.com/cve/CVE-2021-20251', 'severity': 'medium', 'description': '|     \tCVE-2021-20251\t5.9\thttps://vulners.com/cve/CVE-2021-20251', 'tool': 'nmap', 'cve': 'CVE-2021-20251', 'id': 'CVE-2021-20251', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2016-2124\t5.9\thttps://vulners.com/cve/CVE-2016-2124', 'severity': 'medium', 'description': '|     \tCVE-2016-2124\t5.9\thttps://vulners.com/cve/CVE-2016-2124', 'tool': 'nmap', 'cve': 'CVE-2016-2124', 'id': 'CVE-2016-2124', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-1615\t5.5\thttps://vulners.com/cve/CVE-2022-1615', 'severity': 'medium', 'description': '|     \tCVE-2022-1615\t5.5\thttps://vulners.com/cve/CVE-2022-1615', 'tool': 'nmap', 'cve': 'CVE-2022-1615', 'id': 'CVE-2022-1615', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-14323\t5.5\thttps://vulners.com/cve/CVE-2020-14323', 'severity': 'medium', 'description': '|     \tCVE-2020-14323\t5.5\thttps://vulners.com/cve/CVE-2020-14323', 'tool': 'nmap', 'cve': 'CVE-2020-14323', 'id': 'CVE-2020-14323', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-3880\t5.5\thttps://vulners.com/cve/CVE-2019-3880', 'severity': 'medium', 'description': '|     \tCVE-2019-3880\t5.5\thttps://vulners.com/cve/CVE-2019-3880', 'tool': 'nmap', 'cve': 'CVE-2019-3880', 'id': 'CVE-2019-3880', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-14902\t5.5\thttps://vulners.com/cve/CVE-2019-14902', 'severity': 'medium', 'description': '|     \tCVE-2019-14902\t5.5\thttps://vulners.com/cve/CVE-2019-14902', 'tool': 'nmap', 'cve': 'CVE-2019-14902', 'id': 'CVE-2019-14902', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-32746\t5.4\thttps://vulners.com/cve/CVE-2022-32746', 'severity': 'medium', 'description': '|     \tCVE-2022-32746\t5.4\thttps://vulners.com/cve/CVE-2022-32746', 'tool': 'nmap', 'cve': 'CVE-2022-32746', 'id': 'CVE-2022-32746', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-14833\t5.4\thttps://vulners.com/cve/CVE-2019-14833', 'severity': 'medium', 'description': '|     \tCVE-2019-14833\t5.4\thttps://vulners.com/cve/CVE-2019-14833', 'tool': 'nmap', 'cve': 'CVE-2019-14833', 'id': 'CVE-2019-14833', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-34968\t5.3\thttps://vulners.com/cve/CVE-2023-34968', 'severity': 'medium', 'description': '|     \tCVE-2023-34968\t5.3\thttps://vulners.com/cve/CVE-2023-34968', 'tool': 'nmap', 'cve': 'CVE-2023-34968', 'id': 'CVE-2023-34968', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-34967\t5.3\thttps://vulners.com/cve/CVE-2023-34967', 'severity': 'medium', 'description': '|     \tCVE-2023-34967\t5.3\thttps://vulners.com/cve/CVE-2023-34967', 'tool': 'nmap', 'cve': 'CVE-2023-34967', 'id': 'CVE-2023-34967', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-14861\t5.3\thttps://vulners.com/cve/CVE-2019-14861', 'severity': 'medium', 'description': '|     \tCVE-2019-14861\t5.3\thttps://vulners.com/cve/CVE-2019-14861', 'tool': 'nmap', 'cve': 'CVE-2019-14861', 'id': 'CVE-2019-14861', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-14847\t4.9\thttps://vulners.com/cve/CVE-2019-14847', 'severity': 'medium', 'description': '|     \tCVE-2019-14847\t4.9\thttps://vulners.com/cve/CVE-2019-14847', 'tool': 'nmap', 'cve': 'CVE-2019-14847', 'id': 'CVE-2019-14847', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-0225\t4.3\thttps://vulners.com/cve/CVE-2023-0225', 'severity': 'medium', 'description': '|     \tCVE-2023-0225\t4.3\thttps://vulners.com/cve/CVE-2023-0225', 'tool': 'nmap', 'cve': 'CVE-2023-0225', 'id': 'CVE-2023-0225', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-32742\t4.3\thttps://vulners.com/cve/CVE-2022-32742', 'severity': 'medium', 'description': '|     \tCVE-2022-32742\t4.3\thttps://vulners.com/cve/CVE-2022-32742', 'tool': 'nmap', 'cve': 'CVE-2022-32742', 'id': 'CVE-2022-32742', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-44141\t4.3\thttps://vulners.com/cve/CVE-2021-44141', 'severity': 'medium', 'description': '|     \tCVE-2021-44141\t4.3\thttps://vulners.com/cve/CVE-2021-44141', 'tool': 'nmap', 'cve': 'CVE-2021-44141', 'id': 'CVE-2021-44141', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-14318\t4.3\thttps://vulners.com/cve/CVE-2020-14318', 'severity': 'medium', 'description': '|     \tCVE-2020-14318\t4.3\thttps://vulners.com/cve/CVE-2020-14318', 'tool': 'nmap', 'cve': 'CVE-2020-14318', 'id': 'CVE-2020-14318', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-14628\t4.3\thttps://vulners.com/cve/CVE-2018-14628', 'severity': 'medium', 'description': '|     \tCVE-2018-14628\t4.3\thttps://vulners.com/cve/CVE-2018-14628', 'tool': 'nmap', 'cve': 'CVE-2018-14628', 'id': 'CVE-2018-14628', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-1050\t4.3\thttps://vulners.com/cve/CVE-2018-1050', 'severity': 'medium', 'description': '|     \tCVE-2018-1050\t4.3\thttps://vulners.com/cve/CVE-2018-1050', 'tool': 'nmap', 'cve': 'CVE-2018-1050', 'id': 'CVE-2018-1050', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-43566\t2.5\thttps://vulners.com/cve/CVE-2021-43566', 'severity': 'medium', 'description': '|     \tCVE-2021-43566\t2.5\thttps://vulners.com/cve/CVE-2021-43566', 'tool': 'nmap', 'cve': 'CVE-2021-43566', 'id': 'CVE-2021-43566', 'source_tool': 'nmap', 'category': 'network'}], 'raw_output': 'Starting Nmap 7.94SVN ( https://nmap.org ) at 2025-06-30 19:18 CEST\nPre-scan script results:\n| targets-asn: \n|_  targets-asn.asn is a mandatory parameter\n|_hostmap-robtex: *TEMPORARILY DISABLED* due to changes in Robtex\'s API. See https://www.robtex.com/api/\n| broadcast-avahi-dos: \n|   Discovered hosts:\n|     ***********\n|   After NULL UDP avahi packet DoS (CVE-2011-1002).\n|_  Hosts are all up (not vulnerable).\n|_http-robtex-shared-ns: *TEMPORARILY DISABLED* due to changes in Robtex\'s API. See https://www.robtex.com/api/\n| broadcast-dns-service-discovery: \n|   ***********\n|     80/tcp http\n|       path=/phpmyadmin/\n|       Address=************ fe80::a07a:1b52:f619:5817\n|     445/tcp smb\n|       Address=************ fe80::a07a:1b52:f619:5817\n|     Device Information\n|       model=MacSamba\n|_      Address=************ fe80::a07a:1b52:f619:5817\nNmap scan report for student-laptop.. (************)\nHost is up (0.000041s latency).\nNot shown: 65531 closed tcp ports (conn-refused)\nPORT    STATE SERVICE     VERSION\n25/tcp  open  smtp        Postfix smtpd\n|_ssl-date: TLS randomness does not represent time\n|_smtp-open-relay: Server doesn\'t seem to be an open relay, all tests failed\n|_smtp-commands: student-laptop, PIPELINING, SIZE 10240000, VRFY, ETRN, STARTTLS, ENHANCEDSTATUSCODES, 8BITMIME, DSN, SMTPUTF8, CHUNKING\n|_banner: 220 student-laptop ESMTP Postfix (Ubuntu)\n| ssl-enum-ciphers: \n|   TLSv1.0: \n|     ciphers: \n|       TLS_DHE_RSA_WITH_AES_128_CBC_SHA (dh 2048) - A\n|       TLS_DHE_RSA_WITH_AES_256_CBC_SHA (dh 2048) - A\n|       TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA (dh 2048) - A\n|       TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA (dh 2048) - A\n|       TLS_DH_anon_WITH_AES_128_CBC_SHA (dh 1024) - F\n|       TLS_DH_anon_WITH_AES_256_CBC_SHA (dh 3072) - F\n|       TLS_DH_anon_WITH_CAMELLIA_128_CBC_SHA (dh 1024) - F\n|       TLS_DH_anon_WITH_CAMELLIA_256_CBC_SHA (dh 3072) - F\n|       TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA (secp256r1) - A\n|       TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA (secp256r1) - A\n|       TLS_ECDH_anon_WITH_AES_128_CBC_SHA (secp256r1) - F\n|       TLS_ECDH_anon_WITH_AES_256_CBC_SHA (secp256r1) - F\n|       TLS_RSA_WITH_AES_128_CBC_SHA (rsa 2048) - A\n|       TLS_RSA_WITH_AES_256_CBC_SHA (rsa 2048) - A\n|       TLS_RSA_WITH_CAMELLIA_128_CBC_SHA (rsa 2048) - A\n|       TLS_RSA_WITH_CAMELLIA_256_CBC_SHA (rsa 2048) - A\n|     compressors: \n|       NULL\n|     cipher preference: client\n|     warnings: \n|       Anonymous key exchange, score capped at F\n|   TLSv1.1: \n|     ciphers: \n|       TLS_DHE_RSA_WITH_AES_128_CBC_SHA (dh 2048) - A\n|       TLS_DHE_RSA_WITH_AES_256_CBC_SHA (dh 2048) - A\n|       TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA (dh 2048) - A\n|       TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA (dh 2048) - A\n|       TLS_DH_anon_WITH_AES_128_CBC_SHA (dh 1024) - F\n|       TLS_DH_anon_WITH_AES_256_CBC_SHA (dh 3072) - F\n|       TLS_DH_anon_WITH_CAMELLIA_128_CBC_SHA (dh 1024) - F\n|       TLS_DH_anon_WITH_CAMELLIA_256_CBC_SHA (dh 3072) - F\n|       TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA (secp256r1) - A\n|       TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA (secp256r1) - A\n|       TLS_ECDH_anon_WITH_AES_128_CBC_SHA (secp256r1) - F\n|       TLS_ECDH_anon_WITH_AES_256_CBC_SHA (secp256r1) - F\n|       TLS_RSA_WITH_AES_128_CBC_SHA (rsa 2048) - A\n|       TLS_RSA_WITH_AES_256_CBC_SHA (rsa 2048) - A\n|       TLS_RSA_WITH_CAMELLIA_128_CBC_SHA (rsa 2048) - A\n|       TLS_RSA_WITH_CAMELLIA_256_CBC_SHA (rsa 2048) - A\n|     compressors: \n|       NULL\n|     cipher preference: client\n|     warnings: \n|       Anonymous key exchange, score capped at F\n|   TLSv1.2: \n|     ciphers: \n|       TLS_DHE_RSA_WITH_AES_128_CBC_SHA (dh 2048) - A\n|       TLS_DHE_RSA_WITH_AES_128_CBC_SHA256 (dh 2048) - A\n|       TLS_DHE_RSA_WITH_AES_128_CCM (dh 2048) - A\n|       TLS_DHE_RSA_WITH_AES_128_CCM_8 (dh 2048) - A\n|       TLS_DHE_RSA_WITH_AES_128_GCM_SHA256 (dh 2048) - A\n|       TLS_DHE_RSA_WITH_AES_256_CBC_SHA (dh 2048) - A\n|       TLS_DHE_RSA_WITH_AES_256_CBC_SHA256 (dh 2048) - A\n|       TLS_DHE_RSA_WITH_AES_256_CCM (dh 2048) - A\n|       TLS_DHE_RSA_WITH_AES_256_CCM_8 (dh 2048) - A\n|       TLS_DHE_RSA_WITH_AES_256_GCM_SHA384 (dh 2048) - A\n|       TLS_DHE_RSA_WITH_ARIA_128_GCM_SHA256 (dh 2048) - A\n|       TLS_DHE_RSA_WITH_ARIA_256_GCM_SHA384 (dh 2048) - A\n|       TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA (dh 2048) - A\n|       TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA256 (dh 2048) - A\n|       TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA (dh 2048) - A\n|       TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA256 (dh 2048) - A\n|       TLS_DHE_RSA_WITH_CHACHA20_POLY1305_SHA256 (dh 2048) - A\n|       TLS_DH_anon_WITH_AES_128_CBC_SHA (dh 1024) - F\n|       TLS_DH_anon_WITH_AES_128_CBC_SHA256 (dh 1024) - F\n|       TLS_DH_anon_WITH_AES_128_GCM_SHA256 (dh 1024) - F\n|       TLS_DH_anon_WITH_AES_256_CBC_SHA (dh 3072) - F\n|       TLS_DH_anon_WITH_AES_256_CBC_SHA256 (dh 3072) - F\n|       TLS_DH_anon_WITH_AES_256_GCM_SHA384 (dh 3072) - F\n|       TLS_DH_anon_WITH_CAMELLIA_128_CBC_SHA (dh 1024) - F\n|       TLS_DH_anon_WITH_CAMELLIA_128_CBC_SHA256 (dh 1024) - F\n|       TLS_DH_anon_WITH_CAMELLIA_256_CBC_SHA (dh 3072) - F\n|       TLS_DH_anon_WITH_CAMELLIA_256_CBC_SHA256 (dh 3072) - F\n|       TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA (secp256r1) - A\n|       TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256 (secp256r1) - A\n|       TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256 (secp256r1) - A\n|       TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA (secp256r1) - A\n|       TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384 (secp256r1) - A\n|       TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 (secp256r1) - A\n|       TLS_ECDHE_RSA_WITH_ARIA_128_GCM_SHA256 (secp256r1) - A\n|       TLS_ECDHE_RSA_WITH_ARIA_256_GCM_SHA384 (secp256r1) - A\n|       TLS_ECDHE_RSA_WITH_CAMELLIA_128_CBC_SHA256 (secp256r1) - A\n|       TLS_ECDHE_RSA_WITH_CAMELLIA_256_CBC_SHA384 (secp256r1) - A\n|       TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 (secp256r1) - A\n|       TLS_ECDH_anon_WITH_AES_128_CBC_SHA (secp256r1) - F\n|       TLS_ECDH_anon_WITH_AES_256_CBC_SHA (secp256r1) - F\n|       TLS_RSA_WITH_AES_128_CBC_SHA (rsa 2048) - A\n|       TLS_RSA_WITH_AES_128_CBC_SHA256 (rsa 2048) - A\n|       TLS_RSA_WITH_AES_128_CCM (rsa 2048) - A\n|       TLS_RSA_WITH_AES_128_CCM_8 (rsa 2048) - A\n|       TLS_RSA_WITH_AES_128_GCM_SHA256 (rsa 2048) - A\n|       TLS_RSA_WITH_AES_256_CBC_SHA (rsa 2048) - A\n|       TLS_RSA_WITH_AES_256_CBC_SHA256 (rsa 2048) - A\n|       TLS_RSA_WITH_AES_256_CCM (rsa 2048) - A\n|       TLS_RSA_WITH_AES_256_CCM_8 (rsa 2048) - A\n|       TLS_RSA_WITH_AES_256_GCM_SHA384 (rsa 2048) - A\n|       TLS_RSA_WITH_ARIA_128_GCM_SHA256 (rsa 2048) - A\n|       TLS_RSA_WITH_ARIA_256_GCM_SHA384 (rsa 2048) - A\n|       TLS_RSA_WITH_CAMELLIA_128_CBC_SHA (rsa 2048) - A\n|       TLS_RSA_WITH_CAMELLIA_128_CBC_SHA256 (rsa 2048) - A\n|       TLS_RSA_WITH_CAMELLIA_256_CBC_SHA (rsa 2048) - A\n|       TLS_RSA_WITH_CAMELLIA_256_CBC_SHA256 (rsa 2048) - A\n|     compressors: \n|       NULL\n|     cipher preference: client\n|     warnings: \n|       Anonymous key exchange, score capped at F\n|   TLSv1.3: \n|     ciphers: \n|       TLS_AKE_WITH_AES_128_GCM_SHA256 (ecdh_x25519) - A\n|       TLS_AKE_WITH_AES_256_GCM_SHA384 (ecdh_x25519) - A\n|       TLS_AKE_WITH_CHACHA20_POLY1305_SHA256 (ecdh_x25519) - A\n|     cipher preference: client\n|_  least strength: F\n| smtp-vuln-cve2010-4344: \n|_  The SMTP server is not Exim: NOT VULNERABLE\n| ssl-dh-params: \n|   VULNERABLE:\n|   Anonymous Diffie-Hellman Key Exchange MitM Vulnerability\n|     State: VULNERABLE\n|       Transport Layer Security (TLS) services that use anonymous\n|       Diffie-Hellman key exchange only provide protection against passive\n|       eavesdropping, and are vulnerable to active man-in-the-middle attacks\n|       which could completely compromise the confidentiality and integrity\n|       of any data exchanged over the resulting session.\n|     Check results:\n|       ANONYMOUS DH GROUP 1\n|             Cipher Suite: TLS_DH_anon_WITH_CAMELLIA_128_CBC_SHA\n|             Modulus Type: Safe prime\n|             Modulus Source: RFC2409/Oakley Group 2\n|             Modulus Length: 1024\n|             Generator Length: 8\n|             Public Key Length: 1024\n|       ANONYMOUS DH GROUP 2\n|             Cipher Suite: TLS_DH_anon_WITH_CAMELLIA_256_CBC_SHA256\n|             Modulus Type: Safe prime\n|             Modulus Source: RFC3526/Oakley Group 15\n|             Modulus Length: 3072\n|             Generator Length: 8\n|             Public Key Length: 3072\n|     References:\n|_      https://www.ietf.org/rfc/rfc2246.txt\n| ssl-cert: Subject: commonName=ubuntu\n| Subject Alternative Name: DNS:ubuntu\n| Not valid before: 2019-06-18T09:33:59\n|_Not valid after:  2029-06-15T09:33:59\n80/tcp  open  http        Apache httpd 2.4.58 ((Ubuntu))\n| http-errors: \n| Spidering limited to: maxpagecount=40; withinhost=student-laptop..\n|   Found the following error pages: \n|   \n|   Error Code: 400\n|_  \thttp://student-laptop..:80/\n| http-headers: \n|   Date: Mon, 30 Jun 2025 17:19:33 GMT\n|   Server: Apache/2.4.58 (Ubuntu)\n|   Content-Length: 301\n|   Connection: close\n|   Content-Type: text/html; charset=iso-8859-1\n|   \n|_  (Request type: GET)\n| http-useragent-tester: \n|   Status for browser useragent: 400\n|   Allowed User Agents: \n|     Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)\n|     libwww\n|     lwp-trivial\n|     libcurl-agent/1.0\n|     PHP/\n|     Python-urllib/2.5\n|     GT::WWW\n|     Snoopy\n|     MFC_Tear_Sample\n|     HTTP::Lite\n|     PHPCrawl\n|     URI::Fetch\n|     Zend_Http_Client\n|     http client\n|     PECL::HTTP\n|     Wget/1.13.4 (linux-gnu)\n|_    WWW-Mechanize/1.34\n|_http-server-header: Apache/2.4.58 (Ubuntu)\n| http-vhosts: \n|_128 names had status 400\n|_http-referer-checker: Couldn\'t find any cross-domain scripts.\n|_http-feed: Couldn\'t find any feeds.\n|_http-mobileversion-checker: No mobile version detected.\n| http-sitemap-generator: \n|   Directory structure:\n|   Longest directory structure:\n|     Depth: 0\n|     Dir: /\n|   Total files found (by extension):\n|_    \n|_http-dombased-xss: Couldn\'t find any DOM based XSS.\n|_http-devframework: Couldn\'t determine the underlying framework or CMS. Try increasing \'httpspider.maxpagecount\' value to spider more pages.\n|_http-csrf: Couldn\'t find any CSRF vulnerabilities.\n|_http-date: Mon, 30 Jun 2025 17:19:31 GMT; 0s from local time.\n| vulners: \n|   cpe:/a:apache:http_server:2.4.58: \n|     \t95499236-C9FE-56A6-9D7D-E943A24B633A\t10.0\thttps://vulners.com/githubexploit/95499236-C9FE-56A6-9D7D-E943A24B633A\t*EXPLOIT*\n|     \t2C119FFA-ECE0-5E14-A4A4-354A2C38071A\t10.0\thttps://vulners.com/githubexploit/2C119FFA-ECE0-5E14-A4A4-354A2C38071A\t*EXPLOIT*\n|     \tCVE-2024-38476\t9.8\thttps://vulners.com/cve/CVE-2024-38476\n|     \tCVE-2024-38474\t9.8\thttps://vulners.com/cve/CVE-2024-38474\n|     \tA5425A79-9D81-513A-9CC5-549D6321897C\t9.8\thttps://vulners.com/githubexploit/A5425A79-9D81-513A-9CC5-549D6321897C\t*EXPLOIT*\n|     \tFD2EE3A5-BAEA-5845-BA35-E6889992214F\t9.1\thttps://vulners.com/githubexploit/FD2EE3A5-BAEA-5845-BA35-E6889992214F\t*EXPLOIT*\n|     \tE606D7F4-5FA2-5907-B30E-367D6FFECD89\t9.1\thttps://vulners.com/githubexploit/E606D7F4-5FA2-5907-B30E-367D6FFECD89\t*EXPLOIT*\n|     \tCVE-2024-40898\t9.1\thttps://vulners.com/cve/CVE-2024-40898\n|     \tCVE-2024-38475\t9.1\thttps://vulners.com/cve/CVE-2024-38475\n|     \tB5E74010-A082-5ECE-AB37-623A5B33FE7D\t9.1\thttps://vulners.com/githubexploit/B5E74010-A082-5ECE-AB37-623A5B33FE7D\t*EXPLOIT*\n|     \t5418A85B-F4B7-5BBD-B106-0800AC961C7A\t9.1\thttps://vulners.com/githubexploit/5418A85B-F4B7-5BBD-B106-0800AC961C7A\t*EXPLOIT*\n|     \t2EF14600-503F-53AF-BA24-683481265D30\t9.1\thttps://vulners.com/githubexploit/2EF14600-503F-53AF-BA24-683481265D30\t*EXPLOIT*\n|     \t0486EBEE-F207-570A-9AD8-33269E72220A\t9.1\thttps://vulners.com/githubexploit/0486EBEE-F207-570A-9AD8-33269E72220A\t*EXPLOIT*\n|     \tB0A9E5E8-7CCC-5984-9922-A89F11D6BF38\t8.2\thttps://vulners.com/githubexploit/B0A9E5E8-7CCC-5984-9922-A89F11D6BF38\t*EXPLOIT*\n|     \tCVE-2024-38473\t8.1\thttps://vulners.com/cve/CVE-2024-38473\n|     \t249A954E-0189-5182-AE95-31C866A057E1\t8.1\thttps://vulners.com/githubexploit/249A954E-0189-5182-AE95-31C866A057E1\t*EXPLOIT*\n|     \t23079A70-8B37-56D2-9D37-F638EBF7F8B5\t8.1\thttps://vulners.com/githubexploit/23079A70-8B37-56D2-9D37-F638EBF7F8B5\t*EXPLOIT*\n|     \tCVE-2024-39573\t7.5\thttps://vulners.com/cve/CVE-2024-39573\n|     \tCVE-2024-38477\t7.5\thttps://vulners.com/cve/CVE-2024-38477\n|     \tCVE-2024-38472\t7.5\thttps://vulners.com/cve/CVE-2024-38472\n|     \tCVE-2024-27316\t7.5\thttps://vulners.com/cve/CVE-2024-27316\n|     \tCNVD-2024-20839\t7.5\thttps://vulners.com/cnvd/CNVD-2024-20839\n|     \tCDC791CD-A414-5ABE-A897-7CFA3C2D3D29\t7.5\thttps://vulners.com/githubexploit/CDC791CD-A414-5ABE-A897-7CFA3C2D3D29\t*EXPLOIT*\n|     \t4B14D194-BDE3-5D7F-A262-A701F90DE667\t7.5\thttps://vulners.com/githubexploit/4B14D194-BDE3-5D7F-A262-A701F90DE667\t*EXPLOIT*\n|     \t45D138AD-BEC6-552A-91EA-8816914CA7F4\t7.5\thttps://vulners.com/githubexploit/45D138AD-BEC6-552A-91EA-8816914CA7F4\t*EXPLOIT*\n|     \tCVE-2023-38709\t7.3\thttps://vulners.com/cve/CVE-2023-38709\n|     \tCNVD-2024-36395\t7.3\thttps://vulners.com/cnvd/CNVD-2024-36395\n|     \tCVE-2024-24795\t6.3\thttps://vulners.com/cve/CVE-2024-24795\n|     \tCVE-2024-39884\t6.2\thttps://vulners.com/cve/CVE-2024-39884\n|_    \tCVE-2024-36387\t5.4\thttps://vulners.com/cve/CVE-2024-36387\n|_http-comments-displayer: Couldn\'t find any comments.\n|_http-title: 400 Bad Request\n|_http-stored-xss: Couldn\'t find any stored XSS vulnerabilities.\n|_http-chrono: Request times for /; avg: 163.11ms; min: 160.93ms; max: 166.28ms\n|_http-xssed: No previously reported XSS vuln.\n|_http-fetch: Please enter the complete path of the directory to save data in.\n139/tcp open  netbios-ssn Samba smbd 4.6.2\n| vulners: \n|   cpe:/a:samba:samba:4.6.2: \n|     \tSSV:93139\t10.0\thttps://vulners.com/seebug/SSV:93139\t*EXPLOIT*\n|     \tSAMBA_IS_KNOWN_PIPENAME\t10.0\thttps://vulners.com/canvas/SAMBA_IS_KNOWN_PIPENAME\t*EXPLOIT*\n|     \tSAINT:C50A339EFD5B2F96051BC00F96014CAA\t10.0\thttps://vulners.com/saint/SAINT:C50A339EFD5B2F96051BC00F96014CAA\t*EXPLOIT*\n|     \tSAINT:6FE788CBA26F517C02B44A699047593B\t10.0\thttps://vulners.com/saint/SAINT:6FE788CBA26F517C02B44A699047593B\t*EXPLOIT*\n|     \tSAINT:3579A721D51A069C725493EA48A26E42\t10.0\thttps://vulners.com/saint/SAINT:3579A721D51A069C725493EA48A26E42\t*EXPLOIT*\n|     \tEXPLOITPACK:11BDEE18B40708887778CCF837705185\t10.0\thttps://vulners.com/exploitpack/EXPLOITPACK:11BDEE18B40708887778CCF837705185\t*EXPLOIT*\n|     \tEDB-ID:49071\t10.0\thttps://vulners.com/exploitdb/EDB-ID:49071\t*EXPLOIT*\n|     \tEDB-ID:42084\t10.0\thttps://vulners.com/exploitdb/EDB-ID:42084\t*EXPLOIT*\n|     \tEDB-ID:42060\t10.0\thttps://vulners.com/exploitdb/EDB-ID:42060\t*EXPLOIT*\n|     \tCVE-2020-1472\t10.0\thttps://vulners.com/cve/CVE-2020-1472\n|     \tCVE-2017-7494\t10.0\thttps://vulners.com/cve/CVE-2017-7494\n|     \t95499236-C9FE-56A6-9D7D-E943A24B633A\t10.0\thttps://vulners.com/githubexploit/95499236-C9FE-56A6-9D7D-E943A24B633A\t*EXPLOIT*\n|     \t2C119FFA-ECE0-5E14-A4A4-354A2C38071A\t10.0\thttps://vulners.com/githubexploit/2C119FFA-ECE0-5E14-A4A4-354A2C38071A\t*EXPLOIT*\n|     \t1337DAY-ID-27859\t10.0\thttps://vulners.com/zdt/1337DAY-ID-27859\t*EXPLOIT*\n|     \t1337DAY-ID-27836\t10.0\thttps://vulners.com/zdt/1337DAY-ID-27836\t*EXPLOIT*\n|     \tMSF:EXPLOIT-LINUX-SAMBA-IS_KNOWN_PIPENAME-\t9.8\thttps://vulners.com/metasploit/MSF:EXPLOIT-LINUX-SAMBA-IS_KNOWN_PIPENAME-\t*EXPLOIT*\n|     \tCVE-2023-3961\t9.8\thttps://vulners.com/cve/CVE-2023-3961\n|     \tCVE-2022-45141\t9.8\thttps://vulners.com/cve/CVE-2022-45141\n|     \tCVE-2017-14746\t9.8\thttps://vulners.com/cve/CVE-2017-14746\n|     \tPACKETSTORM:160127\t9.3\thttps://vulners.com/packetstorm/PACKETSTORM:160127\t*EXPLOIT*\n|     \tCVE-2021-44142\t9.0\thttps://vulners.com/cve/CVE-2021-44142\n|     \tCVE-2020-25719\t9.0\thttps://vulners.com/cve/CVE-2020-25719\n|     \tCVE-2020-17049\t9.0\thttps://vulners.com/cve/CVE-2020-17049\n|     \tB0F54D1F-71E5-54D9-BED8-BA166E090B14\t9.0\thttps://vulners.com/githubexploit/B0F54D1F-71E5-54D9-BED8-BA166E090B14\t*EXPLOIT*\n|     \t3E948D04-4013-561A-9F41-7C692AB14993\t9.0\thttps://vulners.com/githubexploit/3E948D04-4013-561A-9F41-7C692AB14993\t*EXPLOIT*\n|     \tCVE-2022-42898\t8.8\thttps://vulners.com/cve/CVE-2022-42898\n|     \tCVE-2022-32744\t8.8\thttps://vulners.com/cve/CVE-2022-32744\n|     \tCVE-2022-2031\t8.8\thttps://vulners.com/cve/CVE-2022-2031\n|     \tCVE-2022-0336\t8.8\thttps://vulners.com/cve/CVE-2022-0336\n|     \tCVE-2021-3738\t8.8\thttps://vulners.com/cve/CVE-2021-3738\n|     \tCVE-2020-25722\t8.8\thttps://vulners.com/cve/CVE-2020-25722\n|     \tCVE-2020-25721\t8.8\thttps://vulners.com/cve/CVE-2020-25721\n|     \tCVE-2020-25718\t8.8\thttps://vulners.com/cve/CVE-2020-25718\n|     \tCVE-2018-10858\t8.8\thttps://vulners.com/cve/CVE-2018-10858\n|     \tCVE-2018-1057\t8.8\thttps://vulners.com/cve/CVE-2018-1057\n|     \t9AAA7F0F-9868-5520-89F3-D09DFC7BE995\t8.8\thttps://vulners.com/githubexploit/9AAA7F0F-9868-5520-89F3-D09DFC7BE995\t*EXPLOIT*\n|     \tCVE-2020-25717\t8.5\thttps://vulners.com/cve/CVE-2020-25717\n|     \tCVE-2022-38023\t8.1\thttps://vulners.com/cve/CVE-2022-38023\n|     \tCVE-2022-37966\t8.1\thttps://vulners.com/cve/CVE-2022-37966\n|     \tCVE-2022-32745\t8.1\thttps://vulners.com/cve/CVE-2022-32745\n|     \tCVE-2017-11103\t8.1\thttps://vulners.com/cve/CVE-2017-11103\n|     \tCVE-2020-10745\t7.8\thttps://vulners.com/cve/CVE-2020-10745\n|     \tC841D92F-11E1-5077-AE70-CA2FEF0BC96E\t7.8\thttps://vulners.com/githubexploit/C841D92F-11E1-5077-AE70-CA2FEF0BC96E\t*EXPLOIT*\n|     \tCVE-2023-0614\t7.7\thttps://vulners.com/cve/CVE-2023-0614\n|     \tCVE-2023-4154\t7.5\thttps://vulners.com/cve/CVE-2023-4154\n|     \tCVE-2023-34966\t7.5\thttps://vulners.com/cve/CVE-2023-34966\n|     \tCVE-2022-32743\t7.5\thttps://vulners.com/cve/CVE-2022-32743\n|     \tCVE-2021-23192\t7.5\thttps://vulners.com/cve/CVE-2021-23192\n|     \tCVE-2021-20277\t7.5\thttps://vulners.com/cve/CVE-2021-20277\n|     \tCVE-2020-27840\t7.5\thttps://vulners.com/cve/CVE-2020-27840\n|     \tCVE-2020-14303\t7.5\thttps://vulners.com/cve/CVE-2020-14303\n|     \tCVE-2020-10704\t7.5\thttps://vulners.com/cve/CVE-2020-10704\n|     \tCVE-2018-16860\t7.5\thttps://vulners.com/cve/CVE-2018-16860\n|     \tCVE-2017-15275\t7.5\thttps://vulners.com/cve/CVE-2017-15275\n|     \tCVE-2017-12151\t7.4\thttps://vulners.com/cve/CVE-2017-12151\n|     \tCVE-2017-12150\t7.4\thttps://vulners.com/cve/CVE-2017-12150\n|     \tCVE-2022-37967\t7.2\thttps://vulners.com/cve/CVE-2022-37967\n|     \tCVE-2017-12163\t7.1\thttps://vulners.com/cve/CVE-2017-12163\n|     \t87B06BBD-7ED2-5BD2-95E1-21EE66501505\t7.0\thttps://vulners.com/githubexploit/87B06BBD-7ED2-5BD2-95E1-21EE66501505\t*EXPLOIT*\n|     \tCVE-2021-20316\t6.8\thttps://vulners.com/cve/CVE-2021-20316\n|     \tCVE-2021-20254\t6.8\thttps://vulners.com/cve/CVE-2021-20254\n|     \tCNVD-2021-39694\t6.8\thttps://vulners.com/cnvd/CNVD-2021-39694\n|     \tCVE-2023-5568\t6.5\thttps://vulners.com/cve/CVE-2023-5568\n|     \tCVE-2023-42670\t6.5\thttps://vulners.com/cve/CVE-2023-42670\n|     \tCVE-2023-42669\t6.5\thttps://vulners.com/cve/CVE-2023-42669\n|     \tCVE-2023-4091\t6.5\thttps://vulners.com/cve/CVE-2023-4091\n|     \tCVE-2022-3592\t6.5\thttps://vulners.com/cve/CVE-2022-3592\n|     \tCVE-2022-3437\t6.5\thttps://vulners.com/cve/CVE-2022-3437\n|     \tCVE-2021-3671\t6.5\thttps://vulners.com/cve/CVE-2021-3671\n|     \tCVE-2021-3670\t6.5\thttps://vulners.com/cve/CVE-2021-3670\n|     \tCVE-2020-14383\t6.5\thttps://vulners.com/cve/CVE-2020-14383\n|     \tCVE-2020-10760\t6.5\thttps://vulners.com/cve/CVE-2020-10760\n|     \tCVE-2020-10730\t6.5\thttps://vulners.com/cve/CVE-2020-10730\n|     \tCVE-2019-3824\t6.5\thttps://vulners.com/cve/CVE-2019-3824\n|     \tCVE-2019-10218\t6.5\thttps://vulners.com/cve/CVE-2019-10218\n|     \tCVE-2018-16851\t6.5\thttps://vulners.com/cve/CVE-2018-16851\n|     \tCVE-2018-16841\t6.5\thttps://vulners.com/cve/CVE-2018-16841\n|     \tCVE-2018-14629\t6.5\thttps://vulners.com/cve/CVE-2018-14629\n|     \tCVE-2018-10919\t6.5\thttps://vulners.com/cve/CVE-2018-10919\n|     \tCVE-2019-14870\t6.4\thttps://vulners.com/cve/CVE-2019-14870\n|     \tCVE-2023-0922\t5.9\thttps://vulners.com/cve/CVE-2023-0922\n|     \tCVE-2021-20251\t5.9\thttps://vulners.com/cve/CVE-2021-20251\n|     \tCVE-2016-2124\t5.9\thttps://vulners.com/cve/CVE-2016-2124\n|     \tPACKETSTORM:180777\t5.5\thttps://vulners.com/packetstorm/PACKETSTORM:180777\t*EXPLOIT*\n|     \tMSF:AUXILIARY-ADMIN-DCERPC-CVE_2020_1472_ZEROLOGON-\t5.5\thttps://vulners.com/metasploit/MSF:AUXILIARY-ADMIN-DCERPC-CVE_2020_1472_ZEROLOGON-\t*EXPLOIT*\n|     \tFC661572-B96B-5B2C-B12F-E8D279E189BF\t5.5\thttps://vulners.com/githubexploit/FC661572-B96B-5B2C-B12F-E8D279E189BF\t*EXPLOIT*\n|     \tF472C105-E3B1-524A-BBF5-1C436185F6EE\t5.5\thttps://vulners.com/githubexploit/F472C105-E3B1-524A-BBF5-1C436185F6EE\t*EXPLOIT*\n|     \tF085F702-F1C3-5ACB-99BE-086DA182D98B\t5.5\thttps://vulners.com/githubexploit/F085F702-F1C3-5ACB-99BE-086DA182D98B\t*EXPLOIT*\n|     \tE9F25671-2BEF-5E8B-A60A-55C6DD9DE820\t5.5\thttps://vulners.com/githubexploit/E9F25671-2BEF-5E8B-A60A-55C6DD9DE820\t*EXPLOIT*\n|     \tDEC5B8BB-1933-54FF-890E-9C2720E9966E\t5.5\thttps://vulners.com/githubexploit/DEC5B8BB-1933-54FF-890E-9C2720E9966E\t*EXPLOIT*\n|     \tD7AB3F4A-8E41-5E5B-B987-99AFB571FE9C\t5.5\thttps://vulners.com/githubexploit/D7AB3F4A-8E41-5E5B-B987-99AFB571FE9C\t*EXPLOIT*\n|     \tD3C401E0-D013-59E2-8FFB-6BEF41DA3D1B\t5.5\thttps://vulners.com/githubexploit/D3C401E0-D013-59E2-8FFB-6BEF41DA3D1B\t*EXPLOIT*\n|     \tD178DAA4-01D0-50D0-A741-1C3C76A7D023\t5.5\thttps://vulners.com/githubexploit/D178DAA4-01D0-50D0-A741-1C3C76A7D023\t*EXPLOIT*\n|     \tCVE-2022-1615\t5.5\thttps://vulners.com/cve/CVE-2022-1615\n|     \tCVE-2020-14323\t5.5\thttps://vulners.com/cve/CVE-2020-14323\n|     \tCVE-2019-3880\t5.5\thttps://vulners.com/cve/CVE-2019-3880\n|     \tCVE-2019-14902\t5.5\thttps://vulners.com/cve/CVE-2019-14902\n|     \tCF07CF32-0B8E-58E5-A410-8FA68D411ED0\t5.5\thttps://vulners.com/githubexploit/CF07CF32-0B8E-58E5-A410-8FA68D411ED0\t*EXPLOIT*\n|     \tC7F6FB3B-581D-53E1-A2BF-C935FE7B03C8\t5.5\thttps://vulners.com/githubexploit/C7F6FB3B-581D-53E1-A2BF-C935FE7B03C8\t*EXPLOIT*\n|     \tC7CE5D12-A4E5-5FF2-9F07-CD5E84B4C02F\t5.5\thttps://vulners.com/githubexploit/C7CE5D12-A4E5-5FF2-9F07-CD5E84B4C02F\t*EXPLOIT*\n|     \tC5B49BD0-D347-5AEB-A774-EE7BB35688E9\t5.5\thttps://vulners.com/githubexploit/C5B49BD0-D347-5AEB-A774-EE7BB35688E9\t*EXPLOIT*\n|     \tBBE1926E-1EC7-5657-8766-3CA8418F815C\t5.5\thttps://vulners.com/githubexploit/BBE1926E-1EC7-5657-8766-3CA8418F815C\t*EXPLOIT*\n|     \tBA280EB1-2FF9-52DA-8BA4-A276A1158DD8\t5.5\thttps://vulners.com/githubexploit/BA280EB1-2FF9-52DA-8BA4-A276A1158DD8\t*EXPLOIT*\n|     \tB7C1C535-3653-5D12-8922-4C6A5CCBD5F3\t5.5\thttps://vulners.com/githubexploit/B7C1C535-3653-5D12-8922-4C6A5CCBD5F3\t*EXPLOIT*\n|     \tAEF449B8-DC3E-544A-A748-5A1C6F7EBA59\t5.5\thttps://vulners.com/githubexploit/AEF449B8-DC3E-544A-A748-5A1C6F7EBA59\t*EXPLOIT*\n|     \tA24AC1AC-55EF-51D8-B696-32F369DCAB96\t5.5\thttps://vulners.com/githubexploit/A24AC1AC-55EF-51D8-B696-32F369DCAB96\t*EXPLOIT*\n|     \t9C9BD402-511C-597D-9864-647131FE6647\t5.5\thttps://vulners.com/githubexploit/9C9BD402-511C-597D-9864-647131FE6647\t*EXPLOIT*\n|     \t939F3BE7-AF69-5351-BD56-12412FA184C5\t5.5\thttps://vulners.com/githubexploit/939F3BE7-AF69-5351-BD56-12412FA184C5\t*EXPLOIT*\n|     \t879CF3A7-ECBC-552A-A044-5E2724F63279\t5.5\thttps://vulners.com/githubexploit/879CF3A7-ECBC-552A-A044-5E2724F63279\t*EXPLOIT*\n|     \t7078ED42-959E-5242-BE9D-17F2F99C76A8\t5.5\thttps://vulners.com/githubexploit/7078ED42-959E-5242-BE9D-17F2F99C76A8\t*EXPLOIT*\n|     \t6FB0B63E-DE9A-5065-B577-ECA3ED5E9F4B\t5.5\thttps://vulners.com/githubexploit/6FB0B63E-DE9A-5065-B577-ECA3ED5E9F4B\t*EXPLOIT*\n|     \t63C36F7A-5F99-5A79-B99F-260360AC237F\t5.5\thttps://vulners.com/githubexploit/63C36F7A-5F99-5A79-B99F-260360AC237F\t*EXPLOIT*\n|     \t5E80DB20-575C-537A-9B83-CCFCCB55E448\t5.5\thttps://vulners.com/githubexploit/5E80DB20-575C-537A-9B83-CCFCCB55E448\t*EXPLOIT*\n|     \t5B025A0D-055E-552C-B1FB-287C6F191F8E\t5.5\thttps://vulners.com/githubexploit/5B025A0D-055E-552C-B1FB-287C6F191F8E\t*EXPLOIT*\n|     \t50FA6373-CBCD-5EF5-B37D-0ECD621C6134\t5.5\thttps://vulners.com/githubexploit/50FA6373-CBCD-5EF5-B37D-0ECD621C6134\t*EXPLOIT*\n|     \t4CB63A18-5D6F-57E3-8CD8-9110CF63E120\t5.5\thttps://vulners.com/githubexploit/4CB63A18-5D6F-57E3-8CD8-9110CF63E120\t*EXPLOIT*\n|     \t49EC151F-12F0-59CF-960C-25BD54F46680\t5.5\thttps://vulners.com/githubexploit/49EC151F-12F0-59CF-960C-25BD54F46680\t*EXPLOIT*\n|     \t3F400483-1F7E-5BE5-8612-4D55D450D553\t5.5\thttps://vulners.com/githubexploit/3F400483-1F7E-5BE5-8612-4D55D450D553\t*EXPLOIT*\n|     \t2E71FF50-1B48-5A8E-9212-C4CF9399715C\t5.5\thttps://vulners.com/githubexploit/2E71FF50-1B48-5A8E-9212-C4CF9399715C\t*EXPLOIT*\n|     \t2D16FB2A-7A61-5E45-AAF8-1E090E0ADCC0\t5.5\thttps://vulners.com/githubexploit/2D16FB2A-7A61-5E45-AAF8-1E090E0ADCC0\t*EXPLOIT*\n|     \t28D42B84-AB24-5FC6-ADE1-610374D67F21\t5.5\thttps://vulners.com/githubexploit/28D42B84-AB24-5FC6-ADE1-610374D67F21\t*EXPLOIT*\n|     \t2255B39F-1B91-56F4-A323-8704808620D3\t5.5\thttps://vulners.com/githubexploit/2255B39F-1B91-56F4-A323-8704808620D3\t*EXPLOIT*\n|     \t20466D13-6C5B-5326-9C8B-160E9BE37195\t5.5\thttps://vulners.com/githubexploit/20466D13-6C5B-5326-9C8B-160E9BE37195\t*EXPLOIT*\n|     \t14BD2DBD-3A91-55FC-9836-14EF9ABF56CF\t5.5\thttps://vulners.com/githubexploit/14BD2DBD-3A91-55FC-9836-14EF9ABF56CF\t*EXPLOIT*\n|     \t1337DAY-ID-35274\t5.5\thttps://vulners.com/zdt/1337DAY-ID-35274\t*EXPLOIT*\n|     \t12E44744-1AF0-523A-ACA2-593B4D33E014\t5.5\thttps://vulners.com/githubexploit/12E44744-1AF0-523A-ACA2-593B4D33E014\t*EXPLOIT*\n|     \t0CFAB531-412C-57A0-BD9E-EF072620C078\t5.5\thttps://vulners.com/githubexploit/0CFAB531-412C-57A0-BD9E-EF072620C078\t*EXPLOIT*\n|     \t07E56BF6-A72B-5ACD-A2FF-818C48E4E132\t5.5\thttps://vulners.com/githubexploit/07E56BF6-A72B-5ACD-A2FF-818C48E4E132\t*EXPLOIT*\n|     \t07DF268C-467E-54A3-B713-057BA19C72F7\t5.5\thttps://vulners.com/githubexploit/07DF268C-467E-54A3-B713-057BA19C72F7\t*EXPLOIT*\n|     \t06BAC40D-74DF-5994-909F-3A87FC3B76C8\t5.5\thttps://vulners.com/githubexploit/06BAC40D-74DF-5994-909F-3A87FC3B76C8\t*EXPLOIT*\n|     \t04BCA9BC-E3AD-5234-A5F0-7A1ED826F600\t5.5\thttps://vulners.com/githubexploit/04BCA9BC-E3AD-5234-A5F0-7A1ED826F600\t*EXPLOIT*\n|     \t042AB58A-C86A-5A8B-AED3-2FF3624E97E3\t5.5\thttps://vulners.com/githubexploit/042AB58A-C86A-5A8B-AED3-2FF3624E97E3\t*EXPLOIT*\n|     \tCVE-2022-32746\t5.4\thttps://vulners.com/cve/CVE-2022-32746\n|     \tCVE-2019-14833\t5.4\thttps://vulners.com/cve/CVE-2019-14833\n|     \tCVE-2023-34968\t5.3\thttps://vulners.com/cve/CVE-2023-34968\n|     \tCVE-2023-34967\t5.3\thttps://vulners.com/cve/CVE-2023-34967\n|     \tCVE-2019-14861\t5.3\thttps://vulners.com/cve/CVE-2019-14861\n|     \tCVE-2019-14847\t4.9\thttps://vulners.com/cve/CVE-2019-14847\n|     \tCVE-2023-0225\t4.3\thttps://vulners.com/cve/CVE-2023-0225\n|     \tCVE-2022-32742\t4.3\thttps://vulners.com/cve/CVE-2022-32742\n|     \tCVE-2021-44141\t4.3\thttps://vulners.com/cve/CVE-2021-44141\n|     \tCVE-2020-14318\t4.3\thttps://vulners.com/cve/CVE-2020-14318\n|     \tCVE-2018-14628\t4.3\thttps://vulners.com/cve/CVE-2018-14628\n|     \tCVE-2018-1050\t4.3\thttps://vulners.com/cve/CVE-2018-1050\n|     \tCVE-2021-43566\t2.5\thttps://vulners.com/cve/CVE-2021-43566\n|     \tCNVD-2022-18047\t2.5\thttps://vulners.com/cnvd/CNVD-2022-18047\n|     \tPACKETSTORM:142782\t0.0\thttps://vulners.com/packetstorm/PACKETSTORM:142782\t*EXPLOIT*\n|     \tPACKETSTORM:142715\t0.0\thttps://vulners.com/packetstorm/PACKETSTORM:142715\t*EXPLOIT*\n|     \tPACKETSTORM:142657\t0.0\thttps://vulners.com/packetstorm/PACKETSTORM:142657\t*EXPLOIT*\n|     \t6D5F0255-A870-5809-94B1-79F2F2D9170B\t0.0\thttps://vulners.com/githubexploit/6D5F0255-A870-5809-94B1-79F2F2D9170B\t*EXPLOIT*\n|_    \t1337DAY-ID-29999\t0.0\thttps://vulners.com/zdt/1337DAY-ID-29999\t*EXPLOIT*\n|_smb-enum-services: ERROR: Script execution failed (use -d to debug)\n445/tcp open  netbios-ssn Samba smbd 4.6.2\n|_smb-enum-services: ERROR: Script execution failed (use -d to debug)\n| vulners: \n|   cpe:/a:samba:samba:4.6.2: \n|     \tSSV:93139\t10.0\thttps://vulners.com/seebug/SSV:93139\t*EXPLOIT*\n|     \tSAMBA_IS_KNOWN_PIPENAME\t10.0\thttps://vulners.com/canvas/SAMBA_IS_KNOWN_PIPENAME\t*EXPLOIT*\n|     \tSAINT:C50A339EFD5B2F96051BC00F96014CAA\t10.0\thttps://vulners.com/saint/SAINT:C50A339EFD5B2F96051BC00F96014CAA\t*EXPLOIT*\n|     \tSAINT:6FE788CBA26F517C02B44A699047593B\t10.0\thttps://vulners.com/saint/SAINT:6FE788CBA26F517C02B44A699047593B\t*EXPLOIT*\n|     \tSAINT:3579A721D51A069C725493EA48A26E42\t10.0\thttps://vulners.com/saint/SAINT:3579A721D51A069C725493EA48A26E42\t*EXPLOIT*\n|     \tEXPLOITPACK:11BDEE18B40708887778CCF837705185\t10.0\thttps://vulners.com/exploitpack/EXPLOITPACK:11BDEE18B40708887778CCF837705185\t*EXPLOIT*\n|     \tEDB-ID:49071\t10.0\thttps://vulners.com/exploitdb/EDB-ID:49071\t*EXPLOIT*\n|     \tEDB-ID:42084\t10.0\thttps://vulners.com/exploitdb/EDB-ID:42084\t*EXPLOIT*\n|     \tEDB-ID:42060\t10.0\thttps://vulners.com/exploitdb/EDB-ID:42060\t*EXPLOIT*\n|     \tCVE-2020-1472\t10.0\thttps://vulners.com/cve/CVE-2020-1472\n|     \tCVE-2017-7494\t10.0\thttps://vulners.com/cve/CVE-2017-7494\n|     \t95499236-C9FE-56A6-9D7D-E943A24B633A\t10.0\thttps://vulners.com/githubexploit/95499236-C9FE-56A6-9D7D-E943A24B633A\t*EXPLOIT*\n|     \t2C119FFA-ECE0-5E14-A4A4-354A2C38071A\t10.0\thttps://vulners.com/githubexploit/2C119FFA-ECE0-5E14-A4A4-354A2C38071A\t*EXPLOIT*\n|     \t1337DAY-ID-27859\t10.0\thttps://vulners.com/zdt/1337DAY-ID-27859\t*EXPLOIT*\n|     \t1337DAY-ID-27836\t10.0\thttps://vulners.com/zdt/1337DAY-ID-27836\t*EXPLOIT*\n|     \tMSF:EXPLOIT-LINUX-SAMBA-IS_KNOWN_PIPENAME-\t9.8\thttps://vulners.com/metasploit/MSF:EXPLOIT-LINUX-SAMBA-IS_KNOWN_PIPENAME-\t*EXPLOIT*\n|     \tCVE-2023-3961\t9.8\thttps://vulners.com/cve/CVE-2023-3961\n|     \tCVE-2022-45141\t9.8\thttps://vulners.com/cve/CVE-2022-45141\n|     \tCVE-2017-14746\t9.8\thttps://vulners.com/cve/CVE-2017-14746\n|     \tPACKETSTORM:160127\t9.3\thttps://vulners.com/packetstorm/PACKETSTORM:160127\t*EXPLOIT*\n|     \tCVE-2021-44142\t9.0\thttps://vulners.com/cve/CVE-2021-44142\n|     \tCVE-2020-25719\t9.0\thttps://vulners.com/cve/CVE-2020-25719\n|     \tCVE-2020-17049\t9.0\thttps://vulners.com/cve/CVE-2020-17049\n|     \tB0F54D1F-71E5-54D9-BED8-BA166E090B14\t9.0\thttps://vulners.com/githubexploit/B0F54D1F-71E5-54D9-BED8-BA166E090B14\t*EXPLOIT*\n|     \t3E948D04-4013-561A-9F41-7C692AB14993\t9.0\thttps://vulners.com/githubexploit/3E948D04-4013-561A-9F41-7C692AB14993\t*EXPLOIT*\n|     \tCVE-2022-42898\t8.8\thttps://vulners.com/cve/CVE-2022-42898\n|     \tCVE-2022-32744\t8.8\thttps://vulners.com/cve/CVE-2022-32744\n|     \tCVE-2022-2031\t8.8\thttps://vulners.com/cve/CVE-2022-2031\n|     \tCVE-2022-0336\t8.8\thttps://vulners.com/cve/CVE-2022-0336\n|     \tCVE-2021-3738\t8.8\thttps://vulners.com/cve/CVE-2021-3738\n|     \tCVE-2020-25722\t8.8\thttps://vulners.com/cve/CVE-2020-25722\n|     \tCVE-2020-25721\t8.8\thttps://vulners.com/cve/CVE-2020-25721\n|     \tCVE-2020-25718\t8.8\thttps://vulners.com/cve/CVE-2020-25718\n|     \tCVE-2018-10858\t8.8\thttps://vulners.com/cve/CVE-2018-10858\n|     \tCVE-2018-1057\t8.8\thttps://vulners.com/cve/CVE-2018-1057\n|     \t9AAA7F0F-9868-5520-89F3-D09DFC7BE995\t8.8\thttps://vulners.com/githubexploit/9AAA7F0F-9868-5520-89F3-D09DFC7BE995\t*EXPLOIT*\n|     \tCVE-2020-25717\t8.5\thttps://vulners.com/cve/CVE-2020-25717\n|     \tCVE-2022-38023\t8.1\thttps://vulners.com/cve/CVE-2022-38023\n|     \tCVE-2022-37966\t8.1\thttps://vulners.com/cve/CVE-2022-37966\n|     \tCVE-2022-32745\t8.1\thttps://vulners.com/cve/CVE-2022-32745\n|     \tCVE-2017-11103\t8.1\thttps://vulners.com/cve/CVE-2017-11103\n|     \tCVE-2020-10745\t7.8\thttps://vulners.com/cve/CVE-2020-10745\n|     \tC841D92F-11E1-5077-AE70-CA2FEF0BC96E\t7.8\thttps://vulners.com/githubexploit/C841D92F-11E1-5077-AE70-CA2FEF0BC96E\t*EXPLOIT*\n|     \tCVE-2023-0614\t7.7\thttps://vulners.com/cve/CVE-2023-0614\n|     \tCVE-2023-4154\t7.5\thttps://vulners.com/cve/CVE-2023-4154\n|     \tCVE-2023-34966\t7.5\thttps://vulners.com/cve/CVE-2023-34966\n|     \tCVE-2022-32743\t7.5\thttps://vulners.com/cve/CVE-2022-32743\n|     \tCVE-2021-23192\t7.5\thttps://vulners.com/cve/CVE-2021-23192\n|     \tCVE-2021-20277\t7.5\thttps://vulners.com/cve/CVE-2021-20277\n|     \tCVE-2020-27840\t7.5\thttps://vulners.com/cve/CVE-2020-27840\n|     \tCVE-2020-14303\t7.5\thttps://vulners.com/cve/CVE-2020-14303\n|     \tCVE-2020-10704\t7.5\thttps://vulners.com/cve/CVE-2020-10704\n|     \tCVE-2018-16860\t7.5\thttps://vulners.com/cve/CVE-2018-16860\n|     \tCVE-2017-15275\t7.5\thttps://vulners.com/cve/CVE-2017-15275\n|     \tCVE-2017-12151\t7.4\thttps://vulners.com/cve/CVE-2017-12151\n|     \tCVE-2017-12150\t7.4\thttps://vulners.com/cve/CVE-2017-12150\n|     \tCVE-2022-37967\t7.2\thttps://vulners.com/cve/CVE-2022-37967\n|     \tCVE-2017-12163\t7.1\thttps://vulners.com/cve/CVE-2017-12163\n|     \t87B06BBD-7ED2-5BD2-95E1-21EE66501505\t7.0\thttps://vulners.com/githubexploit/87B06BBD-7ED2-5BD2-95E1-21EE66501505\t*EXPLOIT*\n|     \tCVE-2021-20316\t6.8\thttps://vulners.com/cve/CVE-2021-20316\n|     \tCVE-2021-20254\t6.8\thttps://vulners.com/cve/CVE-2021-20254\n|     \tCNVD-2021-39694\t6.8\thttps://vulners.com/cnvd/CNVD-2021-39694\n|     \tCVE-2023-5568\t6.5\thttps://vulners.com/cve/CVE-2023-5568\n|     \tCVE-2023-42670\t6.5\thttps://vulners.com/cve/CVE-2023-42670\n|     \tCVE-2023-42669\t6.5\thttps://vulners.com/cve/CVE-2023-42669\n|     \tCVE-2023-4091\t6.5\thttps://vulners.com/cve/CVE-2023-4091\n|     \tCVE-2022-3592\t6.5\thttps://vulners.com/cve/CVE-2022-3592\n|     \tCVE-2022-3437\t6.5\thttps://vulners.com/cve/CVE-2022-3437\n|     \tCVE-2021-3671\t6.5\thttps://vulners.com/cve/CVE-2021-3671\n|     \tCVE-2021-3670\t6.5\thttps://vulners.com/cve/CVE-2021-3670\n|     \tCVE-2020-14383\t6.5\thttps://vulners.com/cve/CVE-2020-14383\n|     \tCVE-2020-10760\t6.5\thttps://vulners.com/cve/CVE-2020-10760\n|     \tCVE-2020-10730\t6.5\thttps://vulners.com/cve/CVE-2020-10730\n|     \tCVE-2019-3824\t6.5\thttps://vulners.com/cve/CVE-2019-3824\n|     \tCVE-2019-10218\t6.5\thttps://vulners.com/cve/CVE-2019-10218\n|     \tCVE-2018-16851\t6.5\thttps://vulners.com/cve/CVE-2018-16851\n|     \tCVE-2018-16841\t6.5\thttps://vulners.com/cve/CVE-2018-16841\n|     \tCVE-2018-14629\t6.5\thttps://vulners.com/cve/CVE-2018-14629\n|     \tCVE-2018-10919\t6.5\thttps://vulners.com/cve/CVE-2018-10919\n|     \tCVE-2019-14870\t6.4\thttps://vulners.com/cve/CVE-2019-14870\n|     \tCVE-2023-0922\t5.9\thttps://vulners.com/cve/CVE-2023-0922\n|     \tCVE-2021-20251\t5.9\thttps://vulners.com/cve/CVE-2021-20251\n|     \tCVE-2016-2124\t5.9\thttps://vulners.com/cve/CVE-2016-2124\n|     \tPACKETSTORM:180777\t5.5\thttps://vulners.com/packetstorm/PACKETSTORM:180777\t*EXPLOIT*\n|     \tMSF:AUXILIARY-ADMIN-DCERPC-CVE_2020_1472_ZEROLOGON-\t5.5\thttps://vulners.com/metasploit/MSF:AUXILIARY-ADMIN-DCERPC-CVE_2020_1472_ZEROLOGON-\t*EXPLOIT*\n|     \tFC661572-B96B-5B2C-B12F-E8D279E189BF\t5.5\thttps://vulners.com/githubexploit/FC661572-B96B-5B2C-B12F-E8D279E189BF\t*EXPLOIT*\n|     \tF472C105-E3B1-524A-BBF5-1C436185F6EE\t5.5\thttps://vulners.com/githubexploit/F472C105-E3B1-524A-BBF5-1C436185F6EE\t*EXPLOIT*\n|     \tF085F702-F1C3-5ACB-99BE-086DA182D98B\t5.5\thttps://vulners.com/githubexploit/F085F702-F1C3-5ACB-99BE-086DA182D98B\t*EXPLOIT*\n|     \tE9F25671-2BEF-5E8B-A60A-55C6DD9DE820\t5.5\thttps://vulners.com/githubexploit/E9F25671-2BEF-5E8B-A60A-55C6DD9DE820\t*EXPLOIT*\n|     \tDEC5B8BB-1933-54FF-890E-9C2720E9966E\t5.5\thttps://vulners.com/githubexploit/DEC5B8BB-1933-54FF-890E-9C2720E9966E\t*EXPLOIT*\n|     \tD7AB3F4A-8E41-5E5B-B987-99AFB571FE9C\t5.5\thttps://vulners.com/githubexploit/D7AB3F4A-8E41-5E5B-B987-99AFB571FE9C\t*EXPLOIT*\n|     \tD3C401E0-D013-59E2-8FFB-6BEF41DA3D1B\t5.5\thttps://vulners.com/githubexploit/D3C401E0-D013-59E2-8FFB-6BEF41DA3D1B\t*EXPLOIT*\n|     \tD178DAA4-01D0-50D0-A741-1C3C76A7D023\t5.5\thttps://vulners.com/githubexploit/D178DAA4-01D0-50D0-A741-1C3C76A7D023\t*EXPLOIT*\n|     \tCVE-2022-1615\t5.5\thttps://vulners.com/cve/CVE-2022-1615\n|     \tCVE-2020-14323\t5.5\thttps://vulners.com/cve/CVE-2020-14323\n|     \tCVE-2019-3880\t5.5\thttps://vulners.com/cve/CVE-2019-3880\n|     \tCVE-2019-14902\t5.5\thttps://vulners.com/cve/CVE-2019-14902\n|     \tCF07CF32-0B8E-58E5-A410-8FA68D411ED0\t5.5\thttps://vulners.com/githubexploit/CF07CF32-0B8E-58E5-A410-8FA68D411ED0\t*EXPLOIT*\n|     \tC7F6FB3B-581D-53E1-A2BF-C935FE7B03C8\t5.5\thttps://vulners.com/githubexploit/C7F6FB3B-581D-53E1-A2BF-C935FE7B03C8\t*EXPLOIT*\n|     \tC7CE5D12-A4E5-5FF2-9F07-CD5E84B4C02F\t5.5\thttps://vulners.com/githubexploit/C7CE5D12-A4E5-5FF2-9F07-CD5E84B4C02F\t*EXPLOIT*\n|     \tC5B49BD0-D347-5AEB-A774-EE7BB35688E9\t5.5\thttps://vulners.com/githubexploit/C5B49BD0-D347-5AEB-A774-EE7BB35688E9\t*EXPLOIT*\n|     \tBBE1926E-1EC7-5657-8766-3CA8418F815C\t5.5\thttps://vulners.com/githubexploit/BBE1926E-1EC7-5657-8766-3CA8418F815C\t*EXPLOIT*\n|     \tBA280EB1-2FF9-52DA-8BA4-A276A1158DD8\t5.5\thttps://vulners.com/githubexploit/BA280EB1-2FF9-52DA-8BA4-A276A1158DD8\t*EXPLOIT*\n|     \tB7C1C535-3653-5D12-8922-4C6A5CCBD5F3\t5.5\thttps://vulners.com/githubexploit/B7C1C535-3653-5D12-8922-4C6A5CCBD5F3\t*EXPLOIT*\n|     \tAEF449B8-DC3E-544A-A748-5A1C6F7EBA59\t5.5\thttps://vulners.com/githubexploit/AEF449B8-DC3E-544A-A748-5A1C6F7EBA59\t*EXPLOIT*\n|     \tA24AC1AC-55EF-51D8-B696-32F369DCAB96\t5.5\thttps://vulners.com/githubexploit/A24AC1AC-55EF-51D8-B696-32F369DCAB96\t*EXPLOIT*\n|     \t9C9BD402-511C-597D-9864-647131FE6647\t5.5\thttps://vulners.com/githubexploit/9C9BD402-511C-597D-9864-647131FE6647\t*EXPLOIT*\n|     \t939F3BE7-AF69-5351-BD56-12412FA184C5\t5.5\thttps://vulners.com/githubexploit/939F3BE7-AF69-5351-BD56-12412FA184C5\t*EXPLOIT*\n|     \t879CF3A7-ECBC-552A-A044-5E2724F63279\t5.5\thttps://vulners.com/githubexploit/879CF3A7-ECBC-552A-A044-5E2724F63279\t*EXPLOIT*\n|     \t7078ED42-959E-5242-BE9D-17F2F99C76A8\t5.5\thttps://vulners.com/githubexploit/7078ED42-959E-5242-BE9D-17F2F99C76A8\t*EXPLOIT*\n|     \t6FB0B63E-DE9A-5065-B577-ECA3ED5E9F4B\t5.5\thttps://vulners.com/githubexploit/6FB0B63E-DE9A-5065-B577-ECA3ED5E9F4B\t*EXPLOIT*\n|     \t63C36F7A-5F99-5A79-B99F-260360AC237F\t5.5\thttps://vulners.com/githubexploit/63C36F7A-5F99-5A79-B99F-260360AC237F\t*EXPLOIT*\n|     \t5E80DB20-575C-537A-9B83-CCFCCB55E448\t5.5\thttps://vulners.com/githubexploit/5E80DB20-575C-537A-9B83-CCFCCB55E448\t*EXPLOIT*\n|     \t5B025A0D-055E-552C-B1FB-287C6F191F8E\t5.5\thttps://vulners.com/githubexploit/5B025A0D-055E-552C-B1FB-287C6F191F8E\t*EXPLOIT*\n|     \t50FA6373-CBCD-5EF5-B37D-0ECD621C6134\t5.5\thttps://vulners.com/githubexploit/50FA6373-CBCD-5EF5-B37D-0ECD621C6134\t*EXPLOIT*\n|     \t4CB63A18-5D6F-57E3-8CD8-9110CF63E120\t5.5\thttps://vulners.com/githubexploit/4CB63A18-5D6F-57E3-8CD8-9110CF63E120\t*EXPLOIT*\n|     \t49EC151F-12F0-59CF-960C-25BD54F46680\t5.5\thttps://vulners.com/githubexploit/49EC151F-12F0-59CF-960C-25BD54F46680\t*EXPLOIT*\n|     \t3F400483-1F7E-5BE5-8612-4D55D450D553\t5.5\thttps://vulners.com/githubexploit/3F400483-1F7E-5BE5-8612-4D55D450D553\t*EXPLOIT*\n|     \t2E71FF50-1B48-5A8E-9212-C4CF9399715C\t5.5\thttps://vulners.com/githubexploit/2E71FF50-1B48-5A8E-9212-C4CF9399715C\t*EXPLOIT*\n|     \t2D16FB2A-7A61-5E45-AAF8-1E090E0ADCC0\t5.5\thttps://vulners.com/githubexploit/2D16FB2A-7A61-5E45-AAF8-1E090E0ADCC0\t*EXPLOIT*\n|     \t28D42B84-AB24-5FC6-ADE1-610374D67F21\t5.5\thttps://vulners.com/githubexploit/28D42B84-AB24-5FC6-ADE1-610374D67F21\t*EXPLOIT*\n|     \t2255B39F-1B91-56F4-A323-8704808620D3\t5.5\thttps://vulners.com/githubexploit/2255B39F-1B91-56F4-A323-8704808620D3\t*EXPLOIT*\n|     \t20466D13-6C5B-5326-9C8B-160E9BE37195\t5.5\thttps://vulners.com/githubexploit/20466D13-6C5B-5326-9C8B-160E9BE37195\t*EXPLOIT*\n|     \t14BD2DBD-3A91-55FC-9836-14EF9ABF56CF\t5.5\thttps://vulners.com/githubexploit/14BD2DBD-3A91-55FC-9836-14EF9ABF56CF\t*EXPLOIT*\n|     \t1337DAY-ID-35274\t5.5\thttps://vulners.com/zdt/1337DAY-ID-35274\t*EXPLOIT*\n|     \t12E44744-1AF0-523A-ACA2-593B4D33E014\t5.5\thttps://vulners.com/githubexploit/12E44744-1AF0-523A-ACA2-593B4D33E014\t*EXPLOIT*\n|     \t0CFAB531-412C-57A0-BD9E-EF072620C078\t5.5\thttps://vulners.com/githubexploit/0CFAB531-412C-57A0-BD9E-EF072620C078\t*EXPLOIT*\n|     \t07E56BF6-A72B-5ACD-A2FF-818C48E4E132\t5.5\thttps://vulners.com/githubexploit/07E56BF6-A72B-5ACD-A2FF-818C48E4E132\t*EXPLOIT*\n|     \t07DF268C-467E-54A3-B713-057BA19C72F7\t5.5\thttps://vulners.com/githubexploit/07DF268C-467E-54A3-B713-057BA19C72F7\t*EXPLOIT*\n|     \t06BAC40D-74DF-5994-909F-3A87FC3B76C8\t5.5\thttps://vulners.com/githubexploit/06BAC40D-74DF-5994-909F-3A87FC3B76C8\t*EXPLOIT*\n|     \t04BCA9BC-E3AD-5234-A5F0-7A1ED826F600\t5.5\thttps://vulners.com/githubexploit/04BCA9BC-E3AD-5234-A5F0-7A1ED826F600\t*EXPLOIT*\n|     \t042AB58A-C86A-5A8B-AED3-2FF3624E97E3\t5.5\thttps://vulners.com/githubexploit/042AB58A-C86A-5A8B-AED3-2FF3624E97E3\t*EXPLOIT*\n|     \tCVE-2022-32746\t5.4\thttps://vulners.com/cve/CVE-2022-32746\n|     \tCVE-2019-14833\t5.4\thttps://vulners.com/cve/CVE-2019-14833\n|     \tCVE-2023-34968\t5.3\thttps://vulners.com/cve/CVE-2023-34968\n|     \tCVE-2023-34967\t5.3\thttps://vulners.com/cve/CVE-2023-34967\n|     \tCVE-2019-14861\t5.3\thttps://vulners.com/cve/CVE-2019-14861\n|     \tCVE-2019-14847\t4.9\thttps://vulners.com/cve/CVE-2019-14847\n|     \tCVE-2023-0225\t4.3\thttps://vulners.com/cve/CVE-2023-0225\n|     \tCVE-2022-32742\t4.3\thttps://vulners.com/cve/CVE-2022-32742\n|     \tCVE-2021-44141\t4.3\thttps://vulners.com/cve/CVE-2021-44141\n|     \tCVE-2020-14318\t4.3\thttps://vulners.com/cve/CVE-2020-14318\n|     \tCVE-2018-14628\t4.3\thttps://vulners.com/cve/CVE-2018-14628\n|     \tCVE-2018-1050\t4.3\thttps://vulners.com/cve/CVE-2018-1050\n|     \tCVE-2021-43566\t2.5\thttps://vulners.com/cve/CVE-2021-43566\n|     \tCNVD-2022-18047\t2.5\thttps://vulners.com/cnvd/CNVD-2022-18047\n|     \tPACKETSTORM:142782\t0.0\thttps://vulners.com/packetstorm/PACKETSTORM:142782\t*EXPLOIT*\n|     \tPACKETSTORM:142715\t0.0\thttps://vulners.com/packetstorm/PACKETSTORM:142715\t*EXPLOIT*\n|     \tPACKETSTORM:142657\t0.0\thttps://vulners.com/packetstorm/PACKETSTORM:142657\t*EXPLOIT*\n|     \t6D5F0255-A870-5809-94B1-79F2F2D9170B\t0.0\thttps://vulners.com/githubexploit/6D5F0255-A870-5809-94B1-79F2F2D9170B\t*EXPLOIT*\n|_    \t1337DAY-ID-29999\t0.0\thttps://vulners.com/zdt/1337DAY-ID-29999\t*EXPLOIT*\nService Info: Host:  student-laptop\n\nHost script results:\n| port-states: \n|   tcp: \n|     open: 25,80,139,445\n|_    closed: 1-24,26-79,81-138,140-444,446-65535\n|_samba-vuln-cve-2012-1182: Could not negotiate a connection:SMB: ERROR: Server returned less data than it was supposed to (one or more fields are missing); aborting [9]\n|_nbstat: NetBIOS name: STUDENT-LAPTOP, NetBIOS user: <unknown>, NetBIOS MAC: <unknown> (unknown)\n|_fcrdns: PASS (student-laptop)\n|_smb-vuln-ms10-054: false\n|_dns-brute: Can\'t guess domain of "student-laptop.."; use dns-brute.domain script argument.\n|_smb-vuln-ms10-061: Could not negotiate a connection:SMB: ERROR: Server returned less data than it was supposed to (one or more fields are missing); aborting [9]\n| smb-protocols: \n|   dialects: \n|     2:0:2\n|     2:1:0\n|     3:0:0\n|     3:0:2\n|_    3:1:1\n|_msrpc-enum: Could not negotiate a connection:SMB: ERROR: Server returned less data than it was supposed to (one or more fields are missing); aborting [9]\n| dns-blacklist: \n|   SPAM\n|_    all.spamrats.com - FAIL\n| smb2-time: \n|   date: 2025-06-30T17:19:28\n|_  start_date: N/A\n| smb2-capabilities: \n|   2:0:2: \n|     Distributed File System\n|   2:1:0: \n|     Distributed File System\n|     Leasing\n|     Multi-credit operations\n|   3:0:0: \n|     Distributed File System\n|     Leasing\n|     Multi-credit operations\n|   3:0:2: \n|     Distributed File System\n|     Leasing\n|     Multi-credit operations\n|   3:1:1: \n|     Distributed File System\n|     Leasing\n|_    Multi-credit operations\n| smb-mbenum: \n|_  ERROR: Failed to connect to browser service: Could not negotiate a connection:SMB: ERROR: Server returned less data than it was supposed to (one or more fields are missing); aborting [9]\n| smb2-security-mode: \n|   3:1:1: \n|_    Message signing enabled but not required\n\nPost-scan script results:\n| reverse-index: \n|   25/tcp: ************\n|   80/tcp: ************\n|   139/tcp: ************\n|_  445/tcp: ************\nService detection performed. Please report any incorrect results at https://nmap.org/submit/ .\nNmap done: 1 IP address (1 host up) scanned in 116.03 seconds\n'}, 'openvas': {'status': 'completed', 'scan_id': '1441914e-43f8-4174-a288-8a36c593e08b', 'task_id': 'greenbone_task_1441914e-43f8-4174-a288-8a36c593e08b', 'target_id': 'greenbone_target_1441914e-43f8-4174-a288-8a36c593e08b', 'vulnerabilities': [{'name': 'SMTP Service Detected', 'severity': 'medium', 'description': 'SMTP service - check for open relay', 'port': 25, 'solution': 'Review SMTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'SMB Service Detected', 'severity': 'high', 'description': 'SMB service - check for SMB vulnerabilities', 'port': 445, 'solution': 'Review SMB service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}], 'message': 'Greenbone scan completed for ************', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/portscan/tcp', 'auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 4, 'raw_output': "=== TCP Port Scanner ===\n\x1b[0m\x1b[0mRHOSTS => ************\n\x1b[0mPORTS => 1-1000\n\x1b[0m\x1b[1m\x1b[32m[+]\x1b[0m ************          - ************:25 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ************          - ************:80 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ************          - ************:139 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ************          - ************:445 - TCP OPEN\n\x1b[1m\x1b[34m[*]\x1b[0m ************          - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ************:445      - SMB Detected (versions:2, 3) (preferred dialect:SMB 3.1.1) (compression capabilities:) (encryption capabilities:AES-128-GCM) (signatures:optional) (guid:{64757473-6e65-2d74-6c61-70746f700000}) (authentication domain:STUDENT-LAPTOP)\n\x1b[1m\x1b[32m[+]\x1b[0m ************:445      -   Host is running Version 6.1.0 (unknown OS)\n\x1b[1m\x1b[34m[*]\x1b[0m ************          - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m Error: ************: Errno::ECONNREFUSED Connection refused - connect(2) for ************:22\n\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => ************\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m ************:445      - An SMB Login Error occurred while connecting to the IPC$ tree.\n\x1b[1m\x1b[34m[*]\x1b[0m ************:445      - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'web': {'nikto': {'status': 'completed', 'vulnerabilities': [{'type': 'Information Disclosure via ETags', 'severity': 'medium', 'description': 'GET /: Server leaks inodes via ETags, header found with file /, fields: 0x2aa6 0x58c4b46756eda', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Missing X-Frame-Options Header', 'severity': 'medium', 'description': 'GET /: The anti-clickjacking X-Frame-Options header is not present.', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'HTTP Methods Disclosure', 'severity': 'medium', 'description': 'OPTIONS /: Allowed HTTP Methods: GET, POST, OPTIONS, HEAD', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Security Finding', 'severity': 'low', 'description': '-561: GET /server-status: /server-status: This reveals Apache information. Comment out appropriate line in httpd.conf or restrict access to allowed hosts.', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Administrative Interface Found', 'severity': 'medium', 'description': 'GET /phpmyadmin/: /phpmyadmin/: phpMyAdmin directory found', 'source_tool': 'nikto', 'category': 'web'}], 'scan_time': '10 seconds', 'total_tests': 9, 'raw_output': '- Nikto v2.1.5/2.1.5\n+ Target Host: ************\n+ Target Port: 80\n+ GET /: Server leaks inodes via ETags, header found with file /, fields: 0x2aa6 0x58c4b46756eda \n+ GET /: The anti-clickjacking X-Frame-Options header is not present.\n+ OPTIONS /: Allowed HTTP Methods: GET, POST, OPTIONS, HEAD \n+ -561: GET /server-status: /server-status: This reveals Apache information. Comment out appropriate line in httpd.conf or restrict access to allowed hosts.\n+ GET /phpmyadmin/: /phpmyadmin/: phpMyAdmin directory found\n'}, 'sqlmap': {'status': 'completed', 'injections': [], 'raw_output': '        ___\n       __H__\n ___ ___[\']_____ ___ ___  {1.8.4#stable}\n|_ -| . ["]     | .\'| . |\n|___|_  [,]_|_|_|__,|  _|\n      |_|V...       |_|   https://sqlmap.org\n\n[!] legal disclaimer: Usage of sqlmap for attacking targets without prior mutual consent is illegal. It is the end user\'s responsibility to obey all applicable local, state and federal laws. Developers assume no liability and are not responsible for any misuse or damage caused by this program\n\n[*] starting @ 19:17:55 /2025-06-30/\n\n[19:17:55] [INFO] loading tamper module \'space2comment\'\n[19:17:55] [INFO] testing connection to the target URL\n[19:18:00] [INFO] checking if the target is protected by some kind of WAF/IPS\n[19:18:00] [INFO] testing if the target URL content is stable\n[19:18:00] [INFO] target URL content is stable\n[19:18:00] [INFO] testing if parameter \'User-Agent\' is dynamic\n[19:18:00] [WARNING] parameter \'User-Agent\' does not appear to be dynamic\n[19:18:00] [WARNING] heuristic (basic) test shows that parameter \'User-Agent\' might not be injectable\n[19:18:00] [INFO] testing for SQL injection on parameter \'User-Agent\'\n[19:18:00] [INFO] testing \'AND boolean-based blind - WHERE or HAVING clause\'\n[19:18:01] [INFO] testing \'OR boolean-based blind - WHERE or HAVING clause\'\n[19:18:01] [INFO] testing \'OR boolean-based blind - WHERE or HAVING clause (NOT)\'\n[19:18:01] [INFO] testing \'AND boolean-based blind - WHERE or HAVING clause (subquery - comment)\'\n[19:18:02] [INFO] testing \'OR boolean-based blind - WHERE or HAVING clause (subquery - comment)\'\n[19:18:02] [INFO] testing \'AND boolean-based blind - WHERE or HAVING clause (comment)\'\n[19:18:02] [INFO] testing \'OR boolean-based blind - WHERE or HAVING clause (comment)\'\n[19:18:02] [INFO] testing \'OR boolean-based blind - WHERE or HAVING clause (NOT - comment)\'\n[19:18:02] [INFO] testing \'AND boolean-based blind - WHERE or HAVING clause (MySQL comment)\'\n[19:18:02] [INFO] testing \'OR boolean-based blind - WHERE or HAVING clause (MySQL comment)\'\n[19:18:02] [INFO] testing \'OR boolean-based blind - WHERE or HAVING clause (NOT - MySQL comment)\'\n[19:18:02] [INFO] testing \'AND boolean-based blind - WHERE or HAVING clause (Microsoft Access comment)\'\n[19:18:03] [INFO] testing \'OR boolean-based blind - WHERE or HAVING clause (Microsoft Access comment)\'\n[19:18:03] [INFO] testing \'MySQL RLIKE boolean-based blind - WHERE, HAVING, ORDER BY or GROUP BY clause\'\n[19:18:03] [INFO] testing \'MySQL AND boolean-based blind - WHERE, HAVING, ORDER BY or GROUP BY clause (MAKE_SET)\'\n[19:18:03] [INFO] testing \'MySQL OR boolean-based blind - WHERE, HAVING, ORDER BY or GROUP BY clause (MAKE_SET)\'\n[19:18:04] [INFO] testing \'MySQL AND boolean-based blind - WHERE, HAVING, ORDER BY or GROUP BY clause (ELT)\'\n[19:18:04] [INFO] testing \'MySQL OR boolean-based blind - WHERE, HAVING, ORDER BY or GROUP BY clause (ELT)\'\n[19:18:04] [INFO] testing \'MySQL AND boolean-based blind - WHERE, HAVING, ORDER BY or GROUP BY clause (EXTRACTVALUE)\'\n[19:18:04] [INFO] testing \'MySQL OR boolean-based blind - WHERE, HAVING, ORDER BY or GROUP BY clause (EXTRACTVALUE)\'\n[19:18:05] [INFO] testing \'PostgreSQL AND boolean-based blind - WHERE or HAVING clause (CAST)\'\n[19:18:05] [INFO] testing \'PostgreSQL OR boolean-based blind - WHERE or HAVING clause (CAST)\'\n[19:18:05] [INFO] testing \'Oracle AND boolean-based blind - WHERE or HAVING clause (CTXSYS.DRITHSX.SN)\'\n[19:18:05] [INFO] testing \'Oracle OR boolean-based blind - WHERE or HAVING clause (CTXSYS.DRITHSX.SN)\'\n[19:18:06] [INFO] testing \'SQLite AND boolean-based blind - WHERE, HAVING, GROUP BY or HAVING clause (JSON)\'\n[19:18:06] [INFO] testing \'SQLite OR boolean-based blind - WHERE, HAVING, GROUP BY or HAVING clause (JSON)\'\n[19:18:06] [INFO] testing \'Boolean-based blind - Parameter replace (original value)\'\n[19:18:06] [INFO] testing \'MySQL boolean-based blind - Parameter replace (MAKE_SET)\'\n[19:18:06] [INFO] testing \'MySQL boolean-based blind - Parameter replace (MAKE_SET - original value)\'\n[19:18:06] [INFO] testing \'MySQL boolean-based blind - Parameter replace (ELT)\'\n[19:18:06] [INFO] testing \'MySQL boolean-based blind - Parameter replace (ELT - original value)\'\n[19:18:06] [INFO] testing \'MySQL boolean-based blind - Parameter replace (bool*int)\'\n[19:18:06] [INFO] testing \'MySQL boolean-based blind - Parameter replace (bool*int - original value)\'\n[19:18:06] [INFO] testing \'PostgreSQL boolean-based blind - Parameter replace\'\n[19:18:06] [INFO] testing \'PostgreSQL boolean-based blind - Parameter replace (original value)\'\n[19:18:06] [INFO] testing \'PostgreSQL boolean-based blind - Parameter replace (GENERATE_SERIES)\'\n[19:18:06] [INFO] testing \'PostgreSQL boolean-based blind - Parameter replace (GENERATE_SERIES - original value)\'\n[19:18:06] [INFO] testing \'Microsoft SQL Server/Sybase boolean-based blind - Parameter replace\'\n[19:18:06] [INFO] testing \'Microsoft SQL Server/Sybase boolean-based blind - Parameter replace (original value)\'\n[19:18:06] [INFO] testing \'Oracle boolean-based blind - Parameter replace\'\n[19:18:06] [INFO] testing \'Oracle boolean-based blind - Parameter replace (original value)\'\n[19:18:06] [INFO] testing \'Informix boolean-based blind - Parameter replace\'\n[19:18:06] [INFO] testing \'Informix boolean-based blind - Parameter replace (original value)\'\n[19:18:06] [INFO] testing \'Microsoft Access boolean-based blind - Parameter replace\'\n[19:18:06] [INFO] testing \'Microsoft Access boolean-based blind - Parameter replace (original value)\'\n[19:18:06] [INFO] testing \'Boolean-based blind - Parameter replace (DUAL)\'\n[19:18:06] [INFO] testing \'Boolean-based blind - Parameter replace (DUAL - original value)\'\n[19:18:06] [INFO] testing \'Boolean-based blind - Parameter replace (CASE)\'\n[19:18:06] [INFO] testing \'Boolean-based blind - Parameter replace (CASE - original value)\'\n[19:18:06] [INFO] testing \'MySQL >= 5.0 boolean-based blind - ORDER BY, GROUP BY clause\'\n[19:18:06] [INFO] testing \'MySQL >= 5.0 boolean-based blind - ORDER BY, GROUP BY clause (original value)\'\n[19:18:06] [INFO] testing \'MySQL < 5.0 boolean-based blind - ORDER BY, GROUP BY clause\'\n[19:18:06] [INFO] testing \'MySQL < 5.0 boolean-based blind - ORDER BY, GROUP BY clause (original value)\'\n[19:18:06] [INFO] testing \'PostgreSQL boolean-based blind - ORDER BY, GROUP BY clause\'\n[19:18:06] [INFO] testing \'PostgreSQL boolean-based blind - ORDER BY clause (original value)\'\n[19:18:06] [INFO] testing \'PostgreSQL boolean-based blind - ORDER BY clause (GENERATE_SERIES)\'\n[19:18:06] [INFO] testing \'Microsoft SQL Server/Sybase boolean-based blind - ORDER BY clause\'\n[19:18:06] [INFO] testing \'Microsoft SQL Server/Sybase boolean-based blind - ORDER BY clause (original value)\'\n[19:18:07] [INFO] testing \'Oracle boolean-based blind - ORDER BY, GROUP BY clause\'\n[19:18:07] [INFO] testing \'Oracle boolean-based blind - ORDER BY, GROUP BY clause (original value)\'\n[19:18:07] [INFO] testing \'Microsoft Access boolean-based blind - ORDER BY, GROUP BY clause\'\n[19:18:07] [INFO] testing \'Microsoft Access boolean-based blind - ORDER BY, GROUP BY clause (original value)\'\n[19:18:07] [INFO] testing \'SAP MaxDB boolean-based blind - ORDER BY, GROUP BY clause\'\n[19:18:07] [INFO] testing \'SAP MaxDB boolean-based blind - ORDER BY, GROUP BY clause (original value)\'\n[19:18:07] [INFO] testing \'IBM DB2 boolean-based blind - ORDER BY clause\'\n[19:18:07] [INFO] testing \'IBM DB2 boolean-based blind - ORDER BY clause (original value)\'\n[19:18:07] [INFO] testing \'HAVING boolean-based blind - WHERE, GROUP BY clause\'\n[19:18:07] [INFO] testing \'MySQL >= 5.0 boolean-based blind - Stacked queries\'\n[19:18:07] [INFO] testing \'MySQL < 5.0 boolean-based blind - Stacked queries\'\n[19:18:07] [INFO] testing \'PostgreSQL boolean-based blind - Stacked queries\'\n[19:18:07] [INFO] testing \'PostgreSQL boolean-based blind - Stacked queries (GENERATE_SERIES)\'\n[19:18:07] [INFO] testing \'Microsoft SQL Server/Sybase boolean-based blind - Stacked queries (IF)\'\n[19:18:07] [INFO] testing \'Microsoft SQL Server/Sybase boolean-based blind - Stacked queries\'\n[19:18:08] [INFO] testing \'Oracle boolean-based blind - Stacked queries\'\n[19:18:08] [INFO] testing \'Microsoft Access boolean-based blind - Stacked queries\'\n[19:18:08] [INFO] testing \'SAP MaxDB boolean-based blind - Stacked queries\'\n[19:18:08] [INFO] testing \'MySQL >= 5.5 AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (BIGINT UNSIGNED)\'\n[19:18:08] [INFO] testing \'MySQL >= 5.5 OR error-based - WHERE or HAVING clause (BIGINT UNSIGNED)\'\n[19:18:08] [INFO] testing \'MySQL >= 5.5 AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (EXP)\'\n[19:18:09] [INFO] testing \'MySQL >= 5.5 OR error-based - WHERE or HAVING clause (EXP)\'\n[19:18:09] [INFO] testing \'MySQL >= 5.6 AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (GTID_SUBSET)\'\n[19:18:09] [INFO] testing \'MySQL >= 5.6 OR error-based - WHERE or HAVING clause (GTID_SUBSET)\'\n[19:18:09] [INFO] testing \'MySQL >= 5.7.8 AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (JSON_KEYS)\'\n[19:18:09] [INFO] testing \'MySQL >= 5.7.8 OR error-based - WHERE or HAVING clause (JSON_KEYS)\'\n[19:18:09] [INFO] testing \'MySQL >= 5.0 AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (FLOOR)\'\n[19:18:10] [INFO] testing \'MySQL >= 5.0 OR error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (FLOOR)\'\n[19:18:10] [INFO] testing \'MySQL >= 5.1 AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (EXTRACTVALUE)\'\n[19:18:10] [INFO] testing \'MySQL >= 5.1 OR error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (EXTRACTVALUE)\'\n[19:18:10] [INFO] testing \'MySQL >= 5.1 AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (UPDATEXML)\'\n[19:18:10] [INFO] testing \'MySQL >= 5.1 OR error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (UPDATEXML)\'\n[19:18:11] [INFO] testing \'MySQL >= 4.1 AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (FLOOR)\'\n[19:18:11] [INFO] testing \'MySQL >= 4.1 OR error-based - WHERE or HAVING clause (FLOOR)\'\n[19:18:11] [INFO] testing \'MySQL OR error-based - WHERE or HAVING clause (FLOOR)\'\n[19:18:11] [INFO] testing \'PostgreSQL AND error-based - WHERE or HAVING clause\'\n[19:18:11] [INFO] testing \'PostgreSQL OR error-based - WHERE or HAVING clause\'\n[19:18:11] [INFO] testing \'Microsoft SQL Server/Sybase AND error-based - WHERE or HAVING clause (IN)\'\n[19:18:11] [INFO] testing \'Microsoft SQL Server/Sybase OR error-based - WHERE or HAVING clause (IN)\'\n[19:18:12] [INFO] testing \'Microsoft SQL Server/Sybase AND error-based - WHERE or HAVING clause (CONVERT)\'\n[19:18:12] [INFO] testing \'Microsoft SQL Server/Sybase OR error-based - WHERE or HAVING clause (CONVERT)\'\n[19:18:12] [INFO] testing \'Microsoft SQL Server/Sybase AND error-based - WHERE or HAVING clause (CONCAT)\'\n[19:18:12] [INFO] testing \'Microsoft SQL Server/Sybase OR error-based - WHERE or HAVING clause (CONCAT)\'\n[19:18:12] [INFO] testing \'Oracle AND error-based - WHERE or HAVING clause (XMLType)\'\n[19:18:12] [INFO] testing \'Oracle OR error-based - WHERE or HAVING clause (XMLType)\'\n[19:18:13] [INFO] testing \'Oracle AND error-based - WHERE or HAVING clause (UTL_INADDR.GET_HOST_ADDRESS)\'\n[19:18:13] [INFO] testing \'Oracle OR error-based - WHERE or HAVING clause (UTL_INADDR.GET_HOST_ADDRESS)\'\n[19:18:13] [INFO] testing \'Oracle AND error-based - WHERE or HAVING clause (CTXSYS.DRITHSX.SN)\'\n[19:18:13] [INFO] testing \'Oracle OR error-based - WHERE or HAVING clause (CTXSYS.DRITHSX.SN)\'\n[19:18:13] [INFO] testing \'Oracle AND error-based - WHERE or HAVING clause (DBMS_UTILITY.SQLID_TO_SQLHASH)\'\n[19:18:13] [INFO] testing \'Oracle OR error-based - WHERE or HAVING clause (DBMS_UTILITY.SQLID_TO_SQLHASH)\'\n[19:18:13] [INFO] testing \'Firebird AND error-based - WHERE or HAVING clause\'\n[19:18:14] [INFO] testing \'Firebird OR error-based - WHERE or HAVING clause\'\n[19:18:14] [INFO] testing \'MonetDB AND error-based - WHERE or HAVING clause\'\n[19:18:14] [INFO] testing \'MonetDB OR error-based - WHERE or HAVING clause\'\n[19:18:14] [INFO] testing \'Vertica AND error-based - WHERE or HAVING clause\'\n[19:18:14] [INFO] testing \'Vertica OR error-based - WHERE or HAVING clause\'\n[19:18:14] [INFO] testing \'IBM DB2 AND error-based - WHERE or HAVING clause\'\n[19:18:14] [INFO] testing \'IBM DB2 OR error-based - WHERE or HAVING clause\'\n[19:18:15] [INFO] testing \'ClickHouse AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause\'\n[19:18:15] [INFO] testing \'ClickHouse OR error-based - WHERE, HAVING, ORDER BY or GROUP BY clause\'\n[19:18:15] [INFO] testing \'MySQL >= 5.1 error-based - PROCEDURE ANALYSE (EXTRACTVALUE)\'\n[19:18:15] [INFO] testing \'MySQL >= 5.5 error-based - Parameter replace (BIGINT UNSIGNED)\'\n[19:18:15] [INFO] testing \'MySQL >= 5.5 error-based - Parameter replace (EXP)\'\n[19:18:15] [INFO] testing \'MySQL >= 5.6 error-based - Parameter replace (GTID_SUBSET)\'\n[19:18:15] [INFO] testing \'MySQL >= 5.7.8 error-based - Parameter replace (JSON_KEYS)\'\n[19:18:15] [INFO] testing \'MySQL >= 5.0 error-based - Parameter replace (FLOOR)\'\n[19:18:15] [INFO] testing \'MySQL >= 5.1 error-based - Parameter replace (UPDATEXML)\'\n[19:18:15] [INFO] testing \'MySQL >= 5.1 error-based - Parameter replace (EXTRACTVALUE)\'\n[19:18:15] [INFO] testing \'PostgreSQL error-based - Parameter replace\'\n[19:18:15] [INFO] testing \'PostgreSQL error-based - Parameter replace (GENERATE_SERIES)\'\n[19:18:15] [INFO] testing \'Microsoft SQL Server/Sybase error-based - Parameter replace\'\n[19:18:15] [INFO] testing \'Microsoft SQL Server/Sybase error-based - Parameter replace (integer column)\'\n[19:18:15] [INFO] testing \'Oracle error-based - Parameter replace\'\n[19:18:15] [INFO] testing \'Firebird error-based - Parameter replace\'\n[19:18:15] [INFO] testing \'IBM DB2 error-based - Parameter replace\'\n[19:18:15] [INFO] testing \'MySQL >= 5.5 error-based - ORDER BY, GROUP BY clause (BIGINT UNSIGNED)\'\n[19:18:15] [INFO] testing \'MySQL >= 5.5 error-based - ORDER BY, GROUP BY clause (EXP)\'\n[19:18:15] [INFO] testing \'MySQL >= 5.6 error-based - ORDER BY, GROUP BY clause (GTID_SUBSET)\'\n[19:18:15] [INFO] testing \'MySQL >= 5.7.8 error-based - ORDER BY, GROUP BY clause (JSON_KEYS)\'\n[19:18:15] [INFO] testing \'MySQL >= 5.0 error-based - ORDER BY, GROUP BY clause (FLOOR)\'\n[19:18:15] [INFO] testing \'MySQL >= 5.1 error-based - ORDER BY, GROUP BY clause (EXTRACTVALUE)\'\n[19:18:15] [INFO] testing \'MySQL >= 5.1 error-based - ORDER BY, GROUP BY clause (UPDATEXML)\'\n[19:18:15] [INFO] testing \'MySQL >= 4.1 error-based - ORDER BY, GROUP BY clause (FLOOR)\'\n[19:18:15] [INFO] testing \'PostgreSQL error-based - ORDER BY, GROUP BY clause\'\n[19:18:15] [INFO] testing \'PostgreSQL error-based - ORDER BY, GROUP BY clause (GENERATE_SERIES)\'\n[19:18:15] [INFO] testing \'Microsoft SQL Server/Sybase error-based - ORDER BY clause\'\n[19:18:15] [INFO] testing \'Oracle error-based - ORDER BY, GROUP BY clause\'\n[19:18:15] [INFO] testing \'Firebird error-based - ORDER BY clause\'\n[19:18:15] [INFO] testing \'IBM DB2 error-based - ORDER BY clause\'\n[19:18:15] [INFO] testing \'Microsoft SQL Server/Sybase error-based - Stacking (EXEC)\'\n[19:18:15] [INFO] testing \'Generic inline queries\'\n[19:18:15] [INFO] testing \'MySQL inline queries\'\n[19:18:15] [INFO] testing \'PostgreSQL inline queries\'\n[19:18:15] [INFO] testing \'Microsoft SQL Server/Sybase inline queries\'\n[19:18:15] [INFO] testing \'Oracle inline queries\'\n[19:18:15] [INFO] testing \'SQLite inline queries\'\n[19:18:15] [INFO] testing \'Firebird inline queries\'\n[19:18:15] [INFO] testing \'ClickHouse inline queries\'\n[19:18:15] [INFO] testing \'MySQL >= 5.0.12 stacked queries (comment)\'\n[19:18:16] [INFO] testing \'MySQL >= 5.0.12 stacked queries\'\n[19:18:16] [INFO] testing \'MySQL >= 5.0.12 stacked queries (query SLEEP - comment)\'\n[19:18:16] [INFO] testing \'MySQL >= 5.0.12 stacked queries (query SLEEP)\'\n[19:18:16] [INFO] testing \'MySQL < 5.0.12 stacked queries (BENCHMARK - comment)\'\n[19:18:16] [INFO] testing \'MySQL < 5.0.12 stacked queries (BENCHMARK)\'\n[19:18:16] [INFO] testing \'PostgreSQL > 8.1 stacked queries (comment)\'\n[19:18:16] [INFO] testing \'PostgreSQL > 8.1 stacked queries\'\n[19:18:17] [INFO] testing \'PostgreSQL stacked queries (heavy query - comment)\'\n[19:18:17] [INFO] testing \'PostgreSQL stacked queries (heavy query)\'\n[19:18:17] [INFO] testing \'PostgreSQL < 8.2 stacked queries (Glibc - comment)\'\n[19:18:17] [INFO] testing \'PostgreSQL < 8.2 stacked queries (Glibc)\'\n[19:18:17] [INFO] testing \'Microsoft SQL Server/Sybase stacked queries (comment)\'\n[19:18:17] [INFO] testing \'Microsoft SQL Server/Sybase stacked queries (DECLARE - comment)\'\n[19:18:17] [INFO] testing \'Microsoft SQL Server/Sybase stacked queries\'\n[19:18:17] [INFO] testing \'Microsoft SQL Server/Sybase stacked queries (DECLARE)\'\n[19:18:18] [INFO] testing \'Oracle stacked queries (DBMS_PIPE.RECEIVE_MESSAGE - comment)\'\n[19:18:18] [INFO] testing \'Oracle stacked queries (DBMS_PIPE.RECEIVE_MESSAGE)\'\n[19:18:18] [INFO] testing \'Oracle stacked queries (heavy query - comment)\'\n[19:18:18] [INFO] testing \'Oracle stacked queries (heavy query)\'\n[19:18:18] [INFO] testing \'Oracle stacked queries (DBMS_LOCK.SLEEP - comment)\'\n[19:18:18] [INFO] testing \'Oracle stacked queries (DBMS_LOCK.SLEEP)\'\n[19:18:18] [INFO] testing \'Oracle stacked queries (USER_LOCK.SLEEP - comment)\'\n[19:18:18] [INFO] testing \'Oracle stacked queries (USER_LOCK.SLEEP)\'\n[19:18:18] [INFO] testing \'IBM DB2 stacked queries (heavy query - comment)\'\n[19:18:18] [INFO] testing \'IBM DB2 stacked queries (heavy query)\'\n[19:18:19] [INFO] testing \'SQLite > 2.0 stacked queries (heavy query - comment)\'\n[19:18:19] [INFO] testing \'SQLite > 2.0 stacked queries (heavy query)\'\n[19:18:19] [INFO] testing \'Firebird stacked queries (heavy query - comment)\'\n[19:18:19] [INFO] testing \'Firebird stacked queries (heavy query)\'\n[19:18:19] [INFO] testing \'SAP MaxDB stacked queries (heavy query - comment)\'\n[19:18:19] [INFO] testing \'SAP MaxDB stacked queries (heavy query)\'\n[19:18:19] [INFO] testing \'HSQLDB >= 1.7.2 stacked queries (heavy query - comment)\'\n[19:18:19] [INFO] testing \'HSQLDB >= 1.7.2 stacked queries (heavy query)\'\n[19:18:20] [INFO] testing \'HSQLDB >= 2.0 stacked queries (heavy query - comment)\'\n[19:18:20] [INFO] testing \'HSQLDB >= 2.0 stacked queries (heavy query)\'\n[19:18:20] [INFO] testing \'MySQL >= 5.0.12 AND time-based blind (query SLEEP)\'\n[19:18:20] [INFO] testing \'MySQL >= 5.0.12 OR time-based blind (query SLEEP)\'\n[19:18:20] [INFO] testing \'MySQL >= 5.0.12 AND time-based blind (SLEEP)\'\n[19:18:20] [INFO] testing \'MySQL >= 5.0.12 OR time-based blind (SLEEP)\'\n[19:18:21] [INFO] testing \'MySQL >= 5.0.12 AND time-based blind (SLEEP - comment)\'\n[19:18:21] [INFO] testing \'MySQL >= 5.0.12 OR time-based blind (SLEEP - comment)\'\n[19:18:21] [INFO] testing \'MySQL >= 5.0.12 AND time-based blind (query SLEEP - comment)\'\n[19:18:21] [INFO] testing \'MySQL >= 5.0.12 OR time-based blind (query SLEEP - comment)\'\n[19:18:21] [INFO] testing \'MySQL < 5.0.12 AND time-based blind (BENCHMARK)\'\n[19:18:21] [INFO] testing \'MySQL > 5.0.12 AND time-based blind (heavy query)\'\n[19:18:22] [INFO] testing \'MySQL < 5.0.12 OR time-based blind (BENCHMARK)\'\n[19:18:22] [INFO] testing \'MySQL > 5.0.12 OR time-based blind (heavy query)\'\n[19:18:22] [INFO] testing \'MySQL < 5.0.12 AND time-based blind (BENCHMARK - comment)\'\n[19:18:22] [INFO] testing \'MySQL > 5.0.12 AND time-based blind (heavy query - comment)\'\n[19:18:22] [INFO] testing \'MySQL < 5.0.12 OR time-based blind (BENCHMARK - comment)\'\n[19:18:22] [INFO] testing \'MySQL > 5.0.12 OR time-based blind (heavy query - comment)\'\n[19:18:22] [INFO] testing \'MySQL >= 5.0.12 RLIKE time-based blind\'\n[19:18:23] [INFO] testing \'MySQL >= 5.0.12 RLIKE time-based blind (comment)\'\n[19:18:23] [INFO] testing \'MySQL >= 5.0.12 RLIKE time-based blind (query SLEEP)\'\n[19:18:23] [INFO] testing \'MySQL >= 5.0.12 RLIKE time-based blind (query SLEEP - comment)\'\n[19:18:23] [INFO] testing \'MySQL AND time-based blind (ELT)\'\n[19:18:23] [INFO] testing \'MySQL OR time-based blind (ELT)\'\n[19:18:23] [INFO] testing \'MySQL AND time-based blind (ELT - comment)\'\n[19:18:24] [INFO] testing \'MySQL OR time-based blind (ELT - comment)\'\n[19:18:24] [INFO] testing \'PostgreSQL > 8.1 AND time-based blind\'\n[19:18:24] [INFO] testing \'PostgreSQL > 8.1 OR time-based blind\'\n[19:18:24] [INFO] testing \'PostgreSQL > 8.1 AND time-based blind (comment)\'\n[19:18:24] [INFO] testing \'PostgreSQL > 8.1 OR time-based blind (comment)\'\n[19:18:24] [INFO] testing \'PostgreSQL AND time-based blind (heavy query)\'\n[19:18:25] [INFO] testing \'PostgreSQL OR time-based blind (heavy query)\'\n[19:18:25] [INFO] testing \'PostgreSQL AND time-based blind (heavy query - comment)\'\n[19:18:25] [INFO] testing \'PostgreSQL OR time-based blind (heavy query - comment)\'\n[19:18:25] [INFO] testing \'Microsoft SQL Server/Sybase time-based blind (IF)\'\n[19:18:25] [INFO] testing \'Microsoft SQL Server/Sybase time-based blind (IF - comment)\'\n[19:18:25] [INFO] testing \'Microsoft SQL Server/Sybase AND time-based blind (heavy query)\'\n[19:18:25] [INFO] testing \'Microsoft SQL Server/Sybase OR time-based blind (heavy query)\'\n[19:18:26] [INFO] testing \'Microsoft SQL Server/Sybase AND time-based blind (heavy query - comment)\'\n[19:18:26] [INFO] testing \'Microsoft SQL Server/Sybase OR time-based blind (heavy query - comment)\'\n[19:18:26] [INFO] testing \'Oracle AND time-based blind\'\n[19:18:26] [INFO] testing \'Oracle OR time-based blind\'\n[19:18:26] [INFO] testing \'Oracle AND time-based blind (comment)\'\n[19:18:26] [INFO] testing \'Oracle OR time-based blind (comment)\'\n[19:18:27] [INFO] testing \'Oracle AND time-based blind (heavy query)\'\n[19:18:27] [INFO] testing \'Oracle OR time-based blind (heavy query)\'\n[19:18:27] [INFO] testing \'Oracle AND time-based blind (heavy query - comment)\'\n[19:18:27] [INFO] testing \'Oracle OR time-based blind (heavy query - comment)\'\n[19:18:27] [INFO] testing \'IBM DB2 AND time-based blind (heavy query)\'\n[19:18:27] [INFO] testing \'IBM DB2 OR time-based blind (heavy query)\'\n[19:18:27] [INFO] testing \'IBM DB2 AND time-based blind (heavy query - comment)\'\n[19:18:28] [INFO] testing \'IBM DB2 OR time-based blind (heavy query - comment)\'\n[19:18:28] [INFO] testing \'SQLite > 2.0 AND time-based blind (heavy query)\'\n[19:18:28] [INFO] testing \'SQLite > 2.0 OR time-based blind (heavy query)\'\n[19:18:28] [INFO] testing \'SQLite > 2.0 AND time-based blind (heavy query - comment)\'\n[19:18:28] [INFO] testing \'SQLite > 2.0 OR time-based blind (heavy query - comment)\'\n[19:18:28] [INFO] testing \'Firebird >= 2.0 AND time-based blind (heavy query)\'\n[19:18:29] [INFO] testing \'Firebird >= 2.0 OR time-based blind (heavy query)\'\n[19:18:29] [INFO] testing \'Firebird >= 2.0 AND time-based blind (heavy query - comment)\'\n[19:18:29] [INFO] testing \'Firebird >= 2.0 OR time-based blind (heavy query - comment)\'\n[19:18:29] [INFO] testing \'SAP MaxDB AND time-based blind (heavy query)\'\n[19:18:29] [INFO] testing \'SAP MaxDB OR time-based blind (heavy query)\'\n[19:18:29] [INFO] testing \'SAP MaxDB AND time-based blind (heavy query - comment)\'\n[19:18:29] [INFO] testing \'SAP MaxDB OR time-based blind (heavy query - comment)\'\n[19:18:30] [INFO] testing \'HSQLDB >= 1.7.2 AND time-based blind (heavy query)\'\n[19:18:30] [INFO] testing \'HSQLDB >= 1.7.2 OR time-based blind (heavy query)\'\n[19:18:30] [INFO] testing \'HSQLDB >= 1.7.2 AND time-based blind (heavy query - comment)\'\n[19:18:30] [INFO] testing \'HSQLDB >= 1.7.2 OR time-based blind (heavy query - comment)\'\n[19:18:30] [INFO] testing \'HSQLDB > 2.0 AND time-based blind (heavy query)\'\n[19:18:30] [INFO] testing \'HSQLDB > 2.0 OR time-based blind (heavy query)\'\n[19:18:31] [INFO] testing \'HSQLDB > 2.0 AND time-based blind (heavy query - comment)\'\n[19:18:31] [INFO] testing \'HSQLDB > 2.0 OR time-based blind (heavy query - comment)\'\n[19:18:31] [INFO] testing \'Informix AND time-based blind (heavy query)\'\n[19:18:31] [INFO] testing \'Informix OR time-based blind (heavy query)\'\n[19:18:31] [INFO] testing \'Informix AND time-based blind (heavy query - comment)\'\n[19:18:31] [INFO] testing \'Informix OR time-based blind (heavy query - comment)\'\n[19:18:31] [INFO] testing \'ClickHouse AND time-based blind (heavy query)\'\n[19:18:32] [INFO] testing \'ClickHouse OR time-based blind (heavy query)\'\n[19:18:32] [INFO] testing \'MySQL >= 5.1 time-based blind (heavy query) - PROCEDURE ANALYSE (EXTRACTVALUE)\'\n[19:18:32] [INFO] testing \'MySQL >= 5.1 time-based blind (heavy query - comment) - PROCEDURE ANALYSE (EXTRACTVALUE)\'\n[19:18:32] [INFO] testing \'MySQL >= 5.0.12 time-based blind - Parameter replace\'\n[19:18:32] [INFO] testing \'MySQL >= 5.0.12 time-based blind - Parameter replace (substraction)\'\n[19:18:32] [INFO] testing \'MySQL < 5.0.12 time-based blind - Parameter replace (BENCHMARK)\'\n[19:18:32] [INFO] testing \'MySQL > 5.0.12 time-based blind - Parameter replace (heavy query - comment)\'\n[19:18:32] [INFO] testing \'MySQL time-based blind - Parameter replace (bool)\'\n[19:18:32] [INFO] testing \'MySQL time-based blind - Parameter replace (ELT)\'\n[19:18:32] [INFO] testing \'MySQL time-based blind - Parameter replace (MAKE_SET)\'\n[19:18:32] [INFO] testing \'PostgreSQL > 8.1 time-based blind - Parameter replace\'\n[19:18:32] [INFO] testing \'PostgreSQL time-based blind - Parameter replace (heavy query)\'\n[19:18:32] [INFO] testing \'Microsoft SQL Server/Sybase time-based blind - Parameter replace (heavy queries)\'\n[19:18:32] [INFO] testing \'Oracle time-based blind - Parameter replace (DBMS_LOCK.SLEEP)\'\n[19:18:32] [INFO] testing \'Oracle time-based blind - Parameter replace (DBMS_PIPE.RECEIVE_MESSAGE)\'\n[19:18:32] [INFO] testing \'Oracle time-based blind - Parameter replace (heavy queries)\'\n[19:18:32] [INFO] testing \'SQLite > 2.0 time-based blind - Parameter replace (heavy query)\'\n[19:18:32] [INFO] testing \'Firebird time-based blind - Parameter replace (heavy query)\'\n[19:18:32] [INFO] testing \'SAP MaxDB time-based blind - Parameter replace (heavy query)\'\n[19:18:32] [INFO] testing \'IBM DB2 time-based blind - Parameter replace (heavy query)\'\n[19:18:32] [INFO] testing \'HSQLDB >= 1.7.2 time-based blind - Parameter replace (heavy query)\'\n[19:18:32] [INFO] testing \'HSQLDB > 2.0 time-based blind - Parameter replace (heavy query)\'\n[19:18:32] [INFO] testing \'Informix time-based blind - Parameter replace (heavy query)\'\n[19:18:32] [INFO] testing \'MySQL >= 5.0.12 time-based blind - ORDER BY, GROUP BY clause\'\n[19:18:32] [INFO] testing \'MySQL < 5.0.12 time-based blind - ORDER BY, GROUP BY clause (BENCHMARK)\'\n[19:18:32] [INFO] testing \'PostgreSQL > 8.1 time-based blind - ORDER BY, GROUP BY clause\'\n[19:18:32] [INFO] testing \'PostgreSQL time-based blind - ORDER BY, GROUP BY clause (heavy query)\'\n[19:18:32] [INFO] testing \'Microsoft SQL Server/Sybase time-based blind - ORDER BY clause (heavy query)\'\n[19:18:32] [INFO] testing \'Oracle time-based blind - ORDER BY, GROUP BY clause (DBMS_LOCK.SLEEP)\'\n[19:18:32] [INFO] testing \'Oracle time-based blind - ORDER BY, GROUP BY clause (DBMS_PIPE.RECEIVE_MESSAGE)\'\n[19:18:32] [INFO] testing \'Oracle time-based blind - ORDER BY, GROUP BY clause (heavy query)\'\n[19:18:32] [INFO] testing \'HSQLDB >= 1.7.2 time-based blind - ORDER BY, GROUP BY clause (heavy query)\'\n[19:18:32] [INFO] testing \'HSQLDB > 2.0 time-based blind - ORDER BY, GROUP BY clause (heavy query)\'\nit is recommended to perform only basic UNION tests if there is not at least one other (potential) technique found. Do you want to reduce the number of requests? [Y/n] Y\n[19:18:32] [INFO] testing \'Generic UNION query (NULL) - 1 to 10 columns\'\n[19:18:32] [INFO] testing \'Generic UNION query (random number) - 1 to 10 columns\'\n[19:18:33] [INFO] testing \'MySQL UNION query (NULL) - 1 to 10 columns\'\n[19:18:33] [INFO] testing \'MySQL UNION query (random number) - 1 to 10 columns\'\n[19:18:33] [WARNING] parameter \'User-Agent\' does not seem to be injectable\n[19:18:33] [INFO] testing if parameter \'Referer\' is dynamic\n[19:18:33] [WARNING] parameter \'Referer\' does not appear to be dynamic\n[19:18:33] [WARNING] heuristic (basic) test shows that parameter \'Referer\' might not be injectable\n[19:18:33] [INFO] testing for SQL injection on parameter \'Referer\'\n[19:18:33] [INFO] testing \'AND boolean-based blind - WHERE or HAVING clause\'\n[19:18:34] [INFO] testing \'OR boolean-based blind - WHERE or HAVING clause\'\n[19:18:34] [INFO] testing \'OR boolean-based blind - WHERE or HAVING clause (NOT)\'\n[19:18:35] [INFO] testing \'AND boolean-based blind - WHERE or HAVING clause (subquery - comment)\'\n[19:18:35] [INFO] testing \'OR boolean-based blind - WHERE or HAVING clause (subquery - comment)\'\n[19:18:35] [INFO] testing \'AND boolean-based blind - WHERE or HAVING clause (comment)\'\n[19:18:35] [INFO] testing \'OR boolean-based blind - WHERE or HAVING clause (comment)\'\n[19:18:35] [INFO] testing \'OR boolean-based blind - WHERE or HAVING clause (NOT - comment)\'\n[19:18:35] [INFO] testing \'AND boolean-based blind - WHERE or HAVING clause (MySQL comment)\'\n[19:18:35] [INFO] testing \'OR boolean-based blind - WHERE or HAVING clause (MySQL comment)\'\n[19:18:36] [INFO] testing \'OR boolean-based blind - WHERE or HAVING clause (NOT - MySQL comment)\'\n[19:18:36] [INFO] testing \'AND boolean-based blind - WHERE or HAVING clause (Microsoft Access comment)\'\n[19:18:36] [INFO] testing \'OR boolean-based blind - WHERE or HAVING clause (Microsoft Access comment)\'\n[19:18:36] [INFO] testing \'MySQL RLIKE boolean-based blind - WHERE, HAVING, ORDER BY or GROUP BY clause\'\n[19:18:36] [INFO] testing \'MySQL AND boolean-based blind - WHERE, HAVING, ORDER BY or GROUP BY clause (MAKE_SET)\'\n[19:18:37] [INFO] testing \'MySQL OR boolean-based blind - WHERE, HAVING, ORDER BY or GROUP BY clause (MAKE_SET)\'\n[19:18:37] [INFO] testing \'MySQL AND boolean-based blind - WHERE, HAVING, ORDER BY or GROUP BY clause (ELT)\'\n[19:18:37] [INFO] testing \'MySQL OR boolean-based blind - WHERE, HAVING, ORDER BY or GROUP BY clause (ELT)\'\n[19:18:38] [INFO] testing \'MySQL AND boolean-based blind - WHERE, HAVING, ORDER BY or GROUP BY clause (EXTRACTVALUE)\'\n[19:18:38] [INFO] testing \'MySQL OR boolean-based blind - WHERE, HAVING, ORDER BY or GROUP BY clause (EXTRACTVALUE)\'\n[19:18:38] [INFO] testing \'PostgreSQL AND boolean-based blind - WHERE or HAVING clause (CAST)\'\n[19:18:38] [INFO] testing \'PostgreSQL OR boolean-based blind - WHERE or HAVING clause (CAST)\'\n[19:18:39] [INFO] testing \'Oracle AND boolean-based blind - WHERE or HAVING clause (CTXSYS.DRITHSX.SN)\'\n[19:18:39] [INFO] testing \'Oracle OR boolean-based blind - WHERE or HAVING clause (CTXSYS.DRITHSX.SN)\'\n[19:18:39] [INFO] testing \'SQLite AND boolean-based blind - WHERE, HAVING, GROUP BY or HAVING clause (JSON)\'\n[19:18:40] [INFO] testing \'SQLite OR boolean-based blind - WHERE, HAVING, GROUP BY or HAVING clause (JSON)\'\n[19:18:40] [INFO] testing \'Boolean-based blind - Parameter replace (original value)\'\n[19:18:40] [INFO] testing \'MySQL boolean-based blind - Parameter replace (MAKE_SET)\'\n[19:18:40] [INFO] testing \'MySQL boolean-based blind - Parameter replace (MAKE_SET - original value)\'\n[19:18:40] [INFO] testing \'MySQL boolean-based blind - Parameter replace (ELT)\'\n[19:18:40] [INFO] testing \'MySQL boolean-based blind - Parameter replace (ELT - original value)\'\n[19:18:40] [INFO] testing \'MySQL boolean-based blind - Parameter replace (bool*int)\'\n[19:18:40] [INFO] testing \'MySQL boolean-based blind - Parameter replace (bool*int - original value)\'\n[19:18:40] [INFO] testing \'PostgreSQL boolean-based blind - Parameter replace\'\n[19:18:40] [INFO] testing \'PostgreSQL boolean-based blind - Parameter replace (original value)\'\n[19:18:40] [INFO] testing \'PostgreSQL boolean-based blind - Parameter replace (GENERATE_SERIES)\'\n[19:18:40] [INFO] testing \'PostgreSQL boolean-based blind - Parameter replace (GENERATE_SERIES - original value)\'\n[19:18:40] [INFO] testing \'Microsoft SQL Server/Sybase boolean-based blind - Parameter replace\'\n[19:18:40] [INFO] testing \'Microsoft SQL Server/Sybase boolean-based blind - Parameter replace (original value)\'\n[19:18:40] [INFO] testing \'Oracle boolean-based blind - Parameter replace\'\n[19:18:40] [INFO] testing \'Oracle boolean-based blind - Parameter replace (original value)\'\n[19:18:40] [INFO] testing \'Informix boolean-based blind - Parameter replace\'\n[19:18:40] [INFO] testing \'Informix boolean-based blind - Parameter replace (original value)\'\n[19:18:40] [INFO] testing \'Microsoft Access boolean-based blind - Parameter replace\'\n[19:18:40] [INFO] testing \'Microsoft Access boolean-based blind - Parameter replace (original value)\'\n[19:18:40] [INFO] testing \'Boolean-based blind - Parameter replace (DUAL)\'\n[19:18:40] [INFO] testing \'Boolean-based blind - Parameter replace (DUAL - original value)\'\n[19:18:40] [INFO] testing \'Boolean-based blind - Parameter replace (CASE)\'\n[19:18:40] [INFO] testing \'Boolean-based blind - Parameter replace (CASE - original value)\'\n[19:18:40] [INFO] testing \'MySQL >= 5.0 boolean-based blind - ORDER BY, GROUP BY clause\'\n[19:18:40] [INFO] testing \'MySQL >= 5.0 boolean-based blind - ORDER BY, GROUP BY clause (original value)\'\n[19:18:40] [INFO] testing \'MySQL < 5.0 boolean-based blind - ORDER BY, GROUP BY clause\'\n[19:18:40] [INFO] testing \'MySQL < 5.0 boolean-based blind - ORDER BY, GROUP BY clause (original value)\'\n[19:18:40] [INFO] testing \'PostgreSQL boolean-based blind - ORDER BY, GROUP BY clause\'\n[19:18:40] [INFO] testing \'PostgreSQL boolean-based blind - ORDER BY clause (original value)\'\n[19:18:40] [INFO] testing \'PostgreSQL boolean-based blind - ORDER BY clause (GENERATE_SERIES)\'\n[19:18:40] [INFO] testing \'Microsoft SQL Server/Sybase boolean-based blind - ORDER BY clause\'\n[19:18:40] [INFO] testing \'Microsoft SQL Server/Sybase boolean-based blind - ORDER BY clause (original value)\'\n[19:18:40] [INFO] testing \'Oracle boolean-based blind - ORDER BY, GROUP BY clause\'\n[19:18:40] [INFO] testing \'Oracle boolean-based blind - ORDER BY, GROUP BY clause (original value)\'\n[19:18:40] [INFO] testing \'Microsoft Access boolean-based blind - ORDER BY, GROUP BY clause\'\n[19:18:40] [INFO] testing \'Microsoft Access boolean-based blind - ORDER BY, GROUP BY clause (original value)\'\n[19:18:40] [INFO] testing \'SAP MaxDB boolean-based blind - ORDER BY, GROUP BY clause\'\n[19:18:40] [INFO] testing \'SAP MaxDB boolean-based blind - ORDER BY, GROUP BY clause (original value)\'\n[19:18:40] [INFO] testing \'IBM DB2 boolean-based blind - ORDER BY clause\'\n[19:18:40] [INFO] testing \'IBM DB2 boolean-based blind - ORDER BY clause (original value)\'\n[19:18:40] [INFO] testing \'HAVING boolean-based blind - WHERE, GROUP BY clause\'\n[19:18:41] [INFO] testing \'MySQL >= 5.0 boolean-based blind - Stacked queries\'\n[19:18:41] [INFO] testing \'MySQL < 5.0 boolean-based blind - Stacked queries\'\n[19:18:41] [INFO] testing \'PostgreSQL boolean-based blind - Stacked queries\'\n[19:18:41] [INFO] testing \'PostgreSQL boolean-based blind - Stacked queries (GENERATE_SERIES)\'\n[19:18:41] [INFO] testing \'Microsoft SQL Server/Sybase boolean-based blind - Stacked queries (IF)\'\n[19:18:42] [INFO] testing \'Microsoft SQL Server/Sybase boolean-based blind - Stacked queries\'\n[19:18:42] [INFO] testing \'Oracle boolean-based blind - Stacked queries\'\n[19:18:42] [INFO] testing \'Microsoft Access boolean-based blind - Stacked queries\'\n[19:18:42] [INFO] testing \'SAP MaxDB boolean-based blind - Stacked queries\'\n[19:18:42] [INFO] testing \'MySQL >= 5.5 AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (BIGINT UNSIGNED)\'\n[19:18:43] [INFO] testing \'MySQL >= 5.5 OR error-based - WHERE or HAVING clause (BIGINT UNSIGNED)\'\n[19:18:43] [INFO] testing \'MySQL >= 5.5 AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (EXP)\'\n[19:18:43] [INFO] testing \'MySQL >= 5.5 OR error-based - WHERE or HAVING clause (EXP)\'\n[19:18:43] [INFO] testing \'MySQL >= 5.6 AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (GTID_SUBSET)\'\n[19:18:44] [INFO] testing \'MySQL >= 5.6 OR error-based - WHERE or HAVING clause (GTID_SUBSET)\'\n[19:18:44] [INFO] testing \'MySQL >= 5.7.8 AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (JSON_KEYS)\'\n[19:18:44] [INFO] testing \'MySQL >= 5.7.8 OR error-based - WHERE or HAVING clause (JSON_KEYS)\'\n[19:18:44] [INFO] testing \'MySQL >= 5.0 AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (FLOOR)\'\n[19:18:44] [INFO] testing \'MySQL >= 5.0 OR error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (FLOOR)\'\n[19:18:45] [INFO] testing \'MySQL >= 5.1 AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (EXTRACTVALUE)\'\n[19:18:45] [INFO] testing \'MySQL >= 5.1 OR error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (EXTRACTVALUE)\'\n[19:18:45] [INFO] testing \'MySQL >= 5.1 AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (UPDATEXML)\'\n[19:18:45] [INFO] testing \'MySQL >= 5.1 OR error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (UPDATEXML)\'\n[19:18:46] [INFO] testing \'MySQL >= 4.1 AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (FLOOR)\'\n[19:18:46] [INFO] testing \'MySQL >= 4.1 OR error-based - WHERE or HAVING clause (FLOOR)\'\n[19:18:46] [INFO] testing \'MySQL OR error-based - WHERE or HAVING clause (FLOOR)\'\n[19:18:46] [INFO] testing \'PostgreSQL AND error-based - WHERE or HAVING clause\'\n[19:18:46] [INFO] testing \'PostgreSQL OR error-based - WHERE or HAVING clause\'\n[19:18:46] [INFO] testing \'Microsoft SQL Server/Sybase AND error-based - WHERE or HAVING clause (IN)\'\n[19:18:47] [INFO] testing \'Microsoft SQL Server/Sybase OR error-based - WHERE or HAVING clause (IN)\'\n[19:18:47] [INFO] testing \'Microsoft SQL Server/Sybase AND error-based - WHERE or HAVING clause (CONVERT)\'\n[19:18:47] [INFO] testing \'Microsoft SQL Server/Sybase OR error-based - WHERE or HAVING clause (CONVERT)\'\n[19:18:47] [INFO] testing \'Microsoft SQL Server/Sybase AND error-based - WHERE or HAVING clause (CONCAT)\'\n[19:18:47] [INFO] testing \'Microsoft SQL Server/Sybase OR error-based - WHERE or HAVING clause (CONCAT)\'\n[19:18:48] [INFO] testing \'Oracle AND error-based - WHERE or HAVING clause (XMLType)\'\n[19:18:48] [INFO] testing \'Oracle OR error-based - WHERE or HAVING clause (XMLType)\'\n[19:18:48] [INFO] testing \'Oracle AND error-based - WHERE or HAVING clause (UTL_INADDR.GET_HOST_ADDRESS)\'\n[19:18:48] [INFO] testing \'Oracle OR error-based - WHERE or HAVING clause (UTL_INADDR.GET_HOST_ADDRESS)\'\n[19:18:48] [INFO] testing \'Oracle AND error-based - WHERE or HAVING clause (CTXSYS.DRITHSX.SN)\'\n[19:18:49] [INFO] testing \'Oracle OR error-based - WHERE or HAVING clause (CTXSYS.DRITHSX.SN)\'\n[19:18:49] [INFO] testing \'Oracle AND error-based - WHERE or HAVING clause (DBMS_UTILITY.SQLID_TO_SQLHASH)\'\n[19:18:49] [INFO] testing \'Oracle OR error-based - WHERE or HAVING clause (DBMS_UTILITY.SQLID_TO_SQLHASH)\'\n[19:18:49] [INFO] testing \'Firebird AND error-based - WHERE or HAVING clause\'\n[19:18:49] [INFO] testing \'Firebird OR error-based - WHERE or HAVING clause\'\n[19:18:49] [INFO] testing \'MonetDB AND error-based - WHERE or HAVING clause\'\n[19:18:50] [INFO] testing \'MonetDB OR error-based - WHERE or HAVING clause\'\n[19:18:50] [INFO] testing \'Vertica AND error-based - WHERE or HAVING clause\'\n[19:18:50] [INFO] testing \'Vertica OR error-based - WHERE or HAVING clause\'\n[19:18:50] [INFO] testing \'IBM DB2 AND error-based - WHERE or HAVING clause\'\n[19:18:50] [INFO] testing \'IBM DB2 OR error-based - WHERE or HAVING clause\'\n[19:18:50] [INFO] testing \'ClickHouse AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause\'\n[19:18:50] [INFO] testing \'ClickHouse OR error-based - WHERE, HAVING, ORDER BY or GROUP BY clause\'\n[19:18:51] [INFO] testing \'MySQL >= 5.1 error-based - PROCEDURE ANALYSE (EXTRACTVALUE)\'\n[19:18:51] [INFO] testing \'MySQL >= 5.5 error-based - Parameter replace (BIGINT UNSIGNED)\'\n[19:18:51] [INFO] testing \'MySQL >= 5.5 error-based - Parameter replace (EXP)\'\n[19:18:51] [INFO] testing \'MySQL >= 5.6 error-based - Parameter replace (GTID_SUBSET)\'\n[19:18:51] [INFO] testing \'MySQL >= 5.7.8 error-based - Parameter replace (JSON_KEYS)\'\n[19:18:51] [INFO] testing \'MySQL >= 5.0 error-based - Parameter replace (FLOOR)\'\n[19:18:51] [INFO] testing \'MySQL >= 5.1 error-based - Parameter replace (UPDATEXML)\'\n[19:18:51] [INFO] testing \'MySQL >= 5.1 error-based - Parameter replace (EXTRACTVALUE)\'\n[19:18:51] [INFO] testing \'PostgreSQL error-based - Parameter replace\'\n[19:18:51] [INFO] testing \'PostgreSQL error-based - Parameter replace (GENERATE_SERIES)\'\n[19:18:51] [INFO] testing \'Microsoft SQL Server/Sybase error-based - Parameter replace\'\n[19:18:51] [INFO] testing \'Microsoft SQL Server/Sybase error-based - Parameter replace (integer column)\'\n[19:18:51] [INFO] testing \'Oracle error-based - Parameter replace\'\n[19:18:51] [INFO] testing \'Firebird error-based - Parameter replace\'\n[19:18:51] [INFO] testing \'IBM DB2 error-based - Parameter replace\'\n[19:18:51] [INFO] testing \'MySQL >= 5.5 error-based - ORDER BY, GROUP BY clause (BIGINT UNSIGNED)\'\n[19:18:51] [INFO] testing \'MySQL >= 5.5 error-based - ORDER BY, GROUP BY clause (EXP)\'\n[19:18:51] [INFO] testing \'MySQL >= 5.6 error-based - ORDER BY, GROUP BY clause (GTID_SUBSET)\'\n[19:18:51] [INFO] testing \'MySQL >= 5.7.8 error-based - ORDER BY, GROUP BY clause (JSON_KEYS)\'\n[19:18:51] [INFO] testing \'MySQL >= 5.0 error-based - ORDER BY, GROUP BY clause (FLOOR)\'\n[19:18:51] [INFO] testing \'MySQL >= 5.1 error-based - ORDER BY, GROUP BY clause (EXTRACTVALUE)\'\n[19:18:51] [INFO] testing \'MySQL >= 5.1 error-based - ORDER BY, GROUP BY clause (UPDATEXML)\'\n[19:18:51] [INFO] testing \'MySQL >= 4.1 error-based - ORDER BY, GROUP BY clause (FLOOR)\'\n[19:18:51] [INFO] testing \'PostgreSQL error-based - ORDER BY, GROUP BY clause\'\n[19:18:51] [INFO] testing \'PostgreSQL error-based - ORDER BY, GROUP BY clause (GENERATE_SERIES)\'\n[19:18:51] [INFO] testing \'Microsoft SQL Server/Sybase error-based - ORDER BY clause\'\n[19:18:51] [INFO] testing \'Oracle error-based - ORDER BY, GROUP BY clause\'\n[19:18:51] [INFO] testing \'Firebird error-based - ORDER BY clause\'\n[19:18:51] [INFO] testing \'IBM DB2 error-based - ORDER BY clause\'\n[19:18:51] [INFO] testing \'Microsoft SQL Server/Sybase error-based - Stacking (EXEC)\'\n[19:18:51] [INFO] testing \'Generic inline queries\'\n[19:18:51] [INFO] testing \'MySQL inline queries\'\n[19:18:51] [INFO] testing \'PostgreSQL inline queries\'\n[19:18:51] [INFO] testing \'Microsoft SQL Server/Sybase inline queries\'\n[19:18:51] [INFO] testing \'Oracle inline queries\'\n[19:18:51] [INFO] testing \'SQLite inline queries\'\n[19:18:51] [INFO] testing \'Firebird inline queries\'\n[19:18:51] [INFO] testing \'ClickHouse inline queries\'\n[19:18:51] [INFO] testing \'MySQL >= 5.0.12 stacked queries (comment)\'\n[19:18:51] [INFO] testing \'MySQL >= 5.0.12 stacked queries\'\n[19:18:51] [INFO] testing \'MySQL >= 5.0.12 stacked queries (query SLEEP - comment)\'\n[19:18:51] [INFO] testing \'MySQL >= 5.0.12 stacked queries (query SLEEP)\'\n[19:18:51] [INFO] testing \'MySQL < 5.0.12 stacked queries (BENCHMARK - comment)\'\n[19:18:51] [INFO] testing \'MySQL < 5.0.12 stacked queries (BENCHMARK)\'\n[19:18:52] [INFO] testing \'PostgreSQL > 8.1 stacked queries (comment)\'\n[19:18:52] [INFO] testing \'PostgreSQL > 8.1 stacked queries\'\n[19:18:52] [INFO] testing \'PostgreSQL stacked queries (heavy query - comment)\'\n[19:18:52] [INFO] testing \'PostgreSQL stacked queries (heavy query)\'\n[19:18:52] [INFO] testing \'PostgreSQL < 8.2 stacked queries (Glibc - comment)\'\n[19:18:52] [INFO] testing \'PostgreSQL < 8.2 stacked queries (Glibc)\'\n[19:18:52] [INFO] testing \'Microsoft SQL Server/Sybase stacked queries (comment)\'\n[19:18:52] [INFO] testing \'Microsoft SQL Server/Sybase stacked queries (DECLARE - comment)\'\n[19:18:52] [INFO] testing \'Microsoft SQL Server/Sybase stacked queries\'\n[19:18:52] [INFO] testing \'Microsoft SQL Server/Sybase stacked queries (DECLARE)\'\n[19:18:52] [INFO] testing \'Oracle stacked queries (DBMS_PIPE.RECEIVE_MESSAGE - comment)\'\n[19:18:53] [INFO] testing \'Oracle stacked queries (DBMS_PIPE.RECEIVE_MESSAGE)\'\n[19:18:53] [INFO] testing \'Oracle stacked queries (heavy query - comment)\'\n[19:18:53] [INFO] testing \'Oracle stacked queries (heavy query)\'\n[19:18:53] [INFO] testing \'Oracle stacked queries (DBMS_LOCK.SLEEP - comment)\'\n[19:18:53] [INFO] testing \'Oracle stacked queries (DBMS_LOCK.SLEEP)\'\n[19:18:53] [INFO] testing \'Oracle stacked queries (USER_LOCK.SLEEP - comment)\'\n[19:18:53] [INFO] testing \'Oracle stacked queries (USER_LOCK.SLEEP)\'\n[19:18:53] [INFO] testing \'IBM DB2 stacked queries (heavy query - comment)\'\n[19:18:53] [INFO] testing \'IBM DB2 stacked queries (heavy query)\'\n[19:18:53] [INFO] testing \'SQLite > 2.0 stacked queries (heavy query - comment)\'\n[19:18:53] [INFO] testing \'SQLite > 2.0 stacked queries (heavy query)\'\n[19:18:54] [INFO] testing \'Firebird stacked queries (heavy query - comment)\'\n[19:18:54] [INFO] testing \'Firebird stacked queries (heavy query)\'\n[19:18:54] [INFO] testing \'SAP MaxDB stacked queries (heavy query - comment)\'\n[19:18:54] [INFO] testing \'SAP MaxDB stacked queries (heavy query)\'\n[19:18:54] [INFO] testing \'HSQLDB >= 1.7.2 stacked queries (heavy query - comment)\'\n[19:18:54] [INFO] testing \'HSQLDB >= 1.7.2 stacked queries (heavy query)\'\n[19:18:54] [INFO] testing \'HSQLDB >= 2.0 stacked queries (heavy query - comment)\'\n[19:18:54] [INFO] testing \'HSQLDB >= 2.0 stacked queries (heavy query)\'\n[19:18:54] [INFO] testing \'MySQL >= 5.0.12 AND time-based blind (query SLEEP)\'\n[19:18:55] [INFO] testing \'MySQL >= 5.0.12 OR time-based blind (query SLEEP)\'\n[19:18:55] [INFO] testing \'MySQL >= 5.0.12 AND time-based blind (SLEEP)\'\n[19:18:55] [INFO] testing \'MySQL >= 5.0.12 OR time-based blind (SLEEP)\'\n[19:18:55] [INFO] testing \'MySQL >= 5.0.12 AND time-based blind (SLEEP - comment)\'\n[19:18:55] [INFO] testing \'MySQL >= 5.0.12 OR time-based blind (SLEEP - comment)\'\n[19:18:55] [INFO] testing \'MySQL >= 5.0.12 AND time-based blind (query SLEEP - comment)\'\n[19:18:55] [INFO] testing \'MySQL >= 5.0.12 OR time-based blind (query SLEEP - comment)\'\n[19:18:55] [INFO] testing \'MySQL < 5.0.12 AND time-based blind (BENCHMARK)\'\n[19:18:56] [INFO] testing \'MySQL > 5.0.12 AND time-based blind (heavy query)\'\n[19:18:56] [INFO] testing \'MySQL < 5.0.12 OR time-based blind (BENCHMARK)\'\n[19:18:56] [INFO] testing \'MySQL > 5.0.12 OR time-based blind (heavy query)\'\n[19:18:56] [INFO] testing \'MySQL < 5.0.12 AND time-based blind (BENCHMARK - comment)\'\n[19:18:56] [INFO] testing \'MySQL > 5.0.12 AND time-based blind (heavy query - comment)\'\n[19:18:56] [INFO] testing \'MySQL < 5.0.12 OR time-based blind (BENCHMARK - comment)\'\n[19:18:56] [INFO] testing \'MySQL > 5.0.12 OR time-based blind (heavy query - comment)\'\n[19:18:57] [INFO] testing \'MySQL >= 5.0.12 RLIKE time-based blind\'\n[19:18:57] [INFO] testing \'MySQL >= 5.0.12 RLIKE time-based blind (comment)\'\n[19:18:57] [INFO] testing \'MySQL >= 5.0.12 RLIKE time-based blind (query SLEEP)\'\n[19:18:57] [INFO] testing \'MySQL >= 5.0.12 RLIKE time-based blind (query SLEEP - comment)\'\n[19:18:57] [INFO] testing \'MySQL AND time-based blind (ELT)\'\n[19:18:57] [INFO] testing \'MySQL OR time-based blind (ELT)\'\n[19:18:58] [INFO] testing \'MySQL AND time-based blind (ELT - comment)\'\n[19:18:58] [INFO] testing \'MySQL OR time-based blind (ELT - comment)\'\n[19:18:58] [INFO] testing \'PostgreSQL > 8.1 AND time-based blind\'\n[19:18:58] [INFO] testing \'PostgreSQL > 8.1 OR time-based blind\'\n[19:18:58] [INFO] testing \'PostgreSQL > 8.1 AND time-based blind (comment)\'\n[19:18:58] [INFO] testing \'PostgreSQL > 8.1 OR time-based blind (comment)\'\n[19:18:58] [INFO] testing \'PostgreSQL AND time-based blind (heavy query)\'\n[19:18:59] [INFO] testing \'PostgreSQL OR time-based blind (heavy query)\'\n[19:18:59] [INFO] testing \'PostgreSQL AND time-based blind (heavy query - comment)\'\n[19:18:59] [INFO] testing \'PostgreSQL OR time-based blind (heavy query - comment)\'\n[19:18:59] [INFO] testing \'Microsoft SQL Server/Sybase time-based blind (IF)\'\n[19:18:59] [INFO] testing \'Microsoft SQL Server/Sybase time-based blind (IF - comment)\'\n[19:18:59] [INFO] testing \'Microsoft SQL Server/Sybase AND time-based blind (heavy query)\'\n[19:18:59] [INFO] testing \'Microsoft SQL Server/Sybase OR time-based blind (heavy query)\'\n[19:18:59] [INFO] testing \'Microsoft SQL Server/Sybase AND time-based blind (heavy query - comment)\'\n[19:19:00] [INFO] testing \'Microsoft SQL Server/Sybase OR time-based blind (heavy query - comment)\'\n[19:19:00] [INFO] testing \'Oracle AND time-based blind\'\n[19:19:00] [INFO] testing \'Oracle OR time-based blind\'\n[19:19:00] [INFO] testing \'Oracle AND time-based blind (comment)\'\n[19:19:00] [INFO] testing \'Oracle OR time-based blind (comment)\'\n[19:19:00] [INFO] testing \'Oracle AND time-based blind (heavy query)\'\n[19:19:00] [INFO] testing \'Oracle OR time-based blind (heavy query)\'\n[19:19:01] [INFO] testing \'Oracle AND time-based blind (heavy query - comment)\'\n[19:19:01] [INFO] testing \'Oracle OR time-based blind (heavy query - comment)\'\n[19:19:01] [INFO] testing \'IBM DB2 AND time-based blind (heavy query)\'\n[19:19:01] [INFO] testing \'IBM DB2 OR time-based blind (heavy query)\'\n[19:19:01] [INFO] testing \'IBM DB2 AND time-based blind (heavy query - comment)\'\n[19:19:01] [INFO] testing \'IBM DB2 OR time-based blind (heavy query - comment)\'\n[19:19:01] [INFO] testing \'SQLite > 2.0 AND time-based blind (heavy query)\'\n[19:19:02] [INFO] testing \'SQLite > 2.0 OR time-based blind (heavy query)\'\n[19:19:02] [INFO] testing \'SQLite > 2.0 AND time-based blind (heavy query - comment)\'\n[19:19:02] [INFO] testing \'SQLite > 2.0 OR time-based blind (heavy query - comment)\'\n[19:19:02] [INFO] testing \'Firebird >= 2.0 AND time-based blind (heavy query)\'\n[19:19:02] [INFO] testing \'Firebird >= 2.0 OR time-based blind (heavy query)\'\n[19:19:02] [INFO] testing \'Firebird >= 2.0 AND time-based blind (heavy query - comment)\'\n[19:19:02] [INFO] testing \'Firebird >= 2.0 OR time-based blind (heavy query - comment)\'\n[19:19:03] [INFO] testing \'SAP MaxDB AND time-based blind (heavy query)\'\n[19:19:03] [INFO] testing \'SAP MaxDB OR time-based blind (heavy query)\'\n[19:19:03] [INFO] testing \'SAP MaxDB AND time-based blind (heavy query - comment)\'\n[19:19:03] [INFO] testing \'SAP MaxDB OR time-based blind (heavy query - comment)\'\n[19:19:03] [INFO] testing \'HSQLDB >= 1.7.2 AND time-based blind (heavy query)\'\n[19:19:03] [INFO] testing \'HSQLDB >= 1.7.2 OR time-based blind (heavy query)\'\n[19:19:04] [INFO] testing \'HSQLDB >= 1.7.2 AND time-based blind (heavy query - comment)\'\n[19:19:04] [INFO] testing \'HSQLDB >= 1.7.2 OR time-based blind (heavy query - comment)\'\n[19:19:04] [INFO] testing \'HSQLDB > 2.0 AND time-based blind (heavy query)\'\n[19:19:04] [INFO] testing \'HSQLDB > 2.0 OR time-based blind (heavy query)\'\n[19:19:04] [INFO] testing \'HSQLDB > 2.0 AND time-based blind (heavy query - comment)\'\n[19:19:04] [INFO] testing \'HSQLDB > 2.0 OR time-based blind (heavy query - comment)\'\n[19:19:04] [INFO] testing \'Informix AND time-based blind (heavy query)\'\n[19:19:05] [INFO] testing \'Informix OR time-based blind (heavy query)\'\n[19:19:05] [INFO] testing \'Informix AND time-based blind (heavy query - comment)\'\n[19:19:05] [INFO] testing \'Informix OR time-based blind (heavy query - comment)\'\n[19:19:05] [INFO] testing \'ClickHouse AND time-based blind (heavy query)\'\n[19:19:05] [INFO] testing \'ClickHouse OR time-based blind (heavy query)\'\n[19:19:05] [INFO] testing \'MySQL >= 5.1 time-based blind (heavy query) - PROCEDURE ANALYSE (EXTRACTVALUE)\'\n[19:19:05] [INFO] testing \'MySQL >= 5.1 time-based blind (heavy query - comment) - PROCEDURE ANALYSE (EXTRACTVALUE)\'\n[19:19:05] [INFO] testing \'MySQL >= 5.0.12 time-based blind - Parameter replace\'\n[19:19:05] [INFO] testing \'MySQL >= 5.0.12 time-based blind - Parameter replace (substraction)\'\n[19:19:05] [INFO] testing \'MySQL < 5.0.12 time-based blind - Parameter replace (BENCHMARK)\'\n[19:19:05] [INFO] testing \'MySQL > 5.0.12 time-based blind - Parameter replace (heavy query - comment)\'\n[19:19:05] [INFO] testing \'MySQL time-based blind - Parameter replace (bool)\'\n[19:19:05] [INFO] testing \'MySQL time-based blind - Parameter replace (ELT)\'\n[19:19:05] [INFO] testing \'MySQL time-based blind - Parameter replace (MAKE_SET)\'\n[19:19:05] [INFO] testing \'PostgreSQL > 8.1 time-based blind - Parameter replace\'\n[19:19:05] [INFO] testing \'PostgreSQL time-based blind - Parameter replace (heavy query)\'\n[19:19:05] [INFO] testing \'Microsoft SQL Server/Sybase time-based blind - Parameter replace (heavy queries)\'\n[19:19:05] [INFO] testing \'Oracle time-based blind - Parameter replace (DBMS_LOCK.SLEEP)\'\n[19:19:05] [INFO] testing \'Oracle time-based blind - Parameter replace (DBMS_PIPE.RECEIVE_MESSAGE)\'\n[19:19:05] [INFO] testing \'Oracle time-based blind - Parameter replace (heavy queries)\'\n[19:19:06] [INFO] testing \'SQLite > 2.0 time-based blind - Parameter replace (heavy query)\'\n[19:19:06] [INFO] testing \'Firebird time-based blind - Parameter replace (heavy query)\'\n[19:19:06] [INFO] testing \'SAP MaxDB time-based blind - Parameter replace (heavy query)\'\n[19:19:06] [INFO] testing \'IBM DB2 time-based blind - Parameter replace (heavy query)\'\n[19:19:06] [INFO] testing \'HSQLDB >= 1.7.2 time-based blind - Parameter replace (heavy query)\'\n[19:19:06] [INFO] testing \'HSQLDB > 2.0 time-based blind - Parameter replace (heavy query)\'\n[19:19:06] [INFO] testing \'Informix time-based blind - Parameter replace (heavy query)\'\n[19:19:06] [INFO] testing \'MySQL >= 5.0.12 time-based blind - ORDER BY, GROUP BY clause\'\n[19:19:06] [INFO] testing \'MySQL < 5.0.12 time-based blind - ORDER BY, GROUP BY clause (BENCHMARK)\'\n[19:19:06] [INFO] testing \'PostgreSQL > 8.1 time-based blind - ORDER BY, GROUP BY clause\'\n[19:19:06] [INFO] testing \'PostgreSQL time-based blind - ORDER BY, GROUP BY clause (heavy query)\'\n[19:19:06] [INFO] testing \'Microsoft SQL Server/Sybase time-based blind - ORDER BY clause (heavy query)\'\n[19:19:06] [INFO] testing \'Oracle time-based blind - ORDER BY, GROUP BY clause (DBMS_LOCK.SLEEP)\'\n[19:19:06] [INFO] testing \'Oracle time-based blind - ORDER BY, GROUP BY clause (DBMS_PIPE.RECEIVE_MESSAGE)\'\n[19:19:06] [INFO] testing \'Oracle time-based blind - ORDER BY, GROUP BY clause (heavy query)\'\n[19:19:06] [INFO] testing \'HSQLDB >= 1.7.2 time-based blind - ORDER BY, GROUP BY clause (heavy query)\'\n[19:19:06] [INFO] testing \'HSQLDB > 2.0 time-based blind - ORDER BY, GROUP BY clause (heavy query)\'\n[19:19:06] [INFO] testing \'Generic UNION query (NULL) - 1 to 10 columns\'\n[19:19:06] [INFO] testing \'Generic UNION query (random number) - 1 to 10 columns\'\n[19:19:06] [INFO] testing \'MySQL UNION query (NULL) - 1 to 10 columns\'\n[19:19:07] [INFO] testing \'MySQL UNION query (random number) - 1 to 10 columns\'\n[19:19:07] [WARNING] parameter \'Referer\' does not seem to be injectable\n[19:19:07] [INFO] testing if parameter \'Host\' is dynamic\n[19:19:07] [WARNING] parameter \'Host\' does not appear to be dynamic\n[19:19:07] [WARNING] heuristic (basic) test shows that parameter \'Host\' might not be injectable\n[19:19:07] [INFO] testing for SQL injection on parameter \'Host\'\n[19:19:07] [INFO] testing \'AND boolean-based blind - WHERE or HAVING clause\'\n[19:19:08] [INFO] testing \'OR boolean-based blind - WHERE or HAVING clause\'\n[19:19:08] [INFO] testing \'OR boolean-based blind - WHERE or HAVING clause (NOT)\'\n[19:19:08] [INFO] testing \'AND boolean-based blind - WHERE or HAVING clause (subquery - comment)\'\n[19:19:09] [INFO] testing \'OR boolean-based blind - WHERE or HAVING clause (subquery - comment)\'\n[19:19:09] [INFO] testing \'AND boolean-based blind - WHERE or HAVING clause (comment)\'\n[19:19:09] [INFO] testing \'OR boolean-based blind - WHERE or HAVING clause (comment)\'\n[19:19:09] [INFO] testing \'OR boolean-based blind - WHERE or HAVING clause (NOT - comment)\'\n[19:19:09] [INFO] testing \'AND boolean-based blind - WHERE or HAVING clause (MySQL comment)\'\n[19:19:09] [INFO] testing \'OR boolean-based blind - WHERE or HAVING clause (MySQL comment)\'\n[19:19:10] [INFO] testing \'OR boolean-based blind - WHERE or HAVING clause (NOT - MySQL comment)\'\n[19:19:10] [INFO] testing \'AND boolean-based blind - WHERE or HAVING clause (Microsoft Access comment)\'\n[19:19:10] [INFO] testing \'OR boolean-based blind - WHERE or HAVING clause (Microsoft Access comment)\'\n[19:19:10] [INFO] testing \'MySQL RLIKE boolean-based blind - WHERE, HAVING, ORDER BY or GROUP BY clause\'\n[19:19:10] [INFO] testing \'MySQL AND boolean-based blind - WHERE, HAVING, ORDER BY or GROUP BY clause (MAKE_SET)\'\n[19:19:11] [INFO] testing \'MySQL OR boolean-based blind - WHERE, HAVING, ORDER BY or GROUP BY clause (MAKE_SET)\'\n[19:19:11] [INFO] testing \'MySQL AND boolean-based blind - WHERE, HAVING, ORDER BY or GROUP BY clause (ELT)\'\n[19:19:11] [INFO] testing \'MySQL OR boolean-based blind - WHERE, HAVING, ORDER BY or GROUP BY clause (ELT)\'\n[19:19:12] [INFO] testing \'MySQL AND boolean-based blind - WHERE, HAVING, ORDER BY or GROUP BY clause (EXTRACTVALUE)\'\n[19:19:12] [INFO] testing \'MySQL OR boolean-based blind - WHERE, HAVING, ORDER BY or GROUP BY clause (EXTRACTVALUE)\'\n[19:19:12] [INFO] testing \'PostgreSQL AND boolean-based blind - WHERE or HAVING clause (CAST)\'\n[19:19:13] [INFO] testing \'PostgreSQL OR boolean-based blind - WHERE or HAVING clause (CAST)\'\n[19:19:13] [INFO] testing \'Oracle AND boolean-based blind - WHERE or HAVING clause (CTXSYS.DRITHSX.SN)\'\n[19:19:13] [INFO] testing \'Oracle OR boolean-based blind - WHERE or HAVING clause (CTXSYS.DRITHSX.SN)\'\n[19:19:14] [INFO] testing \'SQLite AND boolean-based blind - WHERE, HAVING, GROUP BY or HAVING clause (JSON)\'\n[19:19:14] [INFO] testing \'SQLite OR boolean-based blind - WHERE, HAVING, GROUP BY or HAVING clause (JSON)\'\n[19:19:14] [INFO] testing \'Boolean-based blind - Parameter replace (original value)\'\n[19:19:14] [INFO] testing \'MySQL boolean-based blind - Parameter replace (MAKE_SET)\'\n[19:19:14] [INFO] testing \'MySQL boolean-based blind - Parameter replace (MAKE_SET - original value)\'\n[19:19:14] [INFO] testing \'MySQL boolean-based blind - Parameter replace (ELT)\'\n[19:19:14] [INFO] testing \'MySQL boolean-based blind - Parameter replace (ELT - original value)\'\n[19:19:14] [INFO] testing \'MySQL boolean-based blind - Parameter replace (bool*int)\'\n[19:19:14] [INFO] testing \'MySQL boolean-based blind - Parameter replace (bool*int - original value)\'\n[19:19:14] [INFO] testing \'PostgreSQL boolean-based blind - Parameter replace\'\n[19:19:14] [INFO] testing \'PostgreSQL boolean-based blind - Parameter replace (original value)\'\n[19:19:14] [INFO] testing \'PostgreSQL boolean-based blind - Parameter replace (GENERATE_SERIES)\'\n[19:19:14] [INFO] testing \'PostgreSQL boolean-based blind - Parameter replace (GENERATE_SERIES - original value)\'\n[19:19:14] [INFO] testing \'Microsoft SQL Server/Sybase boolean-based blind - Parameter replace\'\n[19:19:14] [INFO] testing \'Microsoft SQL Server/Sybase boolean-based blind - Parameter replace (original value)\'\n[19:19:14] [INFO] testing \'Oracle boolean-based blind - Parameter replace\'\n[19:19:14] [INFO] testing \'Oracle boolean-based blind - Parameter replace (original value)\'\n[19:19:14] [INFO] testing \'Informix boolean-based blind - Parameter replace\'\n[19:19:14] [INFO] testing \'Informix boolean-based blind - Parameter replace (original value)\'\n[19:19:14] [INFO] testing \'Microsoft Access boolean-based blind - Parameter replace\'\n[19:19:14] [INFO] testing \'Microsoft Access boolean-based blind - Parameter replace (original value)\'\n[19:19:14] [INFO] testing \'Boolean-based blind - Parameter replace (DUAL)\'\n[19:19:15] [INFO] testing \'Boolean-based blind - Parameter replace (DUAL - original value)\'\n[19:19:15] [INFO] testing \'Boolean-based blind - Parameter replace (CASE)\'\n[19:19:15] [INFO] testing \'Boolean-based blind - Parameter replace (CASE - original value)\'\n[19:19:15] [INFO] testing \'MySQL >= 5.0 boolean-based blind - ORDER BY, GROUP BY clause\'\n[19:19:15] [INFO] testing \'MySQL >= 5.0 boolean-based blind - ORDER BY, GROUP BY clause (original value)\'\n[19:19:15] [INFO] testing \'MySQL < 5.0 boolean-based blind - ORDER BY, GROUP BY clause\'\n[19:19:15] [INFO] testing \'MySQL < 5.0 boolean-based blind - ORDER BY, GROUP BY clause (original value)\'\n[19:19:15] [INFO] testing \'PostgreSQL boolean-based blind - ORDER BY, GROUP BY clause\'\n[19:19:15] [INFO] testing \'PostgreSQL boolean-based blind - ORDER BY clause (original value)\'\n[19:19:15] [INFO] testing \'PostgreSQL boolean-based blind - ORDER BY clause (GENERATE_SERIES)\'\n[19:19:15] [INFO] testing \'Microsoft SQL Server/Sybase boolean-based blind - ORDER BY clause\'\n[19:19:15] [INFO] testing \'Microsoft SQL Server/Sybase boolean-based blind - ORDER BY clause (original value)\'\n[19:19:15] [INFO] testing \'Oracle boolean-based blind - ORDER BY, GROUP BY clause\'\n[19:19:15] [INFO] testing \'Oracle boolean-based blind - ORDER BY, GROUP BY clause (original value)\'\n[19:19:15] [INFO] testing \'Microsoft Access boolean-based blind - ORDER BY, GROUP BY clause\'\n[19:19:15] [INFO] testing \'Microsoft Access boolean-based blind - ORDER BY, GROUP BY clause (original value)\'\n[19:19:15] [INFO] testing \'SAP MaxDB boolean-based blind - ORDER BY, GROUP BY clause\'\n[19:19:15] [INFO] testing \'SAP MaxDB boolean-based blind - ORDER BY, GROUP BY clause (original value)\'\n[19:19:15] [INFO] testing \'IBM DB2 boolean-based blind - ORDER BY clause\'\n[19:19:15] [INFO] testing \'IBM DB2 boolean-based blind - ORDER BY clause (original value)\'\n[19:19:15] [INFO] testing \'HAVING boolean-based blind - WHERE, GROUP BY clause\'\n[19:19:15] [INFO] testing \'MySQL >= 5.0 boolean-based blind - Stacked queries\'\n[19:19:15] [INFO] testing \'MySQL < 5.0 boolean-based blind - Stacked queries\'\n[19:19:15] [INFO] testing \'PostgreSQL boolean-based blind - Stacked queries\'\n[19:19:16] [INFO] testing \'PostgreSQL boolean-based blind - Stacked queries (GENERATE_SERIES)\'\n[19:19:16] [INFO] testing \'Microsoft SQL Server/Sybase boolean-based blind - Stacked queries (IF)\'\n[19:19:16] [INFO] testing \'Microsoft SQL Server/Sybase boolean-based blind - Stacked queries\'\n[19:19:16] [INFO] testing \'Oracle boolean-based blind - Stacked queries\'\n[19:19:17] [INFO] testing \'Microsoft Access boolean-based blind - Stacked queries\'\n[19:19:17] [INFO] testing \'SAP MaxDB boolean-based blind - Stacked queries\'\n[19:19:17] [INFO] testing \'MySQL >= 5.5 AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (BIGINT UNSIGNED)\'\n[19:19:17] [INFO] testing \'MySQL >= 5.5 OR error-based - WHERE or HAVING clause (BIGINT UNSIGNED)\'\n[19:19:18] [INFO] testing \'MySQL >= 5.5 AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (EXP)\'\n[19:19:18] [INFO] testing \'MySQL >= 5.5 OR error-based - WHERE or HAVING clause (EXP)\'\n[19:19:18] [INFO] testing \'MySQL >= 5.6 AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (GTID_SUBSET)\'\n[19:19:18] [INFO] testing \'MySQL >= 5.6 OR error-based - WHERE or HAVING clause (GTID_SUBSET)\'\n[19:19:19] [INFO] testing \'MySQL >= 5.7.8 AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (JSON_KEYS)\'\n[19:19:19] [INFO] testing \'MySQL >= 5.7.8 OR error-based - WHERE or HAVING clause (JSON_KEYS)\'\n[19:19:19] [INFO] testing \'MySQL >= 5.0 AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (FLOOR)\'\n[19:19:19] [INFO] testing \'MySQL >= 5.0 OR error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (FLOOR)\'\n[19:19:19] [INFO] testing \'MySQL >= 5.1 AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (EXTRACTVALUE)\'\n[19:19:20] [INFO] testing \'MySQL >= 5.1 OR error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (EXTRACTVALUE)\'\n[19:19:20] [INFO] testing \'MySQL >= 5.1 AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (UPDATEXML)\'\n[19:19:20] [INFO] testing \'MySQL >= 5.1 OR error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (UPDATEXML)\'\n[19:19:20] [INFO] testing \'MySQL >= 4.1 AND error-based - WHERE, HAVING, ORDER BY or GROUP BY clause (FLOOR)\'\n[19:19:21] [INFO] testing \'MySQL >= 4.1 OR error-based - WHERE or HAVING clause (FLOOR)\'\n[19:19:21] [INFO] testing \'MySQL OR error-based - WHERE or HAVING clause (FLOOR)\'\n[19:19:21] [INFO] testing \'PostgreSQL AND error-based - WHERE or HAVING clause\'\n[19:19:21] [INFO] testing \'PostgreSQL OR error-based - WHERE or HAVING clause\'\n[19:19:21] [INFO] testing \'Microsoft SQL Server/Sybase AND error-based - WHERE or HAVING clause (IN)\'\n[19:19:22] [INFO] testing \'Microsoft SQL Server/Sybase OR error-based - WHERE or HAVING clause (IN)\'\n[19:19:22] [INFO] testing \'Microsoft SQL Server/Sybase AND error-based - WHERE or HAVING clause (CONVERT)\'\n[19:19:22] [INFO] testing \'Microsoft SQL Server/Sybase OR error-based - WHERE or HAVING clause (CONVERT)\'\n[19:19:22] [INFO] testing \'Microsoft SQL Server/Sybase AND error-based - WHERE or HAVING clause (CONCAT)\'\n[19:19:23] [INFO] testing \'Microsoft SQL Server/Sybase OR error-based - WHERE or HAVING clause (CONCAT)\'\n[19:19:23] [INFO] testing \'Oracle AND error-based - WHERE or HAVING clause (XMLType)\'\n[19:19:23] [INFO] testing \'Oracle OR error-based - WHERE or HAVING clause (XMLType)\'\n[19:19:23] [INFO] testing \'Oracle AND error-based - WHERE or HAVING clause (UTL_INADDR.GET_HOST_ADDRESS)\'\n[19:19:23] [INFO] testing \'Oracle OR error-based - WHERE or HAVING clause (UTL_INADDR.GET_HOST_ADDRESS)\'\n[19:19:24] [INFO] testing \'Oracle AND error-based - WHERE or HAVING clause (CTXSYS.DRITHSX.SN)\'\n[19:19:24] [INFO] testing \'Oracle OR error-based - WHERE or HAVING clause (CTXSYS.DRITHSX.SN)\'\n', 'vulnerabilities': []}, 'dirb': {'status': 'completed', 'directories': [], 'raw_output': '\n-----------------\nDIRB v2.22    \nBy The Dark Raver\n-----------------\n\nSTART_TIME: Mon Jun 30 19:17:55 2025\nURL_BASE: http://************/\nWORDLIST_FILES: /usr/share/dirb/wordlists/big.txt\nOPTION: Not Stopping on warning messages\n\n-----------------\n\n*** Generating Wordlist...\n                                                                               \nGENERATED WORDS: 20458\n\n---- Scanning URL: http://************/ ----\n*** Calculating NOT_FOUND code...\n                                                                               \n                                                                                                                                                                                       \n--> Testing: http://************/!\n                                                                                                                                                                                       \n--> Testing: http://************/!_archives\n                                                                                                                                                                                       \n--> Testing: http://************/!_images\n                                                                                                                                                                                       \n--> Testing: http://************/!backup\n                                                                                                                                                                                       \n--> Testing: http://************/!images\n                                                                                                                                                                                       \n--> Testing: http://************/!res\n                                                                                                                                                                                       \n--> Testing: http://************/!textove_diskuse\n                                                                                                                                                                                       \n--> Testing: http://************/!ut\n                                                                                                                                                                                       \n--> Testing: http://************/.bash_history\n                                                                                                                                                                                       \n--> Testing: http://************/.bashrc\n                                                                                                                                                                                       \n--> Testing: http://************/.cvs\n                                                                                                                                                                                       \n--> Testing: http://************/.cvsignore\n                                                                                                                                                                                       \n--> Testing: http://************/.forward\n                                                                                                                                                                                       \n--> Testing: http://************/.history\n                                                                                                                                                                                       \n--> Testing: http://************/.htaccess\n                                                                                                                                                                                       \n--> Testing: http://************/.htpasswd\n                                                                                                                                                                                       \n--> Testing: http://************/.listing\n                                                                                                                                                                                       \n--> Testing: http://************/.passwd\n                                                                                                                                                                                       \n--> Testing: http://************/.perf\n                                                                                                                                                                                       \n--> Testing: http://************/.profile\n                                                                                                                                                                                       \n--> Testing: http://************/.rhosts\n                                                                                                                                                                                       \n--> Testing: http://************/.ssh\n                                                                                                                                                                                       \n--> Testing: http://************/.subversion\n                                                                                                                                                                                       \n--> Testing: http://************/.svn\n                                                                                                                                                                                       \n--> Testing: http://************/.web\n                                                                                                                                                                                       \n--> Testing: http://************/0\n                                                                                                                                                                                       \n--> Testing: http://************/0-0-1\n                                                                                                                                                                                       \n--> Testing: http://************/0-12\n                                                                                                                                                                                       \n--> Testing: http://************/0-newstore\n                                                                                                                                                                                       \n--> Testing: http://************/00\n                                                                                                                                                                                       \n--> Testing: http://************/00-backup\n                                                                                                                                                                                       \n--> Testing: http://************/00-cache\n                                                                                                                                                                                       \n--> Testing: http://************/00-img\n                                                                                                                                                                                       \n--> Testing: http://************/00-inc\n                                                                                                                                                                                       \n--> Testing: http://************/00-mp\n                                                                                                                                                                                       \n--> Testing: http://************/00-ps\n                                                                                                                                                                                       \n--> Testing: http://************/000\n                                                                                                                                                                                       \n--> Testing: http://************/0000\n                                                                                                                                                                                       \n--> Testing: http://************/000000\n                                                                                                                                                                                       \n--> Testing: http://************/00000000\n                                                                                                                                                                                       \n--> Testing: http://************/0001\n                                                                                                                                                                                       \n--> Testing: http://************/0007\n                                                                                                                                                                                       \n--> Testing: http://************/001\n                                                                                                                                                                                       \n--> Testing: http://************/002\n                                                                                                                                                                                       \n--> Testing: http://************/007\n                                                                                                                                                                                       \n--> Testing: http://************/007007\n                                                                                                                                                                                       \n--> Testing: http://************/01\n                                                                                                                                                                                       \n--> Testing: http://************/02\n                                                                                                                                                                                       \n--> Testing: http://************/0246\n                                                                                                                                                                                       \n--> Testing: http://************/0249\n                                                                                                                                                                                       \n--> Testing: http://************/03\n                                                                                                                                                                                       \n--> Testing: http://************/04\n                                                                                                                                                                                       \n--> Testing: http://************/05\n                                                                                                                                                                                       \n--> Testing: http://************/0594wm\n                                                                                                                                                                                       \n--> Testing: http://************/06\n                                                                                                                                                                                       \n--> Testing: http://************/07\n                                                                                                                                                                                       \n--> Testing: http://************/08\n                                                                                                                                                                                       \n--> Testing: http://************/09\n                                                                                                                                                                                       \n--> Testing: http://************/1\n                                                                                                                                                                                       \n--> Testing: http://************/10\n                                                                                                                                                                                       \n--> Testing: http://************/100\n                                                                                                                                                                                       \n--> Testing: http://************/1000\n                                                                                                                                                                                       \n--> Testing: http://************/1001\n                                                                                                                                                                                       \n--> Testing: http://************/1009\n                                                                                                                                                                                       \n--> Testing: http://************/101\n                                                                                                                                                                                       \n--> Testing: http://************/102\n                                                                                                                                                                                       \n--> Testing: http://************/1022\n                                                                                                                                                                                       \n--> Testing: http://************/1024\n                                                                                                                                                                                       \n--> Testing: http://************/103\n                                                                                                                                                                                       \n--> Testing: http://************/104\n                                                                                                                                                                                       \n--> Testing: http://************/105\n                                                                                                                                                                                       \n--> Testing: http://************/106\n                                                                                                                                                                                       \n--> Testing: http://************/10668\n                                                                                                                                                                                       \n--> Testing: http://************/107\n                                                                                                                                                                                       \n--> Testing: http://************/108\n                                                                                                                                                                                       \n--> Testing: http://************/109\n                                                                                                                                                                                       \n--> Testing: http://************/10sne1\n                                                                                                                                                                                       \n--> Testing: http://************/11\n                                                                                                                                                                                       \n--> Testing: http://************/110\n                                                                                                                                                                                       \n--> Testing: http://************/111\n                                                                                                                                                                                       \n--> Testing: http://************/1111\n                                                                                                                                                                                       \n--> Testing: http://************/111111\n                                                                                                                                                                                       \n--> Testing: http://************/112\n                                                                                                                                                                                       \n--> Testing: http://************/113\n                                                                                                                                                                                       \n--> Testing: http://************/114\n                                                                                                                                                                                       \n--> Testing: http://************/115\n                                                                                                                                                                                       \n--> Testing: http://************/116\n                                                                                                                                                                                       \n--> Testing: http://************/1166\n                                                                                                                                                                                       \n--> Testing: http://************/1168\n                                                                                                                                                                                       \n--> Testing: http://************/1169\n                                                                                                                                                                                       \n--> Testing: http://************/117\n                                                                                                                                                                                       \n--> Testing: http://************/1173\n                                                                                                                                                                                       \n--> Testing: http://************/1178\n                                                                                                                                                                                       \n--> Testing: http://************/1179\n                                                                                                                                                                                       \n--> Testing: http://************/118\n                                                                                                                                                                                       \n--> Testing: http://************/1187\n                                                                                                                                                                                       \n--> Testing: http://************/1188\n                                                                                                                                                                                       \n--> Testing: http://************/1189\n                                                                                                                                                                                       \n--> Testing: http://************/119\n                                                                                                                                                                                       \n--> Testing: http://************/1191\n                                                                                                                                                                                       \n--> Testing: http://************/1193\n                                                                                                                                                                                       \n--> Testing: http://************/12\n                                                                                                                                                                                       \n--> Testing: http://************/120\n                                                                                                                                                                                       \n--> Testing: http://************/1203\n                                                                                                                                                                                       \n--> Testing: http://************/1204\n                                                                                                                                                                                       \n--> Testing: http://************/1205\n                                                                                                                                                                                       \n--> Testing: http://************/1208\n                                                                                                                                                                                       \n--> Testing: http://************/121\n                                                                                                                                                                                       \n--> Testing: http://************/1210\n                                                                                                                                                                                       \n--> Testing: http://************/1211\n                                                                                                                                                                                       \n--> Testing: http://************/1212\n                                                                                                                                                                                       \n--> Testing: http://************/121212\n                                                                                                                                                                                       \n--> Testing: http://************/1213\n                                                                                                                                                                                       \n--> Testing: http://************/1214\n                                                                                                                                                                                       \n--> Testing: http://************/1215\n                                                                                                                                                                                       \n--> Testing: http://************/1216\n                                                                                                                                                                                       \n--> Testing: http://************/1217\n                                                                                                                                                                                       \n--> Testing: http://************/1218\n                                                                                                                                                                                       \n--> Testing: http://************/122\n                                                                                                                                                                                       \n--> Testing: http://************/1221\n                                                                                                                                                                                       \n--> Testing: http://************/1222\n                                                                                                                                                                                       \n--> Testing: http://************/1224\n                                                                                                                                                                                       \n--> Testing: http://************/1225\n                                                                                                                                                                                       \n--> Testing: http://************/1229\n                                                                                                                                                                                       \n--> Testing: http://************/123\n                                                                                                                                                                                       \n--> Testing: http://************/1230\n                                                                                                                                                                                       \n--> Testing: http://************/123123\n                                                                                                                                                                                       \n--> Testing: http://************/1234\n                                                                                                                                                                                       \n--> Testing: http://************/12345\n                                                                                                                                                                                       \n--> Testing: http://************/123456\n                                                                                                                                                                                       \n--> Testing: http://************/1234567\n                                                                                                                                                                                       \n--> Testing: http://************/12345678\n                                                                                                                                                                                       \n--> Testing: http://************/1234qwer\n                                                                                                                                                                                       \n--> Testing: http://************/1237\n                                                                                                                                                                                       \n--> Testing: http://************/123abc\n                                                                                                                                                                                       \n--> Testing: http://************/123go\n                                                                                                                                                                                       \n--> Testing: http://************/124\n                                                                                                                                                                                       \n--> Testing: http://************/1244\n                                                                                                                                                                                       \n--> Testing: http://************/125\n                                                                                                                                                                                       \n--> Testing: http://************/1250\n                                                                                                                                                                                       \n--> Testing: http://************/126\n                                                                                                                                                                                       \n--> Testing: http://************/1261\n                                                                                                                                                                                       \n--> Testing: http://************/1263\n                                                                                                                                                                                       \n--> Testing: http://************/127\n                                                                                                                                                                                       \n--> Testing: http://************/1273\n                                                                                                                                                                                       \n--> Testing: http://************/1277\n                                                                                                                                                                                       \n--> Testing: http://************/1278\n                                                                                                                                                                                       \n--> Testing: http://************/128\n                                                                                                                                                                                       \n--> Testing: http://************/1280\n                                                                                                                                                                                       \n--> Testing: http://************/1283\n                                                                                                                                                                                       \n--> Testing: http://************/129\n                                                                                                                                                                                       \n--> Testing: http://************/1291\n                                                                                                                                                                                       \n--> Testing: http://************/1298\n                                                                                                                                                                                       \n--> Testing: http://************/12all\n                                                                                                                                                                                       \n--> Testing: http://************/12xyz34\n                                                                                                                                                                                       \n--> Testing: http://************/13\n                                                                                                                                                                                       \n--> Testing: http://************/130\n                                                                                                                                                                                       \n--> Testing: http://************/131\n                                                                                                                                                                                       \n--> Testing: http://************/1312\n                                                                                                                                                                                       \n--> Testing: http://************/1313\n                                                                                                                                                                                       \n--> Testing: http://************/131313\n                                                                                                                                                                                       \n--> Testing: http://************/132\n                                                                                                                                                                                       \n--> Testing: http://************/1320\n                                                                                                                                                                                       \n--> Testing: http://************/1324\n                                                                                                                                                                                       \n--> Testing: http://************/133\n                                                                                                                                                                                       \n--> Testing: http://************/1332\n                                                                                                                                                                                       \n--> Testing: http://************/134\n                                                                                                                                                                                       \n--> Testing: http://************/1341\n                                                                                                                                                                                       \n--> Testing: http://************/1349\n                                                                                                                                                                                       \n--> Testing: http://************/135\n                                                                                                                                                                                       \n--> Testing: http://************/1350\n                                                                                                                                                                                       \n--> Testing: http://************/1354\n                                                                                                                                                                                       \n--> Testing: http://************/13579\n                                                                                                                                                                                       \n--> Testing: http://************/1358\n                                                                                                                                                                                       \n--> Testing: http://************/136\n                                                                                                                                                                                       \n--> Testing: http://************/1366\n                                                                                                                                                                                       \n--> Testing: http://************/1369\n                                                                                                                                                                                       \n--> Testing: http://************/137\n                                                                                                                                                                                       \n--> Testing: http://************/1371\n                                                                                                                                                                                       \n--> Testing: http://************/1372\n                                                                                                                                                                                       \n--> Testing: http://************/1373\n                                                                                                                                                                                       \n--> Testing: http://************/1379\n                                                                                                                                                                                       \n--> Testing: http://************/138\n                                                                                                                                                                                       \n--> Testing: http://************/1383\n                                                                                                                                                                                       \n--> Testing: http://************/139\n                                                                                                                                                                                       \n--> Testing: http://************/1399\n                                                                                                                                                                                       \n--> Testing: http://************/14\n                                                                                                                                                                                       \n--> Testing: http://************/140\n                                                                                                                                                                                       \n--> Testing: http://************/1400\n                                                                                                                                                                                       \n--> Testing: http://************/1405\n                                                                                                                                                                                       \n--> Testing: http://************/141\n                                                                                                                                                                                       \n--> Testing: http://************/142\n                                                                                                                                                                                       \n--> Testing: http://************/143\n                                                                                                                                                                                       \n--> Testing: http://************/144\n                                                                                                                                                                                       \n--> Testing: http://************/14430\n                                                                                                                                                                                       \n--> Testing: http://************/145\n                                                                                                                                                                                       \n--> Testing: http://************/146\n                                                                                                                                                                                       \n--> Testing: http://************/147\n                                                                                                                                                                                       \n--> Testing: http://************/148\n                                                                                                                                                                                       \n--> Testing: http://************/1480\n                                                                                                                                                                                       \n--> Testing: http://************/1489\n                                                                                                                                                                                       \n--> Testing: http://************/149\n                                                                                                                                                                                       \n--> Testing: http://************/1493\n                                                                                                                                                                                       \n--> Testing: http://************/1498\n                                                                                                                                                                                       \n--> Testing: http://************/15\n                                                                                                                                                                                       \n--> Testing: http://************/150\n                                                                                                                                                                                       \n--> Testing: http://************/1500\n                                                                                                                                                                                       \n--> Testing: http://************/151\n                                                                                                                                                                                       \n--> Testing: http://************/152\n                                                                                                                                                                                       \n--> Testing: http://************/153\n                                                                                                                                                                                       \n--> Testing: http://************/154\n                                                                                                                                                                                       \n--> Testing: http://************/1548\n                                                                                                                                                                                       \n--> Testing: http://************/155\n                                                                                                                                                                                       \n--> Testing: http://************/156\n                                                                                                                                                                                       \n--> Testing: http://************/157\n                                                                                                                                                                                       \n--> Testing: http://************/1572\n                                                                                                                                                                                       \n--> Testing: http://************/158\n                                                                                                                                                                                       \n--> Testing: http://************/1585\n                                                                                                                                                                                       \n--> Testing: http://************/159\n                                                                                                                                                                                       \n--> Testing: http://************/1590\n                                                                                                                                                                                       \n--> Testing: http://************/1593\n                                                                                                                                                                                       \n--> Testing: http://************/1594\n                                                                                                                                                                                       \n--> Testing: http://************/1595\n                                                                                                                                                                                       \n--> Testing: http://************/1596\n                                                                                                                                                                                       \n--> Testing: http://************/16\n                                                                                                                                                                                       \n--> Testing: http://************/160\n                                                                                                                                                                                       \n--> Testing: http://************/161\n                                                                                                                                                                                       \n--> Testing: http://************/162\n                                                                                                                                                                                       \n--> Testing: http://************/164\n                                                                                                                                                                                       \n--> Testing: http://************/165\n                                                                                                                                                                                       \n--> Testing: http://************/1650\n                                                                                                                                                                                       \n--> Testing: http://************/166\n                                                                                                                                                                                       \n--> Testing: http://************/167\n                                                                                                                                                                                       \n--> Testing: http://************/1676\n                                                                                                                                                                                       \n--> Testing: http://************/168\n                                                                                                                                                                                       \n--> Testing: http://************/169\n                                                                                                                                                                                       \n--> Testing: http://************/1694\n                                                                                                                                                                                       \n--> Testing: http://************/1698\n                                                                                                                                                                                       \n--> Testing: http://************/17\n                                                                                                                                                                                       \n--> Testing: http://************/170\n                                                                                                                                                                                       \n--> Testing: http://************/1701d\n                                                                                                                                                                                       \n--> Testing: http://************/1702\n                                                                                                                                                                                       \n--> Testing: http://************/1703\n                                                                                                                                                                                       \n--> Testing: http://************/1704\n                                                                                                                                                                                       \n--> Testing: http://************/1705\n                                                                                                                                                                                       \n--> Testing: http://************/1706\n                                                                                                                                                                                       \n--> Testing: http://************/1707\n                                                                                                                                                                                       \n--> Testing: http://************/171\n                                                                                                                                                                                       \n--> Testing: http://************/1717\n                                                                                                                                                                                       \n--> Testing: http://************/172\n                                                                                                                                                                                       \n--> Testing: http://************/1720\n                                                                                                                                                                                       \n--> Testing: http://************/173\n                                                                                                                                                                                       \n--> Testing: http://************/1736\n                                                                                                                                                                                       \n--> Testing: http://************/174\n                                                                                                                                                                                       \n--> Testing: http://************/1747\n                                                                                                                                                                                       \n--> Testing: http://************/175\n                                                                                                                                                                                       \n--> Testing: http://************/1756\n                                                                                                                                                                                       \n--> Testing: http://************/1757\n                                                                                                                                                                                       \n--> Testing: http://************/176\n                                                                                                                                                                                       \n--> Testing: http://************/1762\n                                                                                                                                                                                       \n--> Testing: http://************/177\n                                                                                                                                                                                       \n--> Testing: http://************/1771\n                                                                                                                                                                                       \n--> Testing: http://************/1779\n                                                                                                                                                                                       \n--> Testing: http://************/178\n                                                                                                                                                                                       \n--> Testing: http://************/1794\n                                                                                                                                                                                       \n--> Testing: http://************/18\n                                                                                                                                                                                       \n--> Testing: http://************/180\n                                                                                                                                                                                       \n--> Testing: http://************/1809\n                                                                                                                                                                                       \n--> Testing: http://************/181\n                                                                                                                                                                                       \n--> Testing: http://************/1814\n                                                                                                                                                                                       \n--> Testing: http://************/1816\n                                                                                                                                                                                       \n--> Testing: http://************/1825\n                                                                                                                                                                                       \n--> Testing: http://************/183\n                                                                                                                                                                                       \n--> Testing: http://************/184\n                                                                                                                                                                                       \n--> Testing: http://************/185\n                                                                                                                                                                                       \n--> Testing: http://************/187\n                                                                                                                                                                                       \n--> Testing: http://************/188\n                                                                                                                                                                                       \n--> Testing: http://************/189\n                                                                                                                                                                                       \n--> Testing: http://************/1897\n                                                                                                                                                                                       \n--> Testing: http://************/1899-hoffenheim\n                                                                                                                                                                                       \n--> Testing: http://************/19\n                                                                                                                                                                                       \n--> Testing: http://************/190\n                                                                                                                                                                                       \n--> Testing: http://************/191\n                                                                                                                                                                                       \n--> Testing: http://************/192\n                                                                                                                                                                                       \n--> Testing: http://************/1928\n', 'found_paths': 0}, 'gobuster': {'status': 'completed', 'directories': [], 'raw_output': '\n\x1b[2K/<button aria-label="Toggle navigation" aria-expanded="false" type="button" data-view-component="true" class="js-details-target js-nav-padding-recalculate js-header-menu-toggle Button--link Button--medium Button d-lg-none color-fg-inherit p-1">  <span class="Button-content"> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M12 1C5.923 1 1 5.923 1 12c0 4.867 3.149 8.979 7.521 10.436.55.096.756-.233.756-.522 0-.262-.013-1.128-.013-2.049-2.764.509-3.479-.674-3.699-1.292-.124-.317-.66-1.293-1.127-1.554-.385-.207-.936-.715-.014-.729.866-.014 1.485.797 1.691 1.128.99 1.663 2.571 1.196 3.204.907.096-.715.385-1.196.701-1.471-2.448-.275-5.005-1.224-5.005-5.432 0-1.196.426-2.186 1.128-2.956-.111-.275-.496-1.402.11-2.915 0 0 .921-.288 3.024 1.128a10.193 10.193 0 0 1 2.75-.371c.936 0 1.871.123 2.75.371 2.104-1.43 3.025-1.128 3.025-1.128.605 1.513.221 2.64.111 2.915.701.77 1.127 1.747 1.127 2.956 0 4.222-2.571 5.157-5.019 5.432.399.344.743 1.004.743 2.035 0 1.471-.014 2.654-.014 3.025 0 .289.206.632.756.522C19.851 20.979 23 16.854 23 12c0-6.077-4.922-11-11-11Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;site header menu&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;SIGN_UP&quot;,&quot;originating_url&quot;:&quot;https://github.com/xmendez/wfuzz/blob/master/wordlist/general/big.txt&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="f96a1f8960da60bf2ea2f21c2791f6ecbdb6843a1ea80e471f0980e9c68a8c3c" (Status: 403) [Size: 277]\n\n\x1b[2K/<button data-target="react-partial-anchor.anchor" id="icon-button-c40b6d2f-575b-4d33-8ce6-6b651d8549a0" aria-labelledby="tooltip-afc59e55-1755-4d61-9e9b-82606b016b97" type="button" disabled="disabled" data-view-component="true" class="Button Button--iconOnly Button--invisible Button--medium AppHeader-button HeaderMenu-link border cursor-wait">  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-sliders Button-visual"> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M15 2.75a.75.75 0 0 1-.75.75h-4a.75.75 0 0 1 0-1.5h4a.75.75 0 0 1 .75.75Zm-8.5.75v1.25a.75.75 0 0 0 1.5 0v-4a.75.75 0 0 0-1.5 0V2H1.75a.75.75 0 0 0 0 1.5H6.5Zm1.25 5.25a.75.75 0 0 0 0-1.5h-6a.75.75 0 0 0 0 1.5h6ZM15 8a.75.75 0 0 1-.75.75H11.5V10a.75.75 0 1 1-1.5 0V6a.75.75 0 0 1 1.5 0v1.25h2.75A.75.75 0 0 1 15 8Zm-9 5.25v-2a.75.75 0 0 0-1.5 0v1.25H1.75a.75.75 0 0 0 0 1.5H4.5v1.25a.75.75 0 0 0 1.5 0v-2Zm9 0a.75.75 0 0 1-.75.75h-6a.75.75 0 0 1 0-1.5h6a.75.75 0 0 1 .75.75Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;github_copilot&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;github_copilot_link_product_navbar&quot;}" href="https://github.com/features/copilot"> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M23.922 16.992c-.861 1.495-5.859 5.023-11.922 5.023-6.063 0-11.061-3.528-11.922-5.023A.641.641 0 0 1 0 16.736v-2.869a.841.841 0 0 1 .053-.22c.372-.935 1.347-2.292 2.605-2.656.167-.429.414-1.055.644-1.517a10.195 10.195 0 0 1-.052-1.086c0-1.331.282-2.499 1.132-3.368.397-.406.89-.717 1.474-.952 1.399-1.136 3.392-2.093 6.122-2.093 2.731 0 4.767.957 6.166 2.093.584.235 1.077.546 1.474.952.85.869 1.132 2.037 1.132 3.368 0 .368-.014.733-.052 1.086.23.462.477 1.088.644 1.517 1.258.364 2.233 1.721 2.605 2.656a.832.832 0 0 1 .053.22v2.869a.641.641 0 0 1-.078.256ZM12.172 11h-.344a4.323 4.323 0 0 1-.355.508C10.703 12.455 9.555 13 7.965 13c-1.725 0-2.989-.359-3.782-1.259a2.005 2.005 0 0 1-.085-.104L4 11.741v6.585c1.435.779 4.514 2.179 8 2.179 3.486 0 6.565-1.4 8-2.179v-6.585l-.098-.104s-.033.045-.085.104c-.793.9-2.057 1.259-3.782 1.259-1.59 0-2.738-.545-3.508-1.492a4.323 4.323 0 0 1-.355-.508h-.016.016Zm.641-2.935c.136 1.057.403 1.913.878 2.497.442.544 1.134.938 2.344.938 1.573 0 2.292-.337 2.657-.751.384-.435.558-1.15.558-2.361 0-1.14-.243-1.847-.705-2.319-.477-.488-1.319-.862-2.824-1.025-1.487-.161-2.192.138-2.533.529-.269.307-.437.808-.438 1.578v.021c0 .265.021.562.063.893Zm-1.626 0c.042-.331.063-.628.063-.894v-.02c-.001-.77-.169-1.271-.438-1.578-.341-.391-1.046-.69-2.533-.529-1.505.163-2.347.537-2.824 1.025-.462.472-.705 1.179-.705 2.319 0 1.211.175 1.926.558 2.361.365.414 1.084.751 2.657.751 1.21 0 1.902-.394 2.344-.938.475-.584.742-1.44.878-2.497Z"></path><path d="M14.5 14.25a1 1 0 0 1 1 1v2a1 1 0 0 1-2 0v-2a1 1 0 0 1 1-1Zm-5 0a1 1 0 0 1 1 1v2a1 1 0 0 1-2 0v-2a1 1 0 0 1 1-1Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;github_models&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;github_models_link_product_navbar&quot;}" href="https://github.com/features/models"> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M19.375 8.5a3.25 3.25 0 1 1-3.163 4h-3a3.252 3.252 0 0 1-4.443 2.509L7.214 17.76a3.25 3.25 0 1 1-1.342-.674l1.672-2.957A3.238 3.238 0 0 1 6.75 12c0-.907.371-1.727.97-2.316L6.117 6.846A3.253 3.253 0 0 1 1.875 3.75a3.25 3.25 0 1 1 5.526 2.32l1.603 2.836A3.25 3.25 0 0 1 13.093 11h3.119a3.252 3.252 0 0 1 3.163-2.5ZM10 10.25a1.75 1.75 0 1 0-.001 3.499A1.75 1.75 0 0 0 10 10.25ZM5.125 2a1.75 1.75 0 1 0 0 3.5 1.75 1.75 0 0 0 0-3.5Zm12.5 9.75a1.75 1.75 0 1 0 3.5 0 1.75 1.75 0 0 0-3.5 0Zm-14.25 8.5a1.75 1.75 0 1 0 3.501-.001 1.75 1.75 0 0 0-3.501.001Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;github_advanced_security&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;github_advanced_security_link_product_navbar&quot;}" href="https://github.com/security/advanced-security"> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M1 3a2 2 0 0 1 2-2h6.5a2 2 0 0 1 2 2v6.5a2 2 0 0 1-2 2H7v4.063C7 16.355 7.644 17 8.438 17H12.5v-2.5a2 2 0 0 1 2-2H21a2 2 0 0 1 2 2V21a2 2 0 0 1-2 2h-6.5a2 2 0 0 1-2-2v-2.5H8.437A2.939 2.939 0 0 1 5.5 15.562V11.5H3a2 2 0 0 1-2-2Zm2-.5a.5.5 0 0 0-.5.5v6.5a.5.5 0 0 0 .5.5h6.5a.5.5 0 0 0 .5-.5V3a.5.5 0 0 0-.5-.5ZM14.5 14a.5.5 0 0 0-.5.5V21a.5.5 0 0 0 .5.5H21a.5.5 0 0 0 .5-.5v-6.5a.5.5 0 0 0-.5-.5Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;actions&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;actions_link_product_navbar&quot;}" href="https://github.com/features/actions"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;codespaces&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;codespaces_link_product_navbar&quot;}" href="https://github.com/features/codespaces"> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M3.5 3.75C3.5 2.784 4.284 2 5.25 2h13.5c.966 0 1.75.784 1.75 1.75v7.5A1.75 1.75 0 0 1 18.75 13H5.25a1.75 1.75 0 0 1-1.75-1.75Zm-2 12c0-.966.784-1.75 1.75-1.75h17.5c.966 0 1.75.784 1.75 1.75v4a1.75 1.75 0 0 1-1.75 1.75H3.25a1.75 1.75 0 0 1-1.75-1.75ZM5.25 3.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h13.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Zm-2 12a.25.25 0 0 0-.25.25v4c0 .*************.25h17.5a.25.25 0 0 0 .25-.25v-4a.25.25 0 0 0-.25-.25Z"></path><path d="M10 17.75a.75.75 0 0 1 .75-.75h6.5a.75.75 0 0 1 0 1.5h-6.5a.75.75 0 0 1-.75-.75Zm-4 0a.75.75 0 0 1 .75-.75h.5a.75.75 0 0 1 0 1.5h-.5a.75.75 0 0 1-.75-.75Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;issues&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;issues_link_product_navbar&quot;}" href="https://github.com/features/issues"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;code_review&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;code_review_link_product_navbar&quot;}" href="https://github.com/features/code-review"> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M10.3 6.74a.75.75 0 0 1-.04 1.06l-2.908 2.7 2.908 2.7a.75.75 0 1 1-1.02 1.1l-3.5-3.25a.75.75 0 0 1 0-1.1l3.5-3.25a.75.75 0 0 1 1.06.04Zm3.44 1.06a.75.75 0 1 1 1.02-1.1l3.5 3.25a.75.75 0 0 1 0 1.1l-3.5 3.25a.75.75 0 1 1-1.02-1.1l2.908-2.7-2.908-2.7Z"></path><path d="M1.5 4.25c0-.966.784-1.75 1.75-1.75h17.5c.966 0 1.75.784 1.75 1.75v12.5a1.75 1.75 0 0 1-1.75 1.75h-9.69l-3.573 3.573A1.458 1.458 0 0 1 5 21.043V18.5H3.25a1.75 1.75 0 0 1-1.75-1.75ZM3.25 4a.25.25 0 0 0-.25.25v12.5c0 .*************.25h2.5a.75.75 0 0 1 .75.75v3.19l3.72-3.72a.749.749 0 0 1 .53-.22h10a.25.25 0 0 0 .25-.25V4.25a.25.25 0 0 0-.25-.25Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;discussions&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;discussions_link_product_navbar&quot;}" href="https://github.com/features/discussions"> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M1.75 1h12.5c.966 0 1.75.784 1.75 1.75v9.5A1.75 1.75 0 0 1 14.25 14H8.061l-2.574 2.573A1.458 1.458 0 0 1 3 15.543V14H1.75A1.75 1.75 0 0 1 0 12.25v-9.5C0 1.784.784 1 1.75 1ZM1.5 2.75v9.5c0 .*************.25h2a.75.75 0 0 1 .75.75v2.19l2.72-2.72a.749.749 0 0 1 .53-.22h6.5a.25.25 0 0 0 .25-.25v-9.5a.25.25 0 0 0-.25-.25H1.75a.25.25 0 0 0-.25.25Z"></path><path d="M22.5 8.75a.25.25 0 0 0-.25-.25h-3.5a.75.75 0 0 1 0-1.5h3.5c.966 0 1.75.784 1.75 1.75v9.5A1.75 1.75 0 0 1 22.25 20H21v1.543a1.457 1.457 0 0 1-2.487 1.03L15.939 20H10.75A1.75 1.75 0 0 1 9 18.25v-1.465a.75.75 0 0 1 1.5 0v1.465c0 .*************.25h5.5a.75.75 0 0 1 .53.22l2.72 2.72v-2.19a.75.75 0 0 1 .75-.75h2a.25.25 0 0 0 .25-.25v-9.5Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;code_search&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;code_search_link_product_navbar&quot;}" href="https://github.com/features/code-search"> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M10.3 8.24a.75.75 0 0 1-.04 1.06L7.352 12l2.908 2.7a.75.75 0 1 1-1.02 1.1l-3.5-3.25a.75.75 0 0 1 0-1.1l3.5-3.25a.75.75 0 0 1 1.06.04Zm3.44 1.06a.75.75 0 1 1 1.02-1.1l3.5 3.25a.75.75 0 0 1 0 1.1l-3.5 3.25a.75.75 0 1 1-1.02-1.1l2.908-2.7-2.908-2.7Z"></path><path d="M2 3.75C2 2.784 2.784 2 3.75 2h16.5c.966 0 1.75.784 1.75 1.75v16.5A1.75 1.75 0 0 1 20.25 22H3.75A1.75 1.75 0 0 1 2 20.25Zm1.75-.25a.25.25 0 0 0-.25.25v16.5c0 .*************.25h16.5a.25.25 0 0 0 .25-.25V3.75a.25.25 0 0 0-.25-.25Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;why_github&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;why_github_link_product_navbar&quot;}" href="https://github.com/why-github"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;all_features&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;all_features_link_product_navbar&quot;}" href="https://github.com/features"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary Link--external" target="_blank" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;documentation&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;documentation_link_product_navbar&quot;}" href="https://docs.github.com"> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M3.75 2h3.5a.75.75 0 0 1 0 1.5h-3.5a.25.25 0 0 0-.25.25v8.5c0 .*************.25h8.5a.25.25 0 0 0 .25-.25v-3.5a.75.75 0 0 1 1.5 0v3.5A1.75 1.75 0 0 1 12.25 14h-8.5A1.75 1.75 0 0 1 2 12.25v-8.5C2 2.784 2.784 2 3.75 2Zm6.854-1h4.146a.25.25 0 0 1 .25.25v4.146a.25.25 0 0 1-.427.177L13.03 4.03 9.28 7.78a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042l3.75-3.75-1.543-1.543A.25.25 0 0 1 10.604 1Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary Link--external" target="_blank" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;github_skills&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;github_skills_link_product_navbar&quot;}" href="https://skills.github.com"> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M3.75 2h3.5a.75.75 0 0 1 0 1.5h-3.5a.25.25 0 0 0-.25.25v8.5c0 .*************.25h8.5a.25.25 0 0 0 .25-.25v-3.5a.75.75 0 0 1 1.5 0v3.5A1.75 1.75 0 0 1 12.25 14h-8.5A1.75 1.75 0 0 1 2 12.25v-8.5C2 2.784 2.784 2 3.75 2Zm6.854-1h4.146a.25.25 0 0 1 .25.25v4.146a.25.25 0 0 1-.427.177L13.03 4.03 9.28 7.78a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042l3.75-3.75-1.543-1.543A.25.25 0 0 1 10.604 1Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary Link--external" target="_blank" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;blog&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;blog_link_product_navbar&quot;}" href="https://github.blog"> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M3.75 2h3.5a.75.75 0 0 1 0 1.5h-3.5a.25.25 0 0 0-.25.25v8.5c0 .*************.25h8.5a.25.25 0 0 0 .25-.25v-3.5a.75.75 0 0 1 1.5 0v3.5A1.75 1.75 0 0 1 12.25 14h-8.5A1.75 1.75 0 0 1 2 12.25v-8.5C2 2.784 2.784 2 3.75 2Zm6.854-1h4.146a.25.25 0 0 1 .25.25v4.146a.25.25 0 0 1-.427.177L13.03 4.03 9.28 7.78a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042l3.75-3.75-1.543-1.543A.25.25 0 0 1 10.604 1Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;enterprises&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;enterprises_link_solutions_navbar&quot;}" href="https://github.com/enterprise"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;small_and_medium_teams&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;small_and_medium_teams_link_solutions_navbar&quot;}" href="https://github.com/team"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;startups&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;startups_link_solutions_navbar&quot;}" href="https://github.com/enterprise/startups"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;nonprofits&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;nonprofits_link_solutions_navbar&quot;}" href="/solutions/industry/nonprofits"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;devsecops&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;devsecops_link_solutions_navbar&quot;}" href="/solutions/use-case/devsecops"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;devops&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;devops_link_solutions_navbar&quot;}" href="/solutions/use-case/devops"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;ci_cd&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;ci_cd_link_solutions_navbar&quot;}" href="/solutions/use-case/ci-cd"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;view_all_use_cases&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;view_all_use_cases_link_solutions_navbar&quot;}" href="/solutions/use-case"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;healthcare&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;healthcare_link_solutions_navbar&quot;}" href="/solutions/industry/healthcare"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;financial_services&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;financial_services_link_solutions_navbar&quot;}" href="/solutions/industry/financial-services"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;manufacturing&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;manufacturing_link_solutions_navbar&quot;}" href="/solutions/industry/manufacturing"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;view_all_industries&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;view_all_industries_link_solutions_navbar&quot;}" href="/solutions/industry"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;ai&quot;,&quot;context&quot;:&quot;resources&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;ai_link_resources_navbar&quot;}" href="/resources/articles/ai"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;devops&quot;,&quot;context&quot;:&quot;resources&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;devops_link_resources_navbar&quot;}" href="/resources/articles/devops"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;security&quot;,&quot;context&quot;:&quot;resources&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;security_link_resources_navbar&quot;}" href="/resources/articles/security"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;software_development&quot;,&quot;context&quot;:&quot;resources&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;software_development_link_resources_navbar&quot;}" href="/resources/articles/software-development"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;view_all&quot;,&quot;context&quot;:&quot;resources&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;view_all_link_resources_navbar&quot;}" href="/resources/articles"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary Link--external" target="_blank" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;learning_pathways&quot;,&quot;context&quot;:&quot;resources&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;learning_pathways_link_resources_navbar&quot;}" href="https://resources.github.com/learn/pathways"> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M3.75 2h3.5a.75.75 0 0 1 0 1.5h-3.5a.25.25 0 0 0-.25.25v8.5c0 .*************.25h8.5a.25.25 0 0 0 .25-.25v-3.5a.75.75 0 0 1 1.5 0v3.5A1.75 1.75 0 0 1 12.25 14h-8.5A1.75 1.75 0 0 1 2 12.25v-8.5C2 2.784 2.784 2 3.75 2Zm6.854-1h4.146a.25.25 0 0 1 .25.25v4.146a.25.25 0 0 1-.427.177L13.03 4.03 9.28 7.78a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042l3.75-3.75-1.543-1.543A.25.25 0 0 1 10.604 1Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary Link--external" target="_blank" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;events_amp_webinars&quot;,&quot;context&quot;:&quot;resources&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;events_amp_webinars_link_resources_navbar&quot;}" href="https://resources.github.com"> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M3.75 2h3.5a.75.75 0 0 1 0 1.5h-3.5a.25.25 0 0 0-.25.25v8.5c0 .*************.25h8.5a.25.25 0 0 0 .25-.25v-3.5a.75.75 0 0 1 1.5 0v3.5A1.75 1.75 0 0 1 12.25 14h-8.5A1.75 1.75 0 0 1 2 12.25v-8.5C2 2.784 2.784 2 3.75 2Zm6.854-1h4.146a.25.25 0 0 1 .25.25v4.146a.25.25 0 0 1-.427.177L13.03 4.03 9.28 7.78a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042l3.75-3.75-1.543-1.543A.25.25 0 0 1 10.604 1Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;ebooks_amp_whitepapers&quot;,&quot;context&quot;:&quot;resources&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;ebooks_amp_whitepapers_link_resources_navbar&quot;}" href="https://github.com/resources/whitepapers"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;customer_stories&quot;,&quot;context&quot;:&quot;resources&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;customer_stories_link_resources_navbar&quot;}" href="https://github.com/customer-stories"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary Link--external" target="_blank" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;partners&quot;,&quot;context&quot;:&quot;resources&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;partners_link_resources_navbar&quot;}" href="https://partner.github.com"> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M3.75 2h3.5a.75.75 0 0 1 0 1.5h-3.5a.25.25 0 0 0-.25.25v8.5c0 .*************.25h8.5a.25.25 0 0 0 .25-.25v-3.5a.75.75 0 0 1 1.5 0v3.5A1.75 1.75 0 0 1 12.25 14h-8.5A1.75 1.75 0 0 1 2 12.25v-8.5C2 2.784 2.784 2 3.75 2Zm6.854-1h4.146a.25.25 0 0 1 .25.25v4.146a.25.25 0 0 1-.427.177L13.03 4.03 9.28 7.78a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042l3.75-3.75-1.543-1.543A.25.25 0 0 1 10.604 1Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;executive_insights&quot;,&quot;context&quot;:&quot;resources&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;executive_insights_link_resources_navbar&quot;}" href="https://github.com/solutions/executive-insights"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;github_sponsors&quot;,&quot;context&quot;:&quot;open_source&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;github_sponsors_link_open_source_navbar&quot;}" href="/sponsors"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;the_readme_project&quot;,&quot;context&quot;:&quot;open_source&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;the_readme_project_link_open_source_navbar&quot;}" href="https://github.com/readme"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;topics&quot;,&quot;context&quot;:&quot;open_source&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;topics_link_open_source_navbar&quot;}" href="https://github.com/topics"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;trending&quot;,&quot;context&quot;:&quot;open_source&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;trending_link_open_source_navbar&quot;}" href="https://github.com/trending"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;collections&quot;,&quot;context&quot;:&quot;open_source&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;collections_link_open_source_navbar&quot;}" href="https://github.com/collections"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;enterprise_platform&quot;,&quot;context&quot;:&quot;enterprise&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;enterprise_platform_link_enterprise_navbar&quot;}" href="/enterprise"> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M11.063 1.456a1.749 1.749 0 0 1 1.874 0l8.383 5.316a1.751 1.751 0 0 1 0 2.956l-8.383 5.316a1.749 1.749 0 0 1-1.874 0L2.68 9.728a1.751 1.751 0 0 1 0-2.956Zm1.071 1.267a.25.25 0 0 0-.268 0L3.483 8.039a.25.25 0 0 0 0 .422l8.383 5.316a.25.25 0 0 0 .268 0l8.383-5.316a.25.25 0 0 0 0-.422Z"></path><path d="M1.867 12.324a.75.75 0 0 1 1.035-.232l8.964 5.685a.25.25 0 0 0 .268 0l8.964-5.685a.75.75 0 0 1 .804 1.267l-8.965 5.685a1.749 1.749 0 0 1-1.874 0l-8.965-5.685a.75.75 0 0 1-.231-1.035Z"></path><path d="M1.867 16.324a.75.75 0 0 1 1.035-.232l8.964 5.685a.25.25 0 0 0 .268 0l8.964-5.685a.75.75 0 0 1 .804 1.267l-8.965 5.685a1.749 1.749 0 0 1-1.874 0l-8.965-5.685a.75.75 0 0 1-.231-1.035Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;github_advanced_security&quot;,&quot;context&quot;:&quot;enterprise&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;github_advanced_security_link_enterprise_navbar&quot;}" href="https://github.com/security/advanced-security"> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;copilot_for_business&quot;,&quot;context&quot;:&quot;enterprise&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;copilot_for_business_link_enterprise_navbar&quot;}" href="/features/copilot/copilot-business"> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M23.922 16.992c-.861 1.495-5.859 5.023-11.922 5.023-6.063 0-11.061-3.528-11.922-5.023A.641.641 0 0 1 0 16.736v-2.869a.841.841 0 0 1 .053-.22c.372-.935 1.347-2.292 2.605-2.656.167-.429.414-1.055.644-1.517a10.195 10.195 0 0 1-.052-1.086c0-1.331.282-2.499 1.132-3.368.397-.406.89-.717 1.474-.952 1.399-1.136 3.392-2.093 6.122-2.093 2.731 0 4.767.957 6.166 2.093.584.235 1.077.546 1.474.952.85.869 1.132 2.037 1.132 3.368 0 .368-.014.733-.052 1.086.23.462.477 1.088.644 1.517 1.258.364 2.233 1.721 2.605 2.656a.832.832 0 0 1 .053.22v2.869a.641.641 0 0 1-.078.256ZM12.172 11h-.344a4.323 4.323 0 0 1-.355.508C10.703 12.455 9.555 13 7.965 13c-1.725 0-2.989-.359-3.782-1.259a2.005 2.005 0 0 1-.085-.104L4 11.741v6.585c1.435.779 4.514 2.179 8 2.179 3.486 0 6.565-1.4 8-2.179v-6.585l-.098-.104s-.033.045-.085.104c-.793.9-2.057 1.259-3.782 1.259-1.59 0-2.738-.545-3.508-1.492a4.323 4.323 0 0 1-.355-.508h-.016.016Zm.641-2.935c.136 1.057.403 1.913.878 2.497.442.544 1.134.938 2.344.938 1.573 0 2.292-.337 2.657-.751.384-.435.558-1.15.558-2.361 0-1.14-.243-1.847-.705-2.319-.477-.488-1.319-.862-2.824-1.025-1.487-.161-2.192.138-2.533.529-.269.307-.437.808-.438 1.578v.021c0 .265.021.562.063.893Zm-1.626 0c.042-.331.063-.628.063-.894v-.02c-.001-.77-.169-1.271-.438-1.578-.341-.391-1.046-.69-2.533-.529-1.505.163-2.347.537-2.824 1.025-.462.472-.705 1.179-.705 2.319 0 1.211.175 1.926.558 2.361.365.414 1.084.751 2.657.751 1.21 0 1.902-.394 2.344-.938.475-.584.742-1.44.878-2.497Z"></path><path d="M14.5 14.25a1 1 0 0 1 1 1v2a1 1 0 0 1-2 0v-2a1 1 0 0 1 1-1Zm-5 0a1 1 0 0 1 1 1v2a1 1 0 0 1-2 0v-2a1 1 0 0 1 1-1Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;premium_support&quot;,&quot;context&quot;:&quot;enterprise&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;premium_support_link_enterprise_navbar&quot;}" href="/premium-support"> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M1.75 1h12.5c.966 0 1.75.784 1.75 1.75v9.5A1.75 1.75 0 0 1 14.25 14H8.061l-2.574 2.573A1.458 1.458 0 0 1 3 15.543V14H1.75A1.75 1.75 0 0 1 0 12.25v-9.5C0 1.784.784 1 1.75 1ZM1.5 2.75v9.5c0 .*************.25h2a.75.75 0 0 1 .75.75v2.19l2.72-2.72a.749.749 0 0 1 .53-.22h6.5a.25.25 0 0 0 .25-.25v-9.5a.25.25 0 0 0-.25-.25H1.75a.25.25 0 0 0-.25.25Z"></path><path d="M22.5 8.75a.25.25 0 0 0-.25-.25h-3.5a.75.75 0 0 1 0-1.5h3.5c.966 0 1.75.784 1.75 1.75v9.5A1.75 1.75 0 0 1 22.25 20H21v1.543a1.457 1.457 0 0 1-2.487 1.03L15.939 20H10.75A1.75 1.75 0 0 1 9 18.25v-1.465a.75.75 0 0 1 1.5 0v1.465c0 .*************.25h5.5a.75.75 0 0 1 .53.22l2.72 2.72v-2.19a.75.75 0 0 1 .75-.75h2a.25.25 0 0 0 .25-.25v-9.5Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<a class="HeaderMenu-link no-underline px-0 px-lg-2 py-3 py-lg-2 d-block d-lg-inline-block" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;pricing&quot;,&quot;context&quot;:&quot;global&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;pricing_link_global_navbar&quot;}" href="https://github.com/pricing">Pricing</a> (Status: 403) [Size: 277]\n\n\x1b[2K/<input id="query-builder-test" name="query-builder-test" value="" autocomplete="off" type="text" role="combobox" spellcheck="false" aria-expanded="false" aria-describedby="validation-d6389e6f-5ef6-41c0-84f5-dd5ab1fd436b" data-target="query-builder.input" data-action=" (Status: 403) [Size: 277]\n\n\x1b[2K/" variant="small" hidden="hidden" type="button" data-view-component="true" class="Button Button--iconOnly Button--invisible Button--medium mr-1 px-2 py-0 d-flex flex-items-center rounded-1 color-fg-muted">  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-x-circle-fill Button-visual"> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M2.343 13.657A8 8 0 1 1 13.658 2.343 8 8 0 0 1 2.343 13.657ZM6.03 4.97a.751.751 0 0 0-1.042.018.751.751 0 0 0-.018 1.042L6.94 8 4.97 9.97a.749.749 0 0 0 .326 1.275.749.749 0 0 0 .734-.215L8 9.06l1.97 1.97a.749.749 0 0 0 1.275-.326.749.749 0 0 0-.215-.734L9.06 8l1.97-1.97a.749.749 0 0 0-.326-1.275.749.749 0 0 0-.734.215L8 6.94Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="m11.28 3.22 4.25 4.25a.75.75 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.275-.326.749.749 0 0 1 .215-.734L13.94 8l-3.72-3.72a.749.749 0 0 1 .326-1.275.749.749 0 0 1 .734.215Zm-6.56 0a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042L2.06 8l3.72 3.72a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215L.47 8.53a.75.75 0 0 1 0-1.06Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M4 1.75C4 .784 4.784 0 5.75 0h5.586c.464 0 .909.184 1.237.513l2.914 2.914c.329.328.513.773.513 1.237v8.586A1.75 1.75 0 0 1 14.25 15h-9a.75.75 0 0 1 0-1.5h9a.25.25 0 0 0 .25-.25V6h-2.75A1.75 1.75 0 0 1 10 4.25V1.5H5.75a.25.25 0 0 0-.25.25v2.5a.75.75 0 0 1-1.5 0Zm1.72 4.97a.75.75 0 0 1 1.06 0l2 2a.75.75 0 0 1 0 1.06l-2 2a.749.749 0 0 1-1.275-.326.749.749 0 0 1 .215-.734l1.47-1.47-1.47-1.47a.75.75 0 0 1 0-1.06ZM3.28 7.78 1.81 9.25l1.47 1.47a.751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018l-2-2a.75.75 0 0 1 0-1.06l2-2a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042Zm8.22-6.218V4.25c0 .*************.25h2.688l-.011-.013-2.914-2.914-.013-.011Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="m.427 1.927 1.215 1.215a8.002 8.002 0 1 1-1.6 5.685.75.75 0 1 1 1.493-.154 6.5 6.5 0 1 0 1.18-4.458l1.358 1.358A.25.25 0 0 1 3.896 6H.25A.25.25 0 0 1 0 5.75V2.104a.25.25 0 0 1 .427-.177ZM7.75 4a.75.75 0 0 1 .75.75v2.992l2.028.812a.75.75 0 0 1-.557 1.392l-2.5-1A.751.751 0 0 1 7 8.25v-3.5A.75.75 0 0 1 7.75 4Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M2 2.5A2.5 2.5 0 0 1 4.5 0h8.75a.75.75 0 0 1 .75.75v12.5a.75.75 0 0 1-.75.75h-2.5a.75.75 0 0 1 0-1.5h1.75v-2h-8a1 1 0 0 0-.714 1.7.75.75 0 1 1-1.072 1.05A2.495 2.495 0 0 1 2 11.5Zm10.5-1h-8a1 1 0 0 0-1 1v6.708A2.486 2.486 0 0 1 4.5 9h8ZM5 12.25a.25.25 0 0 1 .25-.25h3.5a.25.25 0 0 1 .25.25v3.25a.25.25 0 0 1-.4.2l-1.45-1.087a.249.249 0 0 0-.3 0L5.4 15.7a.25.25 0 0 1-.4-.2Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M11 1.75V3h2.25a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1 0-1.5H5V1.75C5 .784 5.784 0 6.75 0h2.5C10.216 0 11 .784 11 1.75ZM4.496 6.675l.66 6.6a.25.25 0 0 0 .249.225h5.19a.25.25 0 0 0 .249-.225l.66-6.6a.75.75 0 0 1 1.492.149l-.66 6.6A1.748 1.748 0 0 1 10.595 15h-5.19a1.75 1.75 0 0 1-1.741-1.575l-.66-6.6a.75.75 0 1 1 1.492-.15ZM6.5 1.75V3h3V1.75a.25.25 0 0 0-.25-.25h-2.5a.25.25 0 0 0-.25.25Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M2 5.5a3.5 3.5 0 1 1 5.898 2.549 5.508 5.508 0 0 1 3.034 4.084.75.75 0 1 1-1.482.235 4 4 0 0 0-7.9 0 .75.75 0 0 1-1.482-.236A5.507 5.507 0 0 1 3.102 8.05 3.493 3.493 0 0 1 2 5.5ZM11 4a3.001 3.001 0 0 1 2.22 5.018 5.01 5.01 0 0 1 2.56 3.012.749.749 0 0 1-.885.954.752.752 0 0 1-.549-.514 3.507 3.507 0 0 0-2.522-2.372.75.75 0 0 1-.574-.73v-.352a.75.75 0 0 1 .416-.672A1.5 1.5 0 0 0 11 5.5.75.75 0 0 1 11 4Zm-5.5-.5a2 2 0 1 0-.001 3.999A2 2 0 0 0 5.5 3.5Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M1.75 0h12.5C15.216 0 16 .784 16 1.75v12.5A1.75 1.75 0 0 1 14.25 16H1.75A1.75 1.75 0 0 1 0 14.25V1.75C0 .784.784 0 1.75 0ZM1.5 1.75v12.5c0 .*************.25h12.5a.25.25 0 0 0 .25-.25V1.75a.25.25 0 0 0-.25-.25H1.75a.25.25 0 0 0-.25.25ZM11.75 3a.75.75 0 0 1 .75.75v7.5a.75.75 0 0 1-1.5 0v-7.5a.75.75 0 0 1 .75-.75Zm-8.25.75a.75.75 0 0 1 1.5 0v5.5a.75.75 0 0 1-1.5 0ZM8 3a.75.75 0 0 1 .75.75v3.5a.75.75 0 0 1-1.5 0v-3.5A.75.75 0 0 1 8 3Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M11.013 1.427a1.75 1.75 0 0 1 2.474 0l1.086 1.086a1.75 1.75 0 0 1 0 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 0 1-.927-.928l.929-3.25c.081-.286.235-.547.445-.758l8.61-8.61Zm.176 4.823L9.75 4.81l-6.286 6.287a.253.253 0 0 0-.064.108l-.558 1.953 1.953-.558a.253.253 0 0 0 .108-.064Zm1.238-3.763a.25.25 0 0 0-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 0 0 0-.354Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M7.998 15.035c-4.562 0-7.873-2.914-7.998-3.749V9.338c.085-.628.677-1.686 1.588-2.065.013-.07.024-.143.036-.218.029-.183.06-.384.126-.612-.201-.508-.254-1.084-.254-1.656 0-.87.128-1.769.693-2.484.579-.733 1.494-1.124 2.724-1.261 1.206-.134 2.262.034 2.944.765.05.053.096.108.139.165.044-.057.094-.112.143-.165.682-.731 1.738-.899 2.944-.765 1.23.137 2.145.528 2.724 1.261.566.715.693 1.614.693 2.484 0 .572-.053 1.148-.254 1.656.066.228.098.429.126.612.012.076.024.148.037.218.924.385 1.522 1.471 1.591 2.095v1.872c0 .766-3.351 3.795-8.002 3.795Zm0-1.485c2.28 0 4.584-1.11 5.002-1.433V7.862l-.023-.116c-.49.21-1.075.291-1.727.291-1.146 0-2.059-.327-2.71-.991A3.222 3.222 0 0 1 8 6.303a3.24 3.24 0 0 1-.544.743c-.65.664-1.563.991-2.71.991-.652 0-1.236-.081-1.727-.291l-.023.116v4.255c.419.323 2.722 1.433 5.002 1.433ZM6.762 2.83c-.193-.206-.637-.413-1.682-.297-1.019.113-1.479.404-1.713.7-.247.312-.369.789-.369 1.554 0 .793.129 1.171.308 1.371.162.181.519.379 1.442.379.853 0 1.339-.235 1.638-.54.315-.322.527-.827.617-1.553.117-.935-.037-1.395-.241-1.614Zm4.155-.297c-1.044-.116-1.488.091-1.681.297-.204.219-.359.679-.242 1.614.091.726.303 1.231.618 1.553.299.305.784.54 1.638.54.922 0 1.28-.198 1.442-.379.179-.2.308-.578.308-1.371 0-.765-.123-1.242-.37-1.554-.233-.296-.693-.587-1.713-.7Z"></path><path d="M6.25 9.037a.75.75 0 0 1 .75.75v1.501a.75.75 0 0 1-1.5 0V9.787a.75.75 0 0 1 .75-.75Zm4.25.75v1.501a.75.75 0 0 1-1.5 0V9.787a.75.75 0 0 1 1.5 0Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M16 11.24c0 .112-.072.274-.21.467L13 9.688V7.862l-.023-.116c-.49.21-1.075.291-1.727.291-.198 0-.388-.009-.571-.029L6.833 5.226a4.01 4.01 0 0 0 .17-.782c.117-.935-.037-1.395-.241-1.614-.193-.206-.637-.413-1.682-.297-.683.076-1.115.231-1.395.415l-1.257-.91c.579-.564 1.413-.877 2.485-.996 1.206-.134 2.262.034 2.944.765.05.053.096.108.139.165.044-.057.094-.112.143-.165.682-.731 1.738-.899 2.944-.765 1.23.137 2.145.528 2.724 1.261.566.715.693 1.614.693 2.484 0 .572-.053 1.148-.254 1.656.066.228.098.429.126.612.012.076.024.148.037.218.924.385 1.522 1.471 1.591 2.095Zm-5.083-8.707c-1.044-.116-1.488.091-1.681.297-.204.219-.359.679-.242 1.614.091.726.303 1.231.618 1.553.299.305.784.54 1.638.54.922 0 1.28-.198 1.442-.379.179-.2.308-.578.308-1.371 0-.765-.123-1.242-.37-1.554-.233-.296-.693-.587-1.713-.7Zm2.511 11.074c-1.393.776-3.272 1.428-5.43 1.428-4.562 0-7.873-2.914-7.998-3.749V9.338c.085-.628.677-1.686 1.588-2.065.013-.07.024-.143.036-.218.029-.183.06-.384.126-.612-.18-.455-.241-.963-.252-1.475L.31 4.107A.747.747 0 0 1 0 3.509V3.49a.748.748 0 0 1 .625-.73c.156-.026.306.047.435.139l14.667 10.578a.592.592 0 0 1 .227.264.752.752 0 0 1 .046.249v.022a.75.75 0 0 1-1.19.596Zm-1.367-.991L5.635 7.964a5.128 5.128 0 0 1-.889.073c-.652 0-1.236-.081-1.727-.291l-.023.116v4.255c.419.323 2.722 1.433 5.002 1.433 1.539 0 3.089-.505 4.063-.934Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M0 1.75C0 .784.784 0 1.75 0h3.5C6.216 0 7 .784 7 1.75v3.5A1.75 1.75 0 0 1 5.25 7H4v4a1 1 0 0 0 1 1h4v-1.25C9 9.784 9.784 9 10.75 9h3.5c.966 0 1.75.784 1.75 1.75v3.5A1.75 1.75 0 0 1 14.25 16h-3.5A1.75 1.75 0 0 1 9 14.25v-.75H5A2.5 2.5 0 0 1 2.5 11V7h-.75A1.75 1.75 0 0 1 0 5.25Zm1.75-.25a.25.25 0 0 0-.25.25v3.5c0 .*************.25h3.5a.25.25 0 0 0 .25-.25v-3.5a.25.25 0 0 0-.25-.25Zm9 9a.25.25 0 0 0-.25.25v3.5c0 .*************.25h3.5a.25.25 0 0 0 .25-.25v-3.5a.25.25 0 0 0-.25-.25Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M0 1.75A.75.75 0 0 1 .75 1h4.253c1.227 0 2.317.59 3 1.501A3.743 3.743 0 0 1 11.006 1h4.245a.75.75 0 0 1 .75.75v10.5a.75.75 0 0 1-.75.75h-4.507a2.25 2.25 0 0 0-1.591.659l-.622.621a.75.75 0 0 1-1.06 0l-.622-.621A2.25 2.25 0 0 0 5.258 13H.75a.75.75 0 0 1-.75-.75Zm7.251 10.324.004-5.073-.002-2.253A2.25 2.25 0 0 0 5.003 2.5H1.5v9h3.757a3.75 3.75 0 0 1 1.994.574ZM8.755 4.75l-.004 7.322a3.752 3.752 0 0 1 1.992-.572H14.5v-9h-3.495a2.25 2.25 0 0 0-2.25 2.25Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M1.75 1h12.5c.966 0 1.75.784 1.75 1.75v8.5A1.75 1.75 0 0 1 14.25 13H8.061l-2.574 2.573A1.458 1.458 0 0 1 3 14.543V13H1.75A1.75 1.75 0 0 1 0 11.25v-8.5C0 1.784.784 1 1.75 1ZM1.5 2.75v8.5c0 .*************.25h2a.75.75 0 0 1 .75.75v2.19l2.72-2.72a.749.749 0 0 1 .53-.22h6.5a.25.25 0 0 0 .25-.25v-8.5a.25.25 0 0 0-.25-.25H1.75a.25.25 0 0 0-.25.25Zm5.28 1.72a.75.75 0 0 1 0 1.06L5.31 7l1.47 1.47a.751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018l-2-2a.75.75 0 0 1 0-1.06l2-2a.75.75 0 0 1 1.06 0Zm2.44 0a.75.75 0 0 1 1.06 0l2 2a.75.75 0 0 1 0 1.06l-2 2a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042L10.69 7 9.22 5.53a.75.75 0 0 1 0-1.06Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M0 11.25c0-.966.784-1.75 1.75-1.75h12.5c.966 0 1.75.784 1.75 1.75v3A1.75 1.75 0 0 1 14.25 16H1.75A1.75 1.75 0 0 1 0 14.25Zm2-9.5C2 .784 2.784 0 3.75 0h8.5C13.216 0 14 .784 14 1.75v5a1.75 1.75 0 0 1-1.75 1.75h-8.5A1.75 1.75 0 0 1 2 6.75Zm1.75-.25a.25.25 0 0 0-.25.25v5c0 .*************.25h8.5a.25.25 0 0 0 .25-.25v-5a.25.25 0 0 0-.25-.25Zm-2 9.5a.25.25 0 0 0-.25.25v3c0 .*************.25h12.5a.25.25 0 0 0 .25-.25v-3a.25.25 0 0 0-.25-.25Z"></path><path d="M7 12.75a.75.75 0 0 1 .75-.75h4.5a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1-.75-.75Zm-4 0a.75.75 0 0 1 .75-.75h.5a.75.75 0 0 1 0 1.5h-.5a.75.75 0 0 1-.75-.75Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M1 2.75C1 1.784 1.784 1 2.75 1h10.5c.966 0 1.75.784 1.75 1.75v7.5A1.75 1.75 0 0 1 13.25 12H9.06l-2.573 2.573A1.458 1.458 0 0 1 4 13.543V12H2.75A1.75 1.75 0 0 1 1 10.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h2a.75.75 0 0 1 .75.75v2.19l2.72-2.72a.749.749 0 0 1 .53-.22h4.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M1.75 1h8.5c.966 0 1.75.784 1.75 1.75v5.5A1.75 1.75 0 0 1 10.25 10H7.061l-2.574 2.573A1.458 1.458 0 0 1 2 11.543V10h-.25A1.75 1.75 0 0 1 0 8.25v-5.5C0 1.784.784 1 1.75 1ZM1.5 2.75v5.5c0 .*************.25h1a.75.75 0 0 1 .75.75v2.19l2.72-2.72a.749.749 0 0 1 .53-.22h3.5a.25.25 0 0 0 .25-.25v-5.5a.25.25 0 0 0-.25-.25h-8.5a.25.25 0 0 0-.25.25Zm13 2a.25.25 0 0 0-.25-.25h-.5a.75.75 0 0 1 0-1.5h.5c.966 0 1.75.784 1.75 1.75v5.5A1.75 1.75 0 0 1 14.25 12H14v1.543a1.458 1.458 0 0 1-2.487 1.03L9.22 12.28a.749.749 0 0 1 .326-1.275.749.749 0 0 1 .734.215l2.22 2.22v-2.19a.75.75 0 0 1 .75-.75h1a.25.25 0 0 0 .25-.25Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M1.75 16A1.75 1.75 0 0 1 0 14.25V1.75C0 .784.784 0 1.75 0h8.5C11.216 0 12 .784 12 1.75v12.5c0 .085-.006.168-.018.25h2.268a.25.25 0 0 0 .25-.25V8.285a.25.25 0 0 0-.111-.208l-1.055-.703a.749.749 0 1 1 .832-1.248l1.055.703c.487.325.779.871.779 1.456v5.965A1.75 1.75 0 0 1 14.25 16h-3.5a.766.766 0 0 1-.197-.026c-.099.017-.2.026-.303.026h-3a.75.75 0 0 1-.75-.75V14h-1v1.25a.75.75 0 0 1-.75.75Zm-.25-1.75c0 .*************.25H4v-1.25a.75.75 0 0 1 .75-.75h2.5a.75.75 0 0 1 .75.75v1.25h2.25a.25.25 0 0 0 .25-.25V1.75a.25.25 0 0 0-.25-.25h-8.5a.25.25 0 0 0-.25.25ZM3.75 6h.5a.75.75 0 0 1 0 1.5h-.5a.75.75 0 0 1 0-1.5ZM3 3.75A.75.75 0 0 1 3.75 3h.5a.75.75 0 0 1 0 1.5h-.5A.75.75 0 0 1 3 3.75Zm4 3A.75.75 0 0 1 7.75 6h.5a.75.75 0 0 1 0 1.5h-.5A.75.75 0 0 1 7 6.75ZM7.75 3h.5a.75.75 0 0 1 0 1.5h-.5a.75.75 0 0 1 0-1.5ZM3 9.75A.75.75 0 0 1 3.75 9h.5a.75.75 0 0 1 0 1.5h-.5A.75.75 0 0 1 3 9.75ZM7.75 9h.5a.75.75 0 0 1 0 1.5h-.5a.75.75 0 0 1 0-1.5Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M14.064 0h.186C15.216 0 16 .784 16 1.75v.186a8.752 8.752 0 0 1-2.564 6.186l-.458.459c-.314.314-.641.616-.979.904v3.207c0 .608-.315 1.172-.833 1.49l-2.774 1.707a.749.749 0 0 1-1.11-.418l-.954-3.102a1.214 1.214 0 0 1-.145-.125L3.754 9.816a1.218 1.218 0 0 1-.124-.145L.528 8.717a.749.749 0 0 1-.418-1.11l1.71-2.774A1.748 1.748 0 0 1 3.31 4h3.204c.288-.338.59-.665.904-.979l.459-.458A8.749 8.749 0 0 1 14.064 0ZM8.938 3.623h-.002l-.458.458c-.76.76-1.437 1.598-2.02 2.5l-1.5 2.317 2.143 2.143 2.317-1.5c.902-.583 1.74-1.26 2.499-2.02l.459-.458a7.25 7.25 0 0 0 2.123-5.127V1.75a.25.25 0 0 0-.25-.25h-.186a7.249 7.249 0 0 0-5.125 2.123ZM3.56 14.56c-.732.732-2.334 1.045-3.005 1.148a.234.234 0 0 1-.201-.064.234.234 0 0 1-.064-.201c.103-.671.416-2.273 1.15-3.003a1.502 1.502 0 1 1 2.12 2.12Zm6.94-3.935c-.088.06-.177.118-.266.175l-2.35 1.521.548 1.783 1.949-1.2a.25.25 0 0 0 .119-.213ZM3.678 8.116 5.2 5.766c.058-.09.117-.178.176-.266H3.309a.25.25 0 0 0-.213.119l-1.2 1.95ZM12 5a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="m8 14.25.345.666a.75.75 0 0 1-.69 0l-.008-.004-.018-.01a7.152 7.152 0 0 1-.31-.17 22.055 22.055 0 0 1-3.434-2.414C2.045 10.731 0 8.35 0 5.5 0 2.836 2.086 1 4.25 1 5.797 1 7.153 1.802 8 3.02 8.847 1.802 10.203 1 11.75 1 13.914 1 16 2.836 16 5.5c0 2.85-2.045 5.231-3.885 6.818a22.066 22.066 0 0 1-3.744 2.584l-.018.01-.006.003h-.002ZM4.25 2.5c-1.336 0-2.75 1.164-2.75 3 0 2.15 1.58 4.144 3.365 5.682A20.58 20.58 0 0 0 8 13.393a20.58 20.58 0 0 0 3.135-2.211C12.92 9.644 14.5 7.65 14.5 5.5c0-1.836-1.414-3-2.75-3-1.373 0-2.609.986-3.029 2.456a.749.749 0 0 1-1.442 0C6.859 3.486 5.623 2.5 4.25 2.5Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="m8.533.133 5.25 1.68A1.75 1.75 0 0 1 15 3.48V7c0 1.566-.32 3.182-1.303 4.682-.983 1.498-2.585 2.813-5.032 3.855a1.697 1.697 0 0 1-1.33 0c-2.447-1.042-4.049-2.357-5.032-3.855C1.32 10.182 1 8.566 1 7V3.48a1.75 1.75 0 0 1 1.217-1.667l5.25-1.68a1.748 1.748 0 0 1 1.066 0Zm-.61 1.429.001.001-5.25 1.68a.251.251 0 0 0-.174.237V7c0 1.36.275 2.666 1.057 3.859.784 1.194 2.121 2.342 4.366 3.298a.196.196 0 0 0 .154 0c2.245-.957 3.582-2.103 4.366-3.297C13.225 9.666 13.5 8.358 13.5 7V3.48a.25.25 0 0 0-.174-.238l-5.25-1.68a.25.25 0 0 0-.153 0ZM11.28 6.28l-3.5 3.5a.75.75 0 0 1-1.06 0l-1.5-1.5a.749.749 0 0 1 .326-1.275.749.749 0 0 1 .734.215l.97.97 2.97-2.97a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M1.75 1h12.5c.966 0 1.75.784 1.75 1.75v4c0 .372-.116.717-.314 1 .198.283.314.628.314 1v4a1.75 1.75 0 0 1-1.75 1.75H1.75A1.75 1.75 0 0 1 0 12.75v-4c0-.358.109-.707.314-1a1.739 1.739 0 0 1-.314-1v-4C0 1.784.784 1 1.75 1ZM1.5 2.75v4c0 .*************.25h12.5a.25.25 0 0 0 .25-.25v-4a.25.25 0 0 0-.25-.25H1.75a.25.25 0 0 0-.25.25Zm.25 5.75a.25.25 0 0 0-.25.25v4c0 .*************.25h12.5a.25.25 0 0 0 .25-.25v-4a.25.25 0 0 0-.25-.25ZM7 4.75A.75.75 0 0 1 7.75 4h4.5a.75.75 0 0 1 0 1.5h-4.5A.75.75 0 0 1 7 4.75ZM7.75 10h4.5a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1 0-1.5ZM3 4.75A.75.75 0 0 1 3.75 4h.5a.75.75 0 0 1 0 1.5h-.5A.75.75 0 0 1 3 4.75ZM3.75 10h.5a.75.75 0 0 1 0 1.5h-.5a.75.75 0 0 1 0-1.5Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M3.75 0h8.5C13.216 0 14 .784 14 1.75v12.5A1.75 1.75 0 0 1 12.25 16h-8.5A1.75 1.75 0 0 1 2 14.25V1.75C2 .784 2.784 0 3.75 0ZM3.5 1.75v12.5c0 .*************.25h8.5a.25.25 0 0 0 .25-.25V1.75a.25.25 0 0 0-.25-.25h-8.5a.25.25 0 0 0-.25.25ZM8 13a1 1 0 1 1 0-2 1 1 0 0 1 0 2Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="m8.878.392 5.25 3.045c.54.314.872.89.872 1.514v6.098a1.75 1.75 0 0 1-.872 1.514l-5.25 3.045a1.75 1.75 0 0 1-1.756 0l-5.25-3.045A1.75 1.75 0 0 1 1 11.049V4.951c0-.624.332-1.201.872-1.514L7.122.392a1.75 1.75 0 0 1 1.756 0ZM7.875 1.69l-4.63 2.685L8 7.133l4.755-2.758-4.63-2.685a.248.248 0 0 0-.25 0ZM2.5 5.677v5.372c0 .09.047.171.125.216l4.625 2.683V8.432Zm6.25 8.271 4.625-2.683a.25.25 0 0 0 .125-.216V5.677L8.75 8.432Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M2 2.75A2.75 2.75 0 0 1 4.75 0c.983 0 1.873.42 2.57 1.232.268.318.497.668.68 1.042.183-.375.411-.725.68-1.044C9.376.42 10.266 0 11.25 0a2.75 2.75 0 0 1 2.45 4h.55c.966 0 1.75.784 1.75 1.75v2c0 .698-.409 1.301-1 1.582v4.918A1.75 1.75 0 0 1 13.25 16H2.75A1.75 1.75 0 0 1 1 14.25V9.332C.409 9.05 0 8.448 0 7.75v-2C0 4.784.784 4 1.75 4h.55c-.192-.375-.3-.8-.3-1.25ZM7.25 9.5H2.5v4.75c0 .*************.25h4.5Zm1.5 0v5h4.5a.25.25 0 0 0 .25-.25V9.5Zm0-4V8h5.5a.25.25 0 0 0 .25-.25v-2a.25.25 0 0 0-.25-.25Zm-7 0a.25.25 0 0 0-.25.25v2c0 .*************.25h5.5V5.5h-5.5Zm3-4a1.25 1.25 0 0 0 0 2.5h2.309c-.233-.818-.542-1.401-.878-1.793-.43-.502-.915-.707-1.431-.707ZM8.941 4h2.309a1.25 1.25 0 0 0 0-2.5c-.516 0-1 .205-1.43.707-.337.392-.646.975-.879 1.793Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M0 1.75C0 .784.784 0 1.75 0h12.5C15.216 0 16 .784 16 1.75v12.5A1.75 1.75 0 0 1 14.25 16H1.75A1.75 1.75 0 0 1 0 14.25Zm1.75-.25a.25.25 0 0 0-.25.25v12.5c0 .*************.25h12.5a.25.25 0 0 0 .25-.25V1.75a.25.25 0 0 0-.25-.25Zm7.47 3.97a.75.75 0 0 1 1.06 0l2 2a.75.75 0 0 1 0 1.06l-2 2a.749.749 0 0 1-1.275-.326.749.749 0 0 1 .215-.734L10.69 8 9.22 6.53a.75.75 0 0 1 0-1.06ZM6.78 6.53 5.31 8l1.47 1.47a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215l-2-2a.75.75 0 0 1 0-1.06l2-2a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M14.25 1c.966 0 1.75.784 1.75 1.75v7.5A1.75 1.75 0 0 1 14.25 12h-3.727c.099 1.041.52 1.872 1.292 2.757A.752.752 0 0 1 11.25 16h-6.5a.75.75 0 0 1-.565-1.243c.772-.885 1.192-1.716 1.292-2.757H1.75A1.75 1.75 0 0 1 0 10.25v-7.5C0 1.784.784 1 1.75 1ZM1.75 2.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h12.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25ZM9.018 12H6.982a5.72 5.72 0 0 1-.765 2.5h3.566a5.72 5.72 0 0 1-.765-2.5Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<button data-close-dialog-id="feedback-dialog" aria-label="Close" aria-label="Close" type="button" data-view-component="true" class="close-button Overlay-closeButton"><svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-x"> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M3.72 3.72a.75.75 0 0 1 1.06 0L8 6.94l3.22-3.22a.749.749 0 0 1 1.275.326.749.749 0 0 1-.215.734L9.06 8l3.22 3.22a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215L8 9.06l-3.22 3.22a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042L6.94 8 3.72 4.78a.75.75 0 0 1 0-1.06Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<button data-close-dialog-id="custom-scopes-dialog" aria-label="Close" aria-label="Close" type="button" data-view-component="true" class="close-button Overlay-closeButton"><svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-x"> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M3.72 3.72a.75.75 0 0 1 1.06 0L8 6.94l3.22-3.22a.749.749 0 0 1 1.275.326.749.749 0 0 1-.215.734L9.06 8l3.22 3.22a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215L8 9.06l-3.22 3.22a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042L6.94 8 3.72 4.78a.75.75 0 0 1 0-1.06Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;site header menu&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;SIGN_UP&quot;,&quot;originating_url&quot;:&quot;https://github.com/xmendez/wfuzz/blob/master/wordlist/general/big.txt&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="f96a1f8960da60bf2ea2f21c2791f6ecbdb6843a1ea80e471f0980e9c68a8c3c" (Status: 403) [Size: 277]\n\n\x1b[2K/data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;site header menu&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;SIGN_UP&quot;,&quot;originating_url&quot;:&quot;https://github.com/xmendez/wfuzz/blob/master/wordlist/general/big.txt&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="f96a1f8960da60bf2ea2f21c2791f6ecbdb6843a1ea80e471f0980e9c68a8c3c" (Status: 403) [Size: 277]\n\n\x1b[2K/<button data-target="react-partial-anchor.anchor" id="icon-button-80b3cb14-913d-4d2b-bcb5-4e392019b43a" aria-labelledby="tooltip-93d1a8f2-0b8c-4b8e-869a-43464648912f" type="button" disabled="disabled" data-view-component="true" class="Button Button--iconOnly Button--invisible Button--medium AppHeader-button HeaderMenu-link border cursor-wait">  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-sliders Button-visual"> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M15 2.75a.75.75 0 0 1-.75.75h-4a.75.75 0 0 1 0-1.5h4a.75.75 0 0 1 .75.75Zm-8.5.75v1.25a.75.75 0 0 0 1.5 0v-4a.75.75 0 0 0-1.5 0V2H1.75a.75.75 0 0 0 0 1.5H6.5Zm1.25 5.25a.75.75 0 0 0 0-1.5h-6a.75.75 0 0 0 0 1.5h6ZM15 8a.75.75 0 0 1-.75.75H11.5V10a.75.75 0 1 1-1.5 0V6a.75.75 0 0 1 1.5 0v1.25h2.75A.75.75 0 0 1 15 8Zm-9 5.25v-2a.75.75 0 0 0-1.5 0v1.25H1.75a.75.75 0 0 0 0 1.5H4.5v1.25a.75.75 0 0 0 1.5 0v-2Zm9 0a.75.75 0 0 1-.75.75h-6a.75.75 0 0 1 0-1.5h6a.75.75 0 0 1 .75.75Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M6.457 1.047c.659-1.234 2.427-1.234 3.086 0l6.082 11.378A1.75 1.75 0 0 1 14.082 15H1.918a1.75 1.75 0 0 1-1.543-2.575Zm1.763.707a.25.25 0 0 0-.44 0L1.698 13.132a.25.25 0 0 0 .22.368h12.164a.25.25 0 0 0 .22-.368Zm.53 3.996v2.5a.75.75 0 0 1-1.5 0v-2.5a.75.75 0 0 1 1.5 0ZM9 11a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<button id="icon-button-407c749b-3dbd-48dc-82b4-ab0f2cee2e4a" aria-labelledby="tooltip-6d64b76f-469d-4955-8d8d-5519f45c1751" type="button" data-view-component="true" class="Button Button--iconOnly Button--invisible Button--medium flash-close js-flash-close">  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-x Button-visual"> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M3.72 3.72a.75.75 0 0 1 1.06 0L8 6.94l3.22-3.22a.749.749 0 0 1 1.275.326.749.749 0 0 1-.215.734L9.06 8l3.22 3.22a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215L8 9.06l-3.22 3.22a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042L6.94 8 3.72 4.78a.75.75 0 0 1 0-1.06Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M3.72 3.72a.75.75 0 0 1 1.06 0L8 6.94l3.22-3.22a.749.749 0 0 1 1.275.326.749.749 0 0 1-.215.734L9.06 8l3.22 3.22a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215L8 9.06l-3.22 3.22a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042L6.94 8 3.72 4.78a.75.75 0 0 1 0-1.06Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<path d="M2 2.5A2.5 2.5 0 0 1 4.5 0h8.75a.75.75 0 0 1 .75.75v12.5a.75.75 0 0 1-.75.75h-2.5a.75.75 0 0 1 0-1.5h1.75v-2h-8a1 1 0 0 0-.714 1.7.75.75 0 1 1-1.072 1.05A2.495 2.495 0 0 1 2 11.5Zm10.5-1h-8a1 1 0 0 0-1 1v6.708A2.486 2.486 0 0 1 4.5 9h8ZM5 12.25a.25.25 0 0 1 .25-.25h3.5a.25.25 0 0 1 .25.25v3.25a.25.25 0 0 1-.4.2l-1.45-1.087a.249.249 0 0 0-.3 0L5.4 15.7a.25.25 0 0 1-.4-.2Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<a href="/login?return_to=%2Fxmendez%2Fwfuzz" rel="nofollow" id="repository-details-watch-button" data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;notification subscription menu watch&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;LOG_IN&quot;,&quot;originating_url&quot;:&quot;https://github.com/xmendez/wfuzz/blob/master/wordlist/general/big.txt&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="1f9098cbb89c52bd89bf1ad77c9bb7e9901ce5f9b7bacd13af1b4394feff4598" aria-label="You must be signed in to change notification settings" data-view-component="true" class="btn-sm btn">    <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-bell mr-2"> (Status: 400) [Size: 301]\n\n\x1b[2K/<path d="M8 16a2 2 0 0 0 1.985-1.75c.017-.137-.097-.25-.235-.25h-3.5c-.138 0-.252.113-.235.25A2 2 0 0 0 8 16ZM3 5a5 5 0 0 1 10 0v2.947c0 .**************.139l1.703 2.555A1.519 1.519 0 0 1 13.482 13H2.518a1.516 1.516 0 0 1-1.263-2.36l1.703-2.554A.255.255 0 0 0 3 7.947Zm5-3.5A3.5 3.5 0 0 0 4.5 5v2.947c0 .346-.102.683-.294.97l-1.703 2.556a.017.017 0 0 0-.003.01l.001.006c0 .002.002.004.004.006l.006.004.007.001h10.964l.007-.001.006-.004.004-.006.001-.007a.017.017 0 0 0-.003-.01l-1.703-2.554a1.745 1.745 0 0 1-.294-.97V5A3.5 3.5 0 0 0 8 1.5Z"></path> (Status: 403) [Size: 277]\n\n\x1b[2K/<a icon="repo-forked" id="fork-button" href="/login?return_to=%2Fxmendez%2Fwfuzz" rel="nofollow" data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;repo details fork button&quot;,&quot;repository_id&quot;:25605151,&quot;auth_type&quot;:&quot;LOG_IN&quot;,&quot;originating_url&quot;:&quot;https://github.com/xmendez/wfuzz/blob/master/wordlist/general/big.txt&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="0210aaf1f9259117777cb29f9504e5e89f749b27dae9dd665ca9b88aa2a389dd" data-view-component="true" class="btn-sm btn">    <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-repo-forked mr-2"> (Status: 400) [Size: 301]\n', 'found_paths': 0}, 'zap': {'status': 'completed', 'alerts': [{'risk': 'Medium', 'name': 'Missing X-Frame-Options', 'count': 1, 'description': 'Clickjacking protection missing'}, {'risk': 'Medium', 'name': 'Missing X-Content-Type-Options', 'count': 1, 'description': 'MIME sniffing protection missing'}, {'risk': 'Medium', 'name': 'Missing X-XSS-Protection', 'count': 1, 'description': 'XSS protection header missing'}, {'risk': 'Medium', 'name': 'Missing Strict-Transport-Security', 'count': 1, 'description': 'HTTPS enforcement missing'}, {'risk': 'Medium', 'name': 'Missing Content-Security-Policy', 'count': 1, 'description': 'Content Security Policy missing'}, {'risk': 'Low', 'name': 'Server Information Disclosure', 'count': 1, 'description': 'Server header reveals: Apache/2.4.58 (Ubuntu)'}], 'scan_time': '0 seconds', 'raw_output': 'Basic security scan completed. Found 6 potential issues.'}}, 'vulnerabilities': [{'name': '|   After NULL UDP avahi packet DoS (CVE-2011-1002).', 'severity': 'medium', 'description': '|   After NULL UDP avahi packet DoS (CVE-2011-1002).', 'tool': 'nmap', 'cve': 'CVE-2011-1002', 'id': 'CVE-2011-1002', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|_  Hosts are all up (not vulnerable).', 'severity': 'medium', 'description': '|_  Hosts are all up (not vulnerable).', 'tool': 'nmap', 'cve': None, 'source_tool': 'nmap', 'category': 'network'}, {'name': '|_  The SMTP server is not Exim: NOT VULNERABLE', 'severity': 'medium', 'description': '|_  The SMTP server is not Exim: NOT VULNERABLE', 'tool': 'nmap', 'cve': None, 'source_tool': 'nmap', 'category': 'network'}, {'name': '|   VULNERABLE:', 'severity': 'medium', 'description': '|   VULNERABLE:', 'tool': 'nmap', 'cve': None, 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     State: VULNERABLE', 'severity': 'medium', 'description': '|     State: VULNERABLE', 'tool': 'nmap', 'cve': None, 'source_tool': 'nmap', 'category': 'network'}, {'name': '|       eavesdropping, and are vulnerable to active man-in-the-middle attacks', 'severity': 'medium', 'description': '|       eavesdropping, and are vulnerable to active man-in-the-middle attacks', 'tool': 'nmap', 'cve': None, 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2024-38476\t9.8\thttps://vulners.com/cve/CVE-2024-38476', 'severity': 'medium', 'description': '|     \tCVE-2024-38476\t9.8\thttps://vulners.com/cve/CVE-2024-38476', 'tool': 'nmap', 'cve': 'CVE-2024-38476', 'id': 'CVE-2024-38476', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2024-38474\t9.8\thttps://vulners.com/cve/CVE-2024-38474', 'severity': 'medium', 'description': '|     \tCVE-2024-38474\t9.8\thttps://vulners.com/cve/CVE-2024-38474', 'tool': 'nmap', 'cve': 'CVE-2024-38474', 'id': 'CVE-2024-38474', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2024-40898\t9.1\thttps://vulners.com/cve/CVE-2024-40898', 'severity': 'medium', 'description': '|     \tCVE-2024-40898\t9.1\thttps://vulners.com/cve/CVE-2024-40898', 'tool': 'nmap', 'cve': 'CVE-2024-40898', 'id': 'CVE-2024-40898', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2024-38475\t9.1\thttps://vulners.com/cve/CVE-2024-38475', 'severity': 'medium', 'description': '|     \tCVE-2024-38475\t9.1\thttps://vulners.com/cve/CVE-2024-38475', 'tool': 'nmap', 'cve': 'CVE-2024-38475', 'id': 'CVE-2024-38475', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2024-38473\t8.1\thttps://vulners.com/cve/CVE-2024-38473', 'severity': 'medium', 'description': '|     \tCVE-2024-38473\t8.1\thttps://vulners.com/cve/CVE-2024-38473', 'tool': 'nmap', 'cve': 'CVE-2024-38473', 'id': 'CVE-2024-38473', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2024-39573\t7.5\thttps://vulners.com/cve/CVE-2024-39573', 'severity': 'medium', 'description': '|     \tCVE-2024-39573\t7.5\thttps://vulners.com/cve/CVE-2024-39573', 'tool': 'nmap', 'cve': 'CVE-2024-39573', 'id': 'CVE-2024-39573', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2024-38477\t7.5\thttps://vulners.com/cve/CVE-2024-38477', 'severity': 'medium', 'description': '|     \tCVE-2024-38477\t7.5\thttps://vulners.com/cve/CVE-2024-38477', 'tool': 'nmap', 'cve': 'CVE-2024-38477', 'id': 'CVE-2024-38477', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2024-38472\t7.5\thttps://vulners.com/cve/CVE-2024-38472', 'severity': 'medium', 'description': '|     \tCVE-2024-38472\t7.5\thttps://vulners.com/cve/CVE-2024-38472', 'tool': 'nmap', 'cve': 'CVE-2024-38472', 'id': 'CVE-2024-38472', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2024-27316\t7.5\thttps://vulners.com/cve/CVE-2024-27316', 'severity': 'medium', 'description': '|     \tCVE-2024-27316\t7.5\thttps://vulners.com/cve/CVE-2024-27316', 'tool': 'nmap', 'cve': 'CVE-2024-27316', 'id': 'CVE-2024-27316', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-38709\t7.3\thttps://vulners.com/cve/CVE-2023-38709', 'severity': 'medium', 'description': '|     \tCVE-2023-38709\t7.3\thttps://vulners.com/cve/CVE-2023-38709', 'tool': 'nmap', 'cve': 'CVE-2023-38709', 'id': 'CVE-2023-38709', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2024-24795\t6.3\thttps://vulners.com/cve/CVE-2024-24795', 'severity': 'medium', 'description': '|     \tCVE-2024-24795\t6.3\thttps://vulners.com/cve/CVE-2024-24795', 'tool': 'nmap', 'cve': 'CVE-2024-24795', 'id': 'CVE-2024-24795', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2024-39884\t6.2\thttps://vulners.com/cve/CVE-2024-39884', 'severity': 'medium', 'description': '|     \tCVE-2024-39884\t6.2\thttps://vulners.com/cve/CVE-2024-39884', 'tool': 'nmap', 'cve': 'CVE-2024-39884', 'id': 'CVE-2024-39884', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|_    \tCVE-2024-36387\t5.4\thttps://vulners.com/cve/CVE-2024-36387', 'severity': 'medium', 'description': '|_    \tCVE-2024-36387\t5.4\thttps://vulners.com/cve/CVE-2024-36387', 'tool': 'nmap', 'cve': 'CVE-2024-36387', 'id': 'CVE-2024-36387', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-1472\t10.0\thttps://vulners.com/cve/CVE-2020-1472', 'severity': 'medium', 'description': '|     \tCVE-2020-1472\t10.0\thttps://vulners.com/cve/CVE-2020-1472', 'tool': 'nmap', 'cve': 'CVE-2020-1472', 'id': 'CVE-2020-1472', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-7494\t10.0\thttps://vulners.com/cve/CVE-2017-7494', 'severity': 'medium', 'description': '|     \tCVE-2017-7494\t10.0\thttps://vulners.com/cve/CVE-2017-7494', 'tool': 'nmap', 'cve': 'CVE-2017-7494', 'id': 'CVE-2017-7494', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-3961\t9.8\thttps://vulners.com/cve/CVE-2023-3961', 'severity': 'medium', 'description': '|     \tCVE-2023-3961\t9.8\thttps://vulners.com/cve/CVE-2023-3961', 'tool': 'nmap', 'cve': 'CVE-2023-3961', 'id': 'CVE-2023-3961', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-45141\t9.8\thttps://vulners.com/cve/CVE-2022-45141', 'severity': 'medium', 'description': '|     \tCVE-2022-45141\t9.8\thttps://vulners.com/cve/CVE-2022-45141', 'tool': 'nmap', 'cve': 'CVE-2022-45141', 'id': 'CVE-2022-45141', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-14746\t9.8\thttps://vulners.com/cve/CVE-2017-14746', 'severity': 'medium', 'description': '|     \tCVE-2017-14746\t9.8\thttps://vulners.com/cve/CVE-2017-14746', 'tool': 'nmap', 'cve': 'CVE-2017-14746', 'id': 'CVE-2017-14746', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-44142\t9.0\thttps://vulners.com/cve/CVE-2021-44142', 'severity': 'medium', 'description': '|     \tCVE-2021-44142\t9.0\thttps://vulners.com/cve/CVE-2021-44142', 'tool': 'nmap', 'cve': 'CVE-2021-44142', 'id': 'CVE-2021-44142', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-25719\t9.0\thttps://vulners.com/cve/CVE-2020-25719', 'severity': 'medium', 'description': '|     \tCVE-2020-25719\t9.0\thttps://vulners.com/cve/CVE-2020-25719', 'tool': 'nmap', 'cve': 'CVE-2020-25719', 'id': 'CVE-2020-25719', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-17049\t9.0\thttps://vulners.com/cve/CVE-2020-17049', 'severity': 'medium', 'description': '|     \tCVE-2020-17049\t9.0\thttps://vulners.com/cve/CVE-2020-17049', 'tool': 'nmap', 'cve': 'CVE-2020-17049', 'id': 'CVE-2020-17049', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-42898\t8.8\thttps://vulners.com/cve/CVE-2022-42898', 'severity': 'medium', 'description': '|     \tCVE-2022-42898\t8.8\thttps://vulners.com/cve/CVE-2022-42898', 'tool': 'nmap', 'cve': 'CVE-2022-42898', 'id': 'CVE-2022-42898', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-32744\t8.8\thttps://vulners.com/cve/CVE-2022-32744', 'severity': 'medium', 'description': '|     \tCVE-2022-32744\t8.8\thttps://vulners.com/cve/CVE-2022-32744', 'tool': 'nmap', 'cve': 'CVE-2022-32744', 'id': 'CVE-2022-32744', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-2031\t8.8\thttps://vulners.com/cve/CVE-2022-2031', 'severity': 'medium', 'description': '|     \tCVE-2022-2031\t8.8\thttps://vulners.com/cve/CVE-2022-2031', 'tool': 'nmap', 'cve': 'CVE-2022-2031', 'id': 'CVE-2022-2031', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-0336\t8.8\thttps://vulners.com/cve/CVE-2022-0336', 'severity': 'medium', 'description': '|     \tCVE-2022-0336\t8.8\thttps://vulners.com/cve/CVE-2022-0336', 'tool': 'nmap', 'cve': 'CVE-2022-0336', 'id': 'CVE-2022-0336', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-3738\t8.8\thttps://vulners.com/cve/CVE-2021-3738', 'severity': 'medium', 'description': '|     \tCVE-2021-3738\t8.8\thttps://vulners.com/cve/CVE-2021-3738', 'tool': 'nmap', 'cve': 'CVE-2021-3738', 'id': 'CVE-2021-3738', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-25722\t8.8\thttps://vulners.com/cve/CVE-2020-25722', 'severity': 'medium', 'description': '|     \tCVE-2020-25722\t8.8\thttps://vulners.com/cve/CVE-2020-25722', 'tool': 'nmap', 'cve': 'CVE-2020-25722', 'id': 'CVE-2020-25722', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-25721\t8.8\thttps://vulners.com/cve/CVE-2020-25721', 'severity': 'medium', 'description': '|     \tCVE-2020-25721\t8.8\thttps://vulners.com/cve/CVE-2020-25721', 'tool': 'nmap', 'cve': 'CVE-2020-25721', 'id': 'CVE-2020-25721', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-25718\t8.8\thttps://vulners.com/cve/CVE-2020-25718', 'severity': 'medium', 'description': '|     \tCVE-2020-25718\t8.8\thttps://vulners.com/cve/CVE-2020-25718', 'tool': 'nmap', 'cve': 'CVE-2020-25718', 'id': 'CVE-2020-25718', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-10858\t8.8\thttps://vulners.com/cve/CVE-2018-10858', 'severity': 'medium', 'description': '|     \tCVE-2018-10858\t8.8\thttps://vulners.com/cve/CVE-2018-10858', 'tool': 'nmap', 'cve': 'CVE-2018-10858', 'id': 'CVE-2018-10858', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-1057\t8.8\thttps://vulners.com/cve/CVE-2018-1057', 'severity': 'medium', 'description': '|     \tCVE-2018-1057\t8.8\thttps://vulners.com/cve/CVE-2018-1057', 'tool': 'nmap', 'cve': 'CVE-2018-1057', 'id': 'CVE-2018-1057', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-25717\t8.5\thttps://vulners.com/cve/CVE-2020-25717', 'severity': 'medium', 'description': '|     \tCVE-2020-25717\t8.5\thttps://vulners.com/cve/CVE-2020-25717', 'tool': 'nmap', 'cve': 'CVE-2020-25717', 'id': 'CVE-2020-25717', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-38023\t8.1\thttps://vulners.com/cve/CVE-2022-38023', 'severity': 'medium', 'description': '|     \tCVE-2022-38023\t8.1\thttps://vulners.com/cve/CVE-2022-38023', 'tool': 'nmap', 'cve': 'CVE-2022-38023', 'id': 'CVE-2022-38023', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-37966\t8.1\thttps://vulners.com/cve/CVE-2022-37966', 'severity': 'medium', 'description': '|     \tCVE-2022-37966\t8.1\thttps://vulners.com/cve/CVE-2022-37966', 'tool': 'nmap', 'cve': 'CVE-2022-37966', 'id': 'CVE-2022-37966', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-32745\t8.1\thttps://vulners.com/cve/CVE-2022-32745', 'severity': 'medium', 'description': '|     \tCVE-2022-32745\t8.1\thttps://vulners.com/cve/CVE-2022-32745', 'tool': 'nmap', 'cve': 'CVE-2022-32745', 'id': 'CVE-2022-32745', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-11103\t8.1\thttps://vulners.com/cve/CVE-2017-11103', 'severity': 'medium', 'description': '|     \tCVE-2017-11103\t8.1\thttps://vulners.com/cve/CVE-2017-11103', 'tool': 'nmap', 'cve': 'CVE-2017-11103', 'id': 'CVE-2017-11103', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-10745\t7.8\thttps://vulners.com/cve/CVE-2020-10745', 'severity': 'medium', 'description': '|     \tCVE-2020-10745\t7.8\thttps://vulners.com/cve/CVE-2020-10745', 'tool': 'nmap', 'cve': 'CVE-2020-10745', 'id': 'CVE-2020-10745', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-0614\t7.7\thttps://vulners.com/cve/CVE-2023-0614', 'severity': 'medium', 'description': '|     \tCVE-2023-0614\t7.7\thttps://vulners.com/cve/CVE-2023-0614', 'tool': 'nmap', 'cve': 'CVE-2023-0614', 'id': 'CVE-2023-0614', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-4154\t7.5\thttps://vulners.com/cve/CVE-2023-4154', 'severity': 'medium', 'description': '|     \tCVE-2023-4154\t7.5\thttps://vulners.com/cve/CVE-2023-4154', 'tool': 'nmap', 'cve': 'CVE-2023-4154', 'id': 'CVE-2023-4154', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-34966\t7.5\thttps://vulners.com/cve/CVE-2023-34966', 'severity': 'medium', 'description': '|     \tCVE-2023-34966\t7.5\thttps://vulners.com/cve/CVE-2023-34966', 'tool': 'nmap', 'cve': 'CVE-2023-34966', 'id': 'CVE-2023-34966', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-32743\t7.5\thttps://vulners.com/cve/CVE-2022-32743', 'severity': 'medium', 'description': '|     \tCVE-2022-32743\t7.5\thttps://vulners.com/cve/CVE-2022-32743', 'tool': 'nmap', 'cve': 'CVE-2022-32743', 'id': 'CVE-2022-32743', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-23192\t7.5\thttps://vulners.com/cve/CVE-2021-23192', 'severity': 'medium', 'description': '|     \tCVE-2021-23192\t7.5\thttps://vulners.com/cve/CVE-2021-23192', 'tool': 'nmap', 'cve': 'CVE-2021-23192', 'id': 'CVE-2021-23192', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-20277\t7.5\thttps://vulners.com/cve/CVE-2021-20277', 'severity': 'medium', 'description': '|     \tCVE-2021-20277\t7.5\thttps://vulners.com/cve/CVE-2021-20277', 'tool': 'nmap', 'cve': 'CVE-2021-20277', 'id': 'CVE-2021-20277', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-27840\t7.5\thttps://vulners.com/cve/CVE-2020-27840', 'severity': 'medium', 'description': '|     \tCVE-2020-27840\t7.5\thttps://vulners.com/cve/CVE-2020-27840', 'tool': 'nmap', 'cve': 'CVE-2020-27840', 'id': 'CVE-2020-27840', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-14303\t7.5\thttps://vulners.com/cve/CVE-2020-14303', 'severity': 'medium', 'description': '|     \tCVE-2020-14303\t7.5\thttps://vulners.com/cve/CVE-2020-14303', 'tool': 'nmap', 'cve': 'CVE-2020-14303', 'id': 'CVE-2020-14303', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-10704\t7.5\thttps://vulners.com/cve/CVE-2020-10704', 'severity': 'medium', 'description': '|     \tCVE-2020-10704\t7.5\thttps://vulners.com/cve/CVE-2020-10704', 'tool': 'nmap', 'cve': 'CVE-2020-10704', 'id': 'CVE-2020-10704', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-16860\t7.5\thttps://vulners.com/cve/CVE-2018-16860', 'severity': 'medium', 'description': '|     \tCVE-2018-16860\t7.5\thttps://vulners.com/cve/CVE-2018-16860', 'tool': 'nmap', 'cve': 'CVE-2018-16860', 'id': 'CVE-2018-16860', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-15275\t7.5\thttps://vulners.com/cve/CVE-2017-15275', 'severity': 'medium', 'description': '|     \tCVE-2017-15275\t7.5\thttps://vulners.com/cve/CVE-2017-15275', 'tool': 'nmap', 'cve': 'CVE-2017-15275', 'id': 'CVE-2017-15275', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-12151\t7.4\thttps://vulners.com/cve/CVE-2017-12151', 'severity': 'medium', 'description': '|     \tCVE-2017-12151\t7.4\thttps://vulners.com/cve/CVE-2017-12151', 'tool': 'nmap', 'cve': 'CVE-2017-12151', 'id': 'CVE-2017-12151', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-12150\t7.4\thttps://vulners.com/cve/CVE-2017-12150', 'severity': 'medium', 'description': '|     \tCVE-2017-12150\t7.4\thttps://vulners.com/cve/CVE-2017-12150', 'tool': 'nmap', 'cve': 'CVE-2017-12150', 'id': 'CVE-2017-12150', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-37967\t7.2\thttps://vulners.com/cve/CVE-2022-37967', 'severity': 'medium', 'description': '|     \tCVE-2022-37967\t7.2\thttps://vulners.com/cve/CVE-2022-37967', 'tool': 'nmap', 'cve': 'CVE-2022-37967', 'id': 'CVE-2022-37967', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-12163\t7.1\thttps://vulners.com/cve/CVE-2017-12163', 'severity': 'medium', 'description': '|     \tCVE-2017-12163\t7.1\thttps://vulners.com/cve/CVE-2017-12163', 'tool': 'nmap', 'cve': 'CVE-2017-12163', 'id': 'CVE-2017-12163', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-20316\t6.8\thttps://vulners.com/cve/CVE-2021-20316', 'severity': 'medium', 'description': '|     \tCVE-2021-20316\t6.8\thttps://vulners.com/cve/CVE-2021-20316', 'tool': 'nmap', 'cve': 'CVE-2021-20316', 'id': 'CVE-2021-20316', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-20254\t6.8\thttps://vulners.com/cve/CVE-2021-20254', 'severity': 'medium', 'description': '|     \tCVE-2021-20254\t6.8\thttps://vulners.com/cve/CVE-2021-20254', 'tool': 'nmap', 'cve': 'CVE-2021-20254', 'id': 'CVE-2021-20254', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-5568\t6.5\thttps://vulners.com/cve/CVE-2023-5568', 'severity': 'medium', 'description': '|     \tCVE-2023-5568\t6.5\thttps://vulners.com/cve/CVE-2023-5568', 'tool': 'nmap', 'cve': 'CVE-2023-5568', 'id': 'CVE-2023-5568', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-42670\t6.5\thttps://vulners.com/cve/CVE-2023-42670', 'severity': 'medium', 'description': '|     \tCVE-2023-42670\t6.5\thttps://vulners.com/cve/CVE-2023-42670', 'tool': 'nmap', 'cve': 'CVE-2023-42670', 'id': 'CVE-2023-42670', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-42669\t6.5\thttps://vulners.com/cve/CVE-2023-42669', 'severity': 'medium', 'description': '|     \tCVE-2023-42669\t6.5\thttps://vulners.com/cve/CVE-2023-42669', 'tool': 'nmap', 'cve': 'CVE-2023-42669', 'id': 'CVE-2023-42669', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-4091\t6.5\thttps://vulners.com/cve/CVE-2023-4091', 'severity': 'medium', 'description': '|     \tCVE-2023-4091\t6.5\thttps://vulners.com/cve/CVE-2023-4091', 'tool': 'nmap', 'cve': 'CVE-2023-4091', 'id': 'CVE-2023-4091', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-3592\t6.5\thttps://vulners.com/cve/CVE-2022-3592', 'severity': 'medium', 'description': '|     \tCVE-2022-3592\t6.5\thttps://vulners.com/cve/CVE-2022-3592', 'tool': 'nmap', 'cve': 'CVE-2022-3592', 'id': 'CVE-2022-3592', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-3437\t6.5\thttps://vulners.com/cve/CVE-2022-3437', 'severity': 'medium', 'description': '|     \tCVE-2022-3437\t6.5\thttps://vulners.com/cve/CVE-2022-3437', 'tool': 'nmap', 'cve': 'CVE-2022-3437', 'id': 'CVE-2022-3437', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-3671\t6.5\thttps://vulners.com/cve/CVE-2021-3671', 'severity': 'medium', 'description': '|     \tCVE-2021-3671\t6.5\thttps://vulners.com/cve/CVE-2021-3671', 'tool': 'nmap', 'cve': 'CVE-2021-3671', 'id': 'CVE-2021-3671', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-3670\t6.5\thttps://vulners.com/cve/CVE-2021-3670', 'severity': 'medium', 'description': '|     \tCVE-2021-3670\t6.5\thttps://vulners.com/cve/CVE-2021-3670', 'tool': 'nmap', 'cve': 'CVE-2021-3670', 'id': 'CVE-2021-3670', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-14383\t6.5\thttps://vulners.com/cve/CVE-2020-14383', 'severity': 'medium', 'description': '|     \tCVE-2020-14383\t6.5\thttps://vulners.com/cve/CVE-2020-14383', 'tool': 'nmap', 'cve': 'CVE-2020-14383', 'id': 'CVE-2020-14383', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-10760\t6.5\thttps://vulners.com/cve/CVE-2020-10760', 'severity': 'medium', 'description': '|     \tCVE-2020-10760\t6.5\thttps://vulners.com/cve/CVE-2020-10760', 'tool': 'nmap', 'cve': 'CVE-2020-10760', 'id': 'CVE-2020-10760', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-10730\t6.5\thttps://vulners.com/cve/CVE-2020-10730', 'severity': 'medium', 'description': '|     \tCVE-2020-10730\t6.5\thttps://vulners.com/cve/CVE-2020-10730', 'tool': 'nmap', 'cve': 'CVE-2020-10730', 'id': 'CVE-2020-10730', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-3824\t6.5\thttps://vulners.com/cve/CVE-2019-3824', 'severity': 'medium', 'description': '|     \tCVE-2019-3824\t6.5\thttps://vulners.com/cve/CVE-2019-3824', 'tool': 'nmap', 'cve': 'CVE-2019-3824', 'id': 'CVE-2019-3824', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-10218\t6.5\thttps://vulners.com/cve/CVE-2019-10218', 'severity': 'medium', 'description': '|     \tCVE-2019-10218\t6.5\thttps://vulners.com/cve/CVE-2019-10218', 'tool': 'nmap', 'cve': 'CVE-2019-10218', 'id': 'CVE-2019-10218', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-16851\t6.5\thttps://vulners.com/cve/CVE-2018-16851', 'severity': 'medium', 'description': '|     \tCVE-2018-16851\t6.5\thttps://vulners.com/cve/CVE-2018-16851', 'tool': 'nmap', 'cve': 'CVE-2018-16851', 'id': 'CVE-2018-16851', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-16841\t6.5\thttps://vulners.com/cve/CVE-2018-16841', 'severity': 'medium', 'description': '|     \tCVE-2018-16841\t6.5\thttps://vulners.com/cve/CVE-2018-16841', 'tool': 'nmap', 'cve': 'CVE-2018-16841', 'id': 'CVE-2018-16841', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-14629\t6.5\thttps://vulners.com/cve/CVE-2018-14629', 'severity': 'medium', 'description': '|     \tCVE-2018-14629\t6.5\thttps://vulners.com/cve/CVE-2018-14629', 'tool': 'nmap', 'cve': 'CVE-2018-14629', 'id': 'CVE-2018-14629', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-10919\t6.5\thttps://vulners.com/cve/CVE-2018-10919', 'severity': 'medium', 'description': '|     \tCVE-2018-10919\t6.5\thttps://vulners.com/cve/CVE-2018-10919', 'tool': 'nmap', 'cve': 'CVE-2018-10919', 'id': 'CVE-2018-10919', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-14870\t6.4\thttps://vulners.com/cve/CVE-2019-14870', 'severity': 'medium', 'description': '|     \tCVE-2019-14870\t6.4\thttps://vulners.com/cve/CVE-2019-14870', 'tool': 'nmap', 'cve': 'CVE-2019-14870', 'id': 'CVE-2019-14870', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-0922\t5.9\thttps://vulners.com/cve/CVE-2023-0922', 'severity': 'medium', 'description': '|     \tCVE-2023-0922\t5.9\thttps://vulners.com/cve/CVE-2023-0922', 'tool': 'nmap', 'cve': 'CVE-2023-0922', 'id': 'CVE-2023-0922', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-20251\t5.9\thttps://vulners.com/cve/CVE-2021-20251', 'severity': 'medium', 'description': '|     \tCVE-2021-20251\t5.9\thttps://vulners.com/cve/CVE-2021-20251', 'tool': 'nmap', 'cve': 'CVE-2021-20251', 'id': 'CVE-2021-20251', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2016-2124\t5.9\thttps://vulners.com/cve/CVE-2016-2124', 'severity': 'medium', 'description': '|     \tCVE-2016-2124\t5.9\thttps://vulners.com/cve/CVE-2016-2124', 'tool': 'nmap', 'cve': 'CVE-2016-2124', 'id': 'CVE-2016-2124', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-1615\t5.5\thttps://vulners.com/cve/CVE-2022-1615', 'severity': 'medium', 'description': '|     \tCVE-2022-1615\t5.5\thttps://vulners.com/cve/CVE-2022-1615', 'tool': 'nmap', 'cve': 'CVE-2022-1615', 'id': 'CVE-2022-1615', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-14323\t5.5\thttps://vulners.com/cve/CVE-2020-14323', 'severity': 'medium', 'description': '|     \tCVE-2020-14323\t5.5\thttps://vulners.com/cve/CVE-2020-14323', 'tool': 'nmap', 'cve': 'CVE-2020-14323', 'id': 'CVE-2020-14323', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-3880\t5.5\thttps://vulners.com/cve/CVE-2019-3880', 'severity': 'medium', 'description': '|     \tCVE-2019-3880\t5.5\thttps://vulners.com/cve/CVE-2019-3880', 'tool': 'nmap', 'cve': 'CVE-2019-3880', 'id': 'CVE-2019-3880', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-14902\t5.5\thttps://vulners.com/cve/CVE-2019-14902', 'severity': 'medium', 'description': '|     \tCVE-2019-14902\t5.5\thttps://vulners.com/cve/CVE-2019-14902', 'tool': 'nmap', 'cve': 'CVE-2019-14902', 'id': 'CVE-2019-14902', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-32746\t5.4\thttps://vulners.com/cve/CVE-2022-32746', 'severity': 'medium', 'description': '|     \tCVE-2022-32746\t5.4\thttps://vulners.com/cve/CVE-2022-32746', 'tool': 'nmap', 'cve': 'CVE-2022-32746', 'id': 'CVE-2022-32746', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-14833\t5.4\thttps://vulners.com/cve/CVE-2019-14833', 'severity': 'medium', 'description': '|     \tCVE-2019-14833\t5.4\thttps://vulners.com/cve/CVE-2019-14833', 'tool': 'nmap', 'cve': 'CVE-2019-14833', 'id': 'CVE-2019-14833', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-34968\t5.3\thttps://vulners.com/cve/CVE-2023-34968', 'severity': 'medium', 'description': '|     \tCVE-2023-34968\t5.3\thttps://vulners.com/cve/CVE-2023-34968', 'tool': 'nmap', 'cve': 'CVE-2023-34968', 'id': 'CVE-2023-34968', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-34967\t5.3\thttps://vulners.com/cve/CVE-2023-34967', 'severity': 'medium', 'description': '|     \tCVE-2023-34967\t5.3\thttps://vulners.com/cve/CVE-2023-34967', 'tool': 'nmap', 'cve': 'CVE-2023-34967', 'id': 'CVE-2023-34967', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-14861\t5.3\thttps://vulners.com/cve/CVE-2019-14861', 'severity': 'medium', 'description': '|     \tCVE-2019-14861\t5.3\thttps://vulners.com/cve/CVE-2019-14861', 'tool': 'nmap', 'cve': 'CVE-2019-14861', 'id': 'CVE-2019-14861', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-14847\t4.9\thttps://vulners.com/cve/CVE-2019-14847', 'severity': 'medium', 'description': '|     \tCVE-2019-14847\t4.9\thttps://vulners.com/cve/CVE-2019-14847', 'tool': 'nmap', 'cve': 'CVE-2019-14847', 'id': 'CVE-2019-14847', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-0225\t4.3\thttps://vulners.com/cve/CVE-2023-0225', 'severity': 'medium', 'description': '|     \tCVE-2023-0225\t4.3\thttps://vulners.com/cve/CVE-2023-0225', 'tool': 'nmap', 'cve': 'CVE-2023-0225', 'id': 'CVE-2023-0225', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-32742\t4.3\thttps://vulners.com/cve/CVE-2022-32742', 'severity': 'medium', 'description': '|     \tCVE-2022-32742\t4.3\thttps://vulners.com/cve/CVE-2022-32742', 'tool': 'nmap', 'cve': 'CVE-2022-32742', 'id': 'CVE-2022-32742', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-44141\t4.3\thttps://vulners.com/cve/CVE-2021-44141', 'severity': 'medium', 'description': '|     \tCVE-2021-44141\t4.3\thttps://vulners.com/cve/CVE-2021-44141', 'tool': 'nmap', 'cve': 'CVE-2021-44141', 'id': 'CVE-2021-44141', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-14318\t4.3\thttps://vulners.com/cve/CVE-2020-14318', 'severity': 'medium', 'description': '|     \tCVE-2020-14318\t4.3\thttps://vulners.com/cve/CVE-2020-14318', 'tool': 'nmap', 'cve': 'CVE-2020-14318', 'id': 'CVE-2020-14318', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-14628\t4.3\thttps://vulners.com/cve/CVE-2018-14628', 'severity': 'medium', 'description': '|     \tCVE-2018-14628\t4.3\thttps://vulners.com/cve/CVE-2018-14628', 'tool': 'nmap', 'cve': 'CVE-2018-14628', 'id': 'CVE-2018-14628', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-1050\t4.3\thttps://vulners.com/cve/CVE-2018-1050', 'severity': 'medium', 'description': '|     \tCVE-2018-1050\t4.3\thttps://vulners.com/cve/CVE-2018-1050', 'tool': 'nmap', 'cve': 'CVE-2018-1050', 'id': 'CVE-2018-1050', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-43566\t2.5\thttps://vulners.com/cve/CVE-2021-43566', 'severity': 'medium', 'description': '|     \tCVE-2021-43566\t2.5\thttps://vulners.com/cve/CVE-2021-43566', 'tool': 'nmap', 'cve': 'CVE-2021-43566', 'id': 'CVE-2021-43566', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-1472\t10.0\thttps://vulners.com/cve/CVE-2020-1472', 'severity': 'medium', 'description': '|     \tCVE-2020-1472\t10.0\thttps://vulners.com/cve/CVE-2020-1472', 'tool': 'nmap', 'cve': 'CVE-2020-1472', 'id': 'CVE-2020-1472', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-7494\t10.0\thttps://vulners.com/cve/CVE-2017-7494', 'severity': 'medium', 'description': '|     \tCVE-2017-7494\t10.0\thttps://vulners.com/cve/CVE-2017-7494', 'tool': 'nmap', 'cve': 'CVE-2017-7494', 'id': 'CVE-2017-7494', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-3961\t9.8\thttps://vulners.com/cve/CVE-2023-3961', 'severity': 'medium', 'description': '|     \tCVE-2023-3961\t9.8\thttps://vulners.com/cve/CVE-2023-3961', 'tool': 'nmap', 'cve': 'CVE-2023-3961', 'id': 'CVE-2023-3961', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-45141\t9.8\thttps://vulners.com/cve/CVE-2022-45141', 'severity': 'medium', 'description': '|     \tCVE-2022-45141\t9.8\thttps://vulners.com/cve/CVE-2022-45141', 'tool': 'nmap', 'cve': 'CVE-2022-45141', 'id': 'CVE-2022-45141', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-14746\t9.8\thttps://vulners.com/cve/CVE-2017-14746', 'severity': 'medium', 'description': '|     \tCVE-2017-14746\t9.8\thttps://vulners.com/cve/CVE-2017-14746', 'tool': 'nmap', 'cve': 'CVE-2017-14746', 'id': 'CVE-2017-14746', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-44142\t9.0\thttps://vulners.com/cve/CVE-2021-44142', 'severity': 'medium', 'description': '|     \tCVE-2021-44142\t9.0\thttps://vulners.com/cve/CVE-2021-44142', 'tool': 'nmap', 'cve': 'CVE-2021-44142', 'id': 'CVE-2021-44142', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-25719\t9.0\thttps://vulners.com/cve/CVE-2020-25719', 'severity': 'medium', 'description': '|     \tCVE-2020-25719\t9.0\thttps://vulners.com/cve/CVE-2020-25719', 'tool': 'nmap', 'cve': 'CVE-2020-25719', 'id': 'CVE-2020-25719', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-17049\t9.0\thttps://vulners.com/cve/CVE-2020-17049', 'severity': 'medium', 'description': '|     \tCVE-2020-17049\t9.0\thttps://vulners.com/cve/CVE-2020-17049', 'tool': 'nmap', 'cve': 'CVE-2020-17049', 'id': 'CVE-2020-17049', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-42898\t8.8\thttps://vulners.com/cve/CVE-2022-42898', 'severity': 'medium', 'description': '|     \tCVE-2022-42898\t8.8\thttps://vulners.com/cve/CVE-2022-42898', 'tool': 'nmap', 'cve': 'CVE-2022-42898', 'id': 'CVE-2022-42898', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-32744\t8.8\thttps://vulners.com/cve/CVE-2022-32744', 'severity': 'medium', 'description': '|     \tCVE-2022-32744\t8.8\thttps://vulners.com/cve/CVE-2022-32744', 'tool': 'nmap', 'cve': 'CVE-2022-32744', 'id': 'CVE-2022-32744', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-2031\t8.8\thttps://vulners.com/cve/CVE-2022-2031', 'severity': 'medium', 'description': '|     \tCVE-2022-2031\t8.8\thttps://vulners.com/cve/CVE-2022-2031', 'tool': 'nmap', 'cve': 'CVE-2022-2031', 'id': 'CVE-2022-2031', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-0336\t8.8\thttps://vulners.com/cve/CVE-2022-0336', 'severity': 'medium', 'description': '|     \tCVE-2022-0336\t8.8\thttps://vulners.com/cve/CVE-2022-0336', 'tool': 'nmap', 'cve': 'CVE-2022-0336', 'id': 'CVE-2022-0336', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-3738\t8.8\thttps://vulners.com/cve/CVE-2021-3738', 'severity': 'medium', 'description': '|     \tCVE-2021-3738\t8.8\thttps://vulners.com/cve/CVE-2021-3738', 'tool': 'nmap', 'cve': 'CVE-2021-3738', 'id': 'CVE-2021-3738', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-25722\t8.8\thttps://vulners.com/cve/CVE-2020-25722', 'severity': 'medium', 'description': '|     \tCVE-2020-25722\t8.8\thttps://vulners.com/cve/CVE-2020-25722', 'tool': 'nmap', 'cve': 'CVE-2020-25722', 'id': 'CVE-2020-25722', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-25721\t8.8\thttps://vulners.com/cve/CVE-2020-25721', 'severity': 'medium', 'description': '|     \tCVE-2020-25721\t8.8\thttps://vulners.com/cve/CVE-2020-25721', 'tool': 'nmap', 'cve': 'CVE-2020-25721', 'id': 'CVE-2020-25721', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-25718\t8.8\thttps://vulners.com/cve/CVE-2020-25718', 'severity': 'medium', 'description': '|     \tCVE-2020-25718\t8.8\thttps://vulners.com/cve/CVE-2020-25718', 'tool': 'nmap', 'cve': 'CVE-2020-25718', 'id': 'CVE-2020-25718', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-10858\t8.8\thttps://vulners.com/cve/CVE-2018-10858', 'severity': 'medium', 'description': '|     \tCVE-2018-10858\t8.8\thttps://vulners.com/cve/CVE-2018-10858', 'tool': 'nmap', 'cve': 'CVE-2018-10858', 'id': 'CVE-2018-10858', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-1057\t8.8\thttps://vulners.com/cve/CVE-2018-1057', 'severity': 'medium', 'description': '|     \tCVE-2018-1057\t8.8\thttps://vulners.com/cve/CVE-2018-1057', 'tool': 'nmap', 'cve': 'CVE-2018-1057', 'id': 'CVE-2018-1057', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-25717\t8.5\thttps://vulners.com/cve/CVE-2020-25717', 'severity': 'medium', 'description': '|     \tCVE-2020-25717\t8.5\thttps://vulners.com/cve/CVE-2020-25717', 'tool': 'nmap', 'cve': 'CVE-2020-25717', 'id': 'CVE-2020-25717', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-38023\t8.1\thttps://vulners.com/cve/CVE-2022-38023', 'severity': 'medium', 'description': '|     \tCVE-2022-38023\t8.1\thttps://vulners.com/cve/CVE-2022-38023', 'tool': 'nmap', 'cve': 'CVE-2022-38023', 'id': 'CVE-2022-38023', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-37966\t8.1\thttps://vulners.com/cve/CVE-2022-37966', 'severity': 'medium', 'description': '|     \tCVE-2022-37966\t8.1\thttps://vulners.com/cve/CVE-2022-37966', 'tool': 'nmap', 'cve': 'CVE-2022-37966', 'id': 'CVE-2022-37966', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-32745\t8.1\thttps://vulners.com/cve/CVE-2022-32745', 'severity': 'medium', 'description': '|     \tCVE-2022-32745\t8.1\thttps://vulners.com/cve/CVE-2022-32745', 'tool': 'nmap', 'cve': 'CVE-2022-32745', 'id': 'CVE-2022-32745', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-11103\t8.1\thttps://vulners.com/cve/CVE-2017-11103', 'severity': 'medium', 'description': '|     \tCVE-2017-11103\t8.1\thttps://vulners.com/cve/CVE-2017-11103', 'tool': 'nmap', 'cve': 'CVE-2017-11103', 'id': 'CVE-2017-11103', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-10745\t7.8\thttps://vulners.com/cve/CVE-2020-10745', 'severity': 'medium', 'description': '|     \tCVE-2020-10745\t7.8\thttps://vulners.com/cve/CVE-2020-10745', 'tool': 'nmap', 'cve': 'CVE-2020-10745', 'id': 'CVE-2020-10745', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-0614\t7.7\thttps://vulners.com/cve/CVE-2023-0614', 'severity': 'medium', 'description': '|     \tCVE-2023-0614\t7.7\thttps://vulners.com/cve/CVE-2023-0614', 'tool': 'nmap', 'cve': 'CVE-2023-0614', 'id': 'CVE-2023-0614', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-4154\t7.5\thttps://vulners.com/cve/CVE-2023-4154', 'severity': 'medium', 'description': '|     \tCVE-2023-4154\t7.5\thttps://vulners.com/cve/CVE-2023-4154', 'tool': 'nmap', 'cve': 'CVE-2023-4154', 'id': 'CVE-2023-4154', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-34966\t7.5\thttps://vulners.com/cve/CVE-2023-34966', 'severity': 'medium', 'description': '|     \tCVE-2023-34966\t7.5\thttps://vulners.com/cve/CVE-2023-34966', 'tool': 'nmap', 'cve': 'CVE-2023-34966', 'id': 'CVE-2023-34966', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-32743\t7.5\thttps://vulners.com/cve/CVE-2022-32743', 'severity': 'medium', 'description': '|     \tCVE-2022-32743\t7.5\thttps://vulners.com/cve/CVE-2022-32743', 'tool': 'nmap', 'cve': 'CVE-2022-32743', 'id': 'CVE-2022-32743', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-23192\t7.5\thttps://vulners.com/cve/CVE-2021-23192', 'severity': 'medium', 'description': '|     \tCVE-2021-23192\t7.5\thttps://vulners.com/cve/CVE-2021-23192', 'tool': 'nmap', 'cve': 'CVE-2021-23192', 'id': 'CVE-2021-23192', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-20277\t7.5\thttps://vulners.com/cve/CVE-2021-20277', 'severity': 'medium', 'description': '|     \tCVE-2021-20277\t7.5\thttps://vulners.com/cve/CVE-2021-20277', 'tool': 'nmap', 'cve': 'CVE-2021-20277', 'id': 'CVE-2021-20277', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-27840\t7.5\thttps://vulners.com/cve/CVE-2020-27840', 'severity': 'medium', 'description': '|     \tCVE-2020-27840\t7.5\thttps://vulners.com/cve/CVE-2020-27840', 'tool': 'nmap', 'cve': 'CVE-2020-27840', 'id': 'CVE-2020-27840', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-14303\t7.5\thttps://vulners.com/cve/CVE-2020-14303', 'severity': 'medium', 'description': '|     \tCVE-2020-14303\t7.5\thttps://vulners.com/cve/CVE-2020-14303', 'tool': 'nmap', 'cve': 'CVE-2020-14303', 'id': 'CVE-2020-14303', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-10704\t7.5\thttps://vulners.com/cve/CVE-2020-10704', 'severity': 'medium', 'description': '|     \tCVE-2020-10704\t7.5\thttps://vulners.com/cve/CVE-2020-10704', 'tool': 'nmap', 'cve': 'CVE-2020-10704', 'id': 'CVE-2020-10704', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-16860\t7.5\thttps://vulners.com/cve/CVE-2018-16860', 'severity': 'medium', 'description': '|     \tCVE-2018-16860\t7.5\thttps://vulners.com/cve/CVE-2018-16860', 'tool': 'nmap', 'cve': 'CVE-2018-16860', 'id': 'CVE-2018-16860', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-15275\t7.5\thttps://vulners.com/cve/CVE-2017-15275', 'severity': 'medium', 'description': '|     \tCVE-2017-15275\t7.5\thttps://vulners.com/cve/CVE-2017-15275', 'tool': 'nmap', 'cve': 'CVE-2017-15275', 'id': 'CVE-2017-15275', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-12151\t7.4\thttps://vulners.com/cve/CVE-2017-12151', 'severity': 'medium', 'description': '|     \tCVE-2017-12151\t7.4\thttps://vulners.com/cve/CVE-2017-12151', 'tool': 'nmap', 'cve': 'CVE-2017-12151', 'id': 'CVE-2017-12151', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-12150\t7.4\thttps://vulners.com/cve/CVE-2017-12150', 'severity': 'medium', 'description': '|     \tCVE-2017-12150\t7.4\thttps://vulners.com/cve/CVE-2017-12150', 'tool': 'nmap', 'cve': 'CVE-2017-12150', 'id': 'CVE-2017-12150', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-37967\t7.2\thttps://vulners.com/cve/CVE-2022-37967', 'severity': 'medium', 'description': '|     \tCVE-2022-37967\t7.2\thttps://vulners.com/cve/CVE-2022-37967', 'tool': 'nmap', 'cve': 'CVE-2022-37967', 'id': 'CVE-2022-37967', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2017-12163\t7.1\thttps://vulners.com/cve/CVE-2017-12163', 'severity': 'medium', 'description': '|     \tCVE-2017-12163\t7.1\thttps://vulners.com/cve/CVE-2017-12163', 'tool': 'nmap', 'cve': 'CVE-2017-12163', 'id': 'CVE-2017-12163', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-20316\t6.8\thttps://vulners.com/cve/CVE-2021-20316', 'severity': 'medium', 'description': '|     \tCVE-2021-20316\t6.8\thttps://vulners.com/cve/CVE-2021-20316', 'tool': 'nmap', 'cve': 'CVE-2021-20316', 'id': 'CVE-2021-20316', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-20254\t6.8\thttps://vulners.com/cve/CVE-2021-20254', 'severity': 'medium', 'description': '|     \tCVE-2021-20254\t6.8\thttps://vulners.com/cve/CVE-2021-20254', 'tool': 'nmap', 'cve': 'CVE-2021-20254', 'id': 'CVE-2021-20254', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-5568\t6.5\thttps://vulners.com/cve/CVE-2023-5568', 'severity': 'medium', 'description': '|     \tCVE-2023-5568\t6.5\thttps://vulners.com/cve/CVE-2023-5568', 'tool': 'nmap', 'cve': 'CVE-2023-5568', 'id': 'CVE-2023-5568', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-42670\t6.5\thttps://vulners.com/cve/CVE-2023-42670', 'severity': 'medium', 'description': '|     \tCVE-2023-42670\t6.5\thttps://vulners.com/cve/CVE-2023-42670', 'tool': 'nmap', 'cve': 'CVE-2023-42670', 'id': 'CVE-2023-42670', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-42669\t6.5\thttps://vulners.com/cve/CVE-2023-42669', 'severity': 'medium', 'description': '|     \tCVE-2023-42669\t6.5\thttps://vulners.com/cve/CVE-2023-42669', 'tool': 'nmap', 'cve': 'CVE-2023-42669', 'id': 'CVE-2023-42669', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-4091\t6.5\thttps://vulners.com/cve/CVE-2023-4091', 'severity': 'medium', 'description': '|     \tCVE-2023-4091\t6.5\thttps://vulners.com/cve/CVE-2023-4091', 'tool': 'nmap', 'cve': 'CVE-2023-4091', 'id': 'CVE-2023-4091', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-3592\t6.5\thttps://vulners.com/cve/CVE-2022-3592', 'severity': 'medium', 'description': '|     \tCVE-2022-3592\t6.5\thttps://vulners.com/cve/CVE-2022-3592', 'tool': 'nmap', 'cve': 'CVE-2022-3592', 'id': 'CVE-2022-3592', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-3437\t6.5\thttps://vulners.com/cve/CVE-2022-3437', 'severity': 'medium', 'description': '|     \tCVE-2022-3437\t6.5\thttps://vulners.com/cve/CVE-2022-3437', 'tool': 'nmap', 'cve': 'CVE-2022-3437', 'id': 'CVE-2022-3437', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-3671\t6.5\thttps://vulners.com/cve/CVE-2021-3671', 'severity': 'medium', 'description': '|     \tCVE-2021-3671\t6.5\thttps://vulners.com/cve/CVE-2021-3671', 'tool': 'nmap', 'cve': 'CVE-2021-3671', 'id': 'CVE-2021-3671', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-3670\t6.5\thttps://vulners.com/cve/CVE-2021-3670', 'severity': 'medium', 'description': '|     \tCVE-2021-3670\t6.5\thttps://vulners.com/cve/CVE-2021-3670', 'tool': 'nmap', 'cve': 'CVE-2021-3670', 'id': 'CVE-2021-3670', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-14383\t6.5\thttps://vulners.com/cve/CVE-2020-14383', 'severity': 'medium', 'description': '|     \tCVE-2020-14383\t6.5\thttps://vulners.com/cve/CVE-2020-14383', 'tool': 'nmap', 'cve': 'CVE-2020-14383', 'id': 'CVE-2020-14383', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-10760\t6.5\thttps://vulners.com/cve/CVE-2020-10760', 'severity': 'medium', 'description': '|     \tCVE-2020-10760\t6.5\thttps://vulners.com/cve/CVE-2020-10760', 'tool': 'nmap', 'cve': 'CVE-2020-10760', 'id': 'CVE-2020-10760', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-10730\t6.5\thttps://vulners.com/cve/CVE-2020-10730', 'severity': 'medium', 'description': '|     \tCVE-2020-10730\t6.5\thttps://vulners.com/cve/CVE-2020-10730', 'tool': 'nmap', 'cve': 'CVE-2020-10730', 'id': 'CVE-2020-10730', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-3824\t6.5\thttps://vulners.com/cve/CVE-2019-3824', 'severity': 'medium', 'description': '|     \tCVE-2019-3824\t6.5\thttps://vulners.com/cve/CVE-2019-3824', 'tool': 'nmap', 'cve': 'CVE-2019-3824', 'id': 'CVE-2019-3824', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-10218\t6.5\thttps://vulners.com/cve/CVE-2019-10218', 'severity': 'medium', 'description': '|     \tCVE-2019-10218\t6.5\thttps://vulners.com/cve/CVE-2019-10218', 'tool': 'nmap', 'cve': 'CVE-2019-10218', 'id': 'CVE-2019-10218', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-16851\t6.5\thttps://vulners.com/cve/CVE-2018-16851', 'severity': 'medium', 'description': '|     \tCVE-2018-16851\t6.5\thttps://vulners.com/cve/CVE-2018-16851', 'tool': 'nmap', 'cve': 'CVE-2018-16851', 'id': 'CVE-2018-16851', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-16841\t6.5\thttps://vulners.com/cve/CVE-2018-16841', 'severity': 'medium', 'description': '|     \tCVE-2018-16841\t6.5\thttps://vulners.com/cve/CVE-2018-16841', 'tool': 'nmap', 'cve': 'CVE-2018-16841', 'id': 'CVE-2018-16841', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-14629\t6.5\thttps://vulners.com/cve/CVE-2018-14629', 'severity': 'medium', 'description': '|     \tCVE-2018-14629\t6.5\thttps://vulners.com/cve/CVE-2018-14629', 'tool': 'nmap', 'cve': 'CVE-2018-14629', 'id': 'CVE-2018-14629', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-10919\t6.5\thttps://vulners.com/cve/CVE-2018-10919', 'severity': 'medium', 'description': '|     \tCVE-2018-10919\t6.5\thttps://vulners.com/cve/CVE-2018-10919', 'tool': 'nmap', 'cve': 'CVE-2018-10919', 'id': 'CVE-2018-10919', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-14870\t6.4\thttps://vulners.com/cve/CVE-2019-14870', 'severity': 'medium', 'description': '|     \tCVE-2019-14870\t6.4\thttps://vulners.com/cve/CVE-2019-14870', 'tool': 'nmap', 'cve': 'CVE-2019-14870', 'id': 'CVE-2019-14870', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-0922\t5.9\thttps://vulners.com/cve/CVE-2023-0922', 'severity': 'medium', 'description': '|     \tCVE-2023-0922\t5.9\thttps://vulners.com/cve/CVE-2023-0922', 'tool': 'nmap', 'cve': 'CVE-2023-0922', 'id': 'CVE-2023-0922', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-20251\t5.9\thttps://vulners.com/cve/CVE-2021-20251', 'severity': 'medium', 'description': '|     \tCVE-2021-20251\t5.9\thttps://vulners.com/cve/CVE-2021-20251', 'tool': 'nmap', 'cve': 'CVE-2021-20251', 'id': 'CVE-2021-20251', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2016-2124\t5.9\thttps://vulners.com/cve/CVE-2016-2124', 'severity': 'medium', 'description': '|     \tCVE-2016-2124\t5.9\thttps://vulners.com/cve/CVE-2016-2124', 'tool': 'nmap', 'cve': 'CVE-2016-2124', 'id': 'CVE-2016-2124', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-1615\t5.5\thttps://vulners.com/cve/CVE-2022-1615', 'severity': 'medium', 'description': '|     \tCVE-2022-1615\t5.5\thttps://vulners.com/cve/CVE-2022-1615', 'tool': 'nmap', 'cve': 'CVE-2022-1615', 'id': 'CVE-2022-1615', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-14323\t5.5\thttps://vulners.com/cve/CVE-2020-14323', 'severity': 'medium', 'description': '|     \tCVE-2020-14323\t5.5\thttps://vulners.com/cve/CVE-2020-14323', 'tool': 'nmap', 'cve': 'CVE-2020-14323', 'id': 'CVE-2020-14323', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-3880\t5.5\thttps://vulners.com/cve/CVE-2019-3880', 'severity': 'medium', 'description': '|     \tCVE-2019-3880\t5.5\thttps://vulners.com/cve/CVE-2019-3880', 'tool': 'nmap', 'cve': 'CVE-2019-3880', 'id': 'CVE-2019-3880', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-14902\t5.5\thttps://vulners.com/cve/CVE-2019-14902', 'severity': 'medium', 'description': '|     \tCVE-2019-14902\t5.5\thttps://vulners.com/cve/CVE-2019-14902', 'tool': 'nmap', 'cve': 'CVE-2019-14902', 'id': 'CVE-2019-14902', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-32746\t5.4\thttps://vulners.com/cve/CVE-2022-32746', 'severity': 'medium', 'description': '|     \tCVE-2022-32746\t5.4\thttps://vulners.com/cve/CVE-2022-32746', 'tool': 'nmap', 'cve': 'CVE-2022-32746', 'id': 'CVE-2022-32746', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-14833\t5.4\thttps://vulners.com/cve/CVE-2019-14833', 'severity': 'medium', 'description': '|     \tCVE-2019-14833\t5.4\thttps://vulners.com/cve/CVE-2019-14833', 'tool': 'nmap', 'cve': 'CVE-2019-14833', 'id': 'CVE-2019-14833', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-34968\t5.3\thttps://vulners.com/cve/CVE-2023-34968', 'severity': 'medium', 'description': '|     \tCVE-2023-34968\t5.3\thttps://vulners.com/cve/CVE-2023-34968', 'tool': 'nmap', 'cve': 'CVE-2023-34968', 'id': 'CVE-2023-34968', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-34967\t5.3\thttps://vulners.com/cve/CVE-2023-34967', 'severity': 'medium', 'description': '|     \tCVE-2023-34967\t5.3\thttps://vulners.com/cve/CVE-2023-34967', 'tool': 'nmap', 'cve': 'CVE-2023-34967', 'id': 'CVE-2023-34967', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-14861\t5.3\thttps://vulners.com/cve/CVE-2019-14861', 'severity': 'medium', 'description': '|     \tCVE-2019-14861\t5.3\thttps://vulners.com/cve/CVE-2019-14861', 'tool': 'nmap', 'cve': 'CVE-2019-14861', 'id': 'CVE-2019-14861', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2019-14847\t4.9\thttps://vulners.com/cve/CVE-2019-14847', 'severity': 'medium', 'description': '|     \tCVE-2019-14847\t4.9\thttps://vulners.com/cve/CVE-2019-14847', 'tool': 'nmap', 'cve': 'CVE-2019-14847', 'id': 'CVE-2019-14847', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2023-0225\t4.3\thttps://vulners.com/cve/CVE-2023-0225', 'severity': 'medium', 'description': '|     \tCVE-2023-0225\t4.3\thttps://vulners.com/cve/CVE-2023-0225', 'tool': 'nmap', 'cve': 'CVE-2023-0225', 'id': 'CVE-2023-0225', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2022-32742\t4.3\thttps://vulners.com/cve/CVE-2022-32742', 'severity': 'medium', 'description': '|     \tCVE-2022-32742\t4.3\thttps://vulners.com/cve/CVE-2022-32742', 'tool': 'nmap', 'cve': 'CVE-2022-32742', 'id': 'CVE-2022-32742', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-44141\t4.3\thttps://vulners.com/cve/CVE-2021-44141', 'severity': 'medium', 'description': '|     \tCVE-2021-44141\t4.3\thttps://vulners.com/cve/CVE-2021-44141', 'tool': 'nmap', 'cve': 'CVE-2021-44141', 'id': 'CVE-2021-44141', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2020-14318\t4.3\thttps://vulners.com/cve/CVE-2020-14318', 'severity': 'medium', 'description': '|     \tCVE-2020-14318\t4.3\thttps://vulners.com/cve/CVE-2020-14318', 'tool': 'nmap', 'cve': 'CVE-2020-14318', 'id': 'CVE-2020-14318', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-14628\t4.3\thttps://vulners.com/cve/CVE-2018-14628', 'severity': 'medium', 'description': '|     \tCVE-2018-14628\t4.3\thttps://vulners.com/cve/CVE-2018-14628', 'tool': 'nmap', 'cve': 'CVE-2018-14628', 'id': 'CVE-2018-14628', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2018-1050\t4.3\thttps://vulners.com/cve/CVE-2018-1050', 'severity': 'medium', 'description': '|     \tCVE-2018-1050\t4.3\thttps://vulners.com/cve/CVE-2018-1050', 'tool': 'nmap', 'cve': 'CVE-2018-1050', 'id': 'CVE-2018-1050', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|     \tCVE-2021-43566\t2.5\thttps://vulners.com/cve/CVE-2021-43566', 'severity': 'medium', 'description': '|     \tCVE-2021-43566\t2.5\thttps://vulners.com/cve/CVE-2021-43566', 'tool': 'nmap', 'cve': 'CVE-2021-43566', 'id': 'CVE-2021-43566', 'source_tool': 'nmap', 'category': 'network'}, {'name': 'SMTP Service Detected', 'severity': 'medium', 'description': 'SMTP service - check for open relay', 'port': 25, 'solution': 'Review SMTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'SMB Service Detected', 'severity': 'high', 'description': 'SMB service - check for SMB vulnerabilities', 'port': 445, 'solution': 'Review SMB service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'type': 'Information Disclosure via ETags', 'severity': 'medium', 'description': 'GET /: Server leaks inodes via ETags, header found with file /, fields: 0x2aa6 0x58c4b46756eda', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Missing X-Frame-Options Header', 'severity': 'medium', 'description': 'GET /: The anti-clickjacking X-Frame-Options header is not present.', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'HTTP Methods Disclosure', 'severity': 'medium', 'description': 'OPTIONS /: Allowed HTTP Methods: GET, POST, OPTIONS, HEAD', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Security Finding', 'severity': 'low', 'description': '-561: GET /server-status: /server-status: This reveals Apache information. Comment out appropriate line in httpd.conf or restrict access to allowed hosts.', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Administrative Interface Found', 'severity': 'medium', 'description': 'GET /phpmyadmin/: /phpmyadmin/: phpMyAdmin directory found', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Missing X-Frame-Options', 'severity': 'medium', 'description': 'Clickjacking protection missing', 'source_tool': 'zap', 'category': 'web'}, {'type': 'Missing X-Content-Type-Options', 'severity': 'medium', 'description': 'MIME sniffing protection missing', 'source_tool': 'zap', 'category': 'web'}, {'type': 'Missing X-XSS-Protection', 'severity': 'medium', 'description': 'XSS protection header missing', 'source_tool': 'zap', 'category': 'web'}, {'type': 'Missing Strict-Transport-Security', 'severity': 'medium', 'description': 'HTTPS enforcement missing', 'source_tool': 'zap', 'category': 'web'}, {'type': 'Missing Content-Security-Policy', 'severity': 'medium', 'description': 'Content Security Policy missing', 'source_tool': 'zap', 'category': 'web'}, {'type': 'Server Information Disclosure', 'severity': 'low', 'description': 'Server header reveals: Apache/2.4.58 (Ubuntu)', 'source_tool': 'zap', 'category': 'web'}], 'ports': [{'port': 25, 'protocol': 'tcp', 'state': 'open', 'service': 'smtp', 'version': 'Postfix smtpd'}, {'port': 80, 'protocol': 'tcp', 'state': 'open', 'service': 'http', 'version': 'Apache httpd 2.4.58 ((Ubuntu))'}, {'port': 139, 'protocol': 'tcp', 'state': 'open', 'service': 'netbios-ssn', 'version': 'Samba smbd 4.6.2'}, {'port': 445, 'protocol': 'tcp', 'state': 'open', 'service': 'netbios-ssn', 'version': 'Samba smbd 4.6.2'}, {'port': 25, 'state': 'open', 'protocol': 'tcp', 'service': 'smtp', 'source_tool': 'metasploit'}, {'port': 80, 'state': 'open', 'protocol': 'tcp', 'service': 'http', 'source_tool': 'metasploit'}, {'port': 139, 'state': 'open', 'protocol': 'tcp', 'service': 'unknown', 'source_tool': 'metasploit'}, {'port': 445, 'state': 'open', 'protocol': 'tcp', 'service': 'unknown', 'source_tool': 'metasploit'}], 'summary': {'total_ports': 8, 'open_ports': 8, 'total_vulnerabilities': 191, 'high_severity': 1, 'medium_severity': 188, 'low_severity': 2, 'scan_phases': 1, 'tools_executed': 8}}
2025-06-30 19:21:02,005 - scan_af30f8a7-cc89-43cf-9ee5-1875f8b8349d - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 187.0s
