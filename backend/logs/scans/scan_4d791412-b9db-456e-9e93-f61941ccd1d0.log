2025-06-26 17:24:44,337 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - 🚀 SCAN STARTED - Category: vulnerability, Type: basic, Target: *************, Tools: openvas, metasploit, nuclei, nessus_scan
2025-06-26 17:24:44,340 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - 🚀 SCAN STARTED - Category: vulnerability, Type: basic, Target: *************, Tools: openvas, metasploit, nuclei, nessus_scan
2025-06-26 17:24:44,347 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on *************
2025-06-26 17:24:44,348 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on *************
2025-06-26 17:24:44,351 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - 🔧 TOOL START - NUCLEI - Command: nuclei scan on *************
2025-06-26 17:24:44,353 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - 🔧 TOOL START - NESSUS_SCAN - Command: nessus_scan scan on *************
2025-06-26 17:24:44,355 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-26 17:24:44,358 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-26 17:24:44,361 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - 📊 TOOL PROGRESS - NUCLEI: 10% - Starting Nuclei vulnerability scan...
2025-06-26 17:24:44,363 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - 📊 TOOL PROGRESS - NESSUS_SCAN: 10% - Starting Nessus-style vulnerability assessment...
2025-06-26 17:24:44,365 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-26 17:24:44,368 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - 📊 TOOL PROGRESS - NESSUS_SCAN: 20% - Phase 1: Service detection and version scanning...
2025-06-26 17:24:44,369 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-26 17:24:44,372 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - ERROR - ❌ TOOL ERROR - NUCLEI: Nuclei not installed or not found in PATH
2025-06-26 17:24:44,373 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - DEBUG - 📝 TOOL OUTPUT - NESSUS_SCAN (stdout): Running service detection: nmap -sV -sC --script=vuln -p 1-1000 --max-retries 1 --host-timeout 300s *************
2025-06-26 17:24:44,378 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - ✅ TOOL RESULT - NUCLEI: completed
2025-06-26 17:24:44,605 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-26 17:24:45,287 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-26 17:24:45,290 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_dea72c65-5e87-417f-9486-0db248b3d99c
2025-06-26 17:24:45,294 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 5 vulnerabilities
2025-06-26 17:25:38,527 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-26 17:25:38,532 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-26 17:26:19,768 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-26 17:26:19,772 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-26 17:26:56,625 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-26 17:26:56,629 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-26 17:27:30,387 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-26 17:27:30,394 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 4 modules - Found 0 potential vulnerabilities
2025-06-26 17:27:30,401 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-26 17:28:45,220 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - 📊 TOOL PROGRESS - NESSUS_SCAN: 40% - Phase 2: SSL/TLS security assessment...
2025-06-26 17:28:45,364 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - DEBUG - 📝 TOOL OUTPUT - NESSUS_SCAN (stdout): SSL check error: [Errno 111] Connection refused
2025-06-26 17:28:45,371 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - 📊 TOOL PROGRESS - NESSUS_SCAN: 60% - Phase 3: Web application security checks...
2025-06-26 17:28:45,376 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - 📊 TOOL PROGRESS - NESSUS_SCAN: 80% - Phase 4: Network security assessment...
2025-06-26 17:28:47,821 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - 📊 TOOL PROGRESS - NESSUS_SCAN: 100% - Nessus-style scan completed
2025-06-26 17:28:47,827 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - DEBUG - 📝 TOOL OUTPUT - NESSUS_SCAN (stdout): Assessment completed - Found 9 potential vulnerabilities
2025-06-26 17:28:47,841 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - ✅ TOOL RESULT - NESSUS_SCAN: completed - Found 9 vulnerabilities
2025-06-26 17:28:47,862 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'tools': {'nuclei': {'status': 'failed', 'error': 'Nuclei not installed. Install with: go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest'}, 'openvas': {'status': 'completed', 'scan_id': 'dea72c65-5e87-417f-9486-0db248b3d99c', 'task_id': 'greenbone_task_dea72c65-5e87-417f-9486-0db248b3d99c', 'target_id': 'greenbone_target_dea72c65-5e87-417f-9486-0db248b3d99c', 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'SMTP Service Detected', 'severity': 'medium', 'description': 'SMTP service - check for open relay', 'port': 25, 'solution': 'Review SMTP service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'POP3 Service Detected', 'severity': 'medium', 'description': 'POP3 service - unencrypted email protocol', 'port': 110, 'solution': 'Review POP3 service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'IMAP Service Detected', 'severity': 'medium', 'description': 'IMAP service - check for security configuration', 'port': 143, 'solution': 'Review IMAP service configuration and security settings', 'source_tool': 'openvas'}], 'message': 'Greenbone scan completed for *************', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/portscan/tcp', 'auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 4, 'raw_output': "=== TCP Port Scanner ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0mPORTS => 1-1000\n\x1b[0m\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:21 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:25 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:22 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:53 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:80 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:106 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:110 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:143 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:465 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:993 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:995 - TCP OPEN\n\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ************* - Key Fingerprint: ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIOpTVWZ/k5DUxBH0Ce/hiLXvstsiteakMsWsfmVk2i/j\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - SSH server version: SSH-2.0-OpenSSH_9.6p1 Ubuntu-3ubuntu13.12\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - Server Information and Encryption\n=================================\n\n  Type                     Value                                 Note\n  ----                     -----                                 ----\n  encryption.compression   none\n  encryption.compression   <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.encryption    aes128-ctr\n  encryption.encryption    aes192-ctr\n  encryption.encryption    aes256-ctr\n  encryption.encryption    <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          hmac-sha2-256\n  encryption.hmac          hmac-sha2-512\n  encryption.hmac          hmac-sha1\n  encryption.host_key      rsa-sha2-512\n  encryption.host_key      rsa-sha2-256\n  encryption.host_key      ecdsa-sha2-nistp256                   Weak elliptic curve\n  encryption.host_key      ssh-ed25519\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  curve25519-sha256\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  ecdh-sha2-nistp256\n  encryption.key_exchange  ecdh-sha2-nistp384\n  encryption.key_exchange  ecdh-sha2-nistp521\n  encryption.key_exchange  diffie-hellman-group-exchange-sha256\n  encryption.key_exchange  diffie-hellman-group16-sha512\n  encryption.key_exchange  diffie-hellman-group18-sha512\n  encryption.key_exchange  diffie-hellman-group14-sha256\n  encryption.key_exchange  ext-info-s\n  encryption.key_exchange  <EMAIL>\n  fingerprint_db           ssh.banner\n  openssh.comment          Ubuntu-3ubuntu13.12\n  os.certainty             0.75\n  os.cpe23                 cpe:/o:canonical:ubuntu_linux:-\n  os.family                Linux\n  os.product               Linux\n  os.vendor                Ubuntu\n  service.cpe23            cpe:/a:openbsd:openssh:9.6p1\n  service.family           OpenSSH\n  service.product          OpenSSH\n  service.protocol         ssh\n  service.vendor           OpenBSD\n  service.version          9.6p1\n\n\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m *************:445     - Rex::ConnectionRefused: The connection was refused by the remote host (*************:445).\n\x1b[1m\x1b[34m[*]\x1b[0m *************:445     - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}, 'nessus_scan': {'status': 'completed', 'vulnerabilities': [{'type': 'CVE Vulnerability - CVE-2011-1002', 'severity': 'high', 'description': '|   After NULL UDP avahi packet DoS (CVE-2011-1002).', 'cve_id': 'CVE-2011-1002', 'tool': 'nessus_scan', 'source_tool': 'nessus'}, {'type': 'CVE Vulnerability - CVE-2024-6387', 'severity': 'high', 'description': '|     \tCVE-2024-6387\t8.1\thttps://vulners.com/cve/CVE-2024-6387', 'cve_id': 'CVE-2024-6387', 'tool': 'nessus_scan', 'source_tool': 'nessus'}, {'type': 'CVE Vulnerability - CVE-2024-39894', 'severity': 'high', 'description': '|     \tCVE-2024-39894\t7.5\thttps://vulners.com/cve/CVE-2024-39894', 'cve_id': 'CVE-2024-39894', 'tool': 'nessus_scan', 'source_tool': 'nessus'}, {'type': 'CVE Vulnerability - CVE-2025-26465', 'severity': 'high', 'description': '|     \tCVE-2025-26465\t6.8\thttps://vulners.com/cve/CVE-2025-26465', 'cve_id': 'CVE-2025-26465', 'tool': 'nessus_scan', 'source_tool': 'nessus'}, {'type': 'CVE Vulnerability - CVE-2025-26466', 'severity': 'high', 'description': '|     \tCVE-2025-26466\t5.9\thttps://vulners.com/cve/CVE-2025-26466', 'cve_id': 'CVE-2025-26466', 'tool': 'nessus_scan', 'source_tool': 'nessus'}, {'type': 'CVE Vulnerability - CVE-2025-32728', 'severity': 'high', 'description': '|     \tCVE-2025-32728\t4.3\thttps://vulners.com/cve/CVE-2025-32728', 'cve_id': 'CVE-2025-32728', 'tool': 'nessus_scan', 'source_tool': 'nessus'}, {'type': 'FTP Service Exposed', 'severity': 'medium', 'description': 'FTP service detected on port 21', 'port': 21, 'tool': 'nessus_scan', 'source_tool': 'nessus'}, {'type': 'SMTP Service Exposed', 'severity': 'low', 'description': 'SMTP service detected on port 25', 'port': 25, 'tool': 'nessus_scan', 'source_tool': 'nessus'}, {'type': 'DNS Service Exposed', 'severity': 'low', 'description': 'DNS service detected on port 53', 'port': 53, 'tool': 'nessus_scan', 'source_tool': 'nessus'}], 'scan_time': '243 seconds', 'assessment_phases': 4, 'total_checks': 9, 'scan_type': 'comprehensive_vulnerability_assessment'}}, 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'SMTP Service Detected', 'severity': 'medium', 'description': 'SMTP service - check for open relay', 'port': 25, 'solution': 'Review SMTP service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'POP3 Service Detected', 'severity': 'medium', 'description': 'POP3 service - unencrypted email protocol', 'port': 110, 'solution': 'Review POP3 service configuration and security settings', 'source_tool': 'openvas'}, {'name': 'IMAP Service Detected', 'severity': 'medium', 'description': 'IMAP service - check for security configuration', 'port': 143, 'solution': 'Review IMAP service configuration and security settings', 'source_tool': 'openvas'}, {'type': 'CVE Vulnerability - CVE-2011-1002', 'severity': 'high', 'description': '|   After NULL UDP avahi packet DoS (CVE-2011-1002).', 'cve_id': 'CVE-2011-1002', 'tool': 'nessus_scan', 'source_tool': 'nessus'}, {'type': 'CVE Vulnerability - CVE-2024-6387', 'severity': 'high', 'description': '|     \tCVE-2024-6387\t8.1\thttps://vulners.com/cve/CVE-2024-6387', 'cve_id': 'CVE-2024-6387', 'tool': 'nessus_scan', 'source_tool': 'nessus'}, {'type': 'CVE Vulnerability - CVE-2024-39894', 'severity': 'high', 'description': '|     \tCVE-2024-39894\t7.5\thttps://vulners.com/cve/CVE-2024-39894', 'cve_id': 'CVE-2024-39894', 'tool': 'nessus_scan', 'source_tool': 'nessus'}, {'type': 'CVE Vulnerability - CVE-2025-26465', 'severity': 'high', 'description': '|     \tCVE-2025-26465\t6.8\thttps://vulners.com/cve/CVE-2025-26465', 'cve_id': 'CVE-2025-26465', 'tool': 'nessus_scan', 'source_tool': 'nessus'}, {'type': 'CVE Vulnerability - CVE-2025-26466', 'severity': 'high', 'description': '|     \tCVE-2025-26466\t5.9\thttps://vulners.com/cve/CVE-2025-26466', 'cve_id': 'CVE-2025-26466', 'tool': 'nessus_scan', 'source_tool': 'nessus'}, {'type': 'CVE Vulnerability - CVE-2025-32728', 'severity': 'high', 'description': '|     \tCVE-2025-32728\t4.3\thttps://vulners.com/cve/CVE-2025-32728', 'cve_id': 'CVE-2025-32728', 'tool': 'nessus_scan', 'source_tool': 'nessus'}, {'type': 'FTP Service Exposed', 'severity': 'medium', 'description': 'FTP service detected on port 21', 'port': 21, 'tool': 'nessus_scan', 'source_tool': 'nessus'}, {'type': 'SMTP Service Exposed', 'severity': 'low', 'description': 'SMTP service detected on port 25', 'port': 25, 'tool': 'nessus_scan', 'source_tool': 'nessus'}, {'type': 'DNS Service Exposed', 'severity': 'low', 'description': 'DNS service detected on port 53', 'port': 53, 'tool': 'nessus_scan', 'source_tool': 'nessus'}], 'ports': [], 'summary': {'total_vulnerabilities': 14, 'critical_severity': 0, 'high_severity': 6, 'medium_severity': 6, 'low_severity': 2, 'total_ports_scanned': 0, 'tools_executed': 3}}
2025-06-26 17:28:47,870 - scan_4d791412-b9db-456e-9e93-f61941ccd1d0 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 243.5s
