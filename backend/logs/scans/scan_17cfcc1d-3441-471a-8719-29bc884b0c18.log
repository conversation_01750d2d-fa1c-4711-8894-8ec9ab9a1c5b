2025-06-28 04:45:55,101 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 🚀 SCAN STARTED - Category: web, Type: aggressive, Target: http://*************, Tools: nikto, sqlmap, dirb, gobuster, zap
2025-06-28 04:45:55,107 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 🔧 TOOL START - NIKTO - Command: nikto scan on http://*************
2025-06-28 04:45:55,107 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 🔧 TOOL START - SQLMAP - Command: sqlmap scan on http://*************
2025-06-28 04:45:55,110 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 🔧 TOOL START - DIRB - Command: dirb scan on http://*************
2025-06-28 04:45:55,111 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 🔧 TOOL START - GOBUSTER - Command: gobuster scan on http://*************
2025-06-28 04:45:55,111 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 🔧 TOOL START - ZAP - Command: zap scan on http://*************
2025-06-28 04:45:55,116 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Using aggressive scan configuration: Comprehensive scan with high intensity
2025-06-28 04:45:55,120 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Using aggressive scan configuration: Comprehensive scan with high intensity
2025-06-28 04:45:55,120 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Using aggressive scan configuration: Comprehensive scan with high intensity
2025-06-28 04:45:55,121 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Using aggressive scan configuration: Comprehensive scan with high intensity
2025-06-28 04:45:55,124 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Intensity: high, Timeout: 1800s
2025-06-28 04:45:55,126 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Intensity: high, Timeout: 1800s
2025-06-28 04:45:55,129 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Using aggressive scan configuration: Comprehensive scan with high intensity
2025-06-28 04:45:55,130 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Intensity: high, Timeout: 1800s
2025-06-28 04:45:55,131 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Intensity: high, Timeout: 1800s
2025-06-28 04:45:55,133 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Starting Nikto web vulnerability scan...
2025-06-28 04:45:55,135 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Intensity: high, Timeout: 1800s
2025-06-28 04:45:55,138 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 10% - Starting Dirb directory scan (high intensity)...
2025-06-28 04:45:55,140 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - SQLMAP: 10% - Starting SQLMap scan (high intensity)...
2025-06-28 04:45:55,141 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 10% - Starting GoBuster directory scan (high intensity)...
2025-06-28 04:45:55,141 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - ZAP: 10% - Starting ZAP security scan...
2025-06-28 04:45:55,148 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Command: dirb http://************* /usr/share/dirb/wordlists/big.txt -w
2025-06-28 04:45:55,150 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Command: gobuster dir -u http://************* -w /usr/share/wordlists/dirb/big.txt -t 50 -q
2025-06-28 04:45:55,151 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Command: sqlmap -u http://************* --batch --level=3 --risk=3 --threads=3
2025-06-28 04:45:55,161 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 25% - Brute forcing directories... (0s elapsed)
2025-06-28 04:45:55,163 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 25% - Scanning directories... (0s elapsed)
2025-06-28 04:45:55,164 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - SQLMAP: 25% - Testing for SQL injection... (0s elapsed)
2025-06-28 04:45:58,142 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Scanning web vulnerabilities... 10% complete
2025-06-28 04:45:59,153 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - ZAP: 10% - ZAP security scanning... 10% complete
2025-06-28 04:46:00,171 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 27% - Scanning directories... (5s elapsed)
2025-06-28 04:46:00,171 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 28% - Brute forcing directories... (5s elapsed)
2025-06-28 04:46:00,171 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - SQLMAP: 27% - Testing for SQL injection... (5s elapsed)
2025-06-28 04:46:01,150 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Scanning web vulnerabilities... 25% complete
2025-06-28 04:46:03,160 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - ZAP: 30% - ZAP security scanning... 30% complete
2025-06-28 04:46:04,156 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - NIKTO: 40% - Scanning web vulnerabilities... 40% complete
2025-06-28 04:46:05,179 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 30% - Scanning directories... (10s elapsed)
2025-06-28 04:46:05,181 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 32% - Brute forcing directories... (10s elapsed)
2025-06-28 04:46:05,182 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - SQLMAP: 30% - Testing for SQL injection... (10s elapsed)
2025-06-28 04:46:07,161 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - NIKTO: 55% - Scanning web vulnerabilities... 55% complete
2025-06-28 04:46:07,165 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - ZAP: 50% - ZAP security scanning... 50% complete
2025-06-28 04:46:10,166 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - NIKTO: 70% - Scanning web vulnerabilities... 70% complete
2025-06-28 04:46:10,188 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 33% - Scanning directories... (15s elapsed)
2025-06-28 04:46:10,188 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 35% - Brute forcing directories... (15s elapsed)
2025-06-28 04:46:10,188 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - SQLMAP: 33% - Testing for SQL injection... (15s elapsed)
2025-06-28 04:46:11,168 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - ZAP: 70% - ZAP security scanning... 70% complete
2025-06-28 04:46:11,172 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - ZAP: 10% - Starting security scan...
2025-06-28 04:46:11,174 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - ZAP: 25% - Performing HTTP security checks...
2025-06-28 04:46:11,321 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - ZAP: 50% - Analyzing security headers...
2025-06-28 04:46:11,326 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - ZAP: 75% - Checking response content...
2025-06-28 04:46:11,331 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - ZAP: 100% - Security scan completed
2025-06-28 04:46:11,333 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Found 5 security alerts
2025-06-28 04:46:11,342 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - ✅ TOOL RESULT - ZAP: completed
2025-06-28 04:46:13,173 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - NIKTO: 85% - Scanning web vulnerabilities... 85% complete
2025-06-28 04:46:13,177 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Starting Nikto scan (high intensity)...
2025-06-28 04:46:13,180 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Executing Nikto command...
2025-06-28 04:46:13,183 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Command: nikto -h http://************* -Format txt -output /tmp/nikto_1751078773.txt -maxtime 1800 -T 9
2025-06-28 04:46:13,188 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Scanning... (0s elapsed)
2025-06-28 04:46:15,196 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 35% - Scanning directories... (20s elapsed)
2025-06-28 04:46:15,196 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 39% - Brute forcing directories... (20s elapsed)
2025-06-28 04:46:15,198 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - SQLMAP: 35% - Testing for SQL injection... (20s elapsed)
2025-06-28 04:46:20,203 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 43% - Brute forcing directories... (25s elapsed)
2025-06-28 04:46:20,204 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 38% - Scanning directories... (25s elapsed)
2025-06-28 04:46:20,206 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - SQLMAP: 38% - Testing for SQL injection... (25s elapsed)
2025-06-28 04:46:23,194 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - NIKTO: 28% - Scanning... (10s elapsed)
2025-06-28 04:46:25,210 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 41% - Scanning directories... (30s elapsed)
2025-06-28 04:46:25,212 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - SQLMAP: 41% - Testing for SQL injection... (30s elapsed)
2025-06-28 04:46:25,213 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 46% - Brute forcing directories... (30s elapsed)
2025-06-28 04:46:30,217 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 43% - Scanning directories... (35s elapsed)
2025-06-28 04:46:30,220 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - SQLMAP: 43% - Testing for SQL injection... (35s elapsed)
2025-06-28 04:46:30,221 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 50% - Brute forcing directories... (35s elapsed)
2025-06-28 04:46:33,198 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - NIKTO: 32% - Scanning... (20s elapsed)
2025-06-28 04:46:35,225 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 46% - Scanning directories... (40s elapsed)
2025-06-28 04:46:35,229 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - SQLMAP: 46% - Testing for SQL injection... (40s elapsed)
2025-06-28 04:46:35,229 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 53% - Brute forcing directories... (40s elapsed)
2025-06-28 04:46:40,231 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 49% - Scanning directories... (45s elapsed)
2025-06-28 04:46:40,234 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - SQLMAP: 49% - Testing for SQL injection... (45s elapsed)
2025-06-28 04:46:40,235 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 57% - Brute forcing directories... (45s elapsed)
2025-06-28 04:46:43,203 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - NIKTO: 90% - Parsing results...
2025-06-28 04:46:43,208 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - NIKTO: 100% - Nikto scan completed
2025-06-28 04:46:43,210 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Scan completed - Found 4 potential issues
2025-06-28 04:46:43,215 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - ✅ TOOL RESULT - NIKTO: completed - Found 4 vulnerabilities
2025-06-28 04:46:45,240 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 52% - Scanning directories... (50s elapsed)
2025-06-28 04:46:45,240 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - SQLMAP: 52% - Testing for SQL injection... (50s elapsed)
2025-06-28 04:46:45,243 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 61% - Brute forcing directories... (50s elapsed)
2025-06-28 04:46:50,248 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 54% - Scanning directories... (55s elapsed)
2025-06-28 04:46:50,248 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 64% - Brute forcing directories... (55s elapsed)
2025-06-28 04:46:50,250 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - SQLMAP: 90% - Parsing results...
2025-06-28 04:46:50,259 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - ✅ TOOL RESULT - SQLMAP: completed - Found 0 vulnerabilities
2025-06-28 04:46:55,254 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 68% - Brute forcing directories... (60s elapsed)
2025-06-28 04:46:55,255 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 57% - Scanning directories... (60s elapsed)
2025-06-28 04:47:00,260 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 72% - Brute forcing directories... (65s elapsed)
2025-06-28 04:47:00,261 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 60% - Scanning directories... (65s elapsed)
2025-06-28 04:47:05,266 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 75% - Brute forcing directories... (70s elapsed)
2025-06-28 04:47:05,267 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 62% - Scanning directories... (70s elapsed)
2025-06-28 04:47:10,272 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 79% - Brute forcing directories... (75s elapsed)
2025-06-28 04:47:10,272 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 65% - Scanning directories... (75s elapsed)
2025-06-28 04:47:15,278 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 82% - Brute forcing directories... (80s elapsed)
2025-06-28 04:47:15,279 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 68% - Scanning directories... (80s elapsed)
2025-06-28 04:47:20,286 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 86% - Brute forcing directories... (85s elapsed)
2025-06-28 04:47:20,287 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 71% - Scanning directories... (85s elapsed)
2025-06-28 04:47:25,294 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 73% - Scanning directories... (90s elapsed)
2025-06-28 04:47:30,300 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 76% - Scanning directories... (95s elapsed)
2025-06-28 04:47:35,306 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 79% - Scanning directories... (100s elapsed)
2025-06-28 04:47:40,312 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 81% - Scanning directories... (105s elapsed)
2025-06-28 04:47:45,317 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 84% - Scanning directories... (110s elapsed)
2025-06-28 04:47:50,323 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 87% - Scanning directories... (115s elapsed)
2025-06-28 04:48:00,292 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Scan timeout after 2 minutes - completing with current results
2025-06-28 04:48:00,299 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 90% - Parsing results...
2025-06-28 04:48:00,304 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - ✅ TOOL RESULT - GOBUSTER: completed - Found 0 directories
2025-06-28 04:48:00,327 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Scan timeout after 2 minutes - completing with current results
2025-06-28 04:48:00,332 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 📊 TOOL PROGRESS - DIRB: 90% - Parsing results...
2025-06-28 04:48:00,340 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - ✅ TOOL RESULT - DIRB: completed - Found 0 directories
2025-06-28 04:48:00,350 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'zap': {'status': 'completed', 'alerts': [{'risk': 'Medium', 'name': 'Missing X-Frame-Options', 'count': 1, 'description': 'Clickjacking protection missing'}, {'risk': 'Medium', 'name': 'Missing X-Content-Type-Options', 'count': 1, 'description': 'MIME sniffing protection missing'}, {'risk': 'Medium', 'name': 'Missing X-XSS-Protection', 'count': 1, 'description': 'XSS protection header missing'}, {'risk': 'Medium', 'name': 'Missing Strict-Transport-Security', 'count': 1, 'description': 'HTTPS enforcement missing'}, {'risk': 'Medium', 'name': 'Missing Content-Security-Policy', 'count': 1, 'description': 'Content Security Policy missing'}], 'scan_time': '0 seconds', 'raw_output': 'Basic security scan completed. Found 5 potential issues.'}, 'nikto': {'status': 'completed', 'vulnerabilities': [{'type': 'Security Finding', 'severity': 'medium', 'description': 'GET /: Retrieved x-powered-by header: Next.js'}, {'type': 'Information Disclosure via ETags', 'severity': 'medium', 'description': 'GET /: Server leaks inodes via ETags, header found with file /, fields: 0xon0518qny21e2'}, {'type': 'Missing X-Frame-Options Header', 'severity': 'medium', 'description': 'GET /: The anti-clickjacking X-Frame-Options header is not present.'}, {'type': 'Security Finding', 'severity': 'medium', 'description': "GET /LbwRPYmu/: Uncommon header 'refresh' found, with contents: 0;url=/LbwRPYmu"}], 'scan_time': '30 seconds', 'total_tests': 8, 'raw_output': "- Nikto v2.1.5/2.1.5\n+ Target Host: ip82-165-144-72.pbiaas.com\n+ Target Port: 80\n+ GET /: Retrieved x-powered-by header: Next.js\n+ GET /: Server leaks inodes via ETags, header found with file /, fields: 0xon0518qny21e2 \n+ GET /: The anti-clickjacking X-Frame-Options header is not present.\n+ GET /LbwRPYmu/: Uncommon header 'refresh' found, with contents: 0;url=/LbwRPYmu\n"}, 'sqlmap': {'status': 'completed', 'injections': [], 'raw_output': "        ___\n       __H__\n ___ ___[']_____ ___ ___  {1.8.4#stable}\n|_ -| . [)]     | .'| . |\n|___|_  [,]_|_|_|__,|  _|\n      |_|V...       |_|   https://sqlmap.org\n\n[!] legal disclaimer: Usage of sqlmap for attacking targets without prior mutual consent is illegal. It is the end user's responsibility to obey all applicable local, state and federal laws. Developers assume no liability and are not responsible for any misuse or damage caused by this program\n\n[*] starting @ 04:45:55 /2025-06-28/\n\n[04:45:55] [INFO] testing connection to the target URL\n[04:45:57] [INFO] testing if the target URL content is stable\n[04:45:57] [WARNING] target URL content is not stable (i.e. content differs). sqlmap will base the page comparison on a sequence matcher. If no dynamic nor injectable parameters are detected, or in case of junk results, refer to user's manual paragraph 'Page comparison'\nhow do you want to proceed? [(C)ontinue/(s)tring/(r)egex/(q)uit] C\n[04:45:57] [INFO] testing if parameter 'User-Agent' is dynamic\n[04:45:57] [WARNING] parameter 'User-Agent' does not appear to be dynamic\n[04:45:57] [WARNING] heuristic (basic) test shows that parameter 'User-Agent' might not be injectable\n[04:45:58] [INFO] testing for SQL injection on parameter 'User-Agent'\n[04:45:58] [INFO] testing 'AND boolean-based blind - WHERE or HAVING clause'\n[04:46:02] [INFO] parameter 'User-Agent' appears to be 'AND boolean-based blind - WHERE or HAVING clause' injectable \n[04:46:06] [INFO] heuristic (extended) test shows that the back-end DBMS could be 'MimerSQL' \nit looks like the back-end DBMS is 'MimerSQL'. Do you want to skip test payloads specific for other DBMSes? [Y/n] Y\nfor the remaining tests, do you want to include all tests for 'MimerSQL' extending provided level (3) value? [Y/n] Y\n[04:46:06] [INFO] testing 'Generic inline queries'\n[04:46:06] [INFO] testing 'Generic UNION query (NULL) - 1 to 20 columns'\n[04:46:06] [INFO] automatically extending ranges for UNION query injection technique tests as there is at least one other (potential) technique found\n[04:46:11] [INFO] testing 'Generic UNION query (random number) - 1 to 20 columns'\n[04:46:14] [INFO] testing 'Generic UNION query (NULL) - 21 to 40 columns'\n[04:46:17] [INFO] testing 'Generic UNION query (random number) - 21 to 40 columns'\n[04:46:19] [INFO] testing 'Generic UNION query (NULL) - 41 to 60 columns'\n[04:46:22] [INFO] checking if the injection point on User-Agent parameter 'User-Agent' is a false positive\n[04:46:22] [WARNING] false positive or unexploitable injection point detected\n[04:46:22] [WARNING] parameter 'User-Agent' does not seem to be injectable\n[04:46:22] [INFO] testing if parameter 'Referer' is dynamic\n[04:46:23] [INFO] parameter 'Referer' appears to be dynamic\n[04:46:23] [WARNING] heuristic (basic) test shows that parameter 'Referer' might not be injectable\n[04:46:23] [INFO] testing for SQL injection on parameter 'Referer'\n[04:46:23] [INFO] testing 'AND boolean-based blind - WHERE or HAVING clause'\n[04:46:33] [INFO] parameter 'Referer' appears to be 'AND boolean-based blind - WHERE or HAVING clause' injectable \n[04:46:33] [INFO] testing 'Generic inline queries'\n[04:46:33] [INFO] testing 'Generic UNION query (NULL) - 1 to 20 columns'\n[04:46:36] [INFO] testing 'Generic UNION query (random number) - 1 to 20 columns'\n[04:46:40] [INFO] testing 'Generic UNION query (NULL) - 21 to 40 columns'\n[04:46:42] [INFO] testing 'Generic UNION query (random number) - 21 to 40 columns'\n[04:46:45] [INFO] testing 'Generic UNION query (NULL) - 41 to 60 columns'\n[04:46:47] [INFO] checking if the injection point on Referer parameter 'Referer' is a false positive\n[04:46:47] [WARNING] false positive or unexploitable injection point detected\n[04:46:47] [WARNING] parameter 'Referer' does not seem to be injectable\n[04:46:47] [CRITICAL] all tested parameters do not appear to be injectable. Try to increase values for '--level'/'--risk' options if you wish to perform more tests. If you suspect that there is some kind of protection mechanism involved (e.g. WAF) maybe you could try to use option '--tamper' (e.g. '--tamper=space2comment') and/or switch '--random-agent'\n[04:46:47] [WARNING] your sqlmap version is outdated\n\n[*] ending @ 04:46:47 /2025-06-28/\n\n", 'vulnerabilities': []}, 'gobuster': {'status': 'completed', 'directories': [], 'raw_output': '\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_primer_behaviors_dist_esm_index_mjs-c44edfed7f0d.js" defer="defer"></script> (Status: 308) [Size: 235] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_primer_behaviors_dist_esm_index_mjs-c44edfed7f0d.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/high-contrast-cookie-a58297b2ebf8.js"></script> (Status: 308) [Size: 179] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/high-contrast-cookie-a58297b2ebf8.js%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/wp-runtime-cf56dabb355e.js" defer="defer"></script> (Status: 308) [Size: 189] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/wp-runtime-cf56dabb355e.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<link rel="preconnect" href="https://avatars.githubusercontent.com"> (Status: 308) [Size: 84] [--> /%3Clink%20rel=%22preconnect%22%20href=%22https:/avatars.githubusercontent.com%22%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_selector-observer_dist_index_esm_js-cdf2757bd188.js" defer="defer"></script> (Status: 308) [Size: 242] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_github_selector-observer_dist_index_esm_js-cdf2757bd188.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_relative-time-element_dist_index_js-5913bc24f35d.js" defer="defer"></script> (Status: 308) [Size: 242] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_github_relative-time-element_dist_index_js-5913bc24f35d.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/repository-fa462f1c51f1.css" /> (Status: 308) [Size: 165] [--> /%3Clink%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20href=%22https:/github.githubassets.com/assets/repository-fa462f1c51f1.css%22%20/%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_oddbird_popover-polyfill_dist_popover-fn_js-a8c266e5f126.js" defer="defer"></script> (Status: 308) [Size: 243] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_oddbird_popover-polyfill_dist_popover-fn_js-a8c266e5f126.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/code-5aa9e25e0a2c.css" /> (Status: 308) [Size: 159] [--> /%3Clink%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20href=%22https:/github.githubassets.com/assets/code-5aa9e25e0a2c.css%22%20/%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_auto-complete-element_dist_index_js-node_modules_github_catalyst_-8e9f78-c1e2fb329866.js" defer="defer"></script> (Status: 308) [Size: 279] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_github_auto-complete-element_dist_index_js-node_modules_github_catalyst_-8e9f78-c1e2fb329866.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_text-expander-element_dist_index_js-e50fb7a5fe8c.js" defer="defer"></script> (Status: 308) [Size: 242] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_github_text-expander-element_dist_index_js-e50fb7a5fe8c.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/github-elements-2224a8aae785.js" defer="defer"></script> (Status: 308) [Size: 194] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/github-elements-2224a8aae785.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<link rel="dns-prefetch" href="https://github.githubassets.com"> (Status: 308) [Size: 80] [--> /%3Clink%20rel=%22dns-prefetch%22%20href=%22https:/github.githubassets.com%22%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_morphdom_dist_morphdom-esm_js-300e8e4e0414.js" defer="defer"></script> (Status: 308) [Size: 229] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_morphdom_dist_morphdom-esm_js-300e8e4e0414.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/environment-89128d48c6ff.js" defer="defer"></script> (Status: 308) [Size: 190] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/environment-89128d48c6ff.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_turbo_dist_turbo_es2017-esm_js-595819d3686f.js" defer="defer"></script> (Status: 308) [Size: 237] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_github_turbo_dist_turbo_es2017-esm_js-595819d3686f.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_lit-html_lit-html_js-b93a87060d31.js" defer="defer"></script> (Status: 308) [Size: 220] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_lit-html_lit-html_js-b93a87060d31.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_braintree_browser-detection_dist_browser-detection_js-node_modules_githu-bb80ec-34c4b68b1dd3.js" defer="defer"></script> (Status: 308) [Size: 279] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_braintree_browser-detection_dist_browser-detection_js-node_modules_githu-bb80ec-34c4b68b1dd3.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/ui_packages_failbot_failbot_ts-7cc3ec44644a.js" defer="defer"></script> (Status: 308) [Size: 209] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/ui_packages_failbot_failbot_ts-7cc3ec44644a.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/element-registry-bde3cdbe9e92.js" defer="defer"></script> (Status: 308) [Size: 195] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/element-registry-bde3cdbe9e92.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<link rel="dns-prefetch" href="https://avatars.githubusercontent.com"> (Status: 308) [Size: 86] [--> /%3Clink%20rel=%22dns-prefetch%22%20href=%22https:/avatars.githubusercontent.com%22%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_mini-throttle_dist_index_js-node_modules_stacktrace-parser_dist_s-1d3d52-babac9434833.js" defer="defer"></script> (Status: 308) [Size: 279] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_github_mini-throttle_dist_index_js-node_modules_stacktrace-parser_dist_s-1d3d52-babac9434833.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_file-attachment-element_dist_index_js-node_modules_primer_view-co-cadbad-fad3eaf3c7ec.js" defer="defer"></script> (Status: 308) [Size: 279] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_github_file-attachment-element_dist_index_js-node_modules_primer_view-co-cadbad-fad3eaf3c7ec.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_filter-input-element_dist_index_js-node_modules_github_remote-inp-d8c643-251bc3964eb6.js" defer="defer"></script> (Status: 308) [Size: 279] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_github_filter-input-element_dist_index_js-node_modules_github_remote-inp-d8c643-251bc3964eb6.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_markdown-toolbar-element_dist_index_js-6a8c7d9a08fe.js" defer="defer"></script> (Status: 308) [Size: 245] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_github_markdown-toolbar-element_dist_index_js-6a8c7d9a08fe.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-893f9f-1bcf38e06f01.js" defer="defer"></script> (Status: 308) [Size: 279] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-893f9f-1bcf38e06f01.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_fzy_js_index_js-node_modules_github_paste-markdown_dist_index_js-63a26702fa42.js" defer="defer"></script> (Status: 308) [Size: 264] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_fzy_js_index_js-node_modules_github_paste-markdown_dist_index_js-63a26702fa42.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/primer-primitives-225433424a87.css" /> (Status: 308) [Size: 172] [--> /%3Clink%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20href=%22https:/github.githubassets.com/assets/primer-primitives-225433424a87.css%22%20/%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_color-convert_index_js-1a149db8dc99.js" defer="defer"></script> (Status: 308) [Size: 222] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_color-convert_index_js-1a149db8dc99.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_quote-selection_dist_index_js-node_modules_github_session-resume_-c1aa61-91618cb63471.js" defer="defer"></script> (Status: 308) [Size: 279] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_github_quote-selection_dist_index_js-node_modules_github_session-resume_-c1aa61-91618cb63471.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/light-c59dc71e3a4c.css" /><link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/light_high_contrast-4bf0cb726930.css" /><link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/dark-89751e879f8b.css" /><link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/dark_high_contrast-67c7180a598a.css" /><link data-color-theme="light" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/light-c59dc71e3a4c.css" /><link data-color-theme="light_high_contrast" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/light_high_contrast-4bf0cb726930.css" /><link data-color-theme="light_colorblind" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/light_colorblind-6060e905eb78.css" /><link data-color-theme="light_colorblind_high_contrast" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/light_colorblind_high_contrast-04e818620b9c.css" /><link data-color-theme="light_tritanopia" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/light_tritanopia-ae65df249e0f.css" /><link data-color-theme="light_tritanopia_high_contrast" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/light_tritanopia_high_contrast-fdadc12a1ec2.css" /><link data-color-theme="dark" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/dark-89751e879f8b.css" /><link data-color-theme="dark_high_contrast" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/dark_high_contrast-67c7180a598a.css" /><link data-color-theme="dark_colorblind" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/dark_colorblind-4277e18a7c75.css" /><link data-color-theme="dark_colorblind_high_contrast" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/dark_colorblind_high_contrast-2e33ed61bc8c.css" /><link data-color-theme="dark_tritanopia" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/dark_tritanopia-48d44d87614d.css" /><link data-color-theme="dark_tritanopia_high_contrast" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/dark_tritanopia_high_contrast-6adcb5080302.css" /><link data-color-theme="dark_dimmed" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/dark_dimmed-250cee4c1ea8.css" /><link data-color-theme="dark_dimmed_high_contrast" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/dark_dimmed_high_contrast-e3802beb8c06.css" /> (Status: 308) [Size: 3777] [--> /%3Clink%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20href=%22https:/github.githubassets.com/assets/light-c59dc71e3a4c.css%22%20/%3E%3Clink%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20href=%22https:/github.githubassets.com/assets/light_high_contrast-4bf0cb726930.css%22%20/%3E%3Clink%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20href=%22https:/github.githubassets.com/assets/dark-89751e879f8b.css%22%20/%3E%3Clink%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20href=%22https:/github.githubassets.com/assets/dark_high_contrast-67c7180a598a.css%22%20/%3E%3Clink%20data-color-theme=%22light%22%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20data-href=%22https:/github.githubassets.com/assets/light-c59dc71e3a4c.css%22%20/%3E%3Clink%20data-color-theme=%22light_high_contrast%22%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20data-href=%22https:/github.githubassets.com/assets/light_high_contrast-4bf0cb726930.css%22%20/%3E%3Clink%20data-color-theme=%22light_colorblind%22%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20data-href=%22https:/github.githubassets.com/assets/light_colorblind-6060e905eb78.css%22%20/%3E%3Clink%20data-color-theme=%22light_colorblind_high_contrast%22%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20data-href=%22https:/github.githubassets.com/assets/light_colorblind_high_contrast-04e818620b9c.css%22%20/%3E%3Clink%20data-color-theme=%22light_tritanopia%22%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20data-href=%22https:/github.githubassets.com/assets/light_tritanopia-ae65df249e0f.css%22%20/%3E%3Clink%20data-color-theme=%22light_tritanopia_high_contrast%22%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20data-href=%22https:/github.githubassets.com/assets/light_tritanopia_high_contrast-fdadc12a1ec2.css%22%20/%3E%3Clink%20data-color-theme=%22dark%22%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20data-href=%22https:/github.githubassets.com/assets/dark-89751e879f8b.css%22%20/%3E%3Clink%20data-color-theme=%22dark_high_contrast%22%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20data-href=%22https:/github.githubassets.com/assets/dark_high_contrast-67c7180a598a.css%22%20/%3E%3Clink%20data-color-theme=%22dark_colorblind%22%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20data-href=%22https:/github.githubassets.com/assets/dark_colorblind-4277e18a7c75.css%22%20/%3E%3Clink%20data-color-theme=%22dark_colorblind_high_contrast%22%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20data-href=%22https:/github.githubassets.com/assets/dark_colorblind_high_contrast-2e33ed61bc8c.css%22%20/%3E%3Clink%20data-color-theme=%22dark_tritanopia%22%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20data-href=%22https:/github.githubassets.com/assets/dark_tritanopia-48d44d87614d.css%22%20/%3E%3Clink%20data-color-theme=%22dark_tritanopia_high_contrast%22%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20data-href=%22https:/github.githubassets.com/assets/dark_tritanopia_high_contrast-6adcb5080302.css%22%20/%3E%3Clink%20data-color-theme=%22dark_dimmed%22%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20data-href=%22https:/github.githubassets.com/assets/dark_dimmed-250cee4c1ea8.css%22%20/%3E%3Clink%20data-color-theme=%22dark_dimmed_high_contrast%22%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20data-href=%22https:/github.githubassets.com/assets/dark_dimmed_high_contrast-e3802beb8c06.css%22%20/%3E]\n\n\x1b[2K/<link rel="dns-prefetch" href="https://github-cloud.s3.amazonaws.com"> (Status: 308) [Size: 86] [--> /%3Clink%20rel=%22dns-prefetch%22%20href=%22https:/github-cloud.s3.amazonaws.com%22%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/app_assets_modules_github_behaviors_task-list_ts-app_assets_modules_github_sso_ts-ui_packages-900dde-f953ddf42948.js" defer="defer"></script> (Status: 308) [Size: 279] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/app_assets_modules_github_behaviors_task-list_ts-app_assets_modules_github_sso_ts-ui_packages-900dde-f953ddf42948.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/global-d0507817f2fa.css" /> (Status: 308) [Size: 161] [--> /%3Clink%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20href=%22https:/github.githubassets.com/assets/global-d0507817f2fa.css%22%20/%3E]\n\n\x1b[2K/<link rel="preconnect" href="https://github.githubassets.com" crossorigin> (Status: 308) [Size: 92] [--> /%3Clink%20rel=%22preconnect%22%20href=%22https:/github.githubassets.com%22%20crossorigin%3E]\n\n\x1b[2K/<link rel="dns-prefetch" href="https://user-images.githubusercontent.com/"> (Status: 308) [Size: 91] [--> /%3Clink%20rel=%22dns-prefetch%22%20href=%22https:/user-images.githubusercontent.com/%22%3E]\n\n\x1b[2K/<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/github-58ac3ad6cb3f.css" /> (Status: 308) [Size: 161] [--> /%3Clink%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20href=%22https:/github.githubassets.com/assets/github-58ac3ad6cb3f.css%22%20/%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/app_assets_modules_github_behaviors_ajax-error_ts-app_assets_modules_github_behaviors_include-d0d0a6-a7da4270c5f4.js" defer="defer"></script> (Status: 308) [Size: 279] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/app_assets_modules_github_behaviors_ajax-error_ts-app_assets_modules_github_behaviors_include-d0d0a6-a7da4270c5f4.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/ui_packages_updatable-content_updatable-content_ts-a5daa16ae903.js" defer="defer"></script> (Status: 308) [Size: 229] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/ui_packages_updatable-content_updatable-content_ts-a5daa16ae903.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/app_assets_modules_github_sticky-scroll-into-view_ts-e45aabc67d13.js" defer="defer"></script> (Status: 308) [Size: 231] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/app_assets_modules_github_sticky-scroll-into-view_ts-e45aabc67d13.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_delegated-events_dist_index_js-node_modules_github_catalyst_lib_index_js-ea8eaa-eefe25567449.js" defer="defer"></script> (Status: 308) [Size: 279] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_delegated-events_dist_index_js-node_modules_github_catalyst_lib_index_js-ea8eaa-eefe25567449.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/primer-b4bd0459f984.css" /> (Status: 308) [Size: 161] [--> /%3Clink%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20href=%22https:/github.githubassets.com/assets/primer-b4bd0459f984.css%22%20/%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_lodash-es_isEqual_js-a0841ced23fc.js" defer="defer"></script> (Status: 308) [Size: 220] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_lodash-es_isEqual_js-a0841ced23fc.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_lodash-es__Stack_js-node_modules_lodash-es__Uint8Array_js-node_modules_l-4faaa6-16c4e2c524de.js" defer="defer"></script> (Status: 308) [Size: 279] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_lodash-es__Stack_js-node_modules_lodash-es__Uint8Array_js-node_modules_l-4faaa6-16c4e2c524de.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/app_assets_modules_github_behaviors_commenting_edit_ts-app_assets_modules_github_behaviors_ht-83c235-567e0f340e27.js" defer="defer"></script> (Status: 308) [Size: 279] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/app_assets_modules_github_behaviors_commenting_edit_ts-app_assets_modules_github_behaviors_ht-83c235-567e0f340e27.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/notifications-global-eadae94940d6.js" defer="defer"></script> (Status: 308) [Size: 199] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/notifications-global-eadae94940d6.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_dompurify_dist_purify_es_mjs-7457ebdd1a1f.js" defer="defer"></script> (Status: 308) [Size: 228] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_dompurify_dist_purify_es_mjs-7457ebdd1a1f.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_mini-throttle_dist_index_js-node_modules_github_catalyst_lib_inde-dbbea9-558c1f223d1d.js" defer="defer"></script> (Status: 308) [Size: 279] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_github_mini-throttle_dist_index_js-node_modules_github_catalyst_lib_inde-dbbea9-558c1f223d1d.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/react-core-b60e7ea28349.js" defer="defer"></script> (Status: 308) [Size: 189] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/react-core-b60e7ea28349.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/primer-react-a57080a0a6e8.js" defer="defer"></script> (Status: 308) [Size: 191] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/primer-react-a57080a0a6e8.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/code-menu-8c39716e9d81.js" defer="defer"></script> (Status: 308) [Size: 188] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/code-menu-8c39716e9d81.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_emotion_is-prop-valid_dist_emotion-is-prop-valid_esm_js-node_modules_emo-b1c483-b5947865157f.js" defer="defer"></script> (Status: 308) [Size: 279] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_emotion_is-prop-valid_dist_emotion-is-prop-valid_esm_js-node_modules_emo-b1c483-b5947865157f.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/react-lib-8705026b409a.js" defer="defer"></script> (Status: 308) [Size: 188] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/react-lib-8705026b409a.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/octicons-react-9fd6ca6872cc.js" defer="defer"></script> (Status: 308) [Size: 193] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/octicons-react-9fd6ca6872cc.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/behaviors-728e40423fc5.js" defer="defer"></script> (Status: 308) [Size: 188] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/behaviors-728e40423fc5.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_cookie_index_js-node_modules_primer_live-region-element_dist_esm_index_j-1ca8f6-89ab81577c38.js" defer="defer"></script> (Status: 308) [Size: 279] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_cookie_index_js-node_modules_primer_live-region-element_dist_esm_index_j-1ca8f6-89ab81577c38.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_catalyst_lib_index_js-node_modules_github_hydro-analytics-client_-189aa3-aa0d1c491a18.js" defer="defer"></script> (Status: 308) [Size: 279] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_github_catalyst_lib_index_js-node_modules_github_hydro-analytics-client_-189aa3-aa0d1c491a18.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/ui_packages_paths_index_ts-30c9ce58481b.js" defer="defer"></script> (Status: 308) [Size: 205] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/ui_packages_paths_index_ts-30c9ce58481b.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_focus-visible_dist_focus-visible_js-node_modules_fzy_js_index_js-node_mo-296806-a0e432a5dd85.js" defer="defer"></script> (Status: 308) [Size: 279] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_focus-visible_dist_focus-visible_js-node_modules_fzy_js_index_js-node_mo-296806-a0e432a5dd85.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_tanstack_react-virtual_dist_esm_index_js-807aab04afeb.js" defer="defer"></script> (Status: 308) [Size: 240] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_tanstack_react-virtual_dist_esm_index_js-807aab04afeb.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/ui_packages_document-metadata_document-metadata_ts-ui_packages_history_history_ts-ui_packages-417c81-00e1a3522739.js" defer="defer"></script> (Status: 308) [Size: 279] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/ui_packages_document-metadata_document-metadata_ts-ui_packages_history_history_ts-ui_packages-417c81-00e1a3522739.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/ui_packages_commit-attribution_index_ts-ui_packages_commit-checks-status_index_ts-ui_packages-762eaa-7383c64c0bfd.js" defer="defer"></script> (Status: 308) [Size: 279] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/ui_packages_commit-attribution_index_ts-ui_packages_commit-checks-status_index_ts-ui_packages-762eaa-7383c64c0bfd.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/ui_packages_ref-selector_RefSelector_tsx-d5cdb50eb045.js" defer="defer"></script> (Status: 308) [Size: 219] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/ui_packages_ref-selector_RefSelector_tsx-d5cdb50eb045.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/ui_packages_code-view-shared_hooks_use-canonical-object_ts-ui_packages_code-view-shared_hooks-6097ef-062d8d9cda55.js" defer="defer"></script> (Status: 308) [Size: 279] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/ui_packages_code-view-shared_hooks_use-canonical-object_ts-ui_packages_code-view-shared_hooks-6097ef-062d8d9cda55.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/ui_packages_diffs_diff-parts_ts-d15c96aca8c4.js" defer="defer"></script> (Status: 308) [Size: 210] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/ui_packages_diffs_diff-parts_ts-d15c96aca8c4.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/app_assets_modules_github_blob-anchor_ts-ui_packages_code-nav_code-nav_ts-ui_packages_filter--8253c1-5fde020dbad1.js" defer="defer"></script> (Status: 308) [Size: 279] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/app_assets_modules_github_blob-anchor_ts-ui_packages_code-nav_code-nav_ts-ui_packages_filter--8253c1-5fde020dbad1.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/ui_packages_app-uuid_app-uuid_ts-ui_packages_fetch-headers_fetch-headers_ts-ui_packages_repos-0cd8c2-74dfb3a39944.js" defer="defer"></script> (Status: 308) [Size: 279] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/ui_packages_app-uuid_app-uuid_ts-ui_packages_fetch-headers_fetch-headers_ts-ui_packages_repos-0cd8c2-74dfb3a39944.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/primer-react.6278980231d1a55c5718.module.css" /> (Status: 308) [Size: 182] [--> /%3Clink%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20href=%22https:/github.githubassets.com/assets/primer-react.6278980231d1a55c5718.module.css%22%20/%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/react-code-view-0fc72124775f.js" defer="defer"></script> (Status: 308) [Size: 194] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/react-code-view-0fc72124775f.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/react-code-view.4915ca2c68d1510cb07c.module.css" /> (Status: 308) [Size: 185] [--> /%3Clink%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20href=%22https:/github.githubassets.com/assets/react-code-view.4915ca2c68d1510cb07c.module.css%22%20/%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/notifications-subscriptions-menu-c9ab807bd021.js" defer="defer"></script> (Status: 308) [Size: 211] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/notifications-subscriptions-menu-c9ab807bd021.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/primer-react.6278980231d1a55c5718.module.css" /> (Status: 308) [Size: 182] [--> /%3Clink%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20href=%22https:/github.githubassets.com/assets/primer-react.6278980231d1a55c5718.module.css%22%20/%3E]\n\n\x1b[2K/<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/notifications-subscriptions-menu.07dab7f319b881c93ef5.module.css" /> (Status: 308) [Size: 202] [--> /%3Clink%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20href=%22https:/github.githubassets.com/assets/notifications-subscriptions-menu.07dab7f319b881c93ef5.module.css%22%20/%3E]\n\n\x1b[2K/<meta name="octolytics-url" content="https://collector.github.com/github/collect" /> (Status: 308) [Size: 102] [--> /%3Cmeta%20name=%22octolytics-url%22%20content=%22https:/collector.github.com/github/collect%22%20/%3E]\n\n\x1b[2K/<link rel="fluid-icon" href="https://github.com/fluidicon.png" title="GitHub"> (Status: 308) [Size: 100] [--> /%3Clink%20rel=%22fluid-icon%22%20href=%22https:/github.com/fluidicon.png%22%20title=%22GitHub%22%3E]\n\n\x1b[2K/<link rel="assets" href="https://github.githubassets.com/"> (Status: 308) [Size: 75] [--> /%3Clink%20rel=%22assets%22%20href=%22https:/github.githubassets.com/%22%3E]\n\n\x1b[2K/<meta name="apple-itunes-app" content="app-id=**********, app-argument=https://github.com/xmendez/wfuzz/blob/master/wordlist/general/big.txt" /> (Status: 308) [Size: 164] [--> /%3Cmeta%20name=%22apple-itunes-app%22%20content=%22app-id=**********,%20app-argument=https:/github.com/xmendez/wfuzz/blob/master/wordlist/general/big.txt%22%20/%3E]\n\n\x1b[2K/<meta property="og:image" content="https://opengraph.githubassets.com/e8353ff77b7131e858d99a11a93d74c516d077a7ba2c2772415a9d18402597d9/xmendez/wfuzz" /><meta property="og:image:alt" content="Web application fuzzer. Contribute to xmendez/wfuzz development by creating an account on GitHub." /><meta property="og:image:width" content="1200" /><meta property="og:image:height" content="600" /><meta property="og:site_name" content="GitHub" /><meta property="og:type" content="object" /><meta property="og:title" content="wfuzz/wordlist/general/big.txt at master · xmendez/wfuzz" /><meta property="og:url" content="https://github.com/xmendez/wfuzz/blob/master/wordlist/general/big.txt" /><meta property="og:description" content="Web application fuzzer. Contribute to xmendez/wfuzz development by creating an account on GitHub." /> (Status: 308) [Size: 1048] [--> /%3Cmeta%20property=%22og:image%22%20content=%22https:/opengraph.githubassets.com/e8353ff77b7131e858d99a11a93d74c516d077a7ba2c2772415a9d18402597d9/xmendez/wfuzz%22%20/%3E%3Cmeta%20property=%22og:image:alt%22%20content=%22Web%20application%20fuzzer.%20Contribute%20to%20xmendez/wfuzz%20development%20by%20creating%20an%20account%20on%20GitHub.%22%20/%3E%3Cmeta%20property=%22og:image:width%22%20content=%221200%22%20/%3E%3Cmeta%20property=%22og:image:height%22%20content=%22600%22%20/%3E%3Cmeta%20property=%22og:site_name%22%20content=%22GitHub%22%20/%3E%3Cmeta%20property=%22og:type%22%20content=%22object%22%20/%3E%3Cmeta%20property=%22og:title%22%20content=%22wfuzz/wordlist/general/big.txt%20at%20master%20%C2%B7%20xmendez/wfuzz%22%20/%3E%3Cmeta%20property=%22og:url%22%20content=%22https:/github.com/xmendez/wfuzz/blob/master/wordlist/general/big.txt%22%20/%3E%3Cmeta%20property=%22og:description%22%20content=%22Web%20application%20fuzzer.%20Contribute%20to%20xmendez/wfuzz%20development%20by%20creating%20an%20account%20on%20GitHub.%22%20/%3E]\n\n\x1b[2K/<meta name="twitter:image" content="https://opengraph.githubassets.com/e8353ff77b7131e858d99a11a93d74c516d077a7ba2c2772415a9d18402597d9/xmendez/wfuzz" /><meta name="twitter:site" content="@github" /><meta name="twitter:card" content="summary_large_image" /><meta name="twitter:title" content="wfuzz/wordlist/general/big.txt at master · xmendez/wfuzz" /><meta name="twitter:description" content="Web application fuzzer. Contribute to xmendez/wfuzz development by creating an account on GitHub." /> (Status: 308) [Size: 623] [--> /%3Cmeta%20name=%22twitter:image%22%20content=%22https:/opengraph.githubassets.com/e8353ff77b7131e858d99a11a93d74c516d077a7ba2c2772415a9d18402597d9/xmendez/wfuzz%22%20/%3E%3Cmeta%20name=%22twitter:site%22%20content=%22@github%22%20/%3E%3Cmeta%20name=%22twitter:card%22%20content=%22summary_large_image%22%20/%3E%3Cmeta%20name=%22twitter:title%22%20content=%22wfuzz/wordlist/general/big.txt%20at%20master%20%C2%B7%20xmendez/wfuzz%22%20/%3E%3Cmeta%20name=%22twitter:description%22%20content=%22Web%20application%20fuzzer.%20Contribute%20to%20xmendez/wfuzz%20development%20by%20creating%20an%20account%20on%20GitHub.%22%20/%3E]\n\n\x1b[2K/<meta name="go-import" content="github.com/xmendez/wfuzz git https://github.com/xmendez/wfuzz.git"> (Status: 308) [Size: 119] [--> /%3Cmeta%20name=%22go-import%22%20content=%22github.com/xmendez/wfuzz%20git%20https:/github.com/xmendez/wfuzz.git%22%3E]\n\n\x1b[2K/<link rel="alternate icon" class="js-site-favicon" type="image/png" href="https://github.githubassets.com/favicons/favicon.png"> (Status: 308) [Size: 158] [--> /%3Clink%20rel=%22alternate%20icon%22%20class=%22js-site-favicon%22%20type=%22image/png%22%20href=%22https:/github.githubassets.com/favicons/favicon.png%22%3E]\n\n\x1b[2K/<meta name="browser-errors-url" content="https://api.github.com/_private/browser/errors"> (Status: 308) [Size: 105] [--> /%3Cmeta%20name=%22browser-errors-url%22%20content=%22https:/api.github.com/_private/browser/errors%22%3E]\n\n\x1b[2K/<meta name="browser-stats-url" content="https://api.github.com/_private/browser/stats"> (Status: 308) [Size: 103] [--> /%3Cmeta%20name=%22browser-stats-url%22%20content=%22https:/api.github.com/_private/browser/stats%22%3E]\n\n\x1b[2K/<link rel="mask-icon" href="https://github.githubassets.com/assets/pinned-octocat-093da3e6fa40.svg" color="#000000"> (Status: 308) [Size: 125] [--> /%3Clink%20rel=%22mask-icon%22%20href=%22https:/github.githubassets.com/assets/pinned-octocat-093da3e6fa40.svg%22%20color=%22]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/ui_packages_ui-commands_ui-commands_ts-b755d908e0b1.js" defer="defer"></script> (Status: 308) [Size: 217] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/ui_packages_ui-commands_ui-commands_ts-b755d908e0b1.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<link rel="icon" class="js-site-favicon" type="image/svg+xml" href="https://github.githubassets.com/favicons/favicon.svg" data-base-href="https://github.githubassets.com/favicons/favicon"> (Status: 308) [Size: 221] [--> /%3Clink%20rel=%22icon%22%20class=%22js-site-favicon%22%20type=%22image/svg+xml%22%20href=%22https:/github.githubassets.com/favicons/favicon.svg%22%20data-base-href=%22https:/github.githubassets.com/favicons/favicon%22%3E]\n\n\x1b[2K/<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/primer-react.6278980231d1a55c5718.module.css" /> (Status: 308) [Size: 182] [--> /%3Clink%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20href=%22https:/github.githubassets.com/assets/primer-react.6278980231d1a55c5718.module.css%22%20/%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-94fd67-99b04cc350b5.js" defer="defer"></script> (Status: 308) [Size: 279] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-94fd67-99b04cc350b5.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<script type="application/json" data-target="react-partial.embeddedData">{"props":{"docsUrl":"https://docs.github.com/get-started/accessibility/keyboard-shortcuts"}}</script> (Status: 308) [Size: 214] [--> /%3Cscript%20type=%22application/json%22%20data-target=%22react-partial.embeddedData%22%3E%7B%22props%22:%7B%22docsUrl%22:%22https:/docs.github.com/get-started/accessibility/keyboard-shortcuts%22%7D%7D%3C/script%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/keyboard-shortcuts-dialog-b3dd4b1cb532.js" defer="defer"></script> (Status: 308) [Size: 204] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/keyboard-shortcuts-dialog-b3dd4b1cb532.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/keyboard-shortcuts-dialog.47de85e2c17af43cefd5.module.css" /> (Status: 308) [Size: 195] [--> /%3Clink%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20href=%22https:/github.githubassets.com/assets/keyboard-shortcuts-dialog.47de85e2c17af43cefd5.module.css%22%20/%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/sessions-eed3aa0554dd.js" defer="defer"></script> (Status: 308) [Size: 187] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/sessions-eed3aa0554dd.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;site header menu&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;SIGN_UP&quot;,&quot;originating_url&quot;:&quot;https://github.com/xmendez/wfuzz/blob/master/wordlist/general/big.txt&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="f96a1f8960da60bf2ea2f21c2791f6ecbdb6843a1ea80e471f0980e9c68a8c3c" (Status: 308) [Size: 475] [--> /data-hydro-click=%22%7B&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:%7B&quot;location_in_page&quot;:&quot;site%20header%20menu&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;SIGN_UP&quot;,&quot;originating_url&quot;:&quot;https:/github.com/xmendez/wfuzz/blob/master/wordlist/general/big.txt&quot;,&quot;user_id&quot;:null%7D%7D%22%20data-hydro-click-hmac=%22f96a1f8960da60bf2ea2f21c2791f6ecbdb6843a1ea80e471f0980e9c68a8c3c%22]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/ui_packages_document-metadata_document-metadata_ts-ui_packages_promise-with-resolvers-polyfil-1e7a2a-b50af437b812.js" defer="defer"></script> (Status: 308) [Size: 279] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/ui_packages_document-metadata_document-metadata_ts-ui_packages_promise-with-resolvers-polyfil-1e7a2a-b50af437b812.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/primer-react.6278980231d1a55c5718.module.css" /> (Status: 308) [Size: 182] [--> /%3Clink%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20href=%22https:/github.githubassets.com/assets/primer-react.6278980231d1a55c5718.module.css%22%20/%3E]\n\n\x1b[2K/<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/appearance-settings-631c3b2ed371.js" defer="defer"></script> (Status: 308) [Size: 198] [--> /%3Cscript%20crossorigin=%22anonymous%22%20type=%22application/javascript%22%20src=%22https:/github.githubassets.com/assets/appearance-settings-631c3b2ed371.js%22%20defer=%22defer%22%3E%3C/script%3E]\n\n\x1b[2K/<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/appearance-settings.4e1ca273f504ba849f8c.module.css" /> (Status: 308) [Size: 189] [--> /%3Clink%20crossorigin=%22anonymous%22%20media=%22all%22%20rel=%22stylesheet%22%20href=%22https:/github.githubassets.com/assets/appearance-settings.4e1ca273f504ba849f8c.module.css%22%20/%3E]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;github_copilot&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;github_copilot_link_product_navbar&quot;}" href="https://github.com/features/copilot"> (Status: 308) [Size: 487] [--> /%3Ca%20class=%22HeaderMenu-dropdown-link%20d-block%20no-underline%20position-relative%20py-2%20Link--secondary%20d-flex%20flex-items-center%20Link--has-description%20pb-lg-3%22%20data-analytics-event=%22%7B&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;github_copilot&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;github_copilot_link_product_navbar&quot;%7D%22%20href=%22https:/github.com/features/copilot%22%3E]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;github_models&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;github_models_link_product_navbar&quot;}" href="https://github.com/features/models"> (Status: 308) [Size: 484] [--> /%3Ca%20class=%22HeaderMenu-dropdown-link%20d-block%20no-underline%20position-relative%20py-2%20Link--secondary%20d-flex%20flex-items-center%20Link--has-description%20pb-lg-3%22%20data-analytics-event=%22%7B&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;github_models&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;github_models_link_product_navbar&quot;%7D%22%20href=%22https:/github.com/features/models%22%3E]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;github_advanced_security&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;github_advanced_security_link_product_navbar&quot;}" href="https://github.com/security/advanced-security"> (Status: 308) [Size: 517] [--> /%3Ca%20class=%22HeaderMenu-dropdown-link%20d-block%20no-underline%20position-relative%20py-2%20Link--secondary%20d-flex%20flex-items-center%20Link--has-description%20pb-lg-3%22%20data-analytics-event=%22%7B&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;github_advanced_security&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;github_advanced_security_link_product_navbar&quot;%7D%22%20href=%22https:/github.com/security/advanced-security%22%3E]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;actions&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;actions_link_product_navbar&quot;}" href="https://github.com/features/actions"> (Status: 308) [Size: 473] [--> /%3Ca%20class=%22HeaderMenu-dropdown-link%20d-block%20no-underline%20position-relative%20py-2%20Link--secondary%20d-flex%20flex-items-center%20Link--has-description%20pb-lg-3%22%20data-analytics-event=%22%7B&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;actions&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;actions_link_product_navbar&quot;%7D%22%20href=%22https:/github.com/features/actions%22%3E]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;codespaces&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;codespaces_link_product_navbar&quot;}" href="https://github.com/features/codespaces"> (Status: 308) [Size: 472] [--> /%3Ca%20class=%22HeaderMenu-dropdown-link%20d-block%20no-underline%20position-relative%20py-2%20Link--secondary%20d-flex%20flex-items-center%20Link--has-description%22%20data-analytics-event=%22%7B&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;codespaces&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;codespaces_link_product_navbar&quot;%7D%22%20href=%22https:/github.com/features/codespaces%22%3E]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;issues&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;issues_link_product_navbar&quot;}" href="https://github.com/features/issues"> (Status: 308) [Size: 470] [--> /%3Ca%20class=%22HeaderMenu-dropdown-link%20d-block%20no-underline%20position-relative%20py-2%20Link--secondary%20d-flex%20flex-items-center%20Link--has-description%20pb-lg-3%22%20data-analytics-event=%22%7B&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;issues&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;issues_link_product_navbar&quot;%7D%22%20href=%22https:/github.com/features/issues%22%3E]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;code_review&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;code_review_link_product_navbar&quot;}" href="https://github.com/features/code-review"> (Status: 308) [Size: 485] [--> /%3Ca%20class=%22HeaderMenu-dropdown-link%20d-block%20no-underline%20position-relative%20py-2%20Link--secondary%20d-flex%20flex-items-center%20Link--has-description%20pb-lg-3%22%20data-analytics-event=%22%7B&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;code_review&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;code_review_link_product_navbar&quot;%7D%22%20href=%22https:/github.com/features/code-review%22%3E]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;discussions&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;discussions_link_product_navbar&quot;}" href="https://github.com/features/discussions"> (Status: 308) [Size: 485] [--> /%3Ca%20class=%22HeaderMenu-dropdown-link%20d-block%20no-underline%20position-relative%20py-2%20Link--secondary%20d-flex%20flex-items-center%20Link--has-description%20pb-lg-3%22%20data-analytics-event=%22%7B&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;discussions&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;discussions_link_product_navbar&quot;%7D%22%20href=%22https:/github.com/features/discussions%22%3E]\n\n\x1b[2K/<a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;code_search&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;code_search_link_product_navbar&quot;}" href="https://github.com/features/code-search"> (Status: 308) [Size: 475] [--> /%3Ca%20class=%22HeaderMenu-dropdown-link%20d-block%20no-underline%20position-relative%20py-2%20Link--secondary%20d-flex%20flex-items-center%20Link--has-description%22%20data-analytics-event=%22%7B&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;code_search&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;code_search_link_product_navbar&quot;%7D%22%20href=%22https:/github.com/features/code-search%22%3E]\n', 'found_paths': 0}, 'dirb': {'status': 'completed', 'directories': [], 'raw_output': '\n-----------------\nDIRB v2.22    \nBy The Dark Raver\n-----------------\n\nSTART_TIME: Sat Jun 28 04:45:55 2025\nURL_BASE: http://*************/\nWORDLIST_FILES: /usr/share/dirb/wordlists/big.txt\nOPTION: Not Stopping on warning messages\n\n-----------------\n\n*** Generating Wordlist...\n                                                                               \nGENERATED WORDS: 20458\n\n---- Scanning URL: http://*************/ ----\n*** Calculating NOT_FOUND code...\n                                                                               \n                                                                                                                                                                                       \n--> Testing: http://*************/!\n                                                                                                                                                                                       \n--> Testing: http://*************/!_archives\n                                                                                                                                                                                       \n--> Testing: http://*************/!_images\n                                                                                                                                                                                       \n--> Testing: http://*************/!backup\n                                                                                                                                                                                       \n--> Testing: http://*************/!images\n                                                                                                                                                                                       \n--> Testing: http://*************/!res\n                                                                                                                                                                                       \n--> Testing: http://*************/!textove_diskuse\n                                                                                                                                                                                       \n--> Testing: http://*************/!ut\n                                                                                                                                                                                       \n--> Testing: http://*************/.bash_history\n                                                                                                                                                                                       \n--> Testing: http://*************/.bashrc\n                                                                                                                                                                                       \n--> Testing: http://*************/.cvs\n                                                                                                                                                                                       \n--> Testing: http://*************/.cvsignore\n                                                                                                                                                                                       \n--> Testing: http://*************/.forward\n                                                                                                                                                                                       \n--> Testing: http://*************/.history\n                                                                                                                                                                                       \n--> Testing: http://*************/.htaccess\n                                                                                                                                                                                       \n--> Testing: http://*************/.htpasswd\n                                                                                                                                                                                       \n--> Testing: http://*************/.listing\n                                                                                                                                                                                       \n--> Testing: http://*************/.passwd\n                                                                                                                                                                                       \n--> Testing: http://*************/.perf\n                                                                                                                                                                                       \n--> Testing: http://*************/.profile\n                                                                                                                                                                                       \n--> Testing: http://*************/.rhosts\n                                                                                                                                                                                       \n--> Testing: http://*************/.ssh\n                                                                                                                                                                                       \n--> Testing: http://*************/.subversion\n                                                                                                                                                                                       \n--> Testing: http://*************/.svn\n                                                                                                                                                                                       \n--> Testing: http://*************/.web\n                                                                                                                                                                                       \n--> Testing: http://*************/0\n                                                                                                                                                                                       \n--> Testing: http://*************/0-0-1\n                                                                                                                                                                                       \n--> Testing: http://*************/0-12\n                                                                                                                                                                                       \n--> Testing: http://*************/0-newstore\n                                                                                                                                                                                       \n--> Testing: http://*************/00\n                                                                                                                                                                                       \n--> Testing: http://*************/00-backup\n                                                                                                                                                                                       \n--> Testing: http://*************/00-cache\n                                                                                                                                                                                       \n--> Testing: http://*************/00-img\n                                                                                                                                                                                       \n--> Testing: http://*************/00-inc\n                                                                                                                                                                                       \n--> Testing: http://*************/00-mp\n                                                                                                                                                                                       \n--> Testing: http://*************/00-ps\n                                                                                                                                                                                       \n--> Testing: http://*************/000\n                                                                                                                                                                                       \n--> Testing: http://*************/0000\n                                                                                                                                                                                       \n--> Testing: http://*************/000000\n                                                                                                                                                                                       \n--> Testing: http://*************/00000000\n                                                                                                                                                                                       \n--> Testing: http://*************/0001\n                                                                                                                                                                                       \n--> Testing: http://*************/0007\n                                                                                                                                                                                       \n--> Testing: http://*************/001\n                                                                                                                                                                                       \n--> Testing: http://*************/002\n                                                                                                                                                                                       \n--> Testing: http://*************/007\n                                                                                                                                                                                       \n--> Testing: http://*************/007007\n                                                                                                                                                                                       \n--> Testing: http://*************/01\n                                                                                                                                                                                       \n--> Testing: http://*************/02\n                                                                                                                                                                                       \n--> Testing: http://*************/0246\n                                                                                                                                                                                       \n--> Testing: http://*************/0249\n                                                                                                                                                                                       \n--> Testing: http://*************/03\n                                                                                                                                                                                       \n--> Testing: http://*************/04\n                                                                                                                                                                                       \n--> Testing: http://*************/05\n                                                                                                                                                                                       \n--> Testing: http://*************/0594wm\n                                                                                                                                                                                       \n--> Testing: http://*************/06\n                                                                                                                                                                                       \n--> Testing: http://*************/07\n                                                                                                                                                                                       \n--> Testing: http://*************/08\n                                                                                                                                                                                       \n--> Testing: http://*************/09\n                                                                                                                                                                                       \n--> Testing: http://*************/1\n                                                                                                                                                                                       \n--> Testing: http://*************/10\n                                                                                                                                                                                       \n--> Testing: http://*************/100\n                                                                                                                                                                                       \n--> Testing: http://*************/1000\n                                                                                                                                                                                       \n--> Testing: http://*************/1001\n                                                                                                                                                                                       \n--> Testing: http://*************/1009\n                                                                                                                                                                                       \n--> Testing: http://*************/101\n                                                                                                                                                                                       \n--> Testing: http://*************/102\n                                                                                                                                                                                       \n--> Testing: http://*************/1022\n                                                                                                                                                                                       \n--> Testing: http://*************/1024\n                                                                                                                                                                                       \n--> Testing: http://*************/103\n                                                                                                                                                                                       \n--> Testing: http://*************/104\n                                                                                                                                                                                       \n--> Testing: http://*************/105\n                                                                                                                                                                                       \n--> Testing: http://*************/106\n                                                                                                                                                                                       \n--> Testing: http://*************/10668\n                                                                                                                                                                                       \n--> Testing: http://*************/107\n                                                                                                                                                                                       \n--> Testing: http://*************/108\n                                                                                                                                                                                       \n--> Testing: http://*************/109\n                                                                                                                                                                                       \n--> Testing: http://*************/10sne1\n                                                                                                                                                                                       \n--> Testing: http://*************/11\n                                                                                                                                                                                       \n--> Testing: http://*************/110\n                                                                                                                                                                                       \n--> Testing: http://*************/111\n                                                                                                                                                                                       \n--> Testing: http://*************/1111\n                                                                                                                                                                                       \n--> Testing: http://*************/111111\n                                                                                                                                                                                       \n--> Testing: http://*************/112\n                                                                                                                                                                                       \n--> Testing: http://*************/113\n                                                                                                                                                                                       \n--> Testing: http://*************/114\n                                                                                                                                                                                       \n--> Testing: http://*************/115\n                                                                                                                                                                                       \n--> Testing: http://*************/116\n                                                                                                                                                                                       \n--> Testing: http://*************/1166\n                                                                                                                                                                                       \n--> Testing: http://*************/1168\n                                                                                                                                                                                       \n--> Testing: http://*************/1169\n                                                                                                                                                                                       \n--> Testing: http://*************/117\n                                                                                                                                                                                       \n--> Testing: http://*************/1173\n                                                                                                                                                                                       \n--> Testing: http://*************/1178\n                                                                                                                                                                                       \n--> Testing: http://*************/1179\n                                                                                                                                                                                       \n--> Testing: http://*************/118\n                                                                                                                                                                                       \n--> Testing: http://*************/1187\n                                                                                                                                                                                       \n--> Testing: http://*************/1188\n                                                                                                                                                                                       \n--> Testing: http://*************/1189\n                                                                                                                                                                                       \n--> Testing: http://*************/119\n                                                                                                                                                                                       \n--> Testing: http://*************/1191\n                                                                                                                                                                                       \n--> Testing: http://*************/1193\n                                                                                                                                                                                       \n--> Testing: http://*************/12\n                                                                                                                                                                                       \n--> Testing: http://*************/120\n                                                                                                                                                                                       \n--> Testing: http://*************/1203\n                                                                                                                                                                                       \n--> Testing: http://*************/1204\n                                                                                                                                                                                       \n--> Testing: http://*************/1205\n                                                                                                                                                                                       \n--> Testing: http://*************/1208\n                                                                                                                                                                                       \n--> Testing: http://*************/121\n                                                                                                                                                                                       \n--> Testing: http://*************/1210\n                                                                                                                                                                                       \n--> Testing: http://*************/1211\n                                                                                                                                                                                       \n--> Testing: http://*************/1212\n                                                                                                                                                                                       \n--> Testing: http://*************/121212\n                                                                                                                                                                                       \n--> Testing: http://*************/1213\n                                                                                                                                                                                       \n--> Testing: http://*************/1214\n                                                                                                                                                                                       \n--> Testing: http://*************/1215\n                                                                                                                                                                                       \n--> Testing: http://*************/1216\n                                                                                                                                                                                       \n--> Testing: http://*************/1217\n                                                                                                                                                                                       \n--> Testing: http://*************/1218\n                                                                                                                                                                                       \n--> Testing: http://*************/122\n                                                                                                                                                                                       \n--> Testing: http://*************/1221\n                                                                                                                                                                                       \n--> Testing: http://*************/1222\n                                                                                                                                                                                       \n--> Testing: http://*************/1224\n                                                                                                                                                                                       \n--> Testing: http://*************/1225\n                                                                                                                                                                                       \n--> Testing: http://*************/1229\n                                                                                                                                                                                       \n--> Testing: http://*************/123\n                                                                                                                                                                                       \n--> Testing: http://*************/1230\n                                                                                                                                                                                       \n--> Testing: http://*************/123123\n                                                                                                                                                                                       \n--> Testing: http://*************/1234\n                                                                                                                                                                                       \n--> Testing: http://*************/12345\n                                                                                                                                                                                       \n--> Testing: http://*************/123456\n                                                                                                                                                                                       \n--> Testing: http://*************/1234567\n                                                                                                                                                                                       \n--> Testing: http://*************/12345678\n                                                                                                                                                                                       \n--> Testing: http://*************/1234qwer\n                                                                                                                                                                                       \n--> Testing: http://*************/1237\n                                                                                                                                                                                       \n--> Testing: http://*************/123abc\n                                                                                                                                                                                       \n--> Testing: http://*************/123go\n                                                                                                                                                                                       \n--> Testing: http://*************/124\n                                                                                                                                                                                       \n--> Testing: http://*************/1244\n                                                                                                                                                                                       \n--> Testing: http://*************/125\n                                                                                                                                                                                       \n--> Testing: http://*************/1250\n                                                                                                                                                                                       \n--> Testing: http://*************/126\n                                                                                                                                                                                       \n--> Testing: http://*************/1261\n                                                                                                                                                                                       \n--> Testing: http://*************/1263\n                                                                                                                                                                                       \n--> Testing: http://*************/127\n                                                                                                                                                                                       \n--> Testing: http://*************/1273\n                                                                                                                                                                                       \n--> Testing: http://*************/1277\n                                                                                                                                                                                       \n--> Testing: http://*************/1278\n                                                                                                                                                                                       \n--> Testing: http://*************/128\n                                                                                                                                                                                       \n--> Testing: http://*************/1280\n                                                                                                                                                                                       \n--> Testing: http://*************/1283\n                                                                                                                                                                                       \n--> Testing: http://*************/129\n                                                                                                                                                                                       \n--> Testing: http://*************/1291\n                                                                                                                                                                                       \n--> Testing: http://*************/1298\n                                                                                                                                                                                       \n--> Testing: http://*************/12all\n                                                                                                                                                                                       \n--> Testing: http://*************/12xyz34\n                                                                                                                                                                                       \n--> Testing: http://*************/13\n                                                                                                                                                                                       \n--> Testing: http://*************/130\n                                                                                                                                                                                       \n--> Testing: http://*************/131\n                                                                                                                                                                                       \n--> Testing: http://*************/1312\n                                                                                                                                                                                       \n--> Testing: http://*************/1313\n                                                                                                                                                                                       \n--> Testing: http://*************/131313\n                                                                                                                                                                                       \n--> Testing: http://*************/132\n                                                                                                                                                                                       \n--> Testing: http://*************/1320\n                                                                                                                                                                                       \n--> Testing: http://*************/1324\n                                                                                                                                                                                       \n--> Testing: http://*************/133\n                                                                                                                                                                                       \n--> Testing: http://*************/1332\n                                                                                                                                                                                       \n--> Testing: http://*************/134\n                                                                                                                                                                                       \n--> Testing: http://*************/1341\n                                                                                                                                                                                       \n--> Testing: http://*************/1349\n                                                                                                                                                                                       \n--> Testing: http://*************/135\n                                                                                                                                                                                       \n--> Testing: http://*************/1350\n                                                                                                                                                                                       \n--> Testing: http://*************/1354\n                                                                                                                                                                                       \n--> Testing: http://*************/13579\n                                                                                                                                                                                       \n--> Testing: http://*************/1358\n                                                                                                                                                                                       \n--> Testing: http://*************/136\n                                                                                                                                                                                       \n--> Testing: http://*************/1366\n                                                                                                                                                                                       \n--> Testing: http://*************/1369\n                                                                                                                                                                                       \n--> Testing: http://*************/137\n                                                                                                                                                                                       \n--> Testing: http://*************/1371\n                                                                                                                                                                                       \n--> Testing: http://*************/1372\n                                                                                                                                                                                       \n--> Testing: http://*************/1373\n                                                                                                                                                                                       \n--> Testing: http://*************/1379\n                                                                                                                                                                                       \n--> Testing: http://*************/138\n                                                                                                                                                                                       \n--> Testing: http://*************/1383\n                                                                                                                                                                                       \n--> Testing: http://*************/139\n                                                                                                                                                                                       \n--> Testing: http://*************/1399\n                                                                                                                                                                                       \n--> Testing: http://*************/14\n                                                                                                                                                                                       \n--> Testing: http://*************/140\n                                                                                                                                                                                       \n--> Testing: http://*************/1400\n                                                                                                                                                                                       \n--> Testing: http://*************/1405\n                                                                                                                                                                                       \n--> Testing: http://*************/141\n                                                                                                                                                                                       \n--> Testing: http://*************/142\n                                                                                                                                                                                       \n--> Testing: http://*************/143\n                                                                                                                                                                                       \n--> Testing: http://*************/144\n                                                                                                                                                                                       \n--> Testing: http://*************/14430\n                                                                                                                                                                                       \n--> Testing: http://*************/145\n                                                                                                                                                                                       \n--> Testing: http://*************/146\n                                                                                                                                                                                       \n--> Testing: http://*************/147\n                                                                                                                                                                                       \n--> Testing: http://*************/148\n                                                                                                                                                                                       \n--> Testing: http://*************/1480\n                                                                                                                                                                                       \n--> Testing: http://*************/1489\n                                                                                                                                                                                       \n--> Testing: http://*************/149\n                                                                                                                                                                                       \n--> Testing: http://*************/1493\n                                                                                                                                                                                       \n--> Testing: http://*************/1498\n                                                                                                                                                                                       \n--> Testing: http://*************/15\n                                                                                                                                                                                       \n--> Testing: http://*************/150\n                                                                                                                                                                                       \n--> Testing: http://*************/1500\n                                                                                                                                                                                       \n--> Testing: http://*************/151\n                                                                                                                                                                                       \n--> Testing: http://*************/152\n                                                                                                                                                                                       \n--> Testing: http://*************/153\n                                                                                                                                                                                       \n--> Testing: http://*************/154\n                                                                                                                                                                                       \n--> Testing: http://*************/1548\n                                                                                                                                                                                       \n--> Testing: http://*************/155\n                                                                                                                                                                                       \n--> Testing: http://*************/156\n                                                                                                                                                                                       \n--> Testing: http://*************/157\n                                                                                                                                                                                       \n--> Testing: http://*************/1572\n                                                                                                                                                                                       \n--> Testing: http://*************/158\n                                                                                                                                                                                       \n--> Testing: http://*************/1585\n                                                                                                                                                                                       \n--> Testing: http://*************/159\n                                                                                                                                                                                       \n--> Testing: http://*************/1590\n                                                                                                                                                                                       \n--> Testing: http://*************/1593\n                                                                                                                                                                                       \n--> Testing: http://*************/1594\n                                                                                                                                                                                       \n--> Testing: http://*************/1595\n                                                                                                                                                                                       \n--> Testing: http://*************/1596\n                                                                                                                                                                                       \n--> Testing: http://*************/16\n                                                                                                                                                                                       \n--> Testing: http://*************/160\n                                                                                                                                                                                       \n--> Testing: http://*************/161\n                                                                                                                                                                                       \n--> Testing: http://*************/162\n                                                                                                                                                                                       \n--> Testing: http://*************/164\n                                                                                                                                                                                       \n--> Testing: http://*************/165\n                                                                                                                                                                                       \n--> Testing: http://*************/1650\n                                                                                                                                                                                       \n--> Testing: http://*************/166\n                                                                                                                                                                                       \n--> Testing: http://*************/167\n                                                                                                                                                                                       \n--> Testing: http://*************/1676\n                                                                                                                                                                                       \n--> Testing: http://*************/168\n                                                                                                                                                                                       \n--> Testing: http://*************/169\n                                                                                                                                                                                       \n--> Testing: http://*************/1694\n                                                                                                                                                                                       \n--> Testing: http://*************/1698\n                                                                                                                                                                                       \n--> Testing: http://*************/17\n                                                                                                                                                                                       \n--> Testing: http://*************/170\n                                                                                                                                                                                       \n--> Testing: http://*************/1701d\n                                                                                                                                                                                       \n--> Testing: http://*************/1702\n                                                                                                                                                                                       \n--> Testing: http://*************/1703\n                                                                                                                                                                                       \n--> Testing: http://*************/1704\n                                                                                                                                                                                       \n--> Testing: http://*************/1705\n                                                                                                                                                                                       \n--> Testing: http://*************/1706\n                                                                                                                                                                                       \n--> Testing: http://*************/1707\n                                                                                                                                                                                       \n--> Testing: http://*************/171\n                                                                                                                                                                                       \n--> Testing: http://*************/1717\n                                                                                                                                                                                       \n--> Testing: http://*************/172\n                                                                                                                                                                                       \n--> Testing: http://*************/1720\n                                                                                                                                                                                       \n--> Testing: http://*************/173\n                                                                                                                                                                                       \n--> Testing: http://*************/1736\n                                                                                                                                                                                       \n--> Testing: http://*************/174\n                                                                                                                                                                                       \n--> Testing: http://*************/1747\n                                                                                                                                                                                       \n--> Testing: http://*************/175\n                                                                                                                                                                                       \n--> Testing: http://*************/1756\n                                                                                                                                                                                       \n--> Testing: http://*************/1757\n                                                                                                                                                                                       \n--> Testing: http://*************/176\n                                                                                                                                                                                       \n--> Testing: http://*************/1762\n                                                                                                                                                                                       \n--> Testing: http://*************/177\n                                                                                                                                                                                       \n--> Testing: http://*************/1771\n                                                                                                                                                                                       \n--> Testing: http://*************/1779\n                                                                                                                                                                                       \n--> Testing: http://*************/178\n                                                                                                                                                                                       \n--> Testing: http://*************/1794\n                                                                                                                                                                                       \n--> Testing: http://*************/18\n                                                                                                                                                                                       \n--> Testing: http://*************/180\n                                                                                                                                                                                       \n--> Testing: http://*************/1809\n                                                                                                                                                                                       \n--> Testing: http://*************/181\n                                                                                                                                                                                       \n--> Testing: http://*************/1814\n                                                                                                                                                                                       \n--> Testing: http://*************/1816\n                                                                                                                                                                                       \n--> Testing: http://*************/1825\n                                                                                                                                                                                       \n--> Testing: http://*************/183\n                                                                                                                                                                                       \n--> Testing: http://*************/184\n                                                                                                                                                                                       \n--> Testing: http://*************/185\n                                                                                                                                                                                       \n--> Testing: http://*************/187\n                                                                                                                                                                                       \n--> Testing: http://*************/188\n                                                                                                                                                                                       \n--> Testing: http://*************/189\n                                                                                                                                                                                       \n--> Testing: http://*************/1897\n                                                                                                                                                                                       \n--> Testing: http://*************/1899-hoffenheim\n                                                                                                                                                                                       \n--> Testing: http://*************/19\n                                                                                                                                                                                       \n--> Testing: http://*************/190\n                                                                                                                                                                                       \n--> Testing: http://*************/191\n                                                                                                                                                                                       \n--> Testing: http://*************/192\n                                                                                                                                                                                       \n--> Testing: http://*************/1928\n', 'found_paths': 0}}
2025-06-28 04:48:00,413 - scan_17cfcc1d-3441-471a-8719-29bc884b0c18 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 125.3s
