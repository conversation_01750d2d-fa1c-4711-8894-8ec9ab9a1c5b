2025-07-01 00:27:10,612 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: ************, Tools: nmap, openvas, metasploit
2025-07-01 00:27:10,621 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: ************, Tools: nmap, openvas, metasploit
2025-07-01 00:27:10,632 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on ************
2025-07-01 00:27:10,633 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on ************
2025-07-01 00:27:10,638 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on ************
2025-07-01 00:27:10,644 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-07-01 00:27:10,645 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-07-01 00:27:10,649 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-07-01 00:27:10,651 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Intensity: low, Timeout: 300s
2025-07-01 00:27:10,655 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Intensity: low, Timeout: 300s
2025-07-01 00:27:10,656 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Intensity: low, Timeout: 300s
2025-07-01 00:27:10,662 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS vulnerability scan...
2025-07-01 00:27:10,663 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap port scan...
2025-07-01 00:27:10,665 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit exploitation scan...
2025-07-01 00:27:12,673 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Scanning ports... 10% complete
2025-07-01 00:27:13,674 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Metasploit scanning... 10% complete
2025-07-01 00:27:14,682 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - NMAP: 20% - Scanning ports... 20% complete
2025-07-01 00:27:15,672 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - OpenVAS vulnerability scanning... 10% complete
2025-07-01 00:27:16,682 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 25% - Metasploit scanning... 25% complete
2025-07-01 00:27:16,689 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Scanning ports... 30% complete
2025-07-01 00:27:18,696 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - NMAP: 40% - Scanning ports... 40% complete
2025-07-01 00:27:19,691 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 40% - Metasploit scanning... 40% complete
2025-07-01 00:27:20,682 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - OPENVAS: 20% - OpenVAS vulnerability scanning... 20% complete
2025-07-01 00:27:20,705 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - NMAP: 50% - Scanning ports... 50% complete
2025-07-01 00:27:22,700 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 55% - Metasploit scanning... 55% complete
2025-07-01 00:27:22,715 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - NMAP: 60% - Scanning ports... 60% complete
2025-07-01 00:27:24,726 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - NMAP: 70% - Scanning ports... 70% complete
2025-07-01 00:27:25,694 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS vulnerability scanning... 30% complete
2025-07-01 00:27:25,709 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 70% - Metasploit scanning... 70% complete
2025-07-01 00:27:26,742 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - NMAP: 80% - Scanning ports... 80% complete
2025-07-01 00:27:26,747 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan (low intensity)...
2025-07-01 00:27:26,786 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-07-01 00:27:26,791 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using scan config: Quick scan with minimal intrusion
2025-07-01 00:27:26,796 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Nmap options: -T3
2025-07-01 00:27:26,802 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Port range: 1-1000
2025-07-01 00:27:28,718 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 85% - Metasploit scanning... 85% complete
2025-07-01 00:27:28,724 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-07-01 00:27:28,730 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-07-01 00:27:28,739 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-07-01 00:27:29,920 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-07-01 00:27:29,924 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Scan completed: /usr/bin/nmap -T3 -p 1-1000 -oX /tmp/nmap_scan_d7c3c6a0-5e58-4fc3-9f1f-079215de2850.xml ************
2025-07-01 00:27:29,930 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Found 0 ports
2025-07-01 00:27:29,942 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - ✅ TOOL RESULT - NMAP: completed - Found 0 ports - Found 0 vulnerabilities
2025-07-01 00:27:30,703 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - OPENVAS: 40% - OpenVAS vulnerability scanning... 40% complete
2025-07-01 00:27:35,713 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - OPENVAS: 50% - OpenVAS vulnerability scanning... 50% complete
2025-07-01 00:27:40,717 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - OPENVAS: 60% - OpenVAS vulnerability scanning... 60% complete
2025-07-01 00:27:45,720 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS vulnerability scanning... 70% complete
2025-07-01 00:27:50,726 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - OPENVAS: 80% - OpenVAS vulnerability scanning... 80% complete
2025-07-01 00:27:50,730 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-07-01 00:27:51,030 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-07-01 00:28:13,147 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-07-01 00:28:13,152 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_745dc16e-bed9-45c4-83bb-763dc8f0d0b2
2025-07-01 00:28:13,158 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 0 vulnerabilities
2025-07-01 00:29:28,829 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - ERROR - ❌ TOOL ERROR - METASPLOIT: Timeout running TCP Port Scanner
2025-07-01 00:29:28,837 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-07-01 00:29:28,845 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-07-01 00:30:47,129 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-07-01 00:30:47,132 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-07-01 00:31:42,974 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-07-01 00:31:42,978 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-07-01 00:32:55,606 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-07-01 00:32:55,618 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 3 modules - Found 0 potential vulnerabilities
2025-07-01 00:32:55,629 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-07-01 00:32:55,648 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'tools': {'nmap': {'status': 'completed', 'scan_id': 'd7c3c6a0-5e58-4fc3-9f1f-079215de2850', 'target': '************', 'start_time': '2025-06-30T22:27:26.807715', 'end_time': '2025-06-30T22:27:29.919040', 'scan_time': 3.111325, 'command': '/usr/bin/nmap -T3 -p 1-1000 -oX /tmp/nmap_scan_d7c3c6a0-5e58-4fc3-9f1f-079215de2850.xml ************', 'ports': [], 'vulnerabilities': [], 'raw_output': 'Starting Nmap 7.94SVN ( https://nmap.org ) at 2025-07-01 00:27 CEST\nNote: Host seems down. If it is really up, but blocking our ping probes, try -Pn\nNmap done: 1 IP address (0 hosts up) scanned in 3.08 seconds\n'}, 'openvas': {'status': 'completed', 'scan_id': '745dc16e-bed9-45c4-83bb-763dc8f0d0b2', 'task_id': 'greenbone_task_745dc16e-bed9-45c4-83bb-763dc8f0d0b2', 'target_id': 'greenbone_target_745dc16e-bed9-45c4-83bb-763dc8f0d0b2', 'vulnerabilities': [], 'message': 'Greenbone scan completed for ************', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 3, 'raw_output': "=== TCP Port Scanner ===\nTimeout after 120 seconds\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ************          - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => ************\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m ************:445      - Rex::ConnectionTimeout: The connection with (************:445) timed out.\n\x1b[1m\x1b[34m[*]\x1b[0m ************:445      - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'ports': [], 'vulnerabilities': [], 'summary': {'total_ports': 0, 'open_ports': 0, 'total_vulnerabilities': 0}}
2025-07-01 00:32:55,655 - scan_9223f5f1-fa4d-435f-b7d8-c687a3a7f920 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 345.0s
