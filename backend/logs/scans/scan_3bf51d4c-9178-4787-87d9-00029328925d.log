2025-06-24 20:33:26,663 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 🚀 SCAN STARTED - Category: web, Type: basic, Target: http://*************, Tools: nikto, sqlmap, dirb, gobuster, zap
2025-06-24 20:33:26,669 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 🔧 TOOL START - NIKTO - Command: nikto scan on http://*************
2025-06-24 20:33:26,670 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 🔧 TOOL START - SQLMAP - Command: sqlmap scan on http://*************
2025-06-24 20:33:26,674 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 🔧 TOOL START - DIRB - Command: dirb scan on http://*************
2025-06-24 20:33:26,677 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 🔧 TOOL START - GOBUSTER - Command: gobuster scan on http://*************
2025-06-24 20:33:26,678 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - SQLMAP: 10% - Starting SQLMap scan...
2025-06-24 20:33:26,679 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Starting Nikto scan...
2025-06-24 20:33:26,680 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 🔧 TOOL START - ZAP - Command: zap scan on http://*************
2025-06-24 20:33:26,685 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 10% - Starting GoBuster scan...
2025-06-24 20:33:26,686 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - DIRB: 10% - Starting directory scan...
2025-06-24 20:33:26,689 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - SQLMAP: 25% - Executing SQLMap command...
2025-06-24 20:33:26,694 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Executing Nikto command...
2025-06-24 20:33:26,694 - scan_3bf51d4c-9178-4787-87d9-00029328925d - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Command: sqlmap -u http://************* --batch --level=1 --risk=1 --timeout=20 --retries=1 --threads=1 --technique=B --no-cast --disable-coloring --flush-session --fresh-queries
2025-06-24 20:33:26,697 - scan_3bf51d4c-9178-4787-87d9-00029328925d - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Command: nikto -h http://************* -Format txt -output /tmp/nikto_1750790006.txt -maxtime 180 -Tuning 1,2,3,4,5,6,7,8,9,0,a,b,c
2025-06-24 20:33:26,698 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - ZAP: 10% - Starting security scan...
2025-06-24 20:33:26,704 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - SQLMAP: 25% - Testing for SQL injection... (0s elapsed)
2025-06-24 20:33:26,704 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 25% - Executing GoBuster command...
2025-06-24 20:33:26,705 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Scanning... (0s elapsed)
2025-06-24 20:33:26,705 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - ZAP: 25% - Performing HTTP security checks...
2025-06-24 20:33:26,713 - scan_3bf51d4c-9178-4787-87d9-00029328925d - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Command: gobuster dir -u http://************* -w /usr/share/dirb/wordlists/common.txt -t 5 -q --no-error --timeout 5s --delay 100ms
2025-06-24 20:33:26,716 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - DIRB: 25% - Executing Dirb command...
2025-06-24 20:33:26,721 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 25% - Brute forcing directories... (0s elapsed)
2025-06-24 20:33:26,721 - scan_3bf51d4c-9178-4787-87d9-00029328925d - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Command: dirb http://************* /tmp/dirb_small_wordlist.txt -w -r -S -z 50 -X .php,.html,.txt,.js
2025-06-24 20:33:26,724 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - DIRB: 25% - Scanning directories... (0s elapsed)
2025-06-24 20:33:27,101 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - ZAP: 50% - Analyzing security headers...
2025-06-24 20:33:27,103 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - ZAP: 75% - Checking response content...
2025-06-24 20:33:27,105 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - ZAP: 100% - Security scan completed
2025-06-24 20:33:27,106 - scan_3bf51d4c-9178-4787-87d9-00029328925d - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Found 6 security alerts
2025-06-24 20:33:27,108 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - ✅ TOOL RESULT - ZAP: completed
2025-06-24 20:33:31,723 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 27% - Brute forcing directories... (5s elapsed)
2025-06-24 20:33:31,726 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - DIRB: 27% - Scanning directories... (5s elapsed)
2025-06-24 20:33:36,709 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - SQLMAP: 90% - Parsing results...
2025-06-24 20:33:36,711 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - SQLMAP: 100% - SQLMap scan completed
2025-06-24 20:33:36,713 - scan_3bf51d4c-9178-4787-87d9-00029328925d - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): No SQL injection vulnerabilities detected
2025-06-24 20:33:36,714 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - NIKTO: 28% - Scanning... (10s elapsed)
2025-06-24 20:33:36,715 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - ✅ TOOL RESULT - SQLMAP: completed
2025-06-24 20:33:36,728 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 29% - Brute forcing directories... (10s elapsed)
2025-06-24 20:33:36,730 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - DIRB: 30% - Scanning directories... (10s elapsed)
2025-06-24 20:33:41,732 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 31% - Brute forcing directories... (15s elapsed)
2025-06-24 20:33:41,732 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - DIRB: 33% - Scanning directories... (15s elapsed)
2025-06-24 20:33:46,716 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - NIKTO: 32% - Scanning... (20s elapsed)
2025-06-24 20:33:46,734 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 33% - Brute forcing directories... (20s elapsed)
2025-06-24 20:33:46,735 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - DIRB: 35% - Scanning directories... (20s elapsed)
2025-06-24 20:33:51,737 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 35% - Brute forcing directories... (25s elapsed)
2025-06-24 20:33:51,737 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - DIRB: 38% - Scanning directories... (25s elapsed)
2025-06-24 20:33:56,721 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - NIKTO: 35% - Scanning... (30s elapsed)
2025-06-24 20:33:56,742 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - DIRB: 41% - Scanning directories... (30s elapsed)
2025-06-24 20:33:56,743 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 37% - Brute forcing directories... (30s elapsed)
2025-06-24 20:34:01,799 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - DIRB: 43% - Scanning directories... (35s elapsed)
2025-06-24 20:34:01,801 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 39% - Brute forcing directories... (35s elapsed)
2025-06-24 20:34:06,727 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - NIKTO: 90% - Parsing results...
2025-06-24 20:34:06,729 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - NIKTO: 100% - Nikto scan completed
2025-06-24 20:34:06,731 - scan_3bf51d4c-9178-4787-87d9-00029328925d - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Scan completed - Found 3 potential issues
2025-06-24 20:34:06,735 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - ✅ TOOL RESULT - NIKTO: completed - Found 3 vulnerabilities
2025-06-24 20:34:06,805 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - DIRB: 90% - Parsing results...
2025-06-24 20:34:06,806 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 41% - Brute forcing directories... (40s elapsed)
2025-06-24 20:34:06,808 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - DIRB: 100% - Directory scan completed
2025-06-24 20:34:06,811 - scan_3bf51d4c-9178-4787-87d9-00029328925d - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Found 0 directories
2025-06-24 20:34:06,815 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - ✅ TOOL RESULT - DIRB: completed - Found 0 directories
2025-06-24 20:34:11,809 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 43% - Brute forcing directories... (45s elapsed)
2025-06-24 20:34:16,816 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 45% - Brute forcing directories... (50s elapsed)
2025-06-24 20:34:21,821 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 47% - Brute forcing directories... (55s elapsed)
2025-06-24 20:34:26,826 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 50% - Brute forcing directories... (60s elapsed)
2025-06-24 20:34:31,830 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 52% - Brute forcing directories... (65s elapsed)
2025-06-24 20:34:36,836 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 54% - Brute forcing directories... (70s elapsed)
2025-06-24 20:34:41,842 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 56% - Brute forcing directories... (75s elapsed)
2025-06-24 20:34:46,847 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 58% - Brute forcing directories... (80s elapsed)
2025-06-24 20:34:51,852 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 60% - Brute forcing directories... (85s elapsed)
2025-06-24 20:34:56,857 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 62% - Brute forcing directories... (90s elapsed)
2025-06-24 20:35:01,862 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 64% - Brute forcing directories... (95s elapsed)
2025-06-24 20:35:06,867 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 66% - Brute forcing directories... (100s elapsed)
2025-06-24 20:35:11,872 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 68% - Brute forcing directories... (105s elapsed)
2025-06-24 20:35:16,876 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 70% - Brute forcing directories... (110s elapsed)
2025-06-24 20:35:21,879 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 72% - Brute forcing directories... (115s elapsed)
2025-06-24 20:36:31,883 - scan_3bf51d4c-9178-4787-87d9-00029328925d - ERROR - ❌ TOOL ERROR - GOBUSTER: Scan timeout after 3 minutes
2025-06-24 20:36:31,887 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 90% - Parsing results...
2025-06-24 20:36:31,891 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 📊 TOOL PROGRESS - GOBUSTER: 100% - GoBuster scan completed
2025-06-24 20:36:31,895 - scan_3bf51d4c-9178-4787-87d9-00029328925d - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Found 8 paths
2025-06-24 20:36:31,901 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - ✅ TOOL RESULT - GOBUSTER: completed
2025-06-24 20:36:31,906 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'zap': {'status': 'completed', 'alerts': [{'risk': 'Medium', 'name': 'Missing X-Frame-Options', 'count': 1, 'description': 'Clickjacking protection missing'}, {'risk': 'Medium', 'name': 'Missing X-Content-Type-Options', 'count': 1, 'description': 'MIME sniffing protection missing'}, {'risk': 'Medium', 'name': 'Missing X-XSS-Protection', 'count': 1, 'description': 'XSS protection header missing'}, {'risk': 'Medium', 'name': 'Missing Strict-Transport-Security', 'count': 1, 'description': 'HTTPS enforcement missing'}, {'risk': 'Medium', 'name': 'Missing Content-Security-Policy', 'count': 1, 'description': 'Content Security Policy missing'}, {'risk': 'Low', 'name': 'Server Information Disclosure', 'count': 1, 'description': 'Server header reveals: Apache/2.4.58 (Ubuntu)'}], 'scan_time': '0 seconds', 'raw_output': 'Basic security scan completed. Found 6 potential issues.'}, 'sqlmap': {'status': 'completed', 'injections': [], 'scan_time': '10 seconds', 'raw_output': "        ___\n       __H__\n ___ ___[(]_____ ___ ___  {1.8.4#stable}\n|_ -| . [.]     | .'| . |\n|___|_  [,]_|_|_|__,|  _|\n      |_|V...       |_|   https://sqlmap.org\n\n[!] legal disclaimer: Usage of sqlmap for attacking targets without prior mutual consent is illegal. It is the end user's responsibility to obey all applicable local, state and federal laws. Developers assume no liability and are not responsible for any misuse or damage caused by this program\n\n[*] starting @ 20:33:26 /2025-06-24/\n\n\n[20:33:27] [INFO] flushing session file\n\n[20:33:27] [INFO] testing connection to the target URL\n\n[20:33:27] [INFO] checking if the target is protected by some kind of WAF/IPS\n\n[20:33:27] [INFO] testing if the target URL content is stable\n\n[20:33:27] [INFO] target URL content is stable\n\n[20:33:27] [CRITICAL] no parameter(s) found for testing in the provided data (e.g. GET parameter 'id' in 'www.site.com/index.php?id=1'). You are advised to rerun with '--crawl=2'\n\n[20:33:27] [WARNING] your sqlmap version is outdated\n\n[*] ending @ 20:33:27 /2025-06-24/\n\n"}, 'nikto': {'status': 'completed', 'vulnerabilities': [{'type': 'Information Disclosure via ETags', 'severity': 'medium', 'description': 'GET /: Server leaks inodes via ETags, header found with file /, fields: 0x1a24 0x63504a204324e'}, {'type': 'Missing X-Frame-Options Header', 'severity': 'medium', 'description': 'GET /: The anti-clickjacking X-Frame-Options header is not present.'}, {'type': 'HTTP Methods Disclosure', 'severity': 'medium', 'description': 'OPTIONS /: Allowed HTTP Methods: OPTIONS, HEAD, GET, POST'}], 'scan_time': '40 seconds', 'total_tests': 7, 'raw_output': '- Nikto v2.1.5/2.1.5\n+ Target Host: vps-3811c29b.vps.ovh.ca\n+ Target Port: 80\n+ GET /: Server leaks inodes via ETags, header found with file /, fields: 0x1a24 0x63504a204324e \n+ GET /: The anti-clickjacking X-Frame-Options header is not present.\n+ OPTIONS /: Allowed HTTP Methods: OPTIONS, HEAD, GET, POST \n'}, 'dirb': {'status': 'completed', 'directories': [], 'scan_time': '40 seconds', 'raw_output': '\n-----------------\nDIRB v2.22    \nBy The Dark Raver\n-----------------\n\nSTART_TIME: Tue Jun 24 20:33:26 2025\nURL_BASE: http://*************/\nWORDLIST_FILES: /tmp/dirb_small_wordlist.txt\nOPTION: Not Recursive\nOPTION: Silent Mode\nOPTION: Not Stopping on warning messages\nEXTENSIONS_LIST: (.php,.html,.txt,.js) | (.php)(.html)(.txt)(.js) [NUM = 4]\nSPEED_DELAY: 50 milliseconds\n\n-----------------\n\nGENERATED WORDS: 19\n\n---- Scanning URL: http://*************/ ----\n\n-----------------\nEND_TIME: Tue Jun 24 20:34:02 2025\nDOWNLOADED: 76 - FOUND: 0\n'}, 'gobuster': {'status': 'completed', 'found_paths': [{'path': '/.hta', 'status': 403, 'size': 278}, {'path': '/.htaccess', 'status': 403, 'size': 278}, {'path': '/.htpasswd', 'status': 403, 'size': 278}, {'path': '/favicon.ico', 'status': 200, 'size': 15406}, {'path': '/fonts', 'status': 301, 'size': 314}, {'path': '/images', 'status': 301, 'size': 315}, {'path': '/index.html', 'status': 200, 'size': 6692}, {'path': '/javascript', 'status': 301, 'size': 319}], 'scan_time': '185 seconds', 'raw_output': '\n\x1b[2K/.hta                 (Status: 403) [Size: 278]\n\n\x1b[2K/.htaccess            (Status: 403) [Size: 278]\n\n\x1b[2K/.htpasswd            (Status: 403) [Size: 278]\n\n\x1b[2K/favicon.ico          (Status: 200) [Size: 15406]\n\n\x1b[2K/fonts                (Status: 301) [Size: 314] [--> http://*************/fonts/]\n\n\x1b[2K/images               (Status: 301) [Size: 315] [--> http://*************/images/]\n\n\x1b[2K/index.html           (Status: 200) [Size: 6692]\n\n\x1b[2K/javascript           (Status: 301) [Size: 319] [--> http://*************/javascript/]\n'}}
2025-06-24 20:36:31,911 - scan_3bf51d4c-9178-4787-87d9-00029328925d - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 185.3s
