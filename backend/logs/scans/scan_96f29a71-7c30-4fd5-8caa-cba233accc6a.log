2025-06-30 15:41:32,874 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: ***********, Tools: nmap, openvas, metasploit
2025-06-30 15:41:32,876 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: ***********, Tools: nmap, openvas, metasploit
2025-06-30 15:41:32,879 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on ***********
2025-06-30 15:41:32,880 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on ***********
2025-06-30 15:41:32,880 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on ***********
2025-06-30 15:41:32,883 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-30 15:41:32,883 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-30 15:41:32,884 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Using basic scan configuration: Quick scan with minimal intrusion
2025-06-30 15:41:32,887 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Intensity: low, Timeout: 300s
2025-06-30 15:41:32,887 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Intensity: low, Timeout: 300s
2025-06-30 15:41:32,887 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Intensity: low, Timeout: 300s
2025-06-30 15:41:32,890 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS vulnerability scan...
2025-06-30 15:41:32,890 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap port scan...
2025-06-30 15:41:32,891 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit exploitation scan...
2025-06-30 15:41:34,893 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Scanning ports... 10% complete
2025-06-30 15:41:35,893 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Metasploit scanning... 10% complete
2025-06-30 15:41:36,898 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - NMAP: 20% - Scanning ports... 20% complete
2025-06-30 15:41:37,893 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - OpenVAS vulnerability scanning... 10% complete
2025-06-30 15:41:38,897 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 25% - Metasploit scanning... 25% complete
2025-06-30 15:41:38,906 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Scanning ports... 30% complete
2025-06-30 15:41:40,909 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - NMAP: 40% - Scanning ports... 40% complete
2025-06-30 15:41:41,901 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 40% - Metasploit scanning... 40% complete
2025-06-30 15:41:42,896 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - OPENVAS: 20% - OpenVAS vulnerability scanning... 20% complete
2025-06-30 15:41:42,917 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - NMAP: 50% - Scanning ports... 50% complete
2025-06-30 15:41:44,904 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 55% - Metasploit scanning... 55% complete
2025-06-30 15:41:44,923 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - NMAP: 60% - Scanning ports... 60% complete
2025-06-30 15:41:46,930 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - NMAP: 70% - Scanning ports... 70% complete
2025-06-30 15:41:47,898 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS vulnerability scanning... 30% complete
2025-06-30 15:41:47,910 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 70% - Metasploit scanning... 70% complete
2025-06-30 15:41:48,937 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - NMAP: 80% - Scanning ports... 80% complete
2025-06-30 15:41:48,940 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan (low intensity)...
2025-06-30 15:41:48,951 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-06-30 15:41:48,953 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using scan config: Quick scan with minimal intrusion
2025-06-30 15:41:48,955 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Nmap options: -T3 -F
2025-06-30 15:41:48,957 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Port range: 1-1000
2025-06-30 15:41:48,965 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-06-30 15:41:48,968 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - ERROR - ❌ TOOL ERROR - NMAP: You cannot use -F (fast scan) with -p (explicit port selection) but see --top-ports and --port-ratio to fast scan a range of ports
QUITTING!

2025-06-30 15:41:48,973 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - ✅ TOOL RESULT - NMAP: completed
2025-06-30 15:41:50,912 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 85% - Metasploit scanning... 85% complete
2025-06-30 15:41:50,917 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-30 15:41:50,920 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-30 15:41:50,924 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-30 15:41:52,902 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - OPENVAS: 40% - OpenVAS vulnerability scanning... 40% complete
2025-06-30 15:41:57,905 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - OPENVAS: 50% - OpenVAS vulnerability scanning... 50% complete
2025-06-30 15:42:02,908 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - OPENVAS: 60% - OpenVAS vulnerability scanning... 60% complete
2025-06-30 15:42:07,912 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS vulnerability scanning... 70% complete
2025-06-30 15:42:09,001 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-30 15:42:09,004 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-30 15:42:12,915 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - OPENVAS: 80% - OpenVAS vulnerability scanning... 80% complete
2025-06-30 15:42:12,917 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-30 15:42:12,941 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-30 15:42:13,047 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-30 15:42:13,049 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_d1b578b4-80e4-4c49-8f57-281ad38f2e72
2025-06-30 15:42:13,051 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 3 vulnerabilities
2025-06-30 15:42:25,332 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-30 15:42:25,334 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-30 15:42:41,290 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-30 15:42:41,293 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-30 15:42:57,412 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-30 15:42:57,414 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 4 modules - Found 0 potential vulnerabilities
2025-06-30 15:42:57,417 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-30 15:42:57,425 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'tools': {'nmap': {'status': 'failed', 'scan_id': '78a2591b-724f-4ee6-ab89-dca3c6e8a4ad', 'target': '***********', 'error': 'You cannot use -F (fast scan) with -p (explicit port selection) but see --top-ports and --port-ratio to fast scan a range of ports\nQUITTING!\n', 'command': '/usr/bin/nmap -T3 -F -p 1-1000 -oX /tmp/nmap_scan_78a2591b-724f-4ee6-ab89-dca3c6e8a4ad.xml ***********'}, 'openvas': {'status': 'completed', 'scan_id': 'd1b578b4-80e4-4c49-8f57-281ad38f2e72', 'task_id': 'greenbone_task_d1b578b4-80e4-4c49-8f57-281ad38f2e72', 'target_id': 'greenbone_target_d1b578b4-80e4-4c49-8f57-281ad38f2e72', 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings'}, {'name': 'Telnet Service Detected', 'severity': 'high', 'description': 'Telnet service - unencrypted protocol', 'port': 23, 'solution': 'Review Telnet service configuration and security settings'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings'}], 'message': 'Greenbone scan completed for ***********', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/portscan/tcp', 'auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 4, 'raw_output': "=== TCP Port Scanner ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0mPORTS => 1-1000\n\x1b[0m\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:21 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:23 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:53 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:80 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m ***********           - ***********:443 - TCP OPEN\n\x1b[1m\x1b[34m[*]\x1b[0m ***********           - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ***********           - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m Error: ***********: Errno::ECONNREFUSED Connection refused - connect(2) for ***********:22\n\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => ***********\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m ***********:445       - Rex::ConnectionRefused: The connection was refused by the remote host (***********:445).\n\x1b[1m\x1b[34m[*]\x1b[0m ***********:445       - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'ports': [], 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings'}, {'name': 'Telnet Service Detected', 'severity': 'high', 'description': 'Telnet service - unencrypted protocol', 'port': 23, 'solution': 'Review Telnet service configuration and security settings'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings'}], 'summary': {'total_ports': 0, 'open_ports': 0, 'total_vulnerabilities': 3}}
2025-06-30 15:42:57,427 - scan_96f29a71-7c30-4fd5-8caa-cba233accc6a - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 84.6s
