2025-06-26 16:08:57,341 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: ************, Tools: nmap, openvas, metasploit
2025-06-26 16:08:57,346 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: ************, Tools: nmap, openvas, metasploit
2025-06-26 16:08:57,352 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on ************
2025-06-26 16:08:57,354 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on ************
2025-06-26 16:08:57,356 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on ************
2025-06-26 16:08:57,360 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan...
2025-06-26 16:08:57,363 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-26 16:08:57,370 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-26 16:08:57,372 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-26 16:08:57,375 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-26 16:08:57,387 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-06-26 16:08:57,517 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-26 16:09:00,548 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-06-26 16:09:00,551 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Scan completed: /usr/bin/nmap -sT -sV --version-intensity 5 -p 1-1000 -oX /tmp/nmap_scan_09ab968a-61e6-4ef6-8144-524e065dd477.xml ************
2025-06-26 16:09:00,553 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Found 0 ports
2025-06-26 16:09:00,556 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - INFO - ✅ TOOL RESULT - NMAP: completed - Found 0 ports - Found 0 vulnerabilities
2025-06-26 16:09:21,544 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-26 16:09:21,548 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_ff83933e-9361-46c3-a6c5-56de7b25a1fa
2025-06-26 16:09:21,552 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 0 vulnerabilities
2025-06-26 16:10:57,476 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - ERROR - ❌ TOOL ERROR - METASPLOIT: Timeout running TCP Port Scanner
2025-06-26 16:10:57,479 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-26 16:10:57,481 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-26 16:11:53,855 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-26 16:11:53,860 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-26 16:12:40,542 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-26 16:12:40,547 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-26 16:13:22,715 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-26 16:13:22,720 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 3 modules - Found 0 potential vulnerabilities
2025-06-26 16:13:22,724 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-26 16:13:22,730 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'tools': {'nmap': {'status': 'completed', 'scan_id': '09ab968a-61e6-4ef6-8144-524e065dd477', 'target': '************', 'start_time': '2025-06-26T14:08:57.392271', 'end_time': '2025-06-26T14:09:00.548643', 'scan_time': 3.156372, 'command': '/usr/bin/nmap -sT -sV --version-intensity 5 -p 1-1000 -oX /tmp/nmap_scan_09ab968a-61e6-4ef6-8144-524e065dd477.xml ************', 'ports': [], 'vulnerabilities': [], 'raw_output': 'Starting Nmap 7.94SVN ( https://nmap.org ) at 2025-06-26 16:08 CEST\nNote: Host seems down. If it is really up, but blocking our ping probes, try -Pn\nNmap done: 1 IP address (0 hosts up) scanned in 3.14 seconds\n'}, 'openvas': {'status': 'completed', 'scan_id': 'ff83933e-9361-46c3-a6c5-56de7b25a1fa', 'task_id': 'greenbone_task_ff83933e-9361-46c3-a6c5-56de7b25a1fa', 'target_id': 'greenbone_target_ff83933e-9361-46c3-a6c5-56de7b25a1fa', 'vulnerabilities': [], 'message': 'Greenbone scan completed for ************', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 3, 'raw_output': "=== TCP Port Scanner ===\nTimeout after 120 seconds\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ************          - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => ************\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m ************:445      - Rex::ConnectionTimeout: The connection with (************:445) timed out.\n\x1b[1m\x1b[34m[*]\x1b[0m ************:445      - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'ports': [], 'vulnerabilities': [], 'summary': {'total_ports': 0, 'open_ports': 0, 'total_vulnerabilities': 0}}
2025-06-26 16:13:22,735 - scan_faf4b3d3-f38c-41b2-b27b-4744f23686dc - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 265.4s
