2025-06-30 20:34:33,662 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 🚀 SCAN STARTED - Category: deep, Type: comprehensive, Target: ************, Tools: nmap, openvas, metasploit, nikto, sqlmap, dirb, gobuster, zap
2025-06-30 20:34:33,666 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 🚀 SCAN STARTED - Category: deep, Type: comprehensive, Target: ************, Tools: nmap, openvas, metasploit, nikto, sqlmap, dirb, gobuster, zap
2025-06-30 20:34:33,668 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 🔧 TOOL START - DEEP_SCAN - Command: 🚀 Starting Deep Scan - All Tools Running in Parallel
2025-06-30 20:34:33,675 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Launching all tools in parallel...
2025-06-30 20:34:33,676 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started nmap in background
2025-06-30 20:34:33,677 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on ************
2025-06-30 20:34:33,678 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started openvas in background
2025-06-30 20:34:33,680 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on ************
2025-06-30 20:34:33,680 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 20:34:33,682 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started metasploit in background
2025-06-30 20:34:33,682 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on ************
2025-06-30 20:34:33,683 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 20:34:33,684 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 20:34:33,685 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 🔧 TOOL START - NIKTO - Command: nikto scan on ************
2025-06-30 20:34:33,685 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started nikto in background
2025-06-30 20:34:33,687 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 20:34:33,687 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap port scan...
2025-06-30 20:34:33,688 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 20:34:33,690 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 20:34:33,691 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 20:34:33,691 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 20:34:33,693 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started sqlmap in background
2025-06-30 20:34:33,694 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 🔧 TOOL START - SQLMAP - Command: sqlmap scan on ************
2025-06-30 20:34:33,694 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Formatted target for web tool: ************ -> http://************
2025-06-30 20:34:33,695 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS vulnerability scan...
2025-06-30 20:34:33,695 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit exploitation scan...
2025-06-30 20:34:33,696 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 20:34:33,697 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started dirb in background
2025-06-30 20:34:33,697 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 🔧 TOOL START - DIRB - Command: dirb scan on ************
2025-06-30 20:34:33,698 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Starting Nikto web vulnerability scan...
2025-06-30 20:34:33,699 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 20:34:33,702 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started gobuster in background
2025-06-30 20:34:33,702 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 🔧 TOOL START - GOBUSTER - Command: gobuster scan on ************
2025-06-30 20:34:33,702 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 20:34:33,703 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Formatted target for web tool: ************ -> http://************
2025-06-30 20:34:33,706 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 10% - Started zap in background
2025-06-30 20:34:33,706 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 20:34:33,706 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 🔧 TOOL START - ZAP - Command: zap scan on ************
2025-06-30 20:34:33,707 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - SQLMAP: 10% - Starting SQLMap scan (maximum intensity)...
2025-06-30 20:34:33,709 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 20:34:33,709 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Formatted target for web tool: ************ -> http://************
2025-06-30 20:34:33,711 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 15% - All 8 tools are now running in parallel
2025-06-30 20:34:33,711 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 20:34:33,711 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-30 20:34:33,712 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Command: sqlmap -u http://************ --batch --level=5 --risk=3 --threads=5 --tamper=space2comment
2025-06-30 20:34:33,713 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DIRB: 10% - Starting Dirb directory scan (maximum intensity)...
2025-06-30 20:34:33,714 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Formatted target for web tool: ************ -> http://************
2025-06-30 20:34:33,716 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Intensity: maximum, Timeout: 2400s
2025-06-30 20:34:33,716 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Command: dirb http://************ /usr/share/dirb/wordlists/big.txt -w
2025-06-30 20:34:33,717 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 10% - Starting GoBuster directory scan (maximum intensity)...
2025-06-30 20:34:33,717 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - SQLMAP: 25% - Testing for SQL injection... (0s elapsed)
2025-06-30 20:34:33,718 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Formatted target for web tool: ************ -> http://************
2025-06-30 20:34:33,721 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Command: gobuster dir -u http://************ -w /usr/share/wordlists/dirb/big.txt -t 50 -q
2025-06-30 20:34:33,721 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - ZAP: 10% - Starting ZAP security scan...
2025-06-30 20:34:33,721 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DIRB: 25% - Scanning directories... (0s elapsed)
2025-06-30 20:34:33,725 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 25% - Brute forcing directories... (0s elapsed)
2025-06-30 20:34:35,692 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Scanning ports... 10% complete
2025-06-30 20:34:35,718 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 15% - 0/8 tools completed
2025-06-30 20:34:36,701 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Metasploit scanning... 10% complete
2025-06-30 20:34:36,704 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Scanning web vulnerabilities... 10% complete
2025-06-30 20:34:37,699 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - NMAP: 20% - Scanning ports... 20% complete
2025-06-30 20:34:37,724 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - ZAP: 10% - ZAP security scanning... 10% complete
2025-06-30 20:34:37,724 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 15% - 0/8 tools completed
2025-06-30 20:34:38,700 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - OpenVAS vulnerability scanning... 10% complete
2025-06-30 20:34:38,722 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - SQLMAP: 90% - Parsing results...
2025-06-30 20:34:38,724 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DIRB: 90% - Parsing results...
2025-06-30 20:34:38,726 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - ✅ TOOL RESULT - SQLMAP: completed - Found 0 vulnerabilities
2025-06-30 20:34:38,728 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 90% - Parsing results...
2025-06-30 20:34:38,728 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - ✅ TOOL RESULT - DIRB: completed - Found 0 directories
2025-06-30 20:34:38,732 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - ✅ TOOL RESULT - GOBUSTER: completed - Found 0 directories
2025-06-30 20:34:39,705 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Scanning ports... 30% complete
2025-06-30 20:34:39,707 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 25% - Metasploit scanning... 25% complete
2025-06-30 20:34:39,710 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Scanning web vulnerabilities... 25% complete
2025-06-30 20:34:39,732 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 20:34:41,709 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - NMAP: 40% - Scanning ports... 40% complete
2025-06-30 20:34:41,730 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - ZAP: 30% - ZAP security scanning... 30% complete
2025-06-30 20:34:41,736 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 20:34:42,710 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 40% - Metasploit scanning... 40% complete
2025-06-30 20:34:42,713 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - NIKTO: 40% - Scanning web vulnerabilities... 40% complete
2025-06-30 20:34:43,705 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - OPENVAS: 20% - OpenVAS vulnerability scanning... 20% complete
2025-06-30 20:34:43,715 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - NMAP: 50% - Scanning ports... 50% complete
2025-06-30 20:34:43,741 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 20:34:45,718 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 55% - Metasploit scanning... 55% complete
2025-06-30 20:34:45,722 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - NIKTO: 55% - Scanning web vulnerabilities... 55% complete
2025-06-30 20:34:45,723 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - NMAP: 60% - Scanning ports... 60% complete
2025-06-30 20:34:45,736 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - ZAP: 50% - ZAP security scanning... 50% complete
2025-06-30 20:34:45,746 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 20:34:47,731 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - NMAP: 70% - Scanning ports... 70% complete
2025-06-30 20:34:47,750 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 20:34:48,712 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS vulnerability scanning... 30% complete
2025-06-30 20:34:48,728 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 70% - Metasploit scanning... 70% complete
2025-06-30 20:34:48,730 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - NIKTO: 70% - Scanning web vulnerabilities... 70% complete
2025-06-30 20:34:49,736 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - NMAP: 80% - Scanning ports... 80% complete
2025-06-30 20:34:49,738 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan (maximum intensity)...
2025-06-30 20:34:49,742 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - ZAP: 70% - ZAP security scanning... 70% complete
2025-06-30 20:34:49,744 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - ZAP: 10% - Starting security scan...
2025-06-30 20:34:49,746 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - ZAP: 25% - Performing HTTP security checks...
2025-06-30 20:34:49,752 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-06-30 20:34:49,753 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 45% - 3/8 tools completed
2025-06-30 20:34:49,754 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using scan config: Complete audit with all available tools and options
2025-06-30 20:34:49,756 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Nmap options: -T4 -A --script=vuln,safe,discovery
2025-06-30 20:34:49,757 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Port range: 1-65535
2025-06-30 20:34:49,826 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): HTTP request failed: HTTPConnectionPool(host='************', port=80): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7aab73616210>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-06-30 20:34:49,828 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - ZAP: 100% - Security scan completed
2025-06-30 20:34:49,830 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Found 1 security alerts
2025-06-30 20:34:49,833 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - ✅ TOOL RESULT - ZAP: completed
2025-06-30 20:34:51,736 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 85% - Metasploit scanning... 85% complete
2025-06-30 20:34:51,738 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - NIKTO: 85% - Scanning web vulnerabilities... 85% complete
2025-06-30 20:34:51,742 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-30 20:34:51,743 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Starting Nikto scan (maximum intensity)...
2025-06-30 20:34:51,745 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-30 20:34:51,746 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Executing Nikto command...
2025-06-30 20:34:51,748 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-30 20:34:51,749 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Command: nikto -h http://************ -Format txt -output /tmp/nikto_1751308491.txt -maxtime 2400 -Tuning x
2025-06-30 20:34:51,751 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Scanning... (0s elapsed)
2025-06-30 20:34:51,756 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 20:34:53,719 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - OPENVAS: 40% - OpenVAS vulnerability scanning... 40% complete
2025-06-30 20:34:53,760 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 20:34:55,764 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 20:34:57,767 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 20:34:58,722 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - OPENVAS: 50% - OpenVAS vulnerability scanning... 50% complete
2025-06-30 20:34:59,771 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 55% - 4/8 tools completed
2025-06-30 20:35:01,755 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - NIKTO: 90% - Parsing results...
2025-06-30 20:35:01,757 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - NIKTO: 100% - Nikto scan completed
2025-06-30 20:35:01,758 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Scan completed - Found 0 potential issues
2025-06-30 20:35:01,761 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - ✅ TOOL RESULT - NIKTO: completed - Found 0 vulnerabilities
2025-06-30 20:35:01,774 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 20:35:03,725 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - OPENVAS: 60% - OpenVAS vulnerability scanning... 60% complete
2025-06-30 20:35:03,777 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 20:35:05,780 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 20:35:07,783 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 20:35:08,728 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS vulnerability scanning... 70% complete
2025-06-30 20:35:09,786 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 20:35:10,914 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-30 20:35:10,917 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-30 20:35:11,790 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 20:35:13,731 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - OPENVAS: 80% - OpenVAS vulnerability scanning... 80% complete
2025-06-30 20:35:13,734 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-30 20:35:13,759 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-30 20:35:13,793 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 65% - 5/8 tools completed
2025-06-30 20:35:13,950 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-30 20:35:13,953 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_149e5f69-ea94-4f1f-be46-dca32a417fd7
2025-06-30 20:35:13,956 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 0 vulnerabilities
2025-06-30 20:35:15,796 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:35:17,801 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:35:19,805 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:35:21,808 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:35:23,811 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:35:25,814 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:35:27,682 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-30 20:35:27,686 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-30 20:35:27,817 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:35:29,821 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:35:31,824 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:35:33,827 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:35:35,830 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:35:37,833 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:35:39,837 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:35:41,840 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:35:43,843 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:35:45,279 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-30 20:35:45,281 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-30 20:35:45,846 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:35:47,849 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:35:49,854 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:35:51,857 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:35:53,859 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:35:55,863 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:35:57,867 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:35:59,870 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:36:01,874 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - 6/8 tools completed
2025-06-30 20:36:02,788 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-30 20:36:02,790 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 4 modules - Found 0 potential vulnerabilities
2025-06-30 20:36:02,793 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-30 20:36:03,877 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:36:05,880 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:36:07,883 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:36:09,887 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:36:11,890 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:36:13,895 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 85% - 7/8 tools completed
2025-06-30 20:36:14,804 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-06-30 20:36:14,806 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Scan completed: /usr/bin/nmap -T4 -A --script=vuln,safe,discovery -p 1-65535 -oX /tmp/nmap_scan_2c05d477-5498-472e-9841-bb2faf08124e.xml ************
2025-06-30 20:36:14,808 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Found 3 ports
2025-06-30 20:36:14,811 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - ✅ TOOL RESULT - NMAP: completed - Found 3 ports - Found 2 vulnerabilities
2025-06-30 20:36:15,901 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - ✅ TOOL RESULT - DEEP_SCAN: all_tools_complete
2025-06-30 20:36:15,905 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 🔧 TOOL START - DEEP_SCAN - Command: 📊 Results Analysis and Consolidation
2025-06-30 20:36:15,910 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 96% - Consolidating port scan results...
2025-06-30 20:36:15,915 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 96% - Found 3 ports from nmap
2025-06-30 20:36:15,919 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 97% - Found 0 ports from metasploit
2025-06-30 20:36:15,924 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 97% - Consolidating vulnerability results...
2025-06-30 20:36:15,929 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 84% - Added 2 vulnerabilities from nmap
2025-06-30 20:36:15,934 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 98% - Added 1 vulnerabilities from zap
2025-06-30 20:36:15,940 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 98% - Calculating final summary...
2025-06-30 20:36:15,944 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 99% - Summary: 3 ports, 3 vulnerabilities
2025-06-30 20:36:15,951 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 99% - Saving vulnerabilities to database...
2025-06-30 20:36:15,964 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 98% - Saved 2 vulnerabilities to database
2025-06-30 20:36:15,968 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 99% - Creating frontend-compatible results...
2025-06-30 20:36:15,971 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 99% - Finalizing scan results...
2025-06-30 20:36:15,979 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - ✅ TOOL RESULT - DEEP_SCAN: completed
2025-06-30 20:36:15,984 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'network': {'nmap': {'status': 'completed', 'scan_id': '2c05d477-5498-472e-9841-bb2faf08124e', 'target': '************', 'start_time': '2025-06-30T18:34:49.758618', 'end_time': '2025-06-30T18:36:14.804577', 'scan_time': 85.045959, 'command': '/usr/bin/nmap -T4 -A --script=vuln,safe,discovery -p 1-65535 -oX /tmp/nmap_scan_2c05d477-5498-472e-9841-bb2faf08124e.xml ************', 'ports': [{'port': 49152, 'protocol': 'tcp', 'state': 'open', 'service': 'tcpwrapped'}, {'port': 53384, 'protocol': 'tcp', 'state': 'filtered', 'service': 'unknown'}, {'port': 62078, 'protocol': 'tcp', 'state': 'open', 'service': 'tcpwrapped'}], 'vulnerabilities': [{'name': '|   After NULL UDP avahi packet DoS (CVE-2011-1002).', 'severity': 'medium', 'description': '|   After NULL UDP avahi packet DoS (CVE-2011-1002).', 'tool': 'nmap', 'cve': 'CVE-2011-1002', 'id': 'CVE-2011-1002', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|_  Hosts are all up (not vulnerable).', 'severity': 'medium', 'description': '|_  Hosts are all up (not vulnerable).', 'tool': 'nmap', 'cve': None, 'source_tool': 'nmap', 'category': 'network'}], 'raw_output': 'Starting Nmap 7.94SVN ( https://nmap.org ) at 2025-06-30 20:34 CEST\nPre-scan script results:\n| targets-asn: \n|_  targets-asn.asn is a mandatory parameter\n| broadcast-avahi-dos: \n|   Discovered hosts:\n|     ***********\n|   After NULL UDP avahi packet DoS (CVE-2011-1002).\n|_  Hosts are all up (not vulnerable).\n|_http-robtex-shared-ns: *TEMPORARILY DISABLED* due to changes in Robtex\'s API. See https://www.robtex.com/api/\n| broadcast-dns-service-discovery: \n|   ***********\n|     80/tcp http\n|       path=/phpmyadmin/\n|       Address=************ fe80::a07a:1b52:f619:5817\n|     445/tcp smb\n|       Address=************ fe80::a07a:1b52:f619:5817\n|     Device Information\n|       model=MacSamba\n|_      Address=************ fe80::a07a:1b52:f619:5817\n|_hostmap-robtex: *TEMPORARILY DISABLED* due to changes in Robtex\'s API. See https://www.robtex.com/api/\nNmap scan report for ************\nHost is up (0.040s latency).\nNot shown: 65532 closed tcp ports (conn-refused)\nPORT      STATE    SERVICE    VERSION\n49152/tcp open     tcpwrapped\n53384/tcp filtered unknown\n62078/tcp open     tcpwrapped\n|_unusual-port: tcpwrapped unexpected on port tcp/62078\n\nHost script results:\n|_fcrdns: FAIL (No PTR record)\n| dns-blacklist: \n|   SPAM\n|_    all.spamrats.com - FAIL\n|_dns-brute: Can\'t guess domain of "************"; use dns-brute.domain script argument.\n| port-states: \n|   tcp: \n|     open: 49152,62078\n|     filtered: 53384\n|_    closed: 1-49151,49153-53383,53385-62077,62079-65535\n\nPost-scan script results:\n| reverse-index: \n|   49152/tcp: ************\n|_  62078/tcp: ************\nService detection performed. Please report any incorrect results at https://nmap.org/submit/ .\nNmap done: 1 IP address (1 host up) scanned in 85.03 seconds\n'}, 'openvas': {'status': 'completed', 'scan_id': '149e5f69-ea94-4f1f-be46-dca32a417fd7', 'task_id': 'greenbone_task_149e5f69-ea94-4f1f-be46-dca32a417fd7', 'target_id': 'greenbone_target_149e5f69-ea94-4f1f-be46-dca32a417fd7', 'vulnerabilities': [], 'message': 'Greenbone scan completed for ************', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/portscan/tcp', 'auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 4, 'raw_output': "=== TCP Port Scanner ===\n\x1b[0m\x1b[0mRHOSTS => ************\n\x1b[0mPORTS => 1-1000\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ************          - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ************          - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => ************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m Error: ************: Errno::ECONNREFUSED Connection refused - connect(2) for ************:22\n\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => ************\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m ************:445      - Rex::ConnectionRefused: The connection was refused by the remote host (************:445).\n\x1b[1m\x1b[34m[*]\x1b[0m ************:445      - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'web': {'nikto': {'status': 'completed', 'vulnerabilities': [], 'scan_time': '10 seconds', 'total_tests': 2, 'raw_output': '- Nikto v2.1.5/2.1.5\n'}, 'sqlmap': {'status': 'completed', 'injections': [], 'raw_output': "        ___\n       __H__\n ___ ___[,]_____ ___ ___  {1.8.4#stable}\n|_ -| . [']     | .'| . |\n|___|_  [,]_|_|_|__,|  _|\n      |_|V...       |_|   https://sqlmap.org\n\n[!] legal disclaimer: Usage of sqlmap for attacking targets without prior mutual consent is illegal. It is the end user's responsibility to obey all applicable local, state and federal laws. Developers assume no liability and are not responsible for any misuse or damage caused by this program\n\n[*] starting @ 20:34:34 /2025-06-30/\n\n[20:34:34] [INFO] loading tamper module 'space2comment'\n[20:34:34] [INFO] testing connection to the target URL\n[20:34:34] [CRITICAL] unable to connect to the target URL ('Connection refused'). sqlmap is going to retry the request(s)\n[20:34:34] [WARNING] if the problem persists please check that the provided target URL is reachable. In case that it is, you can try to rerun with switch '--random-agent' and/or proxy switches ('--proxy', '--proxy-file'...)\n[20:34:34] [CRITICAL] unable to connect to the target URL ('Connection refused')\n[20:34:34] [WARNING] your sqlmap version is outdated\n\n[*] ending @ 20:34:34 /2025-06-30/\n\n", 'vulnerabilities': []}, 'dirb': {'status': 'completed', 'directories': [], 'raw_output': '\n-----------------\nDIRB v2.22    \nBy The Dark Raver\n-----------------\n\nSTART_TIME: Mon Jun 30 20:34:33 2025\nURL_BASE: http://************/\nWORDLIST_FILES: /usr/share/dirb/wordlists/big.txt\nOPTION: Not Stopping on warning messages\n\n-----------------\n\n*** Generating Wordlist...\n                                                                               \nGENERATED WORDS: 20458\n\n---- Scanning URL: http://************/ ----\n*** Calculating NOT_FOUND code...\n                                                                               \n(!) FATAL: Too many errors connecting to host\n    (Possible cause: COULDNT CONNECT)\n                                                                               \n-----------------\nEND_TIME: Mon Jun 30 20:34:35 2025\nDOWNLOADED: 0 - FOUND: 0\n', 'found_paths': 0}, 'gobuster': {'status': 'completed', 'directories': [], 'raw_output': '', 'found_paths': 0}, 'zap': {'status': 'completed', 'alerts': [{'risk': 'Medium', 'name': 'Connection Issue', 'count': 1, 'description': "Could not analyze target: HTTPConnectionPool(host='************', port=80): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7aab73616210>: Failed to establish a new connection: [Errno 111] Connection refused'))"}], 'scan_time': '0 seconds', 'raw_output': 'Basic security scan completed. Found 1 potential issues.'}}, 'vulnerabilities': [{'name': '|   After NULL UDP avahi packet DoS (CVE-2011-1002).', 'severity': 'medium', 'description': '|   After NULL UDP avahi packet DoS (CVE-2011-1002).', 'tool': 'nmap', 'cve': 'CVE-2011-1002', 'id': 'CVE-2011-1002', 'source_tool': 'nmap', 'category': 'network'}, {'name': '|_  Hosts are all up (not vulnerable).', 'severity': 'medium', 'description': '|_  Hosts are all up (not vulnerable).', 'tool': 'nmap', 'cve': None, 'source_tool': 'nmap', 'category': 'network'}, {'type': 'Connection Issue', 'severity': 'medium', 'description': "Could not analyze target: HTTPConnectionPool(host='************', port=80): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7aab73616210>: Failed to establish a new connection: [Errno 111] Connection refused'))", 'source_tool': 'zap', 'category': 'web'}], 'ports': [{'port': 49152, 'protocol': 'tcp', 'state': 'open', 'service': 'tcpwrapped'}, {'port': 53384, 'protocol': 'tcp', 'state': 'filtered', 'service': 'unknown'}, {'port': 62078, 'protocol': 'tcp', 'state': 'open', 'service': 'tcpwrapped'}], 'summary': {'total_ports': 3, 'open_ports': 2, 'total_vulnerabilities': 3, 'high_severity': 0, 'medium_severity': 3, 'low_severity': 0, 'scan_phases': 1, 'tools_executed': 8}}
2025-06-30 20:36:15,994 - scan_72d10836-1262-4b84-89b0-63f4061f53f9 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 102.3s
