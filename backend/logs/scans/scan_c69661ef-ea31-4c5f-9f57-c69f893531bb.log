2025-06-27 23:23:49,485 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 🚀 SCAN STARTED - Category: deep, Type: stealth, Target: *************, Tools: nmap, openvas, metasploit, nikto, sqlmap, dirb, gobuster, zap
2025-06-27 23:23:49,488 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 🚀 SCAN STARTED - Category: deep, Type: stealth, Target: *************, Tools: nmap, openvas, metasploit, nikto, sqlmap, dirb, gobuster, zap
2025-06-27 23:23:49,490 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 🔧 TOOL START - DEEP_SCAN - Command: 🔍 Phase 1: Network Discovery and Port Scanning
2025-06-27 23:23:49,494 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 5% - Phase 1: Running nmap scan...
2025-06-27 23:23:49,496 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on *************
2025-06-27 23:23:49,499 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using stealth scan configuration: Slow, discrete scan with minimal footprint
2025-06-27 23:23:49,501 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Intensity: minimal, Timeout: 3600s
2025-06-27 23:23:49,504 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan (minimal intensity)...
2025-06-27 23:23:49,520 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-06-27 23:23:49,523 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using scan config: Slow, discrete scan with minimal footprint
2025-06-27 23:23:49,525 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Nmap options: -T1 -sT --randomize-hosts
2025-06-27 23:23:49,527 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Port range: 1-100
2025-06-27 23:51:19,987 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-06-27 23:51:19,993 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Scan completed: /usr/bin/nmap -T1 -sT --randomize-hosts -p 1-100 -oX /tmp/nmap_scan_46ca301a-b7d2-4c02-9dab-007a9e8aacb3.xml *************
2025-06-27 23:51:19,997 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Found 5 ports
2025-06-27 23:51:20,006 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - ✅ TOOL RESULT - NMAP: completed - Found 5 ports - Found 0 vulnerabilities
2025-06-27 23:51:20,014 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 14% - Phase 1: Running openvas scan...
2025-06-27 23:51:20,018 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on *************
2025-06-27 23:51:20,026 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Using stealth scan configuration: Slow, discrete scan with minimal footprint
2025-06-27 23:51:20,032 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Intensity: minimal, Timeout: 3600s
2025-06-27 23:51:20,040 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-27 23:51:20,135 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-27 23:51:20,954 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-27 23:51:20,960 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_4db03be6-4a43-40ad-afb8-3579da1c38a0
2025-06-27 23:51:20,964 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 5 vulnerabilities
2025-06-27 23:51:20,969 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 23% - Phase 1: Running metasploit scan...
2025-06-27 23:51:20,971 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on *************
2025-06-27 23:51:20,974 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Using stealth scan configuration: Slow, discrete scan with minimal footprint
2025-06-27 23:51:20,977 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Intensity: minimal, Timeout: 3600s
2025-06-27 23:51:20,980 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-27 23:51:20,983 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-27 23:51:20,985 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-27 23:52:04,353 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-27 23:52:04,359 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-27 23:52:32,863 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-27 23:52:32,867 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-27 23:53:04,320 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-27 23:53:04,324 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-27 23:53:36,927 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-27 23:53:36,930 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 4 modules - Found 0 potential vulnerabilities
2025-06-27 23:53:36,934 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-27 23:53:36,937 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - ✅ TOOL RESULT - DEEP_SCAN: phase_1_complete
2025-06-27 23:53:36,940 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 🔧 TOOL START - DEEP_SCAN - Command: 🌐 Phase 2: Web Application Security Testing
2025-06-27 23:53:36,944 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - Phase 2: Running nikto scan...
2025-06-27 23:53:36,947 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 🔧 TOOL START - NIKTO - Command: nikto scan on *************
2025-06-27 23:53:36,950 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Using stealth scan configuration: Slow, discrete scan with minimal footprint
2025-06-27 23:53:36,952 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Intensity: minimal, Timeout: 3600s
2025-06-27 23:53:36,956 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Formatted target for web tool: ************* -> http://*************
2025-06-27 23:53:36,957 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Starting Nikto scan (minimal intensity)...
2025-06-27 23:53:36,959 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Executing Nikto command...
2025-06-27 23:53:36,962 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Command: nikto -h http://************* -Format txt -output /tmp/nikto_1751061216.txt -maxtime 3600 -T 1 -timeout 10
2025-06-27 23:53:36,965 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Scanning... (0s elapsed)
2025-06-27 23:53:46,967 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NIKTO: 28% - Scanning... (10s elapsed)
2025-06-27 23:53:56,973 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NIKTO: 32% - Scanning... (20s elapsed)
2025-06-27 23:54:06,978 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NIKTO: 35% - Scanning... (30s elapsed)
2025-06-27 23:54:16,983 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NIKTO: 39% - Scanning... (40s elapsed)
2025-06-27 23:54:26,987 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NIKTO: 43% - Scanning... (50s elapsed)
2025-06-27 23:54:36,991 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NIKTO: 46% - Scanning... (60s elapsed)
2025-06-27 23:54:46,997 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NIKTO: 50% - Scanning... (70s elapsed)
2025-06-27 23:54:57,002 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NIKTO: 53% - Scanning... (80s elapsed)
2025-06-27 23:55:07,006 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NIKTO: 57% - Scanning... (90s elapsed)
2025-06-27 23:55:17,011 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NIKTO: 61% - Scanning... (100s elapsed)
2025-06-27 23:55:27,015 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NIKTO: 64% - Scanning... (110s elapsed)
2025-06-27 23:55:37,020 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NIKTO: 68% - Scanning... (120s elapsed)
2025-06-27 23:55:47,027 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NIKTO: 71% - Scanning... (130s elapsed)
2025-06-27 23:55:57,030 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NIKTO: 75% - Scanning... (140s elapsed)
2025-06-27 23:56:07,035 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NIKTO: 79% - Scanning... (150s elapsed)
2025-06-27 23:56:17,041 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NIKTO: 82% - Scanning... (160s elapsed)
2025-06-27 23:56:27,046 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NIKTO: 86% - Scanning... (170s elapsed)
2025-06-27 23:56:37,053 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NIKTO: 90% - Parsing results...
2025-06-27 23:56:37,057 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - NIKTO: 100% - Nikto scan completed
2025-06-27 23:56:37,060 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Scan completed - Found 4 potential issues
2025-06-27 23:56:37,063 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - ✅ TOOL RESULT - NIKTO: completed - Found 4 vulnerabilities
2025-06-27 23:56:37,068 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 41% - Phase 2: Running sqlmap scan...
2025-06-27 23:56:37,070 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 🔧 TOOL START - SQLMAP - Command: sqlmap scan on *************
2025-06-27 23:56:37,074 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Using stealth scan configuration: Slow, discrete scan with minimal footprint
2025-06-27 23:56:37,077 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Intensity: minimal, Timeout: 3600s
2025-06-27 23:56:37,080 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Formatted target for web tool: ************* -> http://*************
2025-06-27 23:56:37,082 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - SQLMAP: 10% - Starting SQLMap scan (minimal intensity)...
2025-06-27 23:56:37,084 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - SQLMAP: 25% - Executing SQLMap command...
2025-06-27 23:56:37,086 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Command: sqlmap -u http://************* --disable-coloring --flush-session --fresh-queries --batch --level=1 --risk=1 --delay=2 --timeout 30
2025-06-27 23:56:37,093 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - SQLMAP: 25% - Testing for SQL injection... (0s elapsed)
2025-06-27 23:56:47,096 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - SQLMAP: 90% - Parsing results...
2025-06-27 23:56:47,101 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - SQLMAP: 100% - SQLMap scan completed
2025-06-27 23:56:47,104 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Found 1 SQL injection points
2025-06-27 23:56:47,107 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - ✅ TOOL RESULT - SQLMAP: completed
2025-06-27 23:56:47,112 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 47% - Phase 2: Running dirb scan...
2025-06-27 23:56:47,114 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 🔧 TOOL START - DIRB - Command: dirb scan on *************
2025-06-27 23:56:47,117 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Using stealth scan configuration: Slow, discrete scan with minimal footprint
2025-06-27 23:56:47,119 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Intensity: minimal, Timeout: 3600s
2025-06-27 23:56:47,123 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Formatted target for web tool: ************* -> http://*************
2025-06-27 23:56:47,125 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 10% - Starting directory scan...
2025-06-27 23:56:47,141 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Using wordlist: /usr/share/dirb/wordlists/common.txt (intensity: minimal)
2025-06-27 23:56:47,143 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 25% - Executing Dirb command...
2025-06-27 23:56:47,145 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Command: dirb http://************* /usr/share/dirb/wordlists/common.txt -w -S -X .php,.html,.txt,.js -z 100
2025-06-27 23:56:47,148 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 25% - Scanning directories... (0s elapsed)
2025-06-27 23:56:52,150 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 27% - Scanning directories... (5s elapsed)
2025-06-27 23:56:57,153 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 30% - Scanning directories... (10s elapsed)
2025-06-27 23:57:02,156 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 33% - Scanning directories... (15s elapsed)
2025-06-27 23:57:07,163 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 35% - Scanning directories... (20s elapsed)
2025-06-27 23:57:12,168 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 38% - Scanning directories... (25s elapsed)
2025-06-27 23:57:17,172 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 41% - Scanning directories... (30s elapsed)
2025-06-27 23:57:22,176 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 43% - Scanning directories... (35s elapsed)
2025-06-27 23:57:27,181 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 46% - Scanning directories... (40s elapsed)
2025-06-27 23:57:32,187 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 49% - Scanning directories... (45s elapsed)
2025-06-27 23:57:37,192 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 52% - Scanning directories... (50s elapsed)
2025-06-27 23:57:42,195 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 54% - Scanning directories... (55s elapsed)
2025-06-27 23:57:47,200 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 57% - Scanning directories... (60s elapsed)
2025-06-27 23:57:52,206 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 60% - Scanning directories... (65s elapsed)
2025-06-27 23:57:57,211 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 62% - Scanning directories... (70s elapsed)
2025-06-27 23:58:02,216 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 65% - Scanning directories... (75s elapsed)
2025-06-27 23:58:07,222 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 68% - Scanning directories... (80s elapsed)
2025-06-27 23:58:12,227 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 71% - Scanning directories... (85s elapsed)
2025-06-27 23:58:17,233 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 73% - Scanning directories... (90s elapsed)
2025-06-27 23:58:22,237 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 76% - Scanning directories... (95s elapsed)
2025-06-27 23:58:27,242 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 79% - Scanning directories... (100s elapsed)
2025-06-27 23:58:32,249 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 81% - Scanning directories... (105s elapsed)
2025-06-27 23:58:37,253 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 84% - Scanning directories... (110s elapsed)
2025-06-27 23:58:42,259 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 87% - Scanning directories... (115s elapsed)
2025-06-27 23:58:52,265 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Scan timeout after 2 minutes - completing with current results
2025-06-27 23:58:52,271 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 90% - Parsing results...
2025-06-27 23:58:52,274 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DIRB: 100% - Directory scan completed
2025-06-27 23:58:52,276 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Found 0 directories
2025-06-27 23:58:52,280 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - ✅ TOOL RESULT - DIRB: completed - Found 0 directories
2025-06-27 23:58:52,285 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 53% - Phase 2: Running gobuster scan...
2025-06-27 23:58:52,287 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 🔧 TOOL START - GOBUSTER - Command: gobuster scan on *************
2025-06-27 23:58:52,291 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Using stealth scan configuration: Slow, discrete scan with minimal footprint
2025-06-27 23:58:52,293 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Intensity: minimal, Timeout: 3600s
2025-06-27 23:58:52,296 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Formatted target for web tool: ************* -> http://*************
2025-06-27 23:58:52,301 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 10% - Starting GoBuster scan...
2025-06-27 23:58:52,310 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 25% - Executing GoBuster command...
2025-06-27 23:58:52,313 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Command: gobuster dir -u http://************* -w /usr/share/dirb/wordlists/common.txt -t 5 -q --no-error --timeout 5s --delay 100ms
2025-06-27 23:58:52,317 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 25% - Brute forcing directories... (0s elapsed)
2025-06-27 23:58:57,320 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 28% - Brute forcing directories... (5s elapsed)
2025-06-27 23:59:02,324 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 32% - Brute forcing directories... (10s elapsed)
2025-06-27 23:59:07,327 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 35% - Brute forcing directories... (15s elapsed)
2025-06-27 23:59:12,331 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 39% - Brute forcing directories... (20s elapsed)
2025-06-27 23:59:17,334 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 43% - Brute forcing directories... (25s elapsed)
2025-06-27 23:59:22,338 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 46% - Brute forcing directories... (30s elapsed)
2025-06-27 23:59:27,341 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 50% - Brute forcing directories... (35s elapsed)
2025-06-27 23:59:32,347 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 53% - Brute forcing directories... (40s elapsed)
2025-06-27 23:59:37,350 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 57% - Brute forcing directories... (45s elapsed)
2025-06-27 23:59:42,354 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 61% - Brute forcing directories... (50s elapsed)
2025-06-27 23:59:47,358 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 64% - Brute forcing directories... (55s elapsed)
2025-06-27 23:59:52,365 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 68% - Brute forcing directories... (60s elapsed)
2025-06-27 23:59:57,370 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 71% - Brute forcing directories... (65s elapsed)
2025-06-28 00:00:02,374 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 75% - Brute forcing directories... (70s elapsed)
2025-06-28 00:00:07,378 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 79% - Brute forcing directories... (75s elapsed)
2025-06-28 00:00:12,384 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 82% - Brute forcing directories... (80s elapsed)
2025-06-28 00:00:17,388 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 86% - Brute forcing directories... (85s elapsed)
2025-06-28 00:00:57,395 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Scan timeout after 2 minutes - completing with current results
2025-06-28 00:00:57,399 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 90% - Parsing results...
2025-06-28 00:00:57,402 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - GOBUSTER: 100% - GoBuster scan completed
2025-06-28 00:00:57,404 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Found 5 paths
2025-06-28 00:00:57,409 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - ✅ TOOL RESULT - GOBUSTER: completed
2025-06-28 00:00:57,416 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 59% - Phase 2: Running zap scan...
2025-06-28 00:00:57,421 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 🔧 TOOL START - ZAP - Command: zap scan on *************
2025-06-28 00:00:57,428 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Using stealth scan configuration: Slow, discrete scan with minimal footprint
2025-06-28 00:00:57,432 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Intensity: minimal, Timeout: 3600s
2025-06-28 00:00:57,437 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Formatted target for web tool: ************* -> http://*************
2025-06-28 00:00:57,445 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - ZAP: 10% - Starting security scan...
2025-06-28 00:00:57,449 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - ZAP: 25% - Performing HTTP security checks...
2025-06-28 00:00:58,154 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - ZAP: 50% - Analyzing security headers...
2025-06-28 00:00:58,157 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - ZAP: 75% - Checking response content...
2025-06-28 00:00:58,161 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - ZAP: 100% - Security scan completed
2025-06-28 00:00:58,163 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Found 5 security alerts
2025-06-28 00:00:58,168 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - ✅ TOOL RESULT - ZAP: completed
2025-06-28 00:00:58,171 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - ✅ TOOL RESULT - DEEP_SCAN: phase_2_complete
2025-06-28 00:00:58,173 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 🔧 TOOL START - DEEP_SCAN - Command: 📊 Phase 3: Results Analysis and Consolidation
2025-06-28 00:00:58,177 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - Phase 3: Consolidating port scan results...
2025-06-28 00:00:58,181 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 76% - Found 5 ports from nmap
2025-06-28 00:00:58,186 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 78% - Found 11 ports from metasploit
2025-06-28 00:00:58,189 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 82% - Phase 3: Consolidating vulnerability results...
2025-06-28 00:00:58,191 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 84% - Added 5 vulnerabilities from openvas
2025-06-28 00:00:58,193 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 86% - Added 4 vulnerabilities from nikto
2025-06-28 00:00:58,195 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 86% - Added 1 vulnerabilities from sqlmap
2025-06-28 00:00:58,196 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 86% - Added 5 vulnerabilities from zap
2025-06-28 00:00:58,199 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 88% - Phase 3: Calculating final summary...
2025-06-28 00:00:58,200 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 92% - Summary: 16 ports, 15 vulnerabilities
2025-06-28 00:00:58,203 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 96% - Saving vulnerabilities to database...
2025-06-28 00:00:58,215 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 98% - Saved 9 vulnerabilities to database
2025-06-28 00:00:58,217 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 99% - Creating frontend-compatible results...
2025-06-28 00:00:58,218 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 99% - Finalizing scan results...
2025-06-28 00:00:58,222 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - ✅ TOOL RESULT - DEEP_SCAN: completed
2025-06-28 00:00:58,224 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'network': {'nmap': {'status': 'completed', 'scan_id': '46ca301a-b7d2-4c02-9dab-007a9e8aacb3', 'target': '*************', 'start_time': '2025-06-27T21:23:49.529455', 'end_time': '2025-06-27T21:51:19.986790', 'scan_time': 1650.457335, 'command': '/usr/bin/nmap -T1 -sT --randomize-hosts -p 1-100 -oX /tmp/nmap_scan_46ca301a-b7d2-4c02-9dab-007a9e8aacb3.xml *************', 'ports': [{'port': 21, 'protocol': 'tcp', 'state': 'open', 'service': 'ftp'}, {'port': 22, 'protocol': 'tcp', 'state': 'open', 'service': 'ssh'}, {'port': 25, 'protocol': 'tcp', 'state': 'open', 'service': 'smtp'}, {'port': 53, 'protocol': 'tcp', 'state': 'open', 'service': 'domain'}, {'port': 80, 'protocol': 'tcp', 'state': 'open', 'service': 'http'}], 'vulnerabilities': [], 'raw_output': 'Starting Nmap 7.94SVN ( https://nmap.org ) at 2025-06-27 23:23 CEST\nNmap scan report for ip82-165-144-72.pbiaas.com (*************)\nHost is up (0.10s latency).\nNot shown: 95 closed tcp ports (conn-refused)\nPORT   STATE SERVICE\n21/tcp open  ftp\n22/tcp open  ssh\n25/tcp open  smtp\n53/tcp open  domain\n80/tcp open  http\n\nNmap done: 1 IP address (1 host up) scanned in 1650.43 seconds\n'}, 'openvas': {'status': 'completed', 'scan_id': '4db03be6-4a43-40ad-afb8-3579da1c38a0', 'task_id': 'greenbone_task_4db03be6-4a43-40ad-afb8-3579da1c38a0', 'target_id': 'greenbone_target_4db03be6-4a43-40ad-afb8-3579da1c38a0', 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'SMTP Service Detected', 'severity': 'medium', 'description': 'SMTP service - check for open relay', 'port': 25, 'solution': 'Review SMTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'POP3 Service Detected', 'severity': 'medium', 'description': 'POP3 service - unencrypted email protocol', 'port': 110, 'solution': 'Review POP3 service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'IMAP Service Detected', 'severity': 'medium', 'description': 'IMAP service - check for security configuration', 'port': 143, 'solution': 'Review IMAP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}], 'message': 'Greenbone scan completed for *************', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/portscan/tcp', 'auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 4, 'raw_output': "=== TCP Port Scanner ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0mPORTS => 1-1000\n\x1b[0m\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:21 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:22 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:25 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:53 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:80 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:106 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:110 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:143 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:465 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:993 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:995 - TCP OPEN\n\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ************* - Key Fingerprint: ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIOpTVWZ/k5DUxBH0Ce/hiLXvstsiteakMsWsfmVk2i/j\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - SSH server version: SSH-2.0-OpenSSH_9.6p1 Ubuntu-3ubuntu13.12\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - Server Information and Encryption\n=================================\n\n  Type                     Value                                 Note\n  ----                     -----                                 ----\n  encryption.compression   none\n  encryption.compression   <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.encryption    aes128-ctr\n  encryption.encryption    aes192-ctr\n  encryption.encryption    aes256-ctr\n  encryption.encryption    <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          hmac-sha2-256\n  encryption.hmac          hmac-sha2-512\n  encryption.hmac          hmac-sha1\n  encryption.host_key      rsa-sha2-512\n  encryption.host_key      rsa-sha2-256\n  encryption.host_key      ecdsa-sha2-nistp256                   Weak elliptic curve\n  encryption.host_key      ssh-ed25519\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  curve25519-sha256\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  ecdh-sha2-nistp256\n  encryption.key_exchange  ecdh-sha2-nistp384\n  encryption.key_exchange  ecdh-sha2-nistp521\n  encryption.key_exchange  diffie-hellman-group-exchange-sha256\n  encryption.key_exchange  diffie-hellman-group16-sha512\n  encryption.key_exchange  diffie-hellman-group18-sha512\n  encryption.key_exchange  diffie-hellman-group14-sha256\n  encryption.key_exchange  ext-info-s\n  encryption.key_exchange  <EMAIL>\n  fingerprint_db           ssh.banner\n  openssh.comment          Ubuntu-3ubuntu13.12\n  os.certainty             0.75\n  os.cpe23                 cpe:/o:canonical:ubuntu_linux:-\n  os.family                Linux\n  os.product               Linux\n  os.vendor                Ubuntu\n  service.cpe23            cpe:/a:openbsd:openssh:9.6p1\n  service.family           OpenSSH\n  service.product          OpenSSH\n  service.protocol         ssh\n  service.vendor           OpenBSD\n  service.version          9.6p1\n\n\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m *************:445     - Rex::ConnectionRefused: The connection was refused by the remote host (*************:445).\n\x1b[1m\x1b[34m[*]\x1b[0m *************:445     - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'web': {'nikto': {'status': 'completed', 'vulnerabilities': [{'type': 'Security Finding', 'severity': 'medium', 'description': 'GET /: Retrieved x-powered-by header: Next.js', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Information Disclosure via ETags', 'severity': 'medium', 'description': 'GET /: Server leaks inodes via ETags, header found with file /, fields: 0x8pdx9777851e2', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Missing X-Frame-Options Header', 'severity': 'medium', 'description': 'GET /: The anti-clickjacking X-Frame-Options header is not present.', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Security Finding', 'severity': 'medium', 'description': "GET /PaTIVspE/: Uncommon header 'refresh' found, with contents: 0;url=/PaTIVspE", 'source_tool': 'nikto', 'category': 'web'}], 'scan_time': '180 seconds', 'total_tests': 8, 'raw_output': "- Nikto v2.1.5/2.1.5\n+ Target Host: ip82-165-144-72.pbiaas.com\n+ Target Port: 80\n+ GET /: Retrieved x-powered-by header: Next.js\n+ GET /: Server leaks inodes via ETags, header found with file /, fields: 0x8pdx9777851e2 \n+ GET /: The anti-clickjacking X-Frame-Options header is not present.\n+ GET /PaTIVspE/: Uncommon header 'refresh' found, with contents: 0;url=/PaTIVspE\n"}, 'sqlmap': {'status': 'completed', 'injections': [{'parameter': 'unknown', 'type': 'SQL injection', 'dbms': 'Unknown'}], 'scan_time': '10 seconds', 'raw_output': "        ___\n       __H__\n ___ ___[)]_____ ___ ___  {1.8.4#stable}\n|_ -| . [,]     | .'| . |\n|___|_  [,]_|_|_|__,|  _|\n      |_|V...       |_|   https://sqlmap.org\n\n[!] legal disclaimer: Usage of sqlmap for attacking targets without prior mutual consent is illegal. It is the end user's responsibility to obey all applicable local, state and federal laws. Developers assume no liability and are not responsible for any misuse or damage caused by this program\n\n[*] starting @ 23:56:37 /2025-06-27/\n\n\n[23:56:37] [INFO] flushing session file\n\n[23:56:37] [INFO] testing connection to the target URL\n\n[23:56:39] [INFO] checking if the target is protected by some kind of WAF/IPS\n\n[23:56:41] [INFO] testing if the target URL content is stable\n\n[23:56:44] [WARNING] target URL content is not stable (i.e. content differs). sqlmap will base the page comparison on a sequence matcher. If no dynamic nor injectable parameters are detected, or in case of junk results, refer to user's manual paragraph 'Page comparison'\nhow do you want to proceed? [(C)ontinue/(s)tring/(r)egex/(q)uit] C\n\n[23:56:44] [INFO] searching for dynamic content\n\n[23:56:44] [INFO] dynamic content marked for removal (8 regions)\n\n[23:56:46] [CRITICAL] no parameter(s) found for testing in the provided data (e.g. GET parameter 'id' in 'www.site.com/index.php?id=1'). You are advised to rerun with '--crawl=2'\n\n[23:56:46] [WARNING] your sqlmap version is outdated\n\n[*] ending @ 23:56:46 /2025-06-27/\n\n"}, 'dirb': {'status': 'completed', 'directories': [], 'scan_time': '125 seconds', 'raw_output': '\n-----------------\nDIRB v2.22    \nBy The Dark Raver\n-----------------\n\nSTART_TIME: Fri Jun 27 23:56:47 2025\nURL_BASE: http://*************/\nWORDLIST_FILES: /usr/share/dirb/wordlists/common.txt\nOPTION: Silent Mode\nOPTION: Not Stopping on warning messages\nEXTENSIONS_LIST: (.php,.html,.txt,.js) | (.php)(.html)(.txt)(.js) [NUM = 4]\nSPEED_DELAY: 100 milliseconds\n\n-----------------\n\nGENERATED WORDS: 4612\n\n---- Scanning URL: http://*************/ ----\n'}, 'gobuster': {'status': 'completed', 'found_paths': [{'path': '/about', 'status': 200, 'size': 1807}, {'path': '/admin', 'status': 200, 'size': 1807}, {'path': '/blog', 'status': 200, 'size': 1805}, {'path': '/contact', 'status': 200, 'size': 1811}, {'path': '/favicon.ico', 'status': 200, 'size': 25931}], 'scan_time': '125 seconds', 'raw_output': '\n\x1b[2K/about                (Status: 200) [Size: 1807]\n\n\x1b[2K/admin                (Status: 200) [Size: 1807]\n\n\x1b[2K/blog                 (Status: 200) [Size: 1805]\n\n\x1b[2K/cgi-bin/             (Status: 308) [Size: 8] [--> /cgi-bin]\n\n\x1b[2K/contact              (Status: 200) [Size: 1811]\n\n\x1b[2K/favicon.ico          (Status: 200) [Size: 25931]\n'}, 'zap': {'status': 'completed', 'alerts': [{'risk': 'Medium', 'name': 'Missing X-Frame-Options', 'count': 1, 'description': 'Clickjacking protection missing'}, {'risk': 'Medium', 'name': 'Missing X-Content-Type-Options', 'count': 1, 'description': 'MIME sniffing protection missing'}, {'risk': 'Medium', 'name': 'Missing X-XSS-Protection', 'count': 1, 'description': 'XSS protection header missing'}, {'risk': 'Medium', 'name': 'Missing Strict-Transport-Security', 'count': 1, 'description': 'HTTPS enforcement missing'}, {'risk': 'Medium', 'name': 'Missing Content-Security-Policy', 'count': 1, 'description': 'Content Security Policy missing'}], 'scan_time': '0 seconds', 'raw_output': 'Basic security scan completed. Found 5 potential issues.'}}, 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'SMTP Service Detected', 'severity': 'medium', 'description': 'SMTP service - check for open relay', 'port': 25, 'solution': 'Review SMTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'POP3 Service Detected', 'severity': 'medium', 'description': 'POP3 service - unencrypted email protocol', 'port': 110, 'solution': 'Review POP3 service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'IMAP Service Detected', 'severity': 'medium', 'description': 'IMAP service - check for security configuration', 'port': 143, 'solution': 'Review IMAP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'type': 'Security Finding', 'severity': 'medium', 'description': 'GET /: Retrieved x-powered-by header: Next.js', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Information Disclosure via ETags', 'severity': 'medium', 'description': 'GET /: Server leaks inodes via ETags, header found with file /, fields: 0x8pdx9777851e2', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Missing X-Frame-Options Header', 'severity': 'medium', 'description': 'GET /: The anti-clickjacking X-Frame-Options header is not present.', 'source_tool': 'nikto', 'category': 'web'}, {'type': 'Security Finding', 'severity': 'medium', 'description': "GET /PaTIVspE/: Uncommon header 'refresh' found, with contents: 0;url=/PaTIVspE", 'source_tool': 'nikto', 'category': 'web'}, {'type': 'SQL Injection - SQL injection', 'severity': 'high', 'description': 'SQL injection found in parameter: unknown', 'parameter': 'unknown', 'dbms': 'Unknown', 'source_tool': 'sqlmap', 'category': 'web'}, {'type': 'Missing X-Frame-Options', 'severity': 'medium', 'description': 'Clickjacking protection missing', 'source_tool': 'zap', 'category': 'web'}, {'type': 'Missing X-Content-Type-Options', 'severity': 'medium', 'description': 'MIME sniffing protection missing', 'source_tool': 'zap', 'category': 'web'}, {'type': 'Missing X-XSS-Protection', 'severity': 'medium', 'description': 'XSS protection header missing', 'source_tool': 'zap', 'category': 'web'}, {'type': 'Missing Strict-Transport-Security', 'severity': 'medium', 'description': 'HTTPS enforcement missing', 'source_tool': 'zap', 'category': 'web'}, {'type': 'Missing Content-Security-Policy', 'severity': 'medium', 'description': 'Content Security Policy missing', 'source_tool': 'zap', 'category': 'web'}], 'ports': [{'port': 21, 'protocol': 'tcp', 'state': 'open', 'service': 'ftp'}, {'port': 22, 'protocol': 'tcp', 'state': 'open', 'service': 'ssh'}, {'port': 25, 'protocol': 'tcp', 'state': 'open', 'service': 'smtp'}, {'port': 53, 'protocol': 'tcp', 'state': 'open', 'service': 'domain'}, {'port': 80, 'protocol': 'tcp', 'state': 'open', 'service': 'http'}, {'port': 21, 'state': 'open', 'protocol': 'tcp', 'service': 'ftp', 'source_tool': 'metasploit'}, {'port': 22, 'state': 'open', 'protocol': 'tcp', 'service': 'ssh', 'source_tool': 'metasploit'}, {'port': 25, 'state': 'open', 'protocol': 'tcp', 'service': 'smtp', 'source_tool': 'metasploit'}, {'port': 53, 'state': 'open', 'protocol': 'tcp', 'service': 'dns', 'source_tool': 'metasploit'}, {'port': 80, 'state': 'open', 'protocol': 'tcp', 'service': 'http', 'source_tool': 'metasploit'}, {'port': 106, 'state': 'open', 'protocol': 'tcp', 'service': 'unknown', 'source_tool': 'metasploit'}, {'port': 110, 'state': 'open', 'protocol': 'tcp', 'service': 'pop3', 'source_tool': 'metasploit'}, {'port': 143, 'state': 'open', 'protocol': 'tcp', 'service': 'imap', 'source_tool': 'metasploit'}, {'port': 465, 'state': 'open', 'protocol': 'tcp', 'service': 'smtps', 'source_tool': 'metasploit'}, {'port': 993, 'state': 'open', 'protocol': 'tcp', 'service': 'imaps', 'source_tool': 'metasploit'}, {'port': 995, 'state': 'open', 'protocol': 'tcp', 'service': 'pop3s', 'source_tool': 'metasploit'}], 'summary': {'total_ports': 16, 'open_ports': 16, 'total_vulnerabilities': 15, 'high_severity': 1, 'medium_severity': 14, 'low_severity': 0, 'scan_phases': 3, 'tools_executed': 8}}
2025-06-28 00:00:58,228 - scan_c69661ef-ea31-4c5f-9f57-c69f893531bb - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 2228.7s
