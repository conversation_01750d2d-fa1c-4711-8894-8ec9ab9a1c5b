2025-06-26 19:53:53,978 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 🚀 SCAN STARTED - Category: deep, Type: comprehensive, Target: *************, Tools: nmap, openvas, metasploit, nikto, sqlmap, dirb, gobuster, zap
2025-06-26 19:53:53,982 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 🚀 SCAN STARTED - Category: deep, Type: comprehensive, Target: *************, Tools: nmap, openvas, metasploit, nikto, sqlmap, dirb, gobuster, zap
2025-06-26 19:53:53,986 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 🔧 TOOL START - DEEP_SCAN - Command: 🔍 Phase 1: Network Discovery and Port Scanning
2025-06-26 19:53:53,993 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 5% - Phase 1: Running nmap scan...
2025-06-26 19:53:53,995 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on *************
2025-06-26 19:53:53,998 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-26 19:53:53,999 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Intensity: maximum, Timeout: 2400s
2025-06-26 19:53:54,004 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan (maximum intensity)...
2025-06-26 19:53:54,018 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-06-26 19:53:54,021 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Using scan config: Complete audit with all available tools and options
2025-06-26 19:53:54,023 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Nmap options: -T4 -A --script=vuln,safe,discovery
2025-06-26 19:53:54,025 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Port range: 1-65535
2025-06-26 20:03:54,134 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-06-26 20:03:54,138 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - ERROR - ❌ TOOL ERROR - NMAP: Nmap scan timed out after 10 minutes
2025-06-26 20:03:54,143 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - ✅ TOOL RESULT - NMAP: completed
2025-06-26 20:03:54,149 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 14% - Phase 1: Running openvas scan...
2025-06-26 20:03:54,151 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on *************
2025-06-26 20:03:54,154 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-26 20:03:54,156 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): Intensity: maximum, Timeout: 2400s
2025-06-26 20:03:54,158 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-26 20:03:54,206 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - OPENVAS: 30% - OpenVAS is available, starting GMP scan...
2025-06-26 20:03:54,877 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - OPENVAS: 70% - OpenVAS scan initiated...
2025-06-26 20:03:54,883 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - OPENVAS (stdout): OpenVAS scan started: Task greenbone_task_d5f8b407-7e46-4151-b42e-6a620519f9ae
2025-06-26 20:03:54,890 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - ✅ TOOL RESULT - OPENVAS: completed - Found 5 vulnerabilities
2025-06-26 20:03:54,900 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 23% - Phase 1: Running metasploit scan...
2025-06-26 20:03:54,904 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on *************
2025-06-26 20:03:54,910 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-26 20:03:54,915 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Intensity: maximum, Timeout: 2400s
2025-06-26 20:03:54,921 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-26 20:03:54,925 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 20% - Running TCP Port Scanner...
2025-06-26 20:03:54,929 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: TCP Port Scanner
2025-06-26 20:04:37,082 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 35% - Running SMB Version Detection...
2025-06-26 20:04:37,084 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SMB Version Detection
2025-06-26 20:05:02,766 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running SSH Version Detection...
2025-06-26 20:05:02,770 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: SSH Version Detection
2025-06-26 20:05:33,761 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 65% - Running MS17-010 SMB RCE Detection...
2025-06-26 20:05:33,764 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Executing: MS17-010 SMB RCE Detection
2025-06-26 20:06:02,773 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - METASPLOIT: 90% - Processing results...
2025-06-26 20:06:02,778 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Completed 4 modules - Found 0 potential vulnerabilities
2025-06-26 20:06:02,783 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-26 20:06:02,788 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - ✅ TOOL RESULT - DEEP_SCAN: phase_1_complete
2025-06-26 20:06:02,791 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 🔧 TOOL START - DEEP_SCAN - Command: 🌐 Phase 2: Web Application Security Testing
2025-06-26 20:06:02,796 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 35% - Phase 2: Running nikto scan...
2025-06-26 20:06:02,798 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 🔧 TOOL START - NIKTO - Command: nikto scan on *************
2025-06-26 20:06:02,801 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-26 20:06:02,803 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Intensity: maximum, Timeout: 2400s
2025-06-26 20:06:02,806 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Formatted target for web tool: ************* -> http://*************
2025-06-26 20:06:02,808 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NIKTO: 10% - Starting Nikto scan (maximum intensity)...
2025-06-26 20:06:02,809 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Executing Nikto command...
2025-06-26 20:06:02,810 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Command: nikto -h http://************* -Format txt -output /tmp/nikto_1750961162.txt -maxtime 2400 -Tuning x
2025-06-26 20:06:02,812 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NIKTO: 25% - Scanning... (0s elapsed)
2025-06-26 20:06:12,821 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NIKTO: 28% - Scanning... (10s elapsed)
2025-06-26 20:06:22,828 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NIKTO: 32% - Scanning... (20s elapsed)
2025-06-26 20:06:32,834 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NIKTO: 35% - Scanning... (30s elapsed)
2025-06-26 20:06:42,841 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NIKTO: 39% - Scanning... (40s elapsed)
2025-06-26 20:06:52,846 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NIKTO: 43% - Scanning... (50s elapsed)
2025-06-26 20:07:02,850 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NIKTO: 46% - Scanning... (60s elapsed)
2025-06-26 20:07:12,856 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NIKTO: 50% - Scanning... (70s elapsed)
2025-06-26 20:07:22,861 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NIKTO: 53% - Scanning... (80s elapsed)
2025-06-26 20:07:32,865 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NIKTO: 57% - Scanning... (90s elapsed)
2025-06-26 20:07:42,871 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NIKTO: 61% - Scanning... (100s elapsed)
2025-06-26 20:07:52,876 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NIKTO: 64% - Scanning... (110s elapsed)
2025-06-26 20:08:02,881 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NIKTO: 68% - Scanning... (120s elapsed)
2025-06-26 20:08:12,886 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NIKTO: 71% - Scanning... (130s elapsed)
2025-06-26 20:08:22,892 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NIKTO: 75% - Scanning... (140s elapsed)
2025-06-26 20:08:32,898 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NIKTO: 79% - Scanning... (150s elapsed)
2025-06-26 20:08:42,903 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NIKTO: 82% - Scanning... (160s elapsed)
2025-06-26 20:08:52,906 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NIKTO: 86% - Scanning... (170s elapsed)
2025-06-26 20:11:12,914 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - ERROR - ❌ TOOL ERROR - NIKTO: Scan timeout after 5 minutes
2025-06-26 20:11:12,922 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NIKTO: 90% - Parsing results...
2025-06-26 20:11:12,927 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - NIKTO: 100% - Nikto scan completed
2025-06-26 20:11:12,931 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - NIKTO (stdout): Scan completed - Found 0 potential issues
2025-06-26 20:11:12,938 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - ✅ TOOL RESULT - NIKTO: completed - Found 0 vulnerabilities
2025-06-26 20:11:12,947 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 41% - Phase 2: Running sqlmap scan...
2025-06-26 20:11:12,951 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 🔧 TOOL START - SQLMAP - Command: sqlmap scan on *************
2025-06-26 20:11:12,958 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-26 20:11:12,962 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Intensity: maximum, Timeout: 2400s
2025-06-26 20:11:12,967 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Formatted target for web tool: ************* -> http://*************
2025-06-26 20:11:12,972 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - SQLMAP: 10% - Starting SQLMap scan (maximum intensity)...
2025-06-26 20:11:12,977 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - SQLMAP: 25% - Executing SQLMap command...
2025-06-26 20:11:12,981 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Command: sqlmap -u http://************* --disable-coloring --flush-session --fresh-queries --batch --level=3 --risk=2 --threads=5 --timeout 30
2025-06-26 20:11:12,988 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - SQLMAP: 25% - Testing for SQL injection... (0s elapsed)
2025-06-26 20:11:22,992 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - SQLMAP: 30% - Testing for SQL injection... (10s elapsed)
2025-06-26 20:11:32,998 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - SQLMAP: 35% - Testing for SQL injection... (20s elapsed)
2025-06-26 20:11:43,005 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - SQLMAP: 41% - Testing for SQL injection... (30s elapsed)
2025-06-26 20:11:53,009 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - SQLMAP: 90% - Parsing results...
2025-06-26 20:11:53,015 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - SQLMAP: 100% - SQLMap scan completed
2025-06-26 20:11:53,019 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - SQLMAP (stdout): Found 8 SQL injection points
2025-06-26 20:11:53,025 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - ✅ TOOL RESULT - SQLMAP: completed
2025-06-26 20:11:53,034 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 47% - Phase 2: Running dirb scan...
2025-06-26 20:11:53,038 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 🔧 TOOL START - DIRB - Command: dirb scan on *************
2025-06-26 20:11:53,044 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-26 20:11:53,049 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Intensity: maximum, Timeout: 2400s
2025-06-26 20:11:53,056 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Formatted target for web tool: ************* -> http://*************
2025-06-26 20:11:53,061 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 10% - Starting directory scan...
2025-06-26 20:11:53,083 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 25% - Executing Dirb command...
2025-06-26 20:11:53,088 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Command: dirb http://************* /usr/share/dirb/wordlists/small.txt -w -r -S -z 50 -X .php,.html,.txt,.js
2025-06-26 20:11:53,094 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 25% - Scanning directories... (0s elapsed)
2025-06-26 20:11:58,100 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 27% - Scanning directories... (5s elapsed)
2025-06-26 20:12:03,104 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 30% - Scanning directories... (10s elapsed)
2025-06-26 20:12:08,111 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 33% - Scanning directories... (15s elapsed)
2025-06-26 20:12:13,116 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 35% - Scanning directories... (20s elapsed)
2025-06-26 20:12:18,121 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 38% - Scanning directories... (25s elapsed)
2025-06-26 20:12:23,127 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 41% - Scanning directories... (30s elapsed)
2025-06-26 20:12:28,133 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 43% - Scanning directories... (35s elapsed)
2025-06-26 20:12:33,138 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 46% - Scanning directories... (40s elapsed)
2025-06-26 20:12:38,144 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 49% - Scanning directories... (45s elapsed)
2025-06-26 20:12:43,149 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 52% - Scanning directories... (50s elapsed)
2025-06-26 20:12:48,155 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 54% - Scanning directories... (55s elapsed)
2025-06-26 20:12:53,160 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 57% - Scanning directories... (60s elapsed)
2025-06-26 20:12:58,167 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 60% - Scanning directories... (65s elapsed)
2025-06-26 20:13:03,170 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 62% - Scanning directories... (70s elapsed)
2025-06-26 20:13:08,176 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 65% - Scanning directories... (75s elapsed)
2025-06-26 20:13:13,183 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 68% - Scanning directories... (80s elapsed)
2025-06-26 20:13:18,190 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 71% - Scanning directories... (85s elapsed)
2025-06-26 20:13:23,196 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 73% - Scanning directories... (90s elapsed)
2025-06-26 20:13:28,202 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 76% - Scanning directories... (95s elapsed)
2025-06-26 20:13:33,210 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 79% - Scanning directories... (100s elapsed)
2025-06-26 20:13:38,214 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 81% - Scanning directories... (105s elapsed)
2025-06-26 20:13:43,218 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 84% - Scanning directories... (110s elapsed)
2025-06-26 20:13:48,223 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 87% - Scanning directories... (115s elapsed)
2025-06-26 20:13:58,231 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Scan timeout after 2 minutes - completing with current results
2025-06-26 20:13:58,237 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 90% - Parsing results...
2025-06-26 20:13:58,241 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DIRB: 100% - Directory scan completed
2025-06-26 20:13:58,246 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - DIRB (stdout): Found 0 directories
2025-06-26 20:13:58,251 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - ✅ TOOL RESULT - DIRB: completed - Found 0 directories
2025-06-26 20:13:58,257 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 53% - Phase 2: Running gobuster scan...
2025-06-26 20:13:58,261 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 🔧 TOOL START - GOBUSTER - Command: gobuster scan on *************
2025-06-26 20:13:58,266 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-26 20:13:58,268 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Intensity: maximum, Timeout: 2400s
2025-06-26 20:13:58,273 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Formatted target for web tool: ************* -> http://*************
2025-06-26 20:13:58,277 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 10% - Starting GoBuster scan...
2025-06-26 20:13:58,287 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 25% - Executing GoBuster command...
2025-06-26 20:13:58,291 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Command: gobuster dir -u http://************* -w /usr/share/dirb/wordlists/common.txt -t 5 -q --no-error --timeout 5s --delay 100ms
2025-06-26 20:13:58,297 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 25% - Brute forcing directories... (0s elapsed)
2025-06-26 20:14:03,300 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 28% - Brute forcing directories... (5s elapsed)
2025-06-26 20:14:08,306 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 32% - Brute forcing directories... (10s elapsed)
2025-06-26 20:14:13,313 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 35% - Brute forcing directories... (15s elapsed)
2025-06-26 20:14:18,319 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 39% - Brute forcing directories... (20s elapsed)
2025-06-26 20:14:23,325 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 43% - Brute forcing directories... (25s elapsed)
2025-06-26 20:14:28,332 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 46% - Brute forcing directories... (30s elapsed)
2025-06-26 20:14:33,337 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 50% - Brute forcing directories... (35s elapsed)
2025-06-26 20:14:38,343 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 53% - Brute forcing directories... (40s elapsed)
2025-06-26 20:14:43,348 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 57% - Brute forcing directories... (45s elapsed)
2025-06-26 20:14:48,354 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 61% - Brute forcing directories... (50s elapsed)
2025-06-26 20:14:53,359 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 64% - Brute forcing directories... (55s elapsed)
2025-06-26 20:14:58,365 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 68% - Brute forcing directories... (60s elapsed)
2025-06-26 20:15:03,370 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 71% - Brute forcing directories... (65s elapsed)
2025-06-26 20:15:08,378 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 75% - Brute forcing directories... (70s elapsed)
2025-06-26 20:15:13,383 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 79% - Brute forcing directories... (75s elapsed)
2025-06-26 20:15:18,389 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 82% - Brute forcing directories... (80s elapsed)
2025-06-26 20:15:23,396 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 86% - Brute forcing directories... (85s elapsed)
2025-06-26 20:16:03,401 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Scan timeout after 2 minutes - completing with current results
2025-06-26 20:16:03,405 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 90% - Parsing results...
2025-06-26 20:16:03,410 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - GOBUSTER: 100% - GoBuster scan completed
2025-06-26 20:16:03,414 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - GOBUSTER (stdout): Found 6 paths
2025-06-26 20:16:03,420 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - ✅ TOOL RESULT - GOBUSTER: completed
2025-06-26 20:16:03,427 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 59% - Phase 2: Running zap scan...
2025-06-26 20:16:03,431 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 🔧 TOOL START - ZAP - Command: zap scan on *************
2025-06-26 20:16:03,436 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Using comprehensive scan configuration: Complete audit with all available tools and options
2025-06-26 20:16:03,439 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Intensity: maximum, Timeout: 2400s
2025-06-26 20:16:03,445 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Formatted target for web tool: ************* -> http://*************
2025-06-26 20:16:03,453 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - ZAP: 10% - Starting security scan...
2025-06-26 20:16:03,457 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - ZAP: 25% - Performing HTTP security checks...
2025-06-26 20:16:03,625 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - ZAP: 50% - Analyzing security headers...
2025-06-26 20:16:03,629 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - ZAP: 75% - Checking response content...
2025-06-26 20:16:03,632 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - ZAP: 100% - Security scan completed
2025-06-26 20:16:03,634 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - DEBUG - 📝 TOOL OUTPUT - ZAP (stdout): Found 5 security alerts
2025-06-26 20:16:03,639 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - ✅ TOOL RESULT - ZAP: completed
2025-06-26 20:16:03,646 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - ✅ TOOL RESULT - DEEP_SCAN: phase_2_complete
2025-06-26 20:16:03,649 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 🔧 TOOL START - DEEP_SCAN - Command: 📊 Phase 3: Results Analysis and Consolidation
2025-06-26 20:16:03,654 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 75% - Phase 3: Consolidating port scan results...
2025-06-26 20:16:03,665 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 82% - Phase 3: Consolidating vulnerability results...
2025-06-26 20:16:03,668 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 84% - Added 5 vulnerabilities from openvas
2025-06-26 20:16:03,671 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 86% - Added 8 vulnerabilities from sqlmap
2025-06-26 20:16:03,675 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 86% - Added 5 vulnerabilities from zap
2025-06-26 20:16:03,680 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 88% - Phase 3: Calculating final summary...
2025-06-26 20:16:03,684 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 92% - Summary: 0 ports, 18 vulnerabilities
2025-06-26 20:16:03,690 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 96% - Saving vulnerabilities to database...
2025-06-26 20:16:03,704 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 98% - Saved 5 vulnerabilities to database
2025-06-26 20:16:03,707 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 99% - Creating frontend-compatible results...
2025-06-26 20:16:03,711 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 📊 TOOL PROGRESS - DEEP_SCAN: 99% - Finalizing scan results...
2025-06-26 20:16:03,719 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - ✅ TOOL RESULT - DEEP_SCAN: completed
2025-06-26 20:16:03,728 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'network': {'nmap': {'status': 'timeout', 'scan_id': '45472bec-d3ed-4a02-b3f0-02169c624bdb', 'target': '*************', 'error': 'Nmap scan timed out after 10 minutes', 'command': '/usr/bin/nmap -T4 -A --script=vuln,safe,discovery -p 1-65535 -oX /tmp/nmap_scan_45472bec-d3ed-4a02-b3f0-02169c624bdb.xml *************'}, 'openvas': {'status': 'completed', 'scan_id': 'd5f8b407-7e46-4151-b42e-6a620519f9ae', 'task_id': 'greenbone_task_d5f8b407-7e46-4151-b42e-6a620519f9ae', 'target_id': 'greenbone_target_d5f8b407-7e46-4151-b42e-6a620519f9ae', 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'SMTP Service Detected', 'severity': 'medium', 'description': 'SMTP service - check for open relay', 'port': 25, 'solution': 'Review SMTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'POP3 Service Detected', 'severity': 'medium', 'description': 'POP3 service - unencrypted email protocol', 'port': 110, 'solution': 'Review POP3 service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'IMAP Service Detected', 'severity': 'medium', 'description': 'IMAP service - check for security configuration', 'port': 143, 'solution': 'Review IMAP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}], 'message': 'Greenbone scan completed for *************', 'scan_type': 'gmp_vulnerability_scan'}, 'metasploit': {'status': 'completed', 'modules_run': ['auxiliary/scanner/portscan/tcp', 'auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version', 'auxiliary/scanner/smb/smb_ms17_010'], 'vulnerabilities': [], 'total_modules': 4, 'raw_output': "=== TCP Port Scanner ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0mPORTS => 1-1000\n\x1b[0m\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:22 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:25 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:21 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:53 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:80 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:106 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:110 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:143 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:465 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:993 - TCP OPEN\n\x1b[1m\x1b[32m[+]\x1b[0m *************         - *************:995 - TCP OPEN\n\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SMB Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m *************         - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== SSH Version Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[34m[*]\x1b[0m ************* - Key Fingerprint: ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIOpTVWZ/k5DUxBH0Ce/hiLXvstsiteakMsWsfmVk2i/j\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - SSH server version: SSH-2.0-OpenSSH_9.6p1 Ubuntu-3ubuntu13.12\n\x1b[1m\x1b[34m[*]\x1b[0m ************* - Server Information and Encryption\n=================================\n\n  Type                     Value                                 Note\n  ----                     -----                                 ----\n  encryption.compression   none\n  encryption.compression   <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.encryption    aes128-ctr\n  encryption.encryption    aes192-ctr\n  encryption.encryption    aes256-ctr\n  encryption.encryption    <EMAIL>\n  encryption.encryption    <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          <EMAIL>\n  encryption.hmac          hmac-sha2-256\n  encryption.hmac          hmac-sha2-512\n  encryption.hmac          hmac-sha1\n  encryption.host_key      rsa-sha2-512\n  encryption.host_key      rsa-sha2-256\n  encryption.host_key      ecdsa-sha2-nistp256                   Weak elliptic curve\n  encryption.host_key      ssh-ed25519\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  curve25519-sha256\n  encryption.key_exchange  <EMAIL>\n  encryption.key_exchange  ecdh-sha2-nistp256\n  encryption.key_exchange  ecdh-sha2-nistp384\n  encryption.key_exchange  ecdh-sha2-nistp521\n  encryption.key_exchange  diffie-hellman-group-exchange-sha256\n  encryption.key_exchange  diffie-hellman-group16-sha512\n  encryption.key_exchange  diffie-hellman-group18-sha512\n  encryption.key_exchange  diffie-hellman-group14-sha256\n  encryption.key_exchange  ext-info-s\n  encryption.key_exchange  <EMAIL>\n  fingerprint_db           ssh.banner\n  openssh.comment          Ubuntu-3ubuntu13.12\n  os.certainty             0.75\n  os.cpe23                 cpe:/o:canonical:ubuntu_linux:-\n  os.family                Linux\n  os.product               Linux\n  os.vendor                Ubuntu\n  service.cpe23            cpe:/a:openbsd:openssh:9.6p1\n  service.family           OpenSSH\n  service.product          OpenSSH\n  service.protocol         ssh\n  service.vendor           OpenBSD\n  service.version          9.6p1\n\n\x1b[1m\x1b[34m[*]\x1b[0m Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n\n\n=== MS17-010 SMB RCE Detection ===\n\x1b[0m\x1b[0mRHOSTS => *************\n\x1b[0m\x1b[1m\x1b[31m[-]\x1b[0m *************:445     - Rex::ConnectionRefused: The connection was refused by the remote host (*************:445).\n\x1b[1m\x1b[34m[*]\x1b[0m *************:445     - Scanned 1 of 1 hosts (100% complete)\n\x1b[1m\x1b[34m[*]\x1b[0m Auxiliary module execution completed\n\x1b[0mThis copy of metasploit-framework is more than two weeks old.\n Consider running 'msfupdate' to update to the latest version.\n"}}, 'web': {'nikto': {'status': 'completed', 'vulnerabilities': [], 'scan_time': '310 seconds', 'total_tests': 17, 'raw_output': '- Nikto v2.1.5\n---------------------------------------------------------------------------\n+ Target IP:          *************\n+ Target Hostname:    ip82-165-144-72.pbiaas.com\n+ Target Port:        80\n+ Start Time:         2025-06-26 20:06:03 (GMT2)\n---------------------------------------------------------------------------\n+ Server: No banner retrieved\n+ Retrieved x-powered-by header: Next.js\n+ Server leaks inodes via ETags, header found with file /, fields: 0xvtkhtj7rb61e2 \n+ The anti-clickjacking X-Frame-Options header is not present.\n+ Uncommon header \'refresh\' found, with contents: 0;url=/X6LVCYQg\n+ No CGI Directories found (use \'-C all\' to force check all possible dirs)\n+ /node/view/666\\"><script>alert(document.domain)</script>: Drupal 4.2.0 RC is vulnerable to Cross Site Scripting (XSS). http://www.cert.org/advisories/CA-2000-02.html.\n+ /index.php/\\"><script><script>alert(document.cookie)</script><: eZ publish v3 and prior allow Cross Site Scripting (XSS). http://www.cert.org/advisories/CA-2000-02.html.\n+ OSVDB-6659: /F4aKKPgkZbZ4gR1d4X34PdbfdtTFTlLaTd7zwXGqKAZPgO6AAyekT4KVrkcC1b3xdaLy3aUB6FIp31oQ65jKFabagIVcoyLUC9ePfPUtqyId43mhT6xJWLyzVBTy86zKpvSespqBm2r7mZl2GwFjdvzzVW8LZR6jBEAmaOIDlNknQMGp3Z5QB5EslGfADvu2ZLreUDzyroZ79DBAVYDBhZqYVW00PO7<font%20size=50><script>alert(11)</script><!--//--: MyWebServer 1.0.2 is vulnerable to Cross Site Scripting (XSS). http://www.cert.org/advisories/CA-2000-02.html.\n'}, 'sqlmap': {'status': 'completed', 'injections': [{'parameter': 'unknown', 'type': 'SQL injection', 'dbms': 'MySQL'}, {'parameter': 'unknown', 'type': 'SQL injection', 'dbms': 'MySQL'}, {'parameter': 'unknown', 'type': 'boolean-based blind', 'dbms': 'MySQL'}, {'parameter': 'unknown', 'type': 'SQL injection', 'dbms': 'MySQL'}, {'parameter': 'unknown', 'type': 'SQL injection', 'dbms': 'MySQL'}, {'parameter': 'unknown', 'type': 'boolean-based blind', 'dbms': 'MySQL'}, {'parameter': 'unknown', 'type': 'SQL injection', 'dbms': 'MySQL'}, {'parameter': 'unknown', 'type': 'SQL injection', 'dbms': 'MySQL'}], 'scan_time': '40 seconds', 'raw_output': '        ___\n       __H__\n ___ ___[.]_____ ___ ___  {1.8.4#stable}\n|_ -| . ["]     | .\'| . |\n|___|_  [\']_|_|_|__,|  _|\n      |_|V...       |_|   https://sqlmap.org\n\n[!] legal disclaimer: Usage of sqlmap for attacking targets without prior mutual consent is illegal. It is the end user\'s responsibility to obey all applicable local, state and federal laws. Developers assume no liability and are not responsible for any misuse or damage caused by this program\n\n[*] starting @ 20:11:13 /2025-06-26/\n\n\n[20:11:13] [INFO] flushing session file\n\n[20:11:13] [INFO] testing connection to the target URL\n\n[20:11:13] [INFO] checking if the target is protected by some kind of WAF/IPS\n\n[20:11:13] [INFO] testing if the target URL content is stable\n\n[20:11:14] [WARNING] target URL content is not stable (i.e. content differs). sqlmap will base the page comparison on a sequence matcher. If no dynamic nor injectable parameters are detected, or in case of junk results, refer to user\'s manual paragraph \'Page comparison\'\nhow do you want to proceed? [(C)ontinue/(s)tring/(r)egex/(q)uit] C\n\n[20:11:14] [INFO] testing if parameter \'User-Agent\' is dynamic\n\n[20:11:14] [WARNING] parameter \'User-Agent\' does not appear to be dynamic\n\n[20:11:14] [WARNING] heuristic (basic) test shows that parameter \'User-Agent\' might not be injectable\n\n[20:11:14] [INFO] testing for SQL injection on parameter \'User-Agent\'\n\n[20:11:14] [INFO] testing \'AND boolean-based blind - WHERE or HAVING clause\'\n\n[20:11:20] [INFO] testing \'AND boolean-based blind - WHERE or HAVING clause (subquery - comment)\'\n\n[20:11:23] [INFO] testing \'AND boolean-based blind - WHERE or HAVING clause (comment)\'\n\n[20:11:26] [INFO] testing \'AND boolean-based blind - WHERE or HAVING clause (MySQL comment)\'\n\n[20:11:29] [INFO] testing \'AND boolean-based blind - WHERE or HAVING clause (Microsoft Access comment)\'\n\n[20:11:31] [INFO] parameter \'User-Agent\' appears to be \'AND boolean-based blind - WHERE or HAVING clause (Microsoft Access comment)\' injectable \nit'}, 'dirb': {'status': 'completed', 'directories': [], 'scan_time': '125 seconds', 'raw_output': '\n-----------------\nDIRB v2.22    \nBy The Dark Raver\n-----------------\n\nSTART_TIME: Thu Jun 26 20:11:53 2025\nURL_BASE: http://*************/\nWORDLIST_FILES: /usr/share/dirb/wordlists/small.txt\nOPTION: Not Recursive\nOPTION: Silent Mode\nOPTION: Not Stopping on warning messages\nEXTENSIONS_LIST: (.php,.html,.txt,.js) | (.php)(.html)(.txt)(.js) [NUM = 4]\nSPEED_DELAY: 50 milliseconds\n\n-----------------\n\nGENERATED WORDS: 959\n\n---- Scanning URL: http://*************/ ----\n'}, 'gobuster': {'status': 'completed', 'found_paths': [{'path': '/about', 'status': 200, 'size': 1807}, {'path': '/admin', 'status': 200, 'size': 1807}, {'path': '/blog', 'status': 200, 'size': 1805}, {'path': '/contact', 'status': 200, 'size': 1811}, {'path': '/favicon.ico', 'status': 200, 'size': 25931}, {'path': '/profile', 'status': 200, 'size': 1811}], 'scan_time': '125 seconds', 'raw_output': '\n\x1b[2K/about                (Status: 200) [Size: 1807]\n\n\x1b[2K/admin                (Status: 200) [Size: 1807]\n\n\x1b[2K/blog                 (Status: 200) [Size: 1805]\n\n\x1b[2K/cgi-bin/             (Status: 308) [Size: 8] [--> /cgi-bin]\n\n\x1b[2K/contact              (Status: 200) [Size: 1811]\n\n\x1b[2K/favicon.ico          (Status: 200) [Size: 25931]\n\n\x1b[2K/profile              (Status: 200) [Size: 1811]\n'}, 'zap': {'status': 'completed', 'alerts': [{'risk': 'Medium', 'name': 'Missing X-Frame-Options', 'count': 1, 'description': 'Clickjacking protection missing'}, {'risk': 'Medium', 'name': 'Missing X-Content-Type-Options', 'count': 1, 'description': 'MIME sniffing protection missing'}, {'risk': 'Medium', 'name': 'Missing X-XSS-Protection', 'count': 1, 'description': 'XSS protection header missing'}, {'risk': 'Medium', 'name': 'Missing Strict-Transport-Security', 'count': 1, 'description': 'HTTPS enforcement missing'}, {'risk': 'Medium', 'name': 'Missing Content-Security-Policy', 'count': 1, 'description': 'Content Security Policy missing'}], 'scan_time': '0 seconds', 'raw_output': 'Basic security scan completed. Found 5 potential issues.'}}, 'vulnerabilities': [{'name': 'FTP Service Detected', 'severity': 'medium', 'description': 'FTP service may allow anonymous access', 'port': 21, 'solution': 'Review FTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'SMTP Service Detected', 'severity': 'medium', 'description': 'SMTP service - check for open relay', 'port': 25, 'solution': 'Review SMTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'HTTP Service Detected', 'severity': 'medium', 'description': 'HTTP service - check for web vulnerabilities', 'port': 80, 'solution': 'Review HTTP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'POP3 Service Detected', 'severity': 'medium', 'description': 'POP3 service - unencrypted email protocol', 'port': 110, 'solution': 'Review POP3 service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'name': 'IMAP Service Detected', 'severity': 'medium', 'description': 'IMAP service - check for security configuration', 'port': 143, 'solution': 'Review IMAP service configuration and security settings', 'source_tool': 'openvas', 'category': 'network'}, {'type': 'SQL Injection - SQL injection', 'severity': 'high', 'description': 'SQL injection found in parameter: unknown', 'parameter': 'unknown', 'dbms': 'MySQL', 'source_tool': 'sqlmap', 'category': 'web'}, {'type': 'SQL Injection - SQL injection', 'severity': 'high', 'description': 'SQL injection found in parameter: unknown', 'parameter': 'unknown', 'dbms': 'MySQL', 'source_tool': 'sqlmap', 'category': 'web'}, {'type': 'SQL Injection - boolean-based blind', 'severity': 'high', 'description': 'SQL injection found in parameter: unknown', 'parameter': 'unknown', 'dbms': 'MySQL', 'source_tool': 'sqlmap', 'category': 'web'}, {'type': 'SQL Injection - SQL injection', 'severity': 'high', 'description': 'SQL injection found in parameter: unknown', 'parameter': 'unknown', 'dbms': 'MySQL', 'source_tool': 'sqlmap', 'category': 'web'}, {'type': 'SQL Injection - SQL injection', 'severity': 'high', 'description': 'SQL injection found in parameter: unknown', 'parameter': 'unknown', 'dbms': 'MySQL', 'source_tool': 'sqlmap', 'category': 'web'}, {'type': 'SQL Injection - boolean-based blind', 'severity': 'high', 'description': 'SQL injection found in parameter: unknown', 'parameter': 'unknown', 'dbms': 'MySQL', 'source_tool': 'sqlmap', 'category': 'web'}, {'type': 'SQL Injection - SQL injection', 'severity': 'high', 'description': 'SQL injection found in parameter: unknown', 'parameter': 'unknown', 'dbms': 'MySQL', 'source_tool': 'sqlmap', 'category': 'web'}, {'type': 'SQL Injection - SQL injection', 'severity': 'high', 'description': 'SQL injection found in parameter: unknown', 'parameter': 'unknown', 'dbms': 'MySQL', 'source_tool': 'sqlmap', 'category': 'web'}, {'type': 'Missing X-Frame-Options', 'severity': 'medium', 'description': 'Clickjacking protection missing', 'source_tool': 'zap', 'category': 'web'}, {'type': 'Missing X-Content-Type-Options', 'severity': 'medium', 'description': 'MIME sniffing protection missing', 'source_tool': 'zap', 'category': 'web'}, {'type': 'Missing X-XSS-Protection', 'severity': 'medium', 'description': 'XSS protection header missing', 'source_tool': 'zap', 'category': 'web'}, {'type': 'Missing Strict-Transport-Security', 'severity': 'medium', 'description': 'HTTPS enforcement missing', 'source_tool': 'zap', 'category': 'web'}, {'type': 'Missing Content-Security-Policy', 'severity': 'medium', 'description': 'Content Security Policy missing', 'source_tool': 'zap', 'category': 'web'}], 'ports': [], 'summary': {'total_ports': 0, 'open_ports': 0, 'total_vulnerabilities': 18, 'high_severity': 8, 'medium_severity': 10, 'low_severity': 0, 'scan_phases': 3, 'tools_executed': 8}}
2025-06-26 20:16:03,734 - scan_cdf3abc4-b8fa-4dc2-b92c-02d8fa1cbf01 - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 1329.8s
