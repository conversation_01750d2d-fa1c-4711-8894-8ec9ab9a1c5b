2025-06-25 20:03:27,152 - scan_f9dcf21b-10eb-408a-9c32-758616c4fe8a - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: *************, Tools: nmap, openvas, metasploit
2025-06-25 20:03:27,157 - scan_f9dcf21b-10eb-408a-9c32-758616c4fe8a - INFO - 🚀 SCAN STARTED - Category: network, Type: basic, Target: *************, Tools: nmap, openvas, metasploit
2025-06-25 20:03:27,164 - scan_f9dcf21b-10eb-408a-9c32-758616c4fe8a - INFO - 🔧 TOOL START - NMAP - Command: nmap scan on *************
2025-06-25 20:03:27,164 - scan_f9dcf21b-10eb-408a-9c32-758616c4fe8a - INFO - 🔧 TOOL START - OPENVAS - Command: openvas scan on *************
2025-06-25 20:03:27,166 - scan_f9dcf21b-10eb-408a-9c32-758616c4fe8a - INFO - 🔧 TOOL START - METASPLOIT - Command: metasploit scan on *************
2025-06-25 20:03:27,173 - scan_f9dcf21b-10eb-408a-9c32-758616c4fe8a - INFO - 📊 TOOL PROGRESS - NMAP: 10% - Starting Nmap scan...
2025-06-25 20:03:27,176 - scan_f9dcf21b-10eb-408a-9c32-758616c4fe8a - INFO - 📊 TOOL PROGRESS - OPENVAS: 10% - Starting OpenVAS scan...
2025-06-25 20:03:27,186 - scan_f9dcf21b-10eb-408a-9c32-758616c4fe8a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 10% - Starting Metasploit modules...
2025-06-25 20:03:27,191 - scan_f9dcf21b-10eb-408a-9c32-758616c4fe8a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 50% - Running auxiliary modules...
2025-06-25 20:03:27,214 - scan_f9dcf21b-10eb-408a-9c32-758616c4fe8a - INFO - 📊 TOOL PROGRESS - NMAP: 30% - Running port scan...
2025-06-25 20:03:29,197 - scan_f9dcf21b-10eb-408a-9c32-758616c4fe8a - INFO - 📊 TOOL PROGRESS - METASPLOIT: 80% - Analyzing results...
2025-06-25 20:03:30,201 - scan_f9dcf21b-10eb-408a-9c32-758616c4fe8a - DEBUG - 📝 TOOL OUTPUT - METASPLOIT (stdout): Metasploit scan simulated (integration pending)
2025-06-25 20:03:30,206 - scan_f9dcf21b-10eb-408a-9c32-758616c4fe8a - INFO - ✅ TOOL RESULT - METASPLOIT: completed - Found 0 vulnerabilities
2025-06-25 20:03:57,194 - scan_f9dcf21b-10eb-408a-9c32-758616c4fe8a - ERROR - ❌ TOOL ERROR - OPENVAS: OpenVAS not available
2025-06-25 20:03:57,198 - scan_f9dcf21b-10eb-408a-9c32-758616c4fe8a - INFO - ✅ TOOL RESULT - OPENVAS: completed
2025-06-25 20:04:09,091 - scan_f9dcf21b-10eb-408a-9c32-758616c4fe8a - INFO - 📊 TOOL PROGRESS - NMAP: 90% - Processing results...
2025-06-25 20:04:09,094 - scan_f9dcf21b-10eb-408a-9c32-758616c4fe8a - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Scan completed: /usr/bin/nmap -sT -sV --version-intensity 5 -p 1-1000 -oX /tmp/nmap_scan_2addf70f-a03f-4ec9-9d0e-476aff21c4f1.xml *************
2025-06-25 20:04:09,096 - scan_f9dcf21b-10eb-408a-9c32-758616c4fe8a - DEBUG - 📝 TOOL OUTPUT - NMAP (stdout): Found 3 ports
2025-06-25 20:04:09,100 - scan_f9dcf21b-10eb-408a-9c32-758616c4fe8a - INFO - ✅ TOOL RESULT - NMAP: completed - Found 3 ports - Found 0 vulnerabilities
2025-06-25 20:04:09,104 - scan_f9dcf21b-10eb-408a-9c32-758616c4fe8a - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 0.0s - Summary: {'tools': {'metasploit': {'status': 'simulated', 'modules_run': ['auxiliary/scanner/portscan/tcp', 'auxiliary/scanner/smb/smb_version', 'auxiliary/scanner/ssh/ssh_version'], 'note': 'Metasploit integration pending - simulated results', 'vulnerabilities': [], 'exploits': []}, 'openvas': {'status': 'unavailable', 'error': 'OpenVAS service not available'}, 'nmap': {'status': 'completed', 'scan_id': '2addf70f-a03f-4ec9-9d0e-476aff21c4f1', 'target': '*************', 'start_time': '2025-06-25T18:03:27.219055', 'end_time': '2025-06-25T18:04:09.091396', 'scan_time': 41.872341, 'command': '/usr/bin/nmap -sT -sV --version-intensity 5 -p 1-1000 -oX /tmp/nmap_scan_2addf70f-a03f-4ec9-9d0e-476aff21c4f1.xml *************', 'ports': [{'port': 22, 'protocol': 'tcp', 'state': 'open', 'service': 'ssh', 'version': 'OpenSSH 9.6p1 Ubuntu 3ubuntu13.11 (Ubuntu Linux; protocol 2.0)'}, {'port': 80, 'protocol': 'tcp', 'state': 'open', 'service': 'http', 'version': 'Apache httpd 2.4.58 ((Ubuntu))'}, {'port': 443, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/http', 'version': 'Apache httpd 2.4.58 ((Ubuntu))'}], 'vulnerabilities': [], 'raw_output': 'Starting Nmap 7.94SVN ( https://nmap.org ) at 2025-06-25 20:03 CEST\nNmap scan report for vps-3811c29b.vps.ovh.ca (*************)\nHost is up (0.25s latency).\nNot shown: 997 closed tcp ports (conn-refused)\nPORT    STATE SERVICE  VERSION\n22/tcp  open  ssh      OpenSSH 9.6p1 Ubuntu 3ubuntu13.11 (Ubuntu Linux; protocol 2.0)\n80/tcp  open  http     Apache httpd 2.4.58 ((Ubuntu))\n443/tcp open  ssl/http Apache httpd 2.4.58 ((Ubuntu))\nService Info: OS: Linux; CPE: cpe:/o:linux:linux_kernel\n\nService detection performed. Please report any incorrect results at https://nmap.org/submit/ .\nNmap done: 1 IP address (1 host up) scanned in 41.85 seconds\n'}}, 'ports': [{'port': 22, 'protocol': 'tcp', 'state': 'open', 'service': 'ssh', 'version': 'OpenSSH 9.6p1 Ubuntu 3ubuntu13.11 (Ubuntu Linux; protocol 2.0)'}, {'port': 80, 'protocol': 'tcp', 'state': 'open', 'service': 'http', 'version': 'Apache httpd 2.4.58 ((Ubuntu))'}, {'port': 443, 'protocol': 'tcp', 'state': 'open', 'service': 'ssl/http', 'version': 'Apache httpd 2.4.58 ((Ubuntu))'}], 'vulnerabilities': [], 'summary': {'total_ports': 3, 'open_ports': 3, 'total_vulnerabilities': 0}}
2025-06-25 20:04:09,107 - scan_f9dcf21b-10eb-408a-9c32-758616c4fe8a - INFO - 🏁 SCAN COMPLETE - Status: completed, Duration: 42.0s
