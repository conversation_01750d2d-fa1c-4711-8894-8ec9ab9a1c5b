#!/usr/bin/env python3
"""
Script de test pour vérifier la disponibilité des services de pentesting
"""

import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.openvas_service import OpenVASService
from app.services.nikto_service import NiktoService
from app.services.zap_service import ZAPService
from app.services.nmap_service import NmapService
from app.services.sqlmap_service import SQLMapService
from app.services.dirb_service import DirbService
from app.services.gobuster_service import GoBusterService

def test_service_availability():
    """Tester la disponibilité de tous les services"""
    
    services = {
        'OpenVAS': OpenVASService(),
        'Nmap': NmapService(),
        'Nikto': NiktoService(),
        'SQLMap': SQLMapService(),
        'Dirb': DirbService(),
        'GoBuster': GoBusterService(),
        'ZAP': ZAPService()
    }
    
    print("🔍 Testing service availability...")
    print("=" * 50)
    
    available_services = []
    unavailable_services = []
    
    for name, service in services.items():
        try:
            is_available = service.is_available()
            status = "✅ Available" if is_available else "❌ Not available"
            print(f"{name:12} : {status}")
            
            if is_available:
                available_services.append(name)
            else:
                unavailable_services.append(name)
                
        except Exception as e:
            print(f"{name:12} : ❌ Error - {e}")
            unavailable_services.append(name)
    
    print("=" * 50)
    print(f"✅ Available services ({len(available_services)}): {', '.join(available_services)}")
    print(f"❌ Unavailable services ({len(unavailable_services)}): {', '.join(unavailable_services)}")
    
    return available_services, unavailable_services

def test_nmap_scan():
    """Test rapide d'un scan Nmap"""
    print("\n🔍 Testing Nmap scan...")
    
    nmap_service = NmapService()
    if not nmap_service.is_available():
        print("❌ Nmap not available, skipping test")
        return
    
    try:
        # Test sur localhost
        result = nmap_service.scan_target('127.0.0.1', ['-F'])  # Fast scan
        print(f"✅ Nmap test completed: {result.get('status')}")
        if result.get('ports'):
            print(f"   Found {len(result['ports'])} ports")
        if result.get('vulnerabilities'):
            print(f"   Found {len(result['vulnerabilities'])} vulnerabilities")
    except Exception as e:
        print(f"❌ Nmap test failed: {e}")

def test_openvas_connection():
    """Test de connexion OpenVAS"""
    print("\n🛡️ Testing OpenVAS connection...")
    
    openvas_service = OpenVASService()
    try:
        is_available = openvas_service.is_available()
        if is_available:
            print("✅ OpenVAS connection successful")
            
            # Test de récupération des targets
            targets = openvas_service.get_targets()
            print(f"   Found {len(targets)} targets")
            
            # Test de récupération des tasks
            tasks = openvas_service.get_tasks()
            print(f"   Found {len(tasks)} tasks")
        else:
            print("❌ OpenVAS not available")
    except Exception as e:
        print(f"❌ OpenVAS test failed: {e}")

def main():
    """Fonction principale"""
    print("🚀 PICA Pentesting Services Test")
    print("=" * 50)
    
    # Test de disponibilité des services
    available, unavailable = test_service_availability()
    
    # Tests spécifiques pour les services disponibles
    if 'Nmap' in available:
        test_nmap_scan()
    
    if 'OpenVAS' in available:
        test_openvas_connection()
    
    print("\n" + "=" * 50)
    print("🏁 Test completed!")
    
    if len(available) >= len(unavailable):
        print("✅ Most services are available - scan controller should work well")
    else:
        print("⚠️ Many services are unavailable - some scans may be limited")
    
    print("\n💡 To install missing tools:")
    if 'Nmap' in unavailable:
        print("   sudo apt install nmap")
    if 'Nikto' in unavailable:
        print("   sudo apt install nikto")
    if 'SQLMap' in unavailable:
        print("   sudo apt install sqlmap")
    if 'Dirb' in unavailable:
        print("   sudo apt install dirb")
    if 'GoBuster' in unavailable:
        print("   sudo apt install gobuster")
    if 'ZAP' in unavailable:
        print("   sudo apt install zaproxy")
    if 'OpenVAS' in unavailable:
        print("   # OpenVAS requires special installation - see documentation")

if __name__ == "__main__":
    main()
