#!/usr/bin/env python3
"""
Script de migration pour convertir les user_id de username vers ObjectId
dans les collections analyses_scan et scans.
"""

from app.extensions import mongo
from app import create_app
from bson import ObjectId

def migrate_analyses_user_ids():
    """Migre les user_id dans la collection analyses_scan"""
    print("🔄 Migration des user_id dans analyses_scan...")
    
    app = create_app()
    with app.app_context():
        # Récupérer toutes les analyses avec des user_id en string
        analyses = list(mongo.db.analyses_scan.find({}))
        
        migrated_count = 0
        error_count = 0
        
        for analysis in analyses:
            user_id = analysis.get('user_id')
            
            # Si c'est déjà un ObjectId, passer
            if isinstance(user_id, ObjectId):
                continue
                
            # Si c'est un string, essayer de le convertir
            if isinstance(user_id, str):
                # D'abord essayer de voir si c'est déjà un ObjectId valide
                if len(user_id) == 24:
                    try:
                        object_id = ObjectId(user_id)
                        # Vérifier que l'utilisateur existe
                        user_exists = mongo.db.users.find_one({'_id': object_id})
                        if user_exists:
                            continue  # Déjà correct
                    except:
                        pass
                
                # Sinon, chercher l'utilisateur par username
                user_doc = mongo.db.users.find_one({'username': user_id})
                if user_doc:
                    # Mettre à jour avec l'ObjectId
                    result = mongo.db.analyses_scan.update_one(
                        {'_id': analysis['_id']},
                        {'$set': {'user_id': user_doc['_id']}}
                    )
                    if result.modified_count > 0:
                        migrated_count += 1
                        print(f"  ✅ Migré analyse {analysis['analysis_id']}: '{user_id}' -> {user_doc['_id']}")
                    else:
                        error_count += 1
                        print(f"  ❌ Échec migration analyse {analysis['analysis_id']}")
                else:
                    error_count += 1
                    print(f"  ⚠️ Utilisateur non trouvé pour analyse {analysis['analysis_id']}: '{user_id}'")
        
        print(f"\n📊 Résultats migration analyses_scan:")
        print(f"  - Analyses migrées: {migrated_count}")
        print(f"  - Erreurs: {error_count}")
        print(f"  - Total analyses: {len(analyses)}")

def migrate_scans_user_ids():
    """Migre les user_id dans la collection scans"""
    print("\n🔄 Migration des user_id dans scans...")
    
    app = create_app()
    with app.app_context():
        # Récupérer tous les scans avec des user_id en string
        scans = list(mongo.db.scans.find({}))
        
        migrated_count = 0
        error_count = 0
        none_count = 0
        
        for scan in scans:
            user_id = scan.get('user_id')
            
            # Si user_id est None, compter mais ne pas traiter
            if user_id is None:
                none_count += 1
                continue
                
            # Si c'est déjà un ObjectId, passer
            if isinstance(user_id, ObjectId):
                continue
                
            # Si c'est un string, essayer de le convertir
            if isinstance(user_id, str):
                # D'abord essayer de voir si c'est déjà un ObjectId valide
                if len(user_id) == 24:
                    try:
                        object_id = ObjectId(user_id)
                        # Vérifier que l'utilisateur existe
                        user_exists = mongo.db.users.find_one({'_id': object_id})
                        if user_exists:
                            continue  # Déjà correct
                    except:
                        pass
                
                # Sinon, chercher l'utilisateur par username
                user_doc = mongo.db.users.find_one({'username': user_id})
                if user_doc:
                    # Mettre à jour avec l'ObjectId
                    result = mongo.db.scans.update_one(
                        {'_id': scan['_id']},
                        {'$set': {'user_id': user_doc['_id']}}
                    )
                    if result.modified_count > 0:
                        migrated_count += 1
                        print(f"  ✅ Migré scan {scan.get('scan_id', 'unknown')}: '{user_id}' -> {user_doc['_id']}")
                    else:
                        error_count += 1
                        print(f"  ❌ Échec migration scan {scan.get('scan_id', 'unknown')}")
                else:
                    error_count += 1
                    print(f"  ⚠️ Utilisateur non trouvé pour scan {scan.get('scan_id', 'unknown')}: '{user_id}'")
        
        print(f"\n📊 Résultats migration scans:")
        print(f"  - Scans migrés: {migrated_count}")
        print(f"  - Erreurs: {error_count}")
        print(f"  - Scans avec user_id=None: {none_count}")
        print(f"  - Total scans: {len(scans)}")

def verify_migration():
    """Vérifie que la migration s'est bien passée"""
    print("\n🔍 Vérification de la migration...")
    
    app = create_app()
    with app.app_context():
        # Vérifier analyses_scan
        analyses_with_string_ids = list(mongo.db.analyses_scan.find({
            'user_id': {'$type': 'string'}
        }))
        
        # Vérifier scans
        scans_with_string_ids = list(mongo.db.scans.find({
            'user_id': {'$type': 'string'}
        }))
        
        print(f"  - Analyses avec user_id string restants: {len(analyses_with_string_ids)}")
        print(f"  - Scans avec user_id string restants: {len(scans_with_string_ids)}")
        
        if len(analyses_with_string_ids) == 0 and len(scans_with_string_ids) == 0:
            print("  ✅ Migration réussie ! Tous les user_id sont maintenant des ObjectId.")
        else:
            print("  ⚠️ Il reste des user_id en string à migrer.")

if __name__ == "__main__":
    print("🚀 Début de la migration des user_id...")
    
    # Migrer les analyses
    migrate_analyses_user_ids()
    
    # Migrer les scans
    migrate_scans_user_ids()
    
    # Vérifier la migration
    verify_migration()
    
    print("\n✅ Migration terminée !")
