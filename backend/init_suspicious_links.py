#!/usr/bin/env python3
"""
Script d'initialisation de la collection des liens suspects

Ce script cree la collection 'suspicious_links' avec les index necessaires
pour optimiser les performances des requetes.

Usage: python3 init_suspicious_links.py
"""

import sys
import os
from datetime import datetime
from pymongo import MongoClient

# Configuration MongoDB
MONGO_URI = "mongodb://localhost:27017/"
DATABASE_NAME = "pica"

def get_mongo_client():
    """Obtient une connexion MongoDB."""
    try:
        client = MongoClient(MONGO_URI)
        db = client[DATABASE_NAME]
        return client, db
    except Exception as e:
        print(f"❌ Erreur de connexion MongoDB: {str(e)}")
        return None, None

def create_indexes(db):
    """Cree les index necessaires pour la collection suspicious_links."""
    
    print("🔧 Creation des index pour la collection 'suspicious_links'...")
    
    try:
        collection = db.suspicious_links
        
        # Index sur l'URL (unique)
        collection.create_index("url", unique=True)
        print("✅ Index unique sur 'url' cree")
        
        # Index sur le domaine
        collection.create_index("domain")
        print("✅ Index sur 'domain' cree")
        
        # Index sur le score de risque (descendant)
        collection.create_index([("risk_score", -1)])
        print("✅ Index sur 'risk_score' cree")
        
        # Index sur le statut
        collection.create_index("status")
        print("✅ Index sur 'status' cree")
        
        # Index sur la date de derniere detection (descendant)
        collection.create_index([("last_detected", -1)])
        print("✅ Index sur 'last_detected' cree")
        
        # Index sur la date de premiere detection
        collection.create_index([("first_detected", -1)])
        print("✅ Index sur 'first_detected' cree")
        
        # Index sur le nombre de detections (descendant)
        collection.create_index([("detection_count", -1)])
        print("✅ Index sur 'detection_count' cree")
        
        # Index sur l'utilisateur qui a detecte
        collection.create_index("user_id")
        print("✅ Index sur 'user_id' cree")
        
        # Index compose pour les requetes frequentes
        collection.create_index([
            ("status", 1),
            ("risk_score", -1),
            ("last_detected", -1)
        ])
        print("✅ Index compose (status, risk_score, last_detected) cree")
        
        # Index pour les recherches par domaine et score
        collection.create_index([
            ("domain", 1),
            ("risk_score", -1)
        ])
        print("✅ Index compose (domain, risk_score) cree")
        
        print("🎉 Tous les index ont ete crees avec succes!")
        
    except Exception as e:
        print(f"❌ Erreur lors de la creation des index: {str(e)}")
        return False
    
    return True

def create_sample_data(db):
    """Cree quelques donnees d'exemple pour tester la collection."""
    
    print("\n📝 Creation de donnees d'exemple...")
    
    try:
        collection = db.suspicious_links
        
        # Verifier si des donnees existent deja
        existing_count = collection.count_documents({})
        if existing_count > 0:
            print(f"⚠️ La collection contient deja {existing_count} documents. Pas de donnees d'exemple ajoutees.")
            return True
        
        # Donnees d'exemple
        sample_links = [
            {
                'url': 'http://phishing-example-1.com/login',
                'domain': 'phishing-example-1.com',
                'first_detected': datetime.utcnow(),
                'last_detected': datetime.utcnow(),
                'detection_count': 1,
                'risk_score': 85,
                'max_risk_score': 85,
                'likelihood': 'High Risk',
                'max_likelihood': 'High Risk',
                'latest_risk_score': 85,
                'latest_likelihood': 'High Risk',
                'is_phishing': True,
                'analysis_id': 'sample-analysis-1',
                'latest_analysis_id': 'sample-analysis-1',
                'user_id': '<EMAIL>',
                'latest_user_id': '<EMAIL>',
                'status': 'active',
                'verified_by': None,
                'verification_date': None,
                'notes': '',
                'failed_checks': [
                    {'name': 'Suspicious TLD', 'result': 'Failed', 'description': 'Domain uses suspicious TLD'},
                    {'name': 'Brand Impersonation', 'result': 'Failed', 'description': 'Possible brand impersonation'}
                ],
                'warning_checks': [],
                'total_checks': 15,
                'failed_checks_count': 2,
                'warning_checks_count': 0
            },
            {
                'url': 'https://suspicious-site.tk/secure',
                'domain': 'suspicious-site.tk',
                'first_detected': datetime.utcnow(),
                'last_detected': datetime.utcnow(),
                'detection_count': 3,
                'risk_score': 65,
                'max_risk_score': 75,
                'likelihood': 'Medium Risk',
                'max_likelihood': 'High Risk',
                'latest_risk_score': 65,
                'latest_likelihood': 'Medium Risk',
                'is_phishing': True,
                'analysis_id': 'sample-analysis-2',
                'latest_analysis_id': 'sample-analysis-2',
                'user_id': '<EMAIL>',
                'latest_user_id': '<EMAIL>',
                'status': 'verified',
                'verified_by': '<EMAIL>',
                'verification_date': datetime.utcnow(),
                'notes': 'Confirmed phishing site targeting banking credentials',
                'failed_checks': [
                    {'name': 'Suspicious TLD', 'result': 'Failed', 'description': 'Domain uses suspicious TLD (.tk)'}
                ],
                'warning_checks': [
                    {'name': 'Login Form', 'result': 'Warning', 'description': 'Page contains login form'}
                ],
                'total_checks': 15,
                'failed_checks_count': 1,
                'warning_checks_count': 1
            },
            {
                'url': 'http://fake-paypal.ml/signin',
                'domain': 'fake-paypal.ml',
                'first_detected': datetime.utcnow(),
                'last_detected': datetime.utcnow(),
                'detection_count': 1,
                'risk_score': 92,
                'max_risk_score': 92,
                'likelihood': 'High Risk',
                'max_likelihood': 'High Risk',
                'latest_risk_score': 92,
                'latest_likelihood': 'High Risk',
                'is_phishing': True,
                'analysis_id': 'sample-analysis-3',
                'latest_analysis_id': 'sample-analysis-3',
                'user_id': '<EMAIL>',
                'latest_user_id': '<EMAIL>',
                'status': 'active',
                'verified_by': None,
                'verification_date': None,
                'notes': '',
                'failed_checks': [
                    {'name': 'Brand Impersonation', 'result': 'Failed', 'description': 'Domain appears to be impersonating: paypal'},
                    {'name': 'Suspicious TLD', 'result': 'Failed', 'description': 'Domain uses suspicious TLD (.ml)'}
                ],
                'warning_checks': [],
                'total_checks': 15,
                'failed_checks_count': 2,
                'warning_checks_count': 0
            }
        ]
        
        # Inserer les donnees d'exemple
        result = collection.insert_many(sample_links)
        print(f"✅ {len(result.inserted_ids)} liens d'exemple ajoutes")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la creation des donnees d'exemple: {str(e)}")
        return False

def show_collection_info(db):
    """Affiche les informations sur la collection."""
    
    print("\n📊 Informations sur la collection 'suspicious_links':")
    
    try:
        collection = db.suspicious_links
        
        # Compter les documents
        total_docs = collection.count_documents({})
        print(f"📄 Nombre total de documents: {total_docs}")
        
        # Statistiques par statut
        active_count = collection.count_documents({'status': 'active'})
        verified_count = collection.count_documents({'status': 'verified'})
        false_positive_count = collection.count_documents({'status': 'false_positive'})
        
        print(f"🟢 Liens actifs: {active_count}")
        print(f"✅ Liens verifies: {verified_count}")
        print(f"❌ Faux positifs: {false_positive_count}")
        
        # Statistiques par score de risque
        high_risk = collection.count_documents({'risk_score': {'$gte': 70}})
        medium_risk = collection.count_documents({'risk_score': {'$gte': 40, '$lt': 70}})
        low_risk = collection.count_documents({'risk_score': {'$gte': 30, '$lt': 40}})
        
        print(f"🔴 Risque eleve (≥70%): {high_risk}")
        print(f"🟡 Risque moyen (40-69%): {medium_risk}")
        print(f"🟠 Risque faible (30-39%): {low_risk}")
        
        # Lister les index
        indexes = collection.list_indexes()
        print(f"\n🔍 Index disponibles:")
        for index in indexes:
            print(f"  - {index['name']}: {index.get('key', {})}")
        
    except Exception as e:
        print(f"❌ Erreur lors de la recuperation des informations: {str(e)}")

def main():
    """Fonction principale."""
    print("🚀 Initialisation de la collection des liens suspects")
    print("=" * 60)
    
    # Se connecter a MongoDB
    client, db = get_mongo_client()
    if client is None or db is None:
        return 1
    
    try:
        # Creer les index
        if not create_indexes(db):
            print("❌ Echec de la creation des index")
            return 1
        
        # Creer les donnees d'exemple
        if not create_sample_data(db):
            print("❌ Echec de la creation des donnees d'exemple")
            return 1
        
        # Afficher les informations
        show_collection_info(db)
        
    finally:
        # Fermer la connexion
        client.close()
    
    print("\n🎉 Initialisation terminee avec succes!")
    return 0

if __name__ == "__main__":
    exit(main())
