#!/usr/bin/env python3
"""
Script pour corriger les tickets avec user_id: "admin" et les convertir en ObjectId valides.
"""

import sys
import os
from datetime import datetime
from bson import ObjectId

# Ajouter le répertoire parent au path pour importer les modules de l'app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.extensions import mongo
from app import create_app

def get_users_collection():
    """Obtenir la collection des utilisateurs"""
    try:
        return mongo.db.users
    except Exception as e:
        print(f"❌ MongoDB users collection error: {str(e)}")
        return None

def get_tickets_collection():
    """Obtenir la collection des tickets"""
    try:
        return mongo.db.investigation_tickets
    except Exception as e:
        print(f"❌ MongoDB tickets collection error: {str(e)}")
        return None

def fix_admin_tickets():
    """Corriger les tickets avec user_id: 'admin'"""
    print("🔧 Correction des tickets avec user_id: 'admin'...")
    
    users_collection = get_users_collection()
    tickets_collection = get_tickets_collection()
    
    if users_collection is None or tickets_collection is None:
        print("❌ Impossible d'accéder aux collections MongoDB")
        return
    
    # Trouver l'utilisateur admin
    admin_user = users_collection.find_one({'username': 'admin'})
    if not admin_user:
        print("❌ Utilisateur admin non trouvé")
        return
    
    admin_object_id = admin_user['_id']
    admin_email = admin_user['email']
    
    print(f"👑 Utilisateur admin trouvé: {admin_email} (ID: {admin_object_id})")
    
    # Trouver les tickets avec user_id: "admin"
    tickets_to_fix = list(tickets_collection.find({'user_id': 'admin'}))
    print(f"📊 Tickets avec user_id: 'admin' trouvés: {len(tickets_to_fix)}")
    
    if not tickets_to_fix:
        print("✅ Aucun ticket à corriger")
        return
    
    # Corriger les tickets
    tickets_fixed = 0
    for ticket in tickets_to_fix:
        ticket_number = ticket.get('ticket_number', 'Unknown')
        print(f"🔧 Correction du ticket {ticket_number}")
        
        result = tickets_collection.update_one(
            {'_id': ticket['_id']},
            {
                '$set': {
                    'user_id': admin_object_id,
                    'user_email': admin_email,
                    'fixed_by_script': True,
                    'fixed_at': datetime.utcnow()
                }
            }
        )
        
        if result.modified_count > 0:
            tickets_fixed += 1
            print(f"   ✅ Ticket {ticket_number} corrigé")
        else:
            print(f"   ❌ Échec correction ticket {ticket_number}")
    
    print(f"\n✅ Correction terminée:")
    print(f"   - Tickets corrigés: {tickets_fixed}/{len(tickets_to_fix)}")
    print(f"   - user_id: 'admin' → ObjectId({admin_object_id})")

def main():
    """Fonction principale"""
    print("🚀 Script de correction des tickets admin")
    print("=" * 50)
    
    # Créer l'application Flask pour accéder à MongoDB
    app = create_app()
    
    with app.app_context():
        # Compter les tickets à corriger
        tickets_collection = get_tickets_collection()
        
        if tickets_collection is None:
            print("❌ Impossible d'accéder à la collection tickets")
            return
        
        tickets_count = tickets_collection.count_documents({'user_id': 'admin'})
        
        print(f"📊 Tickets avec user_id: 'admin': {tickets_count}")
        
        if tickets_count > 0:
            response = input(f"\n❓ Voulez-vous corriger {tickets_count} tickets ? (y/N): ")
            if response.lower() in ['y', 'yes', 'oui']:
                fix_admin_tickets()
            else:
                print("❌ Correction annulée")
        else:
            print("✅ Aucune correction nécessaire")

if __name__ == "__main__":
    main()
