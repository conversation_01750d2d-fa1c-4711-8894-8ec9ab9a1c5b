#!/bin/bash

# Script pour configurer le nettoyage automatique des tokens
# Ce script configure une tâche cron pour nettoyer automatiquement les tokens expirés

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKEND_DIR="$(cd "$SCRIPT_DIR/../.." && pwd)"
CLEANUP_SCRIPT="$SCRIPT_DIR/../cleanup/cleanup_tokens.py"
PYTHON_PATH="$BACKEND_DIR/venv/bin/python3"

# Vérifier que le script de nettoyage existe
if [ ! -f "$CLEANUP_SCRIPT" ]; then
    echo "❌ Script de nettoyage non trouvé: $CLEANUP_SCRIPT"
    exit 1
fi

# Vérifier que Python existe
if [ ! -f "$PYTHON_PATH" ]; then
    echo "⚠️  Python venv non trouvé à $PYTHON_PATH"
    echo "🔍 Recherche de python3 dans le système..."
    PYTHON_PATH=$(which python3)
    if [ -z "$PYTHON_PATH" ]; then
        echo "❌ Python3 non trouvé"
        exit 1
    fi
    echo "✅ Utilisation de $PYTHON_PATH"
fi

echo "🧹 Configuration du nettoyage automatique des tokens"
echo "📁 Répertoire: $SCRIPT_DIR"
echo "🐍 Python: $PYTHON_PATH"
echo "📜 Script: $CLEANUP_SCRIPT"
echo

# Créer la commande cron
CRON_COMMAND="cd $BACKEND_DIR && $PYTHON_PATH $CLEANUP_SCRIPT --max-age-hours 24"

echo "📋 Commande cron qui sera ajoutée:"
echo "   $CRON_COMMAND"
echo

# Demander confirmation
read -p "🤔 Voulez-vous configurer le nettoyage automatique ? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Configuration annulée"
    exit 0
fi

# Demander la fréquence
echo "⏰ Choisissez la fréquence de nettoyage:"
echo "1) Toutes les heures"
echo "2) Toutes les 6 heures"
echo "3) Une fois par jour (recommandé)"
echo "4) Une fois par semaine"
echo "5) Configuration manuelle"

read -p "Votre choix (1-5): " -n 1 -r
echo

case $REPLY in
    1)
        CRON_SCHEDULE="0 * * * *"
        DESCRIPTION="toutes les heures"
        ;;
    2)
        CRON_SCHEDULE="0 */6 * * *"
        DESCRIPTION="toutes les 6 heures"
        ;;
    3)
        CRON_SCHEDULE="0 2 * * *"
        DESCRIPTION="tous les jours à 2h du matin"
        ;;
    4)
        CRON_SCHEDULE="0 2 * * 0"
        DESCRIPTION="tous les dimanches à 2h du matin"
        ;;
    5)
        read -p "Entrez la planification cron (ex: '0 2 * * *'): " CRON_SCHEDULE
        DESCRIPTION="selon votre planification"
        ;;
    *)
        echo "❌ Choix invalide"
        exit 1
        ;;
esac

# Créer la ligne cron complète
CRON_LINE="$CRON_SCHEDULE $CRON_COMMAND >> $BACKEND_DIR/logs/cleanup.log 2>&1"

echo "📅 Planification: $DESCRIPTION"
echo "📋 Ligne cron complète:"
echo "   $CRON_LINE"
echo

# Demander confirmation finale
read -p "✅ Confirmer l'ajout de cette tâche cron ? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Configuration annulée"
    exit 0
fi

# Sauvegarder la crontab actuelle
echo "💾 Sauvegarde de la crontab actuelle..."
crontab -l > /tmp/crontab_backup_$(date +%Y%m%d_%H%M%S) 2>/dev/null || true

# Ajouter la nouvelle tâche
echo "➕ Ajout de la tâche cron..."
(crontab -l 2>/dev/null; echo "# PICA - Nettoyage automatique des tokens de confirmation"; echo "$CRON_LINE") | crontab -

if [ $? -eq 0 ]; then
    echo "✅ Tâche cron ajoutée avec succès !"
    echo
    echo "📋 Crontab actuelle:"
    crontab -l | grep -A1 -B1 "PICA"
    echo
    echo "📝 Les logs seront dans: $BACKEND_DIR/logs/cleanup.log"
    echo "🔍 Pour voir les logs: tail -f $BACKEND_DIR/logs/cleanup.log"
    echo "🗑️  Pour supprimer la tâche: crontab -e"
else
    echo "❌ Erreur lors de l'ajout de la tâche cron"
    exit 1
fi

echo
echo "🎉 Configuration terminée !"
echo "⚡ Le nettoyage automatique est maintenant actif"
