# Scripts PICA Backend

Ce dossier contient tous les scripts utilitaires pour la maintenance et la gestion du backend PICA.

## 📁 Structure

```
scripts/
├── cleanup/           # Scripts de nettoyage de la base de données
│   └── cleanup_tokens.py
├── maintenance/       # Scripts de maintenance et configuration
│   └── setup_cleanup_cron.sh
└── README.md         # Ce fichier
```

## 🧹 Scripts de nettoyage

### `cleanup/cleanup_tokens.py`

Script de nettoyage des tokens de confirmation d'email expirés.

**Fonctionnalités :**
- Supprime les tokens utilisés anciens (par défaut > 24h)
- Supprime les tokens expirés (> 10 minutes)
- Mode dry-run pour tester sans supprimer
- Statistiques détaillées
- Logs verbeux

**Usage :**
```bash
# Depuis le dossier backend/
python3 scripts/cleanup/cleanup_tokens.py [options]

# Options disponibles :
--dry-run              # Test sans suppression
--verbose, -v          # Affichage détaillé
--max-age-hours N      # Âge max des tokens utilisés (défaut: 24h)
```

**Exemples :**
```bash
# Test en mode dry-run
python3 scripts/cleanup/cleanup_tokens.py --dry-run --verbose

# Nettoyage réel avec logs détaillés
python3 scripts/cleanup/cleanup_tokens.py --verbose

# Garder les tokens utilisés pendant 48h au lieu de 24h
python3 scripts/cleanup/cleanup_tokens.py --max-age-hours 48
```

## 🔧 Scripts de maintenance

### `maintenance/setup_cleanup_cron.sh`

Script interactif pour configurer le nettoyage automatique via cron.

**Fonctionnalités :**
- Configuration interactive
- Choix de la fréquence (horaire, quotidien, hebdomadaire)
- Sauvegarde automatique de la crontab
- Logs automatiques dans `backend/logs/cleanup.log`

**Usage :**
```bash
# Depuis le dossier backend/
./scripts/maintenance/setup_cleanup_cron.sh
```

**Fréquences disponibles :**
- Toutes les heures
- Toutes les 6 heures  
- Une fois par jour (recommandé)
- Une fois par semaine
- Configuration manuelle

## 📊 Monitoring

### Logs
Les logs de nettoyage automatique sont stockés dans :
```
backend/logs/cleanup.log
```

### Visualiser les logs
```bash
# Voir les derniers logs
tail -f backend/logs/cleanup.log

# Voir les logs du jour
grep "$(date +%Y-%m-%d)" backend/logs/cleanup.log
```

### Statistiques manuelles
```bash
# Voir les statistiques sans nettoyer
python3 scripts/cleanup/cleanup_tokens.py --dry-run
```

## 🚀 Recommandations

### Fréquence de nettoyage
- **Production** : Une fois par jour (2h du matin)
- **Développement** : Une fois par semaine
- **Test intensif** : Toutes les 6 heures

### Surveillance
- Vérifier les logs régulièrement
- Surveiller la taille de la collection `used_tokens`
- Ajuster `--max-age-hours` selon les besoins

### Sécurité
- Les tokens expirés sont automatiquement détectés
- Les tokens utilisés sont marqués pour éviter la réutilisation
- Le nettoyage préserve les tokens récents valides

## 🔍 Dépannage

### Erreurs communes

**"Module app not found"**
```bash
# Vérifier que vous êtes dans le bon dossier
cd /path/to/backend
python3 scripts/cleanup/cleanup_tokens.py
```

**"MongoDB connection failed"**
```bash
# Vérifier que MongoDB est démarré
sudo systemctl status mongod

# Vérifier la configuration
cat .env | grep MONGO_URI
```

**"Permission denied"**
```bash
# Rendre le script exécutable
chmod +x scripts/maintenance/setup_cleanup_cron.sh
```

### Support
Pour toute question ou problème, consulter :
- Les logs dans `backend/logs/cleanup.log`
- La documentation MongoDB
- Les issues du projet PICA
