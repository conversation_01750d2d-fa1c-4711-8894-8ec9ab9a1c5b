#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a sample incident with all the new fields for testing
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.extensions import mongo
from datetime import datetime
import uuid

def create_sample_incident():
    """Create a comprehensive sample incident for testing"""
    
    # Sample incident data with all new fields
    incident_data = {
        'incident_id': f"INC-{uuid.uuid4().hex[:8]}",
        'title': 'Critical Database Security Breach',
        'description': 'Unauthorized access detected to customer database. Multiple failed login attempts followed by successful breach through SQL injection vulnerability.',
        'severity': 'critical',
        'status': 'in_progress',
        'created_at': datetime.utcnow(),
        'updated_at': datetime.utcnow(),
        'user_id': 'admin',
        'user_email': '<EMAIL>',
        
        # New comprehensive fields
        'impact_assessment': 'Customer personal data potentially compromised. Affects approximately 10,000 user records including names, emails, and encrypted passwords. No financial data exposed.',
        'business_impact': 'critical',
        'root_cause': 'Unpatched SQL injection vulnerability in user authentication module. Input validation was insufficient for special characters in login form.',
        
        'mitigation_steps': [
            'Immediately blocked suspicious IP addresses',
            'Disabled affected user authentication endpoint',
            'Applied emergency SQL injection patch',
            'Initiated password reset for all potentially affected users',
            'Enabled additional database monitoring and alerting'
        ],
        
        'next_steps': [
            'Complete forensic analysis of database logs',
            'Notify affected customers within 24 hours',
            'File regulatory breach notification',
            'Conduct full security audit of authentication system',
            'Implement additional input validation controls',
            'Review and update incident response procedures'
        ],
        
        'linked_assets': [
            'customer-db-prod-01',
            'auth-service-api',
            'user-portal-frontend',
            'backup-system-primary'
        ],
        
        'assigned_to': '<EMAIL>',
        'assigned_team': 'Security Response Team',
        
        'references': [
            {
                'type': 'cve',
                'value': 'CVE-2023-12345',
                'description': 'SQL injection vulnerability in authentication module'
            },
            {
                'type': 'threat_id',
                'value': 'TID-2024-001',
                'description': 'Internal threat intelligence report'
            },
            {
                'type': 'external_report',
                'value': 'CERT-2024-0156',
                'description': 'CERT advisory on similar attacks'
            }
        ],
        
        'recommendations': [
            'Implement parameterized queries for all database interactions',
            'Deploy Web Application Firewall (WAF) with SQL injection protection',
            'Establish regular penetration testing schedule',
            'Enhance security awareness training for development team'
        ],
        
        'tags': ['sql-injection', 'data-breach', 'critical', 'customer-impact'],
        'evidence': [],
        'timeline': [
            {
                'timestamp': datetime.utcnow().isoformat(),
                'action': 'incident_created',
                'user_id': 'admin',
                'user_email': '<EMAIL>',
                'details': {
                    'severity': 'critical',
                    'initial_assessment': 'Database breach detected'
                }
            }
        ],
        'false_positive': False,
        'escalated': True
    }
    
    try:
        # Insert into MongoDB
        result = mongo.db.incidents.insert_one(incident_data)
        print(f"✅ Sample incident created successfully!")
        print(f"   Incident ID: {incident_data['incident_id']}")
        print(f"   MongoDB ID: {result.inserted_id}")
        print(f"   Title: {incident_data['title']}")
        print(f"   Severity: {incident_data['severity']}")
        print(f"   Status: {incident_data['status']}")
        print(f"\n📋 This incident includes:")
        print(f"   • Impact Assessment")
        print(f"   • Business Impact: {incident_data['business_impact']}")
        print(f"   • Root Cause Analysis")
        print(f"   • {len(incident_data['mitigation_steps'])} Mitigation Steps")
        print(f"   • {len(incident_data['next_steps'])} Next Steps")
        print(f"   • {len(incident_data['linked_assets'])} Linked Assets")
        print(f"   • {len(incident_data['references'])} References")
        print(f"   • Assignment: {incident_data['assigned_team']}")
        
        return incident_data['incident_id']
        
    except Exception as e:
        print(f"❌ Error creating sample incident: {str(e)}")
        return None

if __name__ == "__main__":
    print("🚀 Creating comprehensive sample incident...")
    incident_id = create_sample_incident()
    if incident_id:
        print(f"\n🎉 Done! You can now view this incident in the UI: {incident_id}")
    else:
        print("\n💥 Failed to create sample incident")
