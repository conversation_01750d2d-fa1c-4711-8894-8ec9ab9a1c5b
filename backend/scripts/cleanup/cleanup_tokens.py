#!/usr/bin/env python3
"""
Script de nettoyage des tokens de confirmation d'email expirés.

Ce script supprime :
1. Les tokens utilisés qui ont plus de 24 heures
2. Les tokens expirés (plus de 10 minutes depuis leur création)

Usage:
    python3 cleanup_tokens.py [--dry-run] [--verbose]
"""

import argparse
import sys
from datetime import datetime, timezone, timedelta
from flask_jwt_extended import decode_token
from jwt.exceptions import ExpiredSignatureError, InvalidTokenError
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from app import create_app
from app.extensions import mongo


def parse_arguments():
    """Parse les arguments de ligne de commande."""
    parser = argparse.ArgumentParser(description="Nettoie les tokens de confirmation expirés")
    parser.add_argument(
        "--dry-run", 
        action="store_true", 
        help="Affiche ce qui serait supprimé sans rien supprimer"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true", 
        help="Affichage détaillé"
    )
    parser.add_argument(
        "--max-age-hours",
        type=int,
        default=24,
        help="Âge maximum des tokens utilisés en heures (défaut: 24)"
    )
    return parser.parse_args()


def cleanup_used_tokens(max_age_hours=24, dry_run=False, verbose=False):
    """
    Supprime les tokens utilisés qui sont trop anciens.
    
    Args:
        max_age_hours: Âge maximum en heures pour garder les tokens utilisés
        dry_run: Si True, n'effectue pas la suppression
        verbose: Si True, affiche les détails
    """
    cutoff_time = datetime.now(timezone.utc) - timedelta(hours=max_age_hours)
    
    # Trouver les tokens utilisés trop anciens
    old_tokens = list(mongo.db.used_tokens.find({
        "used_at": {"$lt": cutoff_time}
    }))
    
    if verbose:
        print(f"🔍 Recherche des tokens utilisés avant {cutoff_time}")
        print(f"📊 Trouvé {len(old_tokens)} tokens utilisés anciens")
        
        for token_doc in old_tokens:
            used_at = token_doc.get("used_at", "Unknown")
            email = token_doc.get("email", "Unknown")
            print(f"   - {email}: utilisé le {used_at}")
    
    if old_tokens:
        if dry_run:
            print(f"🧪 [DRY RUN] Supprimerait {len(old_tokens)} tokens utilisés anciens")
        else:
            result = mongo.db.used_tokens.delete_many({
                "used_at": {"$lt": cutoff_time}
            })
            print(f"🗑️  Supprimé {result.deleted_count} tokens utilisés anciens")
    else:
        print("✅ Aucun token utilisé ancien à supprimer")
    
    return len(old_tokens)


def cleanup_expired_tokens(dry_run=False, verbose=False):
    """
    Supprime les tokens qui sont techniquement expirés (même s'ils n'ont pas été utilisés).
    
    Args:
        dry_run: Si True, n'effectue pas la suppression
        verbose: Si True, affiche les détails
    """
    # Récupérer tous les tokens utilisés pour vérifier leur validité
    all_tokens = list(mongo.db.used_tokens.find({}))
    expired_tokens = []
    
    if verbose:
        print(f"🔍 Vérification de l'expiration de {len(all_tokens)} tokens")
    
    for token_doc in all_tokens:
        token = token_doc.get("token")
        if not token:
            continue
            
        try:
            # Essayer de décoder le token pour vérifier s'il est expiré
            decode_token(token)
            if verbose:
                print(f"   ✅ Token valide pour {token_doc.get('email', 'Unknown')}")
        except ExpiredSignatureError:
            expired_tokens.append(token_doc)
            if verbose:
                print(f"   ⏰ Token expiré pour {token_doc.get('email', 'Unknown')}")
        except InvalidTokenError:
            expired_tokens.append(token_doc)
            if verbose:
                print(f"   ❌ Token invalide pour {token_doc.get('email', 'Unknown')}")
    
    if expired_tokens:
        if dry_run:
            print(f"🧪 [DRY RUN] Supprimerait {len(expired_tokens)} tokens expirés")
        else:
            # Supprimer les tokens expirés
            token_ids = [doc["_id"] for doc in expired_tokens]
            result = mongo.db.used_tokens.delete_many({
                "_id": {"$in": token_ids}
            })
            print(f"⏰ Supprimé {result.deleted_count} tokens expirés")
    else:
        print("✅ Aucun token expiré à supprimer")
    
    return len(expired_tokens)


def get_collection_stats():
    """Affiche les statistiques de la collection used_tokens."""
    total_count = mongo.db.used_tokens.count_documents({})
    
    # Compter par âge
    now = datetime.now(timezone.utc)
    last_hour = now - timedelta(hours=1)
    last_day = now - timedelta(days=1)
    last_week = now - timedelta(weeks=1)
    
    recent_count = mongo.db.used_tokens.count_documents({
        "used_at": {"$gte": last_hour}
    })
    
    day_count = mongo.db.used_tokens.count_documents({
        "used_at": {"$gte": last_day}
    })
    
    week_count = mongo.db.used_tokens.count_documents({
        "used_at": {"$gte": last_week}
    })
    
    print("📊 Statistiques de la collection used_tokens:")
    print(f"   Total: {total_count} tokens")
    print(f"   Dernière heure: {recent_count} tokens")
    print(f"   Dernier jour: {day_count} tokens")
    print(f"   Dernière semaine: {week_count} tokens")
    print(f"   Plus anciens: {total_count - week_count} tokens")


def main():
    """Fonction principale."""
    args = parse_arguments()
    
    # Créer l'application Flask pour accéder à la base de données
    app = create_app()
    
    with app.app_context():
        print("🧹 Démarrage du nettoyage des tokens de confirmation")
        print("=" * 50)
        
        if args.dry_run:
            print("🧪 MODE DRY RUN - Aucune suppression ne sera effectuée")
            print()
        
        # Afficher les statistiques avant nettoyage
        get_collection_stats()
        print()
        
        # Nettoyer les tokens utilisés anciens
        old_count = cleanup_used_tokens(
            max_age_hours=args.max_age_hours,
            dry_run=args.dry_run,
            verbose=args.verbose
        )
        
        print()
        
        # Nettoyer les tokens expirés
        expired_count = cleanup_expired_tokens(
            dry_run=args.dry_run,
            verbose=args.verbose
        )
        
        print()
        print("=" * 50)
        
        if not args.dry_run:
            # Afficher les statistiques après nettoyage
            print("📊 Statistiques après nettoyage:")
            get_collection_stats()
            print()
        
        total_cleaned = old_count + expired_count
        if total_cleaned > 0:
            if args.dry_run:
                print(f"🧪 [DRY RUN] {total_cleaned} tokens seraient supprimés")
            else:
                print(f"✅ Nettoyage terminé: {total_cleaned} tokens supprimés")
        else:
            print("✅ Aucun nettoyage nécessaire")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️  Nettoyage interrompu par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Erreur lors du nettoyage: {str(e)}")
        sys.exit(1)
