# PICA API Testing Guide - Phishing Detection & Robust Scan Management

## 🚀 NEW: Robust Scan Management Solution

### Problem Solved:
- ✅ Scans now run independently in background threads
- ✅ Logs are preserved even during page refreshes
- ✅ Scan processes survive API calls and browser navigation
- ✅ Real-time log streaming without interruption

---

# PICA Phishing Detection API - Testing Guide

## Base URL
```
http://localhost:5000/api/phishing
```

## Authentication
All endpoints (except test endpoints) require JWT authentication.

### Headers Required:
```
Authorization: Bearer <your_jwt_token>
Content-Type: application/json
```

### Getting a JWT Token:
First, login to get a token:
```
POST http://localhost:5000/auth/login
Content-Type: application/json

{
  "email": "your_email_or_username",
  "password": "your_password"
}
```

**Note**: The login endpoint accepts either email or username. Check your user credentials.

### Quick Test Token (if you have admin access):
If you have admin credentials, try:
```
POST http://localhost:5000/auth/login
Content-Type: application/json

{
  "email": "admin",
  "password": "admin123"
}
```

## Test Endpoints (No Authentication Required)

### 1. Test Basic Route
```
GET http://localhost:5000/api/phishing/test
```
**Expected Response:**
```json
{
  "message": "Phishing route is working",
  "status": "ok"
}
```

### 2. Test JWT Route
```
GET http://localhost:5000/api/phishing/test-jwt
Authorization: Bearer <your_jwt_token>
```
**Expected Response:**
```json
{
  "message": "JWT is working in phishing route",
  "user_id": "your_username",
  "status": "ok"
}
```

## Main Phishing Detection Endpoints

### 3. Get Phishing Service Status
```
GET http://localhost:5000/api/phishing/
Authorization: Bearer <your_jwt_token>
```
**Expected Response:**
```json
{
  "status": "active",
  "service": "Phishing Detection Service",
  "version": "1.0",
  "statistics": {
    "total_analyses": 0,
    "user_analyses": 0,
    "active_analyses": 0
  },
  "recent_analyses": [],
  "capabilities": [
    "URL Analysis",
    "Domain Reputation Check",
    "Content Analysis",
    "Brand Impersonation Detection",
    "Homoglyph Attack Detection",
    "Real-time Scanning"
  ]
}
```

### 4. Quick Phishing Check (Synchronous)
```
POST http://localhost:5000/api/phishing/quick-check
Authorization: Bearer <your_jwt_token>
Content-Type: application/json

{
  "url": "https://example.com"
}
```
**Expected Response:**
```json
{
  "message": "Quick analysis completed",
  "analysis_id": "uuid-string",
  "db_id": "mongodb-id",
  "url": "https://example.com",
  "result": {
    "url": "https://example.com",
    "domain": "example.com",
    "checks": [...],
    "risk_score": 15,
    "likelihood": "Low Risk",
    "is_phishing": false
  }
}
```

### 5. Start Full Analysis (Asynchronous)
```
POST http://localhost:5000/api/phishing/analyze
Authorization: Bearer <your_jwt_token>
Content-Type: application/json

{
  "url": "https://example.com"
}
```
**Expected Response:**
```json
{
  "message": "Analysis started successfully",
  "analysis_id": "uuid-string",
  "status": "running",
  "url": "https://example.com"
}
```

### 6. Get Analysis Status
```
GET http://localhost:5000/api/phishing/status/<analysis_id>
Authorization: Bearer <your_jwt_token>
```
**Expected Response:**
```json
{
  "analysis_id": "uuid-string",
  "status": "completed",
  "progress": 100,
  "url": "https://example.com",
  "started_at": "2025-06-24T19:30:00.000Z",
  "completed_at": "2025-06-24T19:30:05.000Z",
  "result": {...}
}
```

### 7. Get Analysis History
```
GET http://localhost:5000/api/phishing/history?page=1&limit=20
Authorization: Bearer <your_jwt_token>
```
**Expected Response:**
```json
{
  "analyses": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 0,
    "pages": 0
  }
}
```

### 8. Get Active Analyses
```
GET http://localhost:5000/api/phishing/active-analyses
Authorization: Bearer <your_jwt_token>
```
**Expected Response:**
```json
{
  "active_analyses": [],
  "count": 0
}
```

## Suspicious Links Management

### 9. Get Suspicious Links
```
GET http://localhost:5000/api/phishing/suspicious-links?page=1&limit=20&min_score=30
Authorization: Bearer <your_jwt_token>
```
**Expected Response:**
```json
{
  "suspicious_links": [...],
  "statistics": {
    "total_suspicious": 0,
    "active": 0,
    "verified": 0,
    "false_positive": 0,
    "high_risk": 0,
    "medium_risk": 0
  },
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 0,
    "pages": 0
  }
}
```

### 10. Get Suspicious Link Details
```
GET http://localhost:5000/api/phishing/suspicious-links/<link_id>
Authorization: Bearer <your_jwt_token>
```

### 11. Verify Suspicious Link (Admin Only)
```
POST http://localhost:5000/api/phishing/suspicious-links/<link_id>/verify
Authorization: Bearer <your_jwt_token>
Content-Type: application/json

{
  "status": "verified",
  "notes": "Confirmed phishing site"
}
```

### 12. Delete Suspicious Link (Admin Only)
```
DELETE http://localhost:5000/api/phishing/suspicious-links/<link_id>
Authorization: Bearer <your_jwt_token>
```

## Admin Endpoints

### 13. Get All Analyses (Admin Only)
```
GET http://localhost:5000/api/phishing/admin/all-analyses?page=1&limit=50
Authorization: Bearer <your_jwt_token>
```

### 14. Delete Analysis (Admin Only)
```
DELETE http://localhost:5000/api/phishing/admin/delete/<analysis_id>
Authorization: Bearer <your_jwt_token>
```

### 15. Get Phishing Statistics (Admin Only)
```
GET http://localhost:5000/api/phishing/admin/stats
Authorization: Bearer <your_jwt_token>
```

### 16. Get Suspicious Links Statistics (Admin Only)
```
GET http://localhost:5000/api/phishing/suspicious-links/stats
Authorization: Bearer <your_jwt_token>
```

### 17. Export Suspicious Links (Admin Only)
```
GET http://localhost:5000/api/phishing/suspicious-links/export?min_score=30
Authorization: Bearer <your_jwt_token>
```

## Error Troubleshooting

### Current Issue: 422 UNPROCESSABLE ENTITY

The 422 error indicates JWT token validation is failing. Here's how to debug:

#### Step 1: Test Basic Route (Should Work)
```
GET http://localhost:5000/api/phishing/test
```
If this fails, the phishing routes are not registered correctly.

#### Step 2: Get a Valid JWT Token
```
POST http://localhost:5000/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "your_password"
}
```
Copy the `token` from the response.

#### Step 3: Test JWT Route
```
GET http://localhost:5000/api/phishing/test-jwt
Authorization: Bearer <paste_token_here>
```
If this fails with 422, the JWT token is invalid or expired.

#### Step 4: Check Token Format
Make sure the Authorization header is exactly:
```
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```
(No extra spaces, correct Bearer prefix)

#### Step 5: Check Backend Logs
Look for debug output in the backend console:
- JWT validation errors
- Token decode errors
- Missing claims

### Common 422 Causes:
1. **Invalid JWT Token**: Token is malformed or corrupted
2. **Expired Token**: Token has exceeded its expiration time
3. **Missing Authorization Header**: Header not included in request
4. **Wrong Token Format**: Missing "Bearer " prefix or extra spaces
5. **JWT Secret Mismatch**: Backend JWT secret doesn't match token signature
6. **Missing Claims**: Token doesn't contain required claims (username, role, etc.)

### Debug Commands:
```bash
# Check if backend is running
curl http://localhost:5000/api/phishing/test

# Test with verbose output
curl -v -H "Authorization: Bearer YOUR_TOKEN" http://localhost:5000/api/phishing/test-jwt

# Check backend logs for detailed error messages
```

## Error Resolution

### ✅ ISSUE RESOLVED

**Root Cause**: Frontend token retrieval mismatch
- **Problem**: Phishing service was looking for cookie named `'access_token'`
- **Solution**: Updated to use `getToken()` from auth utils (looks for `'token'` cookie)
- **Fix Applied**: Changed `Cookies.get('access_token')` to `getToken()` in phishingService.ts

### Previous Error Details:
- **Status Code**: 422 UNPROCESSABLE ENTITY
- **Endpoints Affected**: All JWT-protected phishing endpoints
- **Frontend Error**: `Error: HTTP 422: UNPROCESSABLE ENTITY`

### Error Location:
The error occurred because the frontend was sending `Bearer undefined` instead of a valid JWT token, causing Flask-JWT-Extended's `default_invalid_token_callback` to return 422.

### Possible Root Causes:

#### 1. JWT Token Issues:
- Token is malformed or corrupted
- Token has expired
- Token signature doesn't match JWT secret
- Token missing required claims

#### 2. Blueprint Registration Issues:
- Nested blueprint structure causing conflicts
- Flask-Security interfering with JWT validation
- Route registration order problems

#### 3. Configuration Issues:
- JWT_SECRET_KEY mismatch
- Flask-Security configuration conflicts
- CORS header issues

### Debugging Steps:

#### Step 1: Verify Route Registration
```bash
curl http://localhost:5000/api/phishing/test
# Should return: {"message": "Phishing route is working", "status": "ok"}
```

#### Step 2: Get Fresh Token
```bash
curl -X POST http://localhost:5000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "admin", "password": "admin123"}'
```

#### Step 3: Test JWT Validation
```bash
curl -H "Authorization: Bearer <TOKEN>" \
  http://localhost:5000/api/phishing/test-jwt
```

#### Step 4: Compare with Working Endpoints
Test a working JWT endpoint (like scan endpoints):
```bash
curl -H "Authorization: Bearer <TOKEN>" \
  http://localhost:5000/scan/pentesting/
```

If scan endpoints work but phishing endpoints don't, the issue is specific to the phishing blueprint registration.

### Immediate Fix Suggestions:

1. **Simplify Blueprint Registration**: Register phishing_bp directly instead of nested structure
2. **Check JWT Secret**: Ensure JWT_SECRET_KEY is consistent
3. **Add Debug Logging**: Enable JWT debug logging to see exact error
4. **Test Token Claims**: Verify token contains required claims (username, role, etc.)

### Backend Debug Output:
Check the backend console for these debug messages:
- `DEBUG: User ID from JWT: <username>`
- `DEBUG: Request data: <request_body>`
- `DEBUG: Exception in quick_phishing_check: <error>`

---

# ROBUST SCAN MANAGEMENT API

## Problem & Solution

### The Problem:
When starting a pentesting scan (web, network, vulnerability), the scan logs were lost when:
- User refreshes the page
- User navigates to other pages
- Other API calls are made
- Browser is closed and reopened

### The Solution: ScanManager
A robust background scan management system that:
- Runs scans in independent background threads (not tied to HTTP requests)
- Stores all logs persistently in MongoDB
- Maintains scan state across page refreshes
- Provides real-time log streaming
- Allows scan monitoring from any session

## Robust Scan Endpoints

### Base URL for Robust Scans:
```
http://localhost:5000/scan/pentesting
```

### 1. Start Robust Web Scan
```
POST http://localhost:5000/scan/pentesting/scan/web-robust
Authorization: Bearer <your_jwt_token>
Content-Type: application/json

{
  "target_url": "https://example.com",
  "scan_type": "basic"
}
```

**Scan Types:**
- `basic`: Quick scan with basic tools
- `aggressive`: Deep scan with intensive settings
- `stealth`: Slow, discrete scan
- `comprehensive`: All tools with maximum coverage

**Response:**
```json
{
  "message": "Basic web scan started successfully",
  "scan_id": "uuid-string",
  "target": "https://example.com",
  "scan_type": "basic",
  "tools": ["nikto", "sqlmap", "dirb", "gobuster", "zap"],
  "status": "running",
  "note": "This scan runs in background and survives page refreshes"
}
```

### 2. Get Robust Scan Status
```
GET http://localhost:5000/scan/pentesting/scan/robust-status/<scan_id>
Authorization: Bearer <your_jwt_token>
```

**Response:**
```json
{
  "scan_id": "uuid-string",
  "status": "running",
  "current_tool": "nikto",
  "progress": 45,
  "start_time": "2025-06-24T19:30:00.000Z",
  "is_active": true
}
```

### 3. Get Robust Scan Logs (Real-time)
```
GET http://localhost:5000/scan/pentesting/scan/robust-logs/<scan_id>?limit=100&level=info&tool=nikto
Authorization: Bearer <your_jwt_token>
```

**Query Parameters:**
- `limit`: Number of logs to retrieve (default: 100)
- `level`: Filter by log level (info, debug, error)
- `tool`: Filter by specific tool (nikto, sqlmap, etc.)

**Response:**
```json
{
  "scan_id": "uuid-string",
  "logs": [
    {
      "timestamp": "2025-06-24T19:30:15.000Z",
      "level": "info",
      "event_type": "tool_progress",
      "message": "📊 TOOL PROGRESS - NIKTO: 50% - Scanning in progress...",
      "data": {
        "tool": "nikto",
        "progress": 50
      }
    }
  ],
  "total_logs": 25,
  "filters": {
    "limit": 100,
    "level": "info",
    "tool": "nikto"
  }
}
```

### 4. Stop Robust Scan
```
POST http://localhost:5000/scan/pentesting/scan/robust-stop/<scan_id>
Authorization: Bearer <your_jwt_token>
```

**Response:**
```json
{
  "message": "Scan stopped successfully",
  "scan_id": "uuid-string"
}
```

### 5. Get All Active Robust Scans
```
GET http://localhost:5000/scan/pentesting/scan/robust-active
Authorization: Bearer <your_jwt_token>
```

**Response:**
```json
{
  "active_scans": [
    {
      "scan_id": "uuid-string",
      "status": "running",
      "current_tool": "sqlmap",
      "progress": 75,
      "start_time": "2025-06-24T19:30:00.000Z",
      "config": {
        "category": "web",
        "scan_type": "basic",
        "target": "https://example.com"
      }
    }
  ],
  "count": 1
}
```

## Testing the Robust Solution

### Test Scenario 1: Page Refresh Survival
1. Start a web scan using the robust endpoint
2. Note the `scan_id` from the response
3. Refresh the browser page multiple times
4. Call the status endpoint - scan should still be running
5. Call the logs endpoint - all logs should be preserved

### Test Scenario 2: Navigation Survival
1. Start a robust scan
2. Navigate to different pages in the application
3. Make other API calls (phishing, user management, etc.)
4. Return to scan monitoring - scan continues running
5. Logs are still accessible and updating

### Test Scenario 3: Real-time Log Monitoring
1. Start a robust scan
2. Continuously call the logs endpoint every 2-3 seconds
3. Observe real-time log updates as tools execute
4. Logs show detailed progress of each tool (nikto, sqlmap, etc.)

### Test Scenario 4: Multiple Concurrent Scans
1. Start multiple robust scans with different targets
2. Monitor all active scans using the active scans endpoint
3. Each scan runs independently
4. Logs are properly isolated per scan

## Key Benefits

### ✅ Persistence
- Scans survive browser refreshes
- Logs are never lost
- State maintained across sessions

### ✅ Independence
- Scans run in background threads
- Not tied to HTTP request lifecycle
- Continue even if user closes browser

### ✅ Real-time Monitoring
- Live log streaming
- Progress tracking
- Tool-specific filtering

### ✅ Scalability
- Multiple concurrent scans
- Efficient resource management
- Automatic cleanup of old scans

### ✅ Reliability
- Error handling and recovery
- Process isolation
- Database persistence

## Migration Guide

### For Frontend Developers:
Replace existing scan endpoints with robust versions:
```javascript
// Old (loses logs on refresh)
POST /scan/pentesting/scan/web

// New (survives refresh)
POST /scan/pentesting/scan/web-robust
```

### For Testing:
Use the robust endpoints for all new scan testing to ensure logs are preserved during development and testing cycles.
