# API de Gestion des Liens Suspects - PICA

## Vue d'ensemble

Cette API permet de gérer automatiquement les liens suspects détectés par le système de détection de phishing. Tous les liens avec un score de risque supérieur à 30% sont automatiquement sauvegardés dans une collection dédiée pour un suivi et une analyse ultérieurs.

## Collection MongoDB

**Nom de la collection :** `suspicious_links`

### Structure des documents

```json
{
  "_id": "ObjectId",
  "url": "string",
  "domain": "string",
  "first_detected": "datetime",
  "last_detected": "datetime",
  "detection_count": "number",
  "risk_score": "number",
  "max_risk_score": "number",
  "likelihood": "string",
  "max_likelihood": "string",
  "latest_risk_score": "number",
  "latest_likelihood": "string",
  "is_phishing": "boolean",
  "analysis_id": "string",
  "latest_analysis_id": "string",
  "user_id": "string",
  "latest_user_id": "string",
  "status": "string",
  "verified_by": "string",
  "verification_date": "datetime",
  "notes": "string",
  "failed_checks": "array",
  "warning_checks": "array",
  "total_checks": "number",
  "failed_checks_count": "number",
  "warning_checks_count": "number"
}
```

### Statuts possibles

- `active` : Lien suspect non vérifié
- `verified` : Lien confirmé comme phishing
- `false_positive` : Lien marqué comme faux positif

## Endpoints API

### 1. Lister les liens suspects

```http
GET /api/phishing/suspicious-links
```

**Paramètres de requête :**
- `page` (int, optionnel) : Numéro de page (défaut: 1)
- `limit` (int, optionnel) : Nombre d'éléments par page (défaut: 20, max: 100)
- `status` (string, optionnel) : Filtrer par statut
- `min_score` (int, optionnel) : Score minimum de risque (défaut: 30)

**Réponse :**
```json
{
  "suspicious_links": [...],
  "statistics": {
    "total_suspicious": 150,
    "active": 120,
    "verified": 25,
    "false_positive": 5,
    "high_risk": 80,
    "medium_risk": 70
  },
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "pages": 8
  },
  "filters": {
    "status": null,
    "min_score": 30
  }
}
```

### 2. Détails d'un lien suspect

```http
GET /api/phishing/suspicious-links/{link_id}
```

**Réponse :**
```json
{
  "suspicious_link": {
    "_id": "...",
    "url": "http://phishing-site.com",
    "risk_score": 85,
    "status": "active",
    ...
  },
  "related_analyses": [...]
}
```

### 3. Vérifier un lien suspect (Admin)

```http
POST /api/phishing/suspicious-links/{link_id}/verify
```

**Corps de la requête :**
```json
{
  "status": "verified",
  "notes": "Confirmé comme site de phishing ciblant les banques"
}
```

**Réponse :**
```json
{
  "message": "Suspicious link verified successfully",
  "link_id": "...",
  "status": "verified",
  "verified_by": "<EMAIL>"
}
```

### 4. Supprimer un lien suspect (Admin)

```http
DELETE /api/phishing/suspicious-links/{link_id}
```

### 5. Statistiques détaillées (Admin)

```http
GET /api/phishing/suspicious-links/stats
```

**Réponse :**
```json
{
  "overview": {
    "total_suspicious": 150,
    "active_links": 120,
    "verified_phishing": 25,
    "false_positives": 5,
    "verification_rate": 20.0
  },
  "risk_distribution": {
    "high_risk": 80,
    "medium_risk": 70,
    "low_risk": 0
  },
  "top_domains": [
    {
      "_id": "phishing-site.tk",
      "count": 15,
      "avg_score": 78.5
    }
  ],
  "most_detected_links": [...],
  "timeline_30_days": [...],
  "top_detectors": [...]
}
```

### 6. Exporter les liens suspects (Admin)

```http
GET /api/phishing/suspicious-links/export
```

**Paramètres de requête :**
- `status` (string, optionnel) : Filtrer par statut
- `min_score` (int, optionnel) : Score minimum de risque
- `format` (string, optionnel) : Format d'export (json)

## Fonctionnement automatique

### Détection automatique

Lorsqu'une analyse de phishing est effectuée :

1. **Analyse de l'URL** : Le service de phishing analyse l'URL
2. **Calcul du score** : Un score de risque est calculé (0-100%)
3. **Sauvegarde conditionnelle** : Si le score > 30%, le lien est automatiquement ajouté à la collection `suspicious_links`
4. **Gestion des doublons** : Si l'URL existe déjà, les statistiques sont mises à jour

### Mise à jour des liens existants

Pour les liens déjà présents :
- Incrémentation du compteur de détections
- Mise à jour de la date de dernière détection
- Mise à jour du score maximum si nécessaire
- Conservation de l'historique des analyses

## Index MongoDB

Les index suivants sont créés pour optimiser les performances :

```javascript
// Index unique sur l'URL
db.suspicious_links.createIndex({"url": 1}, {unique: true})

// Index sur le score de risque (descendant)
db.suspicious_links.createIndex({"risk_score": -1})

// Index sur le statut
db.suspicious_links.createIndex({"status": 1})

// Index sur les dates
db.suspicious_links.createIndex({"last_detected": -1})
db.suspicious_links.createIndex({"first_detected": -1})

// Index composé pour les requêtes fréquentes
db.suspicious_links.createIndex({
  "status": 1,
  "risk_score": -1,
  "last_detected": -1
})
```

## Initialisation

Pour initialiser la collection avec les index et des données d'exemple :

```bash
cd backend
python app/utils/init_suspicious_links_collection.py
```

## Tests

Pour tester la fonctionnalité :

```bash
cd backend
python test_suspicious_links.py
```

## Sécurité

- **Authentification requise** : Toutes les routes nécessitent un token JWT valide
- **Permissions admin** : Les routes de vérification, suppression et statistiques détaillées nécessitent des privilèges administrateur
- **Validation des données** : Tous les paramètres d'entrée sont validés
- **Gestion d'erreurs** : Gestion robuste des erreurs avec messages explicites

## Cas d'usage

### Pour les utilisateurs normaux
- Consulter les liens suspects qu'ils ont détectés
- Voir les détails des analyses de phishing

### Pour les administrateurs
- Surveiller tous les liens suspects détectés
- Vérifier et classifier les liens (phishing confirmé vs faux positif)
- Exporter les données pour analyse externe
- Consulter les statistiques et tendances

### Pour l'analyse de sécurité
- Identifier les domaines malveillants récurrents
- Analyser les patterns d'attaque
- Générer des rapports de sécurité
- Alimenter les systèmes de blocage/filtrage
