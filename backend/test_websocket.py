#!/usr/bin/env python3
"""
Test simple pour vérifier le WebSocket PICA
"""

from app.utils.websocket import notify_new_incident, notify_new_ticket, notify_critical_alert

def test_notifications():
    """Test des fonctions de notification"""
    
    print("🧪 Test des notifications WebSocket PICA")
    print("=" * 50)
    
    # Test notification incident
    incident_data = {
        'incident_id': 'INC-001',
        'title': 'Test Security Incident',
        'severity': 'high'
    }
    
    print("📡 Test notification incident...")
    result = notify_new_incident(incident_data)
    print(f"   Résultat: {'✅ Envoyé' if result else '❌ Aucun admin connecté'}")
    
    # Test notification ticket
    ticket_data = {
        'ticket_number': 'TKT-001',
        'short_description': 'Test Support Ticket'
    }
    
    print("📡 Test notification ticket...")
    result = notify_new_ticket(ticket_data)
    print(f"   Résultat: {'✅ Envoyé' if result else '❌ Aucun admin connecté'}")
    
    # Test alerte critique
    alert_data = {
        'message': '🔴 Test Critical Alert'
    }
    
    print("📡 Test alerte critique...")
    result = notify_critical_alert(alert_data)
    print(f"   Résultat: {'✅ Envoyé' if result else '❌ Aucun admin connecté'}")
    
    print("\n✅ Tests terminés!")
    print("💡 Pour recevoir les notifications, connectez-vous en tant qu'admin via WebSocket")

if __name__ == "__main__":
    test_notifications()
