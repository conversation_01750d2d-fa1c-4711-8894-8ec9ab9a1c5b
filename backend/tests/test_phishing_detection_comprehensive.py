"""
Comprehensive Test Suite for PICA Phishing Detection System
===========================================================

This test suite validates the complete phishing detection process with all
priority-based indicators, weights, and thresholds as specified.

Test Categories:
- High Priority Indicators (25%, 20%, 20%, 20%, 20%)
- Medium Priority Indicators (12%, 10%, 10%, 10%, 8%)
- Low Priority Indicators (5%, 5%, 5%, 5%)
- Risk Classification Thresholds (0-30%, 31-50%, 51-100%)
- Example Scenarios from Specification
"""

import unittest
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.services.phishing_service import analyze_url

class TestPhishingDetectionComprehensive(unittest.TestCase):
    """Comprehensive test suite for phishing detection system."""

    def setUp(self):
        """Set up test fixtures."""
        self.maxDiff = None

    def test_example_scenario_1_ip_url(self):
        """
        Test Example 1: http://***********/login.php?id=abc123
        Expected: IP in URL (25%) + HTTPS Missing (5%) + Special Characters (10%) = 40%
        Classification: PHISHING (Medium Risk)
        """
        url = "http://***********/login.php?id=abc123"
        result = analyze_url(url)
        
        # Verify basic result structure
        self.assertIsInstance(result, dict)
        self.assertEqual(result['url'], url)
        self.assertTrue(result['is_phishing'])
        self.assertEqual(result['likelihood'], 'Medium Risk')
        
        # Verify risk score is around 40% (allowing for minor variations)
        self.assertGreaterEqual(result['risk_score'], 35)
        self.assertLessEqual(result['risk_score'], 45)
        
        # Verify specific indicators triggered
        triggered_names = [indicator['name'] for indicator in result['scoring_details']['triggered_indicators']]
        self.assertIn('IP in URL', triggered_names)
        self.assertIn('HTTPS Missing', triggered_names)

    def test_example_scenario_2_suspicious_tld(self):
        """
        Test Example 2: https://secure-login.bankaccount.tk/reset?uid=********
        Expected: Suspicious TLD (20%) + Domain Age (20%) + Excessive Subdomains (10%) = 50%
        Classification: PHISHING (Medium Risk)
        """
        url = "https://secure-login.bankaccount.tk/reset?uid=********"
        result = analyze_url(url)
        
        # Verify basic result structure
        self.assertIsInstance(result, dict)
        self.assertEqual(result['url'], url)
        self.assertTrue(result['is_phishing'])
        self.assertEqual(result['likelihood'], 'Medium Risk')
        
        # Verify risk score is around 50% (allowing for variations due to domain age check)
        self.assertGreaterEqual(result['risk_score'], 30)  # At minimum suspicious TLD + subdomains
        
        # Verify specific indicators triggered
        triggered_names = [indicator['name'] for indicator in result['scoring_details']['triggered_indicators']]
        self.assertIn('Suspicious TLD', triggered_names)

    def test_high_priority_indicators(self):
        """Test all high priority indicators individually."""
        
        # Test IP in URL (25%)
        result_ip = analyze_url("http://***********/test")
        triggered_ip = [i for i in result_ip['scoring_details']['triggered_indicators'] if 'IP' in i['name']]
        self.assertTrue(len(triggered_ip) > 0)
        self.assertEqual(triggered_ip[0]['weight'], 25)
        self.assertEqual(triggered_ip[0]['priority'], 'High')

    def test_medium_priority_indicators(self):
        """Test medium priority indicators."""
        
        # Test long URL (10%)
        long_url = "https://example.com/" + "a" * 100  # Create a long URL
        result_long = analyze_url(long_url)
        
        # Should trigger URL length check
        triggered_length = [i for i in result_long['scoring_details']['triggered_indicators'] if 'Length' in i['name']]
        if len(triggered_length) > 0:
            self.assertEqual(triggered_length[0]['weight'], 10)
            self.assertEqual(triggered_length[0]['priority'], 'Medium')

    def test_low_priority_indicators(self):
        """Test low priority indicators."""
        
        # Test HTTPS missing (5%)
        result_http = analyze_url("http://example.com/test")
        triggered_https = [i for i in result_http['scoring_details']['triggered_indicators'] if 'HTTPS' in i['name']]
        if len(triggered_https) > 0:
            self.assertEqual(triggered_https[0]['weight'], 5)
            self.assertEqual(triggered_https[0]['priority'], 'Low')

    def test_risk_classification_thresholds(self):
        """Test risk classification thresholds."""
        
        # Test legitimate URL (should be Low Risk, Not Phishing)
        result_safe = analyze_url("https://www.google.com")
        self.assertFalse(result_safe['is_phishing'])
        self.assertEqual(result_safe['likelihood'], 'Low Risk')
        self.assertLessEqual(result_safe['risk_score'], 30)

    def test_scoring_details_structure(self):
        """Test that scoring details are properly structured."""
        result = analyze_url("http://***********/test")
        
        # Verify scoring_details structure
        self.assertIn('scoring_details', result)
        scoring = result['scoring_details']
        
        self.assertIn('total_suspicion_score', scoring)
        self.assertIn('triggered_indicators_count', scoring)
        self.assertIn('triggered_indicators', scoring)
        self.assertIn('high_priority_triggered', scoring)
        self.assertIn('medium_priority_triggered', scoring)
        self.assertIn('low_priority_triggered', scoring)
        
        # Verify each triggered indicator has required fields
        for indicator in scoring['triggered_indicators']:
            self.assertIn('name', indicator)
            self.assertIn('weight', indicator)
            self.assertIn('priority', indicator)
            self.assertIn('description', indicator)
            self.assertIn('contribution', indicator)

    def test_check_statistics_structure(self):
        """Test that check statistics are properly structured."""
        result = analyze_url("http://***********/test")
        
        # Verify check_statistics structure
        self.assertIn('check_statistics', result)
        stats = result['check_statistics']
        
        self.assertIn('total_checks', stats)
        self.assertIn('failed_checks', stats)
        self.assertIn('warning_checks', stats)
        self.assertIn('passed_checks', stats)
        self.assertIn('high_priority_failed', stats)
        self.assertIn('medium_priority_failed', stats)
        self.assertIn('low_priority_failed', stats)

    def test_classification_summary_structure(self):
        """Test that classification summary is properly structured."""
        result = analyze_url("https://www.google.com")
        
        # Verify classification_summary structure
        self.assertIn('classification_summary', result)
        summary = result['classification_summary']
        
        self.assertIn('threshold_used', summary)
        self.assertIn('decision_rule', summary)
        self.assertIn('risk_levels', summary)
        
        self.assertEqual(summary['threshold_used'], 30)
        self.assertEqual(summary['decision_rule'], 'Score > 30% = Phishing')

    def test_phishing_threshold_30_percent(self):
        """Test that the 30% threshold correctly classifies phishing."""
        
        # Test URL that should be just above 30%
        result_above = analyze_url("http://***********/test")  # IP (25%) + HTTP (5%) = 30%+
        self.assertTrue(result_above['is_phishing'])
        
        # Test legitimate URL that should be below 30%
        result_below = analyze_url("https://www.google.com")
        self.assertFalse(result_below['is_phishing'])

if __name__ == '__main__':
    unittest.main()
