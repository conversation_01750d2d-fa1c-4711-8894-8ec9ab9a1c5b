{"info": {"_postman_id": "12345678-1234-1234-1234-123456789abc", "name": "PICA Pentesting Scans v2.0 - Structure Organisée", "description": "Collection de tests pour la nouvelle structure organisée des scans PICA\n🟦 Network Scan - Nmap + OpenVAS + Metasploit (parallèle)\n🟩 Web Scan - Nikto + SQLMap + Dirb + GoBuster + Zap (parallèle)\n🟪 Vulnerability Scan - OpenVAS + Metasploit (parallèle)\n🟥 Deep Scan - TOUS les outils (séquentiel avec progression)", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "🔐 Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has access_token\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('access_token');", "    pm.environment.set('token', jsonData.access_token);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"admin123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}}]}, {"name": "📊 Status & Info", "item": [{"name": "Get Pentesting Status", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has module info\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('module');", "    pm.expect(jsonData).to.have.property('version');", "    pm.expect(jsonData).to.have.property('categories');", "    pm.expect(jsonData.version).to.eql('2.0.0');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/scan/pentesting/", "host": ["{{baseUrl}}"], "path": ["scan", "pentesting", ""]}}}]}, {"name": "🟦 Network Scans", "item": [{"name": "Network Scan - Basic", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has scan_id\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('scan_id');", "    pm.environment.set('last_scan_id', jsonData.scan_id);", "});", "", "pm.test(\"Scan type is basic\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.scan_type).to.eql('basic');", "});", "", "pm.test(\"Config has network tools\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.config).to.have.property('description');", "    pm.expect(jsonData.config.description).to.include('Ports communs');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"target\": \"scanme.nmap.org\",\n  \"scan_type\": \"basic\",\n  \"ports\": \"22,80,443\"\n}"}, "url": {"raw": "{{baseUrl}}/scan/pentesting/scan/network", "host": ["{{baseUrl}}"], "path": ["scan", "pentesting", "scan", "network"]}}}, {"name": "Network Scan - Aggressive", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"target\": \"scanme.nmap.org\",\n  \"scan_type\": \"aggressive\",\n  \"ports\": \"1-1000\"\n}"}, "url": {"raw": "{{baseUrl}}/scan/pentesting/scan/network", "host": ["{{baseUrl}}"], "path": ["scan", "pentesting", "scan", "network"]}}}, {"name": "Network Scan - Stealth", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"target\": \"scanme.nmap.org\",\n  \"scan_type\": \"stealth\",\n  \"ports\": \"80,443\"\n}"}, "url": {"raw": "{{baseUrl}}/scan/pentesting/scan/network", "host": ["{{baseUrl}}"], "path": ["scan", "pentesting", "scan", "network"]}}}, {"name": "Network Scan - Comprehensive", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"target\": \"scanme.nmap.org\",\n  \"scan_type\": \"comprehensive\",\n  \"ports\": \"1-65535\"\n}"}, "url": {"raw": "{{baseUrl}}/scan/pentesting/scan/network", "host": ["{{baseUrl}}"], "path": ["scan", "pentesting", "scan", "network"]}}}]}, {"name": "🟩 Web Scans", "item": [{"name": "Web Scan - Basic", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has scan_id\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('scan_id');", "});", "", "pm.test(\"Scan type is basic\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.scan_type).to.eql('basic');", "});", "", "pm.test(\"Config has web tools description\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.config.description).to.include('failles connues');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"target\": \"http://testphp.vulnweb.com\",\n  \"scan_type\": \"basic\"\n}"}, "url": {"raw": "{{baseUrl}}/scan/pentesting/scan/web", "host": ["{{baseUrl}}"], "path": ["scan", "pentesting", "scan", "web"]}}}, {"name": "Web Scan - Aggressive", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"target\": \"http://testphp.vulnweb.com\",\n  \"scan_type\": \"aggressive\"\n}"}, "url": {"raw": "{{baseUrl}}/scan/pentesting/scan/web", "host": ["{{baseUrl}}"], "path": ["scan", "pentesting", "scan", "web"]}}}, {"name": "<PERSON> - Stealth", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"target\": \"http://testphp.vulnweb.com\",\n  \"scan_type\": \"stealth\"\n}"}, "url": {"raw": "{{baseUrl}}/scan/pentesting/scan/web", "host": ["{{baseUrl}}"], "path": ["scan", "pentesting", "scan", "web"]}}}, {"name": "Web Scan - Comprehensive", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"target\": \"http://testphp.vulnweb.com\",\n  \"scan_type\": \"comprehensive\"\n}"}, "url": {"raw": "{{baseUrl}}/scan/pentesting/scan/web", "host": ["{{baseUrl}}"], "path": ["scan", "pentesting", "scan", "web"]}}}]}, {"name": "🟪 Vulnerability Scans", "item": [{"name": "Vulnerability Scan - Basic", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has scan_id\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('scan_id');", "});", "", "pm.test(\"Scan type is basic\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.scan_type).to.eql('basic');", "});", "", "pm.test(\"Config has vulnerability description\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.config.description).to.include('Vulnérabilités courantes');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"target\": \"***********\",\n  \"scan_type\": \"basic\",\n  \"ports\": \"22,80,443\"\n}"}, "url": {"raw": "{{baseUrl}}/scan/pentesting/scan/vulnerability", "host": ["{{baseUrl}}"], "path": ["scan", "pentesting", "scan", "vulnerability"]}}}, {"name": "Vulnerability Scan - Aggressive", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"target\": \"***********\",\n  \"scan_type\": \"aggressive\",\n  \"ports\": \"1-1000\"\n}"}, "url": {"raw": "{{baseUrl}}/scan/pentesting/scan/vulnerability", "host": ["{{baseUrl}}"], "path": ["scan", "pentesting", "scan", "vulnerability"]}}}, {"name": "Vulnerability Scan - Stealth", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"target\": \"***********\",\n  \"scan_type\": \"stealth\",\n  \"ports\": \"80,443\"\n}"}, "url": {"raw": "{{baseUrl}}/scan/pentesting/scan/vulnerability", "host": ["{{baseUrl}}"], "path": ["scan", "pentesting", "scan", "vulnerability"]}}}, {"name": "Vulnerability Scan - Comprehensive", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"target\": \"***********\",\n  \"scan_type\": \"comprehensive\",\n  \"ports\": \"1-65535\"\n}"}, "url": {"raw": "{{baseUrl}}/scan/pentesting/scan/vulnerability", "host": ["{{baseUrl}}"], "path": ["scan", "pentesting", "scan", "vulnerability"]}}}]}, {"name": "🟥 Deep Scans", "item": [{"name": "<PERSON> - Basic", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has scan_id\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('scan_id');", "});", "", "pm.test(\"Scan type is basic\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.scan_type).to.eql('basic');", "});", "", "pm.test(\"Has progress tracking\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.progress_tracking).to.eql(true);", "});", "", "pm.test(\"Config has deep scan description\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.config.description).to.include('Aperçu rapide');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"target\": \"scanme.nmap.org\",\n  \"scan_type\": \"basic\",\n  \"ports\": \"22,80,443\"\n}"}, "url": {"raw": "{{baseUrl}}/scan/pentesting/scan/deep", "host": ["{{baseUrl}}"], "path": ["scan", "pentesting", "scan", "deep"]}}}, {"name": "<PERSON> - Comprehensive", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"target\": \"scanme.nmap.org\",\n  \"scan_type\": \"comprehensive\",\n  \"ports\": \"1-1000\"\n}"}, "url": {"raw": "{{baseUrl}}/scan/pentesting/scan/deep", "host": ["{{baseUrl}}"], "path": ["scan", "pentesting", "scan", "deep"]}}}]}, {"name": "📋 Scan History & Results", "item": [{"name": "<PERSON> History", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has scans array\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('scans');", "    pm.expect(jsonData.scans).to.be.an('array');", "});", "", "pm.test(\"Response has total count\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('total');", "    pm.expect(jsonData.total).to.be.a('number');", "});", "", "console.log(\"📊 Total scans found:\", pm.response.json().total);", "console.log(\"📋 Scans:\", pm.response.json().scans.length);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/scan/pentesting/scans", "host": ["{{baseUrl}}"], "path": ["scan", "pentesting", "scans"]}}}, {"name": "Get Specific Scan Result", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200 or 404\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 404]);", "});", "", "if (pm.response.code === 200) {", "    pm.test(\"Response has scan details\", function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('scan_id');", "        pm.expect(jsonData).to.have.property('target');", "        pm.expect(jsonData).to.have.property('status');", "    });", "} else {", "    pm.test(\"<PERSON>an not found message\", function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('error');", "    });", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/scan/pentesting/scan/{{last_scan_id}}", "host": ["{{baseUrl}}"], "path": ["scan", "pentesting", "scan", "{{last_scan_id}}"]}}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:5000", "type": "string"}]}