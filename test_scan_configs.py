#!/usr/bin/env python3
"""
Test script to demonstrate the different scan configurations
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.scan_manager import ScanManager

def test_scan_configurations():
    """Test and display the different scan configurations"""
    
    print("🔧 PICA Scan Configuration Test")
    print("=" * 50)
    
    scan_types = ['basic', 'aggressive', 'stealth', 'comprehensive']
    
    for scan_type in scan_types:
        config = ScanManager.SCAN_CONFIGS.get(scan_type, {})
        
        print(f"\n📋 {scan_type.upper()} SCAN CONFIGURATION")
        print("-" * 30)
        print(f"Description: {config.get('description', 'N/A')}")
        print(f"Intensity: {config.get('intensity', 'N/A')}")
        print(f"Timeout: {config.get('timeout', 'N/A')} seconds")
        
        print(f"\n🔧 Tool Parameters:")
        print(f"  Nmap: {' '.join(config.get('nmap_options', []))}")
        print(f"  Nikto: {' '.join(config.get('nikto_options', []))}")
        print(f"  SQLMap: {' '.join(config.get('sqlmap_options', []))}")
        print(f"  Dirb wordlist: {config.get('dirb_wordlist', 'N/A')}")
        print(f"  GoBuster threads: {config.get('gobuster_threads', 'N/A')}")
        print(f"  OpenVAS profile: {config.get('openvas_profile', 'N/A')}")
        print(f"  Metasploit modules: {len(config.get('metasploit_modules', []))} modules")
        
        # Show differences compared to basic scan
        if scan_type != 'basic':
            basic_config = ScanManager.SCAN_CONFIGS['basic']
            print(f"\n🔄 Differences from Basic scan:")
            
            if config.get('timeout') != basic_config.get('timeout'):
                print(f"  • Timeout: {basic_config.get('timeout')}s → {config.get('timeout')}s")
            
            if config.get('nmap_options') != basic_config.get('nmap_options'):
                print(f"  • Nmap options: {basic_config.get('nmap_options')} → {config.get('nmap_options')}")
            
            if config.get('gobuster_threads') != basic_config.get('gobuster_threads'):
                print(f"  • GoBuster threads: {basic_config.get('gobuster_threads')} → {config.get('gobuster_threads')}")
            
            if len(config.get('metasploit_modules', [])) != len(basic_config.get('metasploit_modules', [])):
                print(f"  • Metasploit modules: {len(basic_config.get('metasploit_modules', []))} → {len(config.get('metasploit_modules', []))}")
    
    print(f"\n🎯 SCAN TYPE RECOMMENDATIONS")
    print("-" * 30)
    print("🟢 BASIC: Quick assessment, minimal impact")
    print("   • Use for: Initial reconnaissance, production systems")
    print("   • Time: ~5 minutes")
    print("   • Risk: Very low")
    
    print("\n🔴 AGGRESSIVE: Maximum detection, high impact")
    print("   • Use for: Penetration testing, test environments")
    print("   • Time: ~30 minutes")
    print("   • Risk: High (may trigger alerts)")
    
    print("\n🔵 STEALTH: Slow and discrete, minimal footprint")
    print("   • Use for: Covert assessment, sensitive environments")
    print("   • Time: ~60 minutes")
    print("   • Risk: Very low (hard to detect)")
    
    print("\n🟡 COMPREHENSIVE: Complete audit, balanced approach")
    print("   • Use for: Security audits, compliance testing")
    print("   • Time: ~40 minutes")
    print("   • Risk: Medium")
    
    print(f"\n✅ Configuration test completed!")

if __name__ == '__main__':
    test_scan_configurations()
