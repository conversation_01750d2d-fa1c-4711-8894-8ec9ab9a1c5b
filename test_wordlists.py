#!/usr/bin/env python3
"""
Test script to verify wordlist configuration
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.scan_manager import <PERSON>anMana<PERSON>

def test_wordlist_configuration():
    """Test wordlist selection for different scan types"""
    
    print("🔧 Testing Dirb Wordlist Configuration")
    print("=" * 50)
    
    scan_types = ['basic', 'aggressive', 'stealth', 'comprehensive']
    
    for scan_type in scan_types:
        config = ScanManager.SCAN_CONFIGS.get(scan_type, {})
        dirb_wordlist = config.get('dirb_wordlist', 'unknown')
        
        print(f"\n📋 {scan_type.upper()} SCAN")
        print(f"   Dirb wordlist: {dirb_wordlist}")
        
        # Check if the wordlist file exists
        if dirb_wordlist == 'common.txt':
            wordlist_path = '/usr/share/dirb/wordlists/common.txt'
        elif dirb_wordlist == 'big.txt':
            wordlist_path = '/usr/share/dirb/wordlists/big.txt'
        else:
            wordlist_path = f'/usr/share/dirb/wordlists/{dirb_wordlist}'
        
        if os.path.exists(wordlist_path):
            # Get file size
            size = os.path.getsize(wordlist_path)
            print(f"   ✅ Wordlist exists: {wordlist_path} ({size} bytes)")
            
            # Count lines
            try:
                with open(wordlist_path, 'r') as f:
                    lines = sum(1 for line in f)
                print(f"   📊 Word count: {lines} entries")
            except:
                print(f"   ⚠️ Could not count lines in wordlist")
        else:
            print(f"   ❌ Wordlist not found: {wordlist_path}")
    
    print(f"\n🔍 Available Dirb Wordlists:")
    dirb_dir = '/usr/share/dirb/wordlists/'
    if os.path.exists(dirb_dir):
        wordlists = [f for f in os.listdir(dirb_dir) if f.endswith('.txt')]
        for wordlist in sorted(wordlists):
            wordlist_path = os.path.join(dirb_dir, wordlist)
            size = os.path.getsize(wordlist_path)
            print(f"   📄 {wordlist} ({size} bytes)")
    else:
        print(f"   ❌ Dirb wordlists directory not found: {dirb_dir}")
    
    print(f"\n✅ Wordlist configuration test completed!")

if __name__ == '__main__':
    test_wordlist_configuration()
